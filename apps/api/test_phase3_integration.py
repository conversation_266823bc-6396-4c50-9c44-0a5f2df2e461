#!/usr/bin/env python3
"""
Phase 3 Integration Test - Verify all components working together
Tests the complete bill processing pipeline with Phase 3 improvements
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Any
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.services.unified_bill_processing_service import UnifiedBillProcessingService
from app.core.config import get_settings
from app.db.database import get_db

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_phase3_integration():
    """Test Phase 3 integration through the unified bill processing service"""
    
    print("🚀 PHASE 3 INTEGRATION TEST")
    print("=" * 60)
    
    # Check configuration
    settings = get_settings()
    print(f"✅ OpenAI API Key: {'Configured' if settings.OPENAI_API_KEY else 'Missing'}")
    print(f"✅ Environment: {settings.ENVIRONMENT}")
    print(f"✅ Database URL: {'Configured' if settings.DATABASE_URL else 'Missing'}")
    print()
    
    # Create database session
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Initialize unified service
        unified_service = UnifiedBillProcessingService(db)
        print("✅ Unified Bill Processing Service initialized")
        print()
        
        # Test bill to process
        test_bill_number = "HR9999"  # Test bill number
        congress_session = 119
        
        print(f"📋 Processing test bill: {test_bill_number}")
        print("-" * 60)
        
        # Process the bill through the complete pipeline
        start_time = time.time()
        
        # Since we can't fetch real bill data for HR9999, we'll test the services directly
        print("🔬 Testing Phase 3 Components Directly...")
        print()
        
        # Test 1: Enhanced Importance Scorer
        print("1️⃣ Testing Enhanced Importance Scorer")
        try:
            from app.services.enhanced_importance_scorer import get_enhanced_importance_scorer
            
            test_metadata = {
                'title': 'Healthcare Technology Advancement Act',
                'summary': 'A comprehensive bill to improve healthcare technology infrastructure with $2 billion in funding',
                'bill_number': 'HR9999',
                'bill_type': 'hr',
                'chamber': 'house'
            }
            
            test_text = """
            SEC. 1. SHORT TITLE.
            This Act may be cited as the "Healthcare Technology Advancement Act".
            
            SEC. 2. APPROPRIATIONS.
            There is authorized to be appropriated $2,000,000,000 for fiscal year 2025 to implement healthcare technology improvements.
            
            SEC. 3. ENFORCEMENT.
            Any healthcare entity that fails to comply shall be subject to a civil penalty of not less than $100,000.
            """
            
            enhanced_scorer = get_enhanced_importance_scorer()
            enhanced_score = await enhanced_scorer.score_bill_with_evidence(
                test_text, test_metadata, None
            )
            
            print(f"   ✅ Enhanced Score: {enhanced_score.score}/100")
            print(f"   ✅ Importance Level: {enhanced_score.level.value}")
            print(f"   ✅ Evidence Quality: {enhanced_score.evidence_quality_score:.2f}")
            print(f"   ✅ Auto-Process: {enhanced_score.auto_process}")
            
            test1_success = enhanced_score.score >= 50  # Should recognize importance
            
        except Exception as e:
            print(f"   ❌ Enhanced scoring failed: {e}")
            test1_success = False
        
        print()
        
        # Test 2: Evidence-First Processing
        print("2️⃣ Testing Evidence-First Processing")
        try:
            from app.services.evidence_first_processor import get_evidence_first_processor
            
            processor = get_evidence_first_processor()
            
            # Create some raw evidence
            raw_evidence = [
                {
                    'id': 'test_ev_1',
                    'quote': 'There is authorized to be appropriated $2,000,000,000 for fiscal year 2025',
                    'heading': 'Appropriations',
                    'start_offset': 100,
                    'end_offset': 200
                },
                {
                    'id': 'test_ev_2',
                    'quote': 'civil penalty of not less than $100,000',
                    'heading': 'Enforcement',
                    'start_offset': 300,
                    'end_offset': 350
                }
            ]
            
            evidence_spans, processing_report = await processor.process_evidence_first(
                test_text, test_metadata, raw_evidence
            )
            
            print(f"   ✅ Processed {len(evidence_spans)} evidence spans")
            print(f"   ✅ Primary Topic: {processing_report['context']['primary_topic']}")
            print(f"   ✅ Quality Metrics: {processing_report['quality_metrics']['avg_priority_score']:.2f}")
            
            test2_success = len(evidence_spans) > 0
            
        except Exception as e:
            print(f"   ❌ Evidence processing failed: {e}")
            test2_success = False
        
        print()
        
        # Test 3: Balanced Analysis with Phase 3
        print("3️⃣ Testing Balanced Analysis with Phase 3 Quality")
        try:
            from app.services.balanced_analysis_service import BalancedAnalysisService
            from app.services.ai_service import AIService
            
            ai_service = AIService()
            balanced_service = BalancedAnalysisService(ai_service)
            
            # Use limited evidence for cost control
            test_evidence = [
                {
                    'id': 'test_1',
                    'quote': '$2,000,000,000 for fiscal year 2025',
                    'heading': 'Funding',
                    'start_offset': 100,
                    'end_offset': 140
                }
            ]
            
            result = await balanced_service.analyze_bill_balanced(
                bill_text=test_text[:500],  # Limit text for cost
                bill_metadata=test_metadata,
                evidence_spans=test_evidence
            )
            
            if result['success']:
                print(f"   ✅ Analysis completed successfully")
                print(f"   ✅ Total Cost: ${result['cost_breakdown']['total_cost']:.4f}")
                print(f"   ✅ Quality Metrics: {result['quality_metrics']['overall_score']:.2f}")
                print(f"   ✅ Evidence Quality: {result['evidence_quality']['average_quality_score']:.2f}")
                
                test3_success = (
                    result['cost_breakdown']['total_cost'] < 0.25 and
                    result['quality_metrics']['overall_score'] >= 0.7
                )
            else:
                print(f"   ❌ Analysis failed: {result.get('error')}")
                test3_success = False
                
        except Exception as e:
            print(f"   ❌ Balanced analysis failed: {e}")
            test3_success = False
        
        print()
        
        # Test 4: Verify Integration in Unified Service
        print("4️⃣ Testing Unified Service Integration")
        try:
            # Check that enhanced scorer is initialized
            has_enhanced_scorer = hasattr(unified_service, 'enhanced_importance_scorer')
            print(f"   {'✅' if has_enhanced_scorer else '❌'} Enhanced importance scorer integrated")
            
            # Check that re-evaluation method exists
            has_reeval_method = hasattr(unified_service, '_re_evaluate_importance_with_evidence')
            print(f"   {'✅' if has_reeval_method else '❌'} Re-evaluation method available")
            
            test4_success = has_enhanced_scorer and has_reeval_method
            
        except Exception as e:
            print(f"   ❌ Integration check failed: {e}")
            test4_success = False
        
        print()
        
        processing_time = time.time() - start_time
        
        # Final Assessment
        print("🏆 PHASE 3 INTEGRATION RESULTS")
        print("=" * 60)
        
        all_tests_passed = all([test1_success, test2_success, test3_success, test4_success])
        
        print(f"Test 1 - Enhanced Importance Scoring: {'✅ PASS' if test1_success else '❌ FAIL'}")
        print(f"Test 2 - Evidence-First Processing: {'✅ PASS' if test2_success else '❌ FAIL'}")
        print(f"Test 3 - Balanced Analysis Quality: {'✅ PASS' if test3_success else '❌ FAIL'}")
        print(f"Test 4 - Unified Service Integration: {'✅ PASS' if test4_success else '❌ FAIL'}")
        print()
        print(f"Total Processing Time: {processing_time:.2f}s")
        print()
        
        if all_tests_passed:
            print("🎉 PHASE 3 FULLY INTEGRATED AND OPERATIONAL!")
            print("✅ All evidence-first processing improvements working")
            print("✅ Enhanced importance scoring integrated")
            print("✅ Quality maintained while controlling costs")
            print("✅ Ready for Phase 4 implementation")
        else:
            print("⚠️ PHASE 3 PARTIAL SUCCESS")
            print("Some components need attention - check individual test results")
        
        return all_tests_passed
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    success = asyncio.run(test_phase3_integration())
    exit(0 if success else 1)