"""
AWS Lambda Function for Officials Data Refresh

This Lambda function automatically refreshes officials data on a weekly schedule.
It queries for stale officials records (older than 30 days) and refreshes them
from the OpenStates API with updated information.

Triggered by: CloudWatch Events (weekly schedule)
Environment Variables Required:
- DATABASE_URL: PostgreSQL connection string
- OPENSTATES_API_KEY: OpenStates API key

Example CloudWatch Event:
{
  "version": "0",
  "id": "...",
  "detail-type": "Scheduled Event",
  "source": "aws.events",
  "account": "...",
  "time": "...",
  "region": "...",
  "detail": {}
}
"""

import json
import logging
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Add modules to path
sys.path.insert(0, '/opt/python')  # Lambda layer path
sys.path.insert(0, os.path.dirname(__file__))

try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
    import requests
    from dataclasses import dataclass
except ImportError as e:
    logger.error(f"Failed to import required modules: {e}")
    raise

@dataclass
class StaleOfficial:
    """Represents a stale official record that needs refreshing"""
    id: str
    name: str
    title: str
    openstates_id: str
    updated_at: datetime
    days_since_update: int

class OfficialsRefreshService:
    """Service for refreshing stale officials data"""
    
    def __init__(self):
        self.database_url = os.environ.get('DATABASE_URL')
        self.openstates_api_key = os.environ.get('OPENSTATES_API_KEY')
        self.openstates_base_url = "https://v3.openstates.org"
        
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable is required")
        
        if not self.openstates_api_key:
            logger.warning("OPENSTATES_API_KEY not configured, refresh will be limited")
        
        self.stats = {
            'stale_officials_found': 0,
            'officials_refreshed': 0,
            'officials_failed': 0,
            'api_calls_made': 0,
            'start_time': datetime.utcnow()
        }
    
    def get_database_connection(self):
        """Get a database connection"""
        try:
            return psycopg2.connect(self.database_url)
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    def find_stale_officials(self, days_threshold: int = 30) -> List[StaleOfficial]:
        """
        Find officials whose data is older than the threshold.
        
        Args:
            days_threshold: Number of days after which data is considered stale
            
        Returns:
            List of StaleOfficial objects
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days_threshold)
        
        query = """
            SELECT id, name, title, openstates_id, updated_at,
                   EXTRACT(days FROM (NOW() - updated_at)) as days_since_update
            FROM officials 
            WHERE is_active = true 
              AND openstates_id IS NOT NULL 
              AND updated_at < %s
            ORDER BY updated_at ASC
            LIMIT 100
        """
        
        try:
            with self.get_database_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute(query, (cutoff_date,))
                    rows = cursor.fetchall()
                    
                    stale_officials = []
                    for row in rows:
                        stale_official = StaleOfficial(
                            id=row['id'],
                            name=row['name'],
                            title=row['title'],
                            openstates_id=row['openstates_id'],
                            updated_at=row['updated_at'],
                            days_since_update=int(row['days_since_update'])
                        )
                        stale_officials.append(stale_official)
                    
                    self.stats['stale_officials_found'] = len(stale_officials)
                    return stale_officials
                    
        except Exception as e:
            logger.error(f"Error finding stale officials: {e}")
            raise
    
    def refresh_official_data(self, official: StaleOfficial) -> bool:
        """
        Refresh data for a single official from OpenStates API.
        
        Args:
            official: StaleOfficial to refresh
            
        Returns:
            True if refresh was successful, False otherwise
        """
        if not self.openstates_api_key:
            logger.warning(f"No API key, skipping refresh for {official.name}")
            return False
        
        try:
            # Fetch updated data from OpenStates API
            headers = {
                'X-API-KEY': self.openstates_api_key,
                'Accept': 'application/json'
            }
            
            url = f"{self.openstates_base_url}/people/{official.openstates_id}"
            
            logger.info(f"Fetching updated data for {official.name} (ID: {official.openstates_id})")
            response = requests.get(url, headers=headers, timeout=30)
            self.stats['api_calls_made'] += 1
            
            if response.status_code == 404:
                logger.warning(f"Official {official.name} not found in OpenStates API, may have left office")
                # Mark as inactive instead of deleting
                self._mark_official_inactive(official.id)
                return True
            
            response.raise_for_status()
            person_data = response.json()
            
            # Extract and update relevant fields
            updated_data = self._extract_official_data(person_data)
            if updated_data:
                return self._update_official_in_database(official.id, updated_data)
            else:
                logger.warning(f"No valid data extracted for {official.name}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API request failed for {official.name}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error refreshing {official.name}: {e}")
            return False
    
    def _extract_official_data(self, person_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract relevant fields from OpenStates person data"""
        try:
            current_role = person_data.get('current_role', {})
            
            # Extract basic info
            extracted_data = {
                'name': person_data.get('name', ''),
                'title': current_role.get('title', ''),
                'party': person_data.get('party', ''),
                'district': current_role.get('district', ''),
                'chamber': current_role.get('chamber', ''),
                'bio': person_data.get('biography', ''),
                'profile_picture_url': person_data.get('image', ''),
            }
            
            # Parse contact details
            contact_details = person_data.get('contact_details', [])
            for contact in contact_details:
                contact_type = contact.get('type', '').lower()
                value = contact.get('value', '')
                
                if contact_type == 'email' and value:
                    extracted_data['email'] = value
                elif contact_type in ['phone', 'voice'] and value:
                    extracted_data['phone'] = value
            
            # Parse social media links
            links = person_data.get('links', [])
            social_media = {}
            
            for link in links:
                url = link.get('url', '').strip()
                note = link.get('note', '').lower().strip()
                
                if 'twitter' in note or 'twitter.com' in url:
                    if 'twitter.com/' in url:
                        handle = url.split('twitter.com/')[-1].split('/')[0].split('?')[0]
                        if handle and handle != 'intent':
                            extracted_data['twitter_handle'] = handle
                            social_media['Twitter'] = url
                elif 'facebook' in note or 'facebook.com' in url:
                    extracted_data['facebook_url'] = url
                    social_media['Facebook'] = url
                elif 'instagram' in note or 'instagram.com' in url:
                    if 'instagram.com/' in url:
                        handle = url.split('instagram.com/')[-1].split('/')[0].split('?')[0]
                        if handle:
                            extracted_data['instagram_handle'] = handle
                            social_media['Instagram'] = url
                elif 'youtube' in note or 'youtube.com' in url:
                    extracted_data['youtube_channel'] = url
                    social_media['YouTube'] = url
                elif 'linkedin' in note or 'linkedin.com' in url:
                    extracted_data['linkedin_url'] = url
                    social_media['LinkedIn'] = url
                elif 'website' in note or 'official' in note:
                    extracted_data['website'] = url
                    if 'house.gov' in url or 'senate.gov' in url or '.gov' in url:
                        social_media['Official Website'] = url
            
            if social_media:
                extracted_data['social_media'] = json.dumps(social_media)
            
            # Extract external IDs
            identifiers = person_data.get('identifiers', [])
            for identifier in identifiers:
                scheme = identifier.get('scheme', '').lower()
                identifier_value = identifier.get('identifier', '')
                
                if scheme == 'govtrack' and identifier_value:
                    extracted_data['govtrack_id'] = identifier_value
                elif scheme == 'bioguide' and identifier_value:
                    extracted_data['bioguide_id'] = identifier_value
            
            # Parse name components
            name = extracted_data.get('name', '')
            if name:
                name_parts = name.strip().split()
                if len(name_parts) >= 2:
                    extracted_data['first_name'] = name_parts[0]
                    extracted_data['last_name'] = ' '.join(name_parts[1:])
                    extracted_data['full_name'] = name
                elif len(name_parts) == 1:
                    extracted_data['first_name'] = name_parts[0]
                    extracted_data['full_name'] = name
            
            return extracted_data
            
        except Exception as e:
            logger.error(f"Error extracting official data: {e}")
            return {}
    
    def _update_official_in_database(self, official_id: str, data: Dict[str, Any]) -> bool:
        """Update official record in database with fresh data"""
        try:
            # Build dynamic update query based on available data
            set_clauses = []
            values = []
            
            for field, value in data.items():
                if value is not None and value != '':
                    set_clauses.append(f"{field} = %s")
                    values.append(value)
            
            if not set_clauses:
                logger.warning(f"No data to update for official {official_id}")
                return False
            
            # Always update the updated_at timestamp
            set_clauses.append("updated_at = NOW()")
            
            query = f"""
                UPDATE officials 
                SET {', '.join(set_clauses)}
                WHERE id = %s
            """
            values.append(official_id)
            
            with self.get_database_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, values)
                    if cursor.rowcount > 0:
                        conn.commit()
                        logger.info(f"Successfully updated official {official_id}")
                        return True
                    else:
                        logger.warning(f"No rows updated for official {official_id}")
                        return False
                        
        except Exception as e:
            logger.error(f"Error updating official {official_id}: {e}")
            return False
    
    def _mark_official_inactive(self, official_id: str):
        """Mark an official as inactive (no longer in office)"""
        try:
            query = """
                UPDATE officials 
                SET is_active = false, updated_at = NOW()
                WHERE id = %s
            """
            
            with self.get_database_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, (official_id,))
                    conn.commit()
                    logger.info(f"Marked official {official_id} as inactive")
                    
        except Exception as e:
            logger.error(f"Error marking official {official_id} as inactive: {e}")
    
    def refresh_all_stale_officials(self, days_threshold: int = 30) -> Dict[str, Any]:
        """
        Refresh all stale officials data.
        
        Args:
            days_threshold: Number of days after which data is considered stale
            
        Returns:
            Dictionary containing refresh statistics
        """
        logger.info(f"Starting officials data refresh (threshold: {days_threshold} days)")
        
        # Find stale officials
        stale_officials = self.find_stale_officials(days_threshold)
        
        if not stale_officials:
            logger.info("No stale officials found")
            return self._get_stats_summary()
        
        logger.info(f"Found {len(stale_officials)} stale officials to refresh")
        
        # Refresh each official
        for official in stale_officials:
            logger.info(f"Refreshing: {official.name} (last updated {official.days_since_update} days ago)")
            
            try:
                success = self.refresh_official_data(official)
                if success:
                    self.stats['officials_refreshed'] += 1
                else:
                    self.stats['officials_failed'] += 1
            except Exception as e:
                logger.error(f"Failed to refresh {official.name}: {e}")
                self.stats['officials_failed'] += 1
        
        return self._get_stats_summary()
    
    def _get_stats_summary(self) -> Dict[str, Any]:
        """Get summary statistics for the refresh operation"""
        end_time = datetime.utcnow()
        duration = end_time - self.stats['start_time']
        
        return {
            'success': True,
            'duration_seconds': duration.total_seconds(),
            'stale_officials_found': self.stats['stale_officials_found'],
            'officials_refreshed': self.stats['officials_refreshed'],
            'officials_failed': self.stats['officials_failed'],
            'api_calls_made': self.stats['api_calls_made'],
            'timestamp': end_time.isoformat()
        }

def lambda_handler(event, context):
    """
    AWS Lambda handler function for officials data refresh.
    
    Args:
        event: Lambda event data (from CloudWatch Events)
        context: Lambda context object
        
    Returns:
        Dict containing operation results
    """
    logger.info("Starting officials data refresh Lambda function")
    logger.info(f"Event: {json.dumps(event)}")
    
    try:
        # Create refresh service
        refresh_service = OfficialsRefreshService()
        
        # Run the refresh
        results = refresh_service.refresh_all_stale_officials()
        
        logger.info(f"Refresh completed successfully: {json.dumps(results)}")
        
        return {
            'statusCode': 200,
            'body': json.dumps(results)
        }
        
    except Exception as e:
        logger.error(f"Officials refresh failed: {e}")
        
        return {
            'statusCode': 500,
            'body': json.dumps({
                'success': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            })
        }

# For local testing
if __name__ == "__main__":
    # Mock event and context for local testing
    test_event = {
        "version": "0",
        "id": "test-event",
        "detail-type": "Scheduled Event",
        "source": "aws.events",
        "detail": {}
    }
    
    class MockContext:
        def __init__(self):
            self.aws_request_id = "test-request-id"
            self.function_name = "officials-refresh-test"
    
    result = lambda_handler(test_event, MockContext())
    print(json.dumps(result, indent=2))