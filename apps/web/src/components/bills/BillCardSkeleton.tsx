import React from 'react';

interface BillCardSkeletonProps {
  count?: number;
}

const SingleBillCardSkeleton = () => (
  <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden h-auto flex flex-col animate-pulse">
    {/* Header Section */}
    <div className="p-6 pb-4 flex-shrink-0">
      {/* Bill Number & Status */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className="bg-gray-200 h-8 w-20 rounded-full"></div>
          <div className="bg-gray-200 h-8 w-24 rounded-full"></div>
        </div>
        <div className="bg-gray-200 h-6 w-16 rounded-full"></div>
      </div>

      {/* Title */}
      <div className="mb-3 space-y-2">
        <div className="bg-gray-200 h-6 w-full rounded"></div>
        <div className="bg-gray-200 h-6 w-3/4 rounded"></div>
      </div>

      {/* Key Metrics Row */}
      <div className="flex items-center gap-3 mb-3 h-5">
        <div className="bg-gray-200 h-4 w-16 rounded"></div>
        <div className="bg-gray-200 h-4 w-12 rounded"></div>
        <div className="bg-gray-200 h-4 w-20 rounded"></div>
      </div>
    </div>

    {/* Content Section */}
    <div className="px-6 flex-1 flex flex-col justify-between">
      {/* Summary */}
      <div className="mb-4 space-y-2">
        <div className="bg-gray-200 h-4 w-full rounded"></div>
        <div className="bg-gray-200 h-4 w-full rounded"></div>
        <div className="bg-gray-200 h-4 w-2/3 rounded"></div>
      </div>

      {/* Tags */}
      <div className="mb-4 flex gap-2">
        <div className="bg-gray-200 h-6 w-16 rounded-full"></div>
        <div className="bg-gray-200 h-6 w-20 rounded-full"></div>
      </div>

      {/* Values */}
      <div className="mb-4 flex gap-1">
        <div className="bg-gray-200 h-6 w-14 rounded-full"></div>
        <div className="bg-gray-200 h-6 w-18 rounded-full"></div>
        <div className="bg-gray-200 h-6 w-16 rounded-full"></div>
      </div>
    </div>

    {/* Action Section */}
    <div className="px-6 pb-6 flex-shrink-0 space-y-3">
      <div className="bg-gray-200 h-12 w-full rounded-lg"></div>
      <div className="flex justify-center">
        <div className="bg-gray-200 h-4 w-24 rounded"></div>
      </div>
    </div>
  </div>
);

export const BillCardSkeleton: React.FC<BillCardSkeletonProps> = ({ count = 6 }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 items-start">
      {Array.from({ length: count }, (_, index) => (
        <SingleBillCardSkeleton key={index} />
      ))}
    </div>
  );
};