"""
Unit tests for AI Usage models that don't require database connection.

These tests validate model structure, defaults, and basic functionality
without requiring a PostgreSQL database connection.
"""

import pytest
from datetime import datetime, timezone
from sqlalchemy import Column, String, Integer, Float, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.dialects import postgresql

from app.models.ai_usage import AIUsageLog, AIUsageSummary, AIBudgetAlert


class TestAIUsageLogStructure:
    """Test AI Usage Log model structure and attributes"""

    def test_ai_usage_log_table_name(self):
        """Test that table name is correct"""
        assert AIUsageLog.__tablename__ == "ai_usage_logs"

    def test_ai_usage_log_columns_exist(self):
        """Test that all required columns exist with correct types"""
        # Get all column names and types
        columns = {col.name: col for col in AIUsageLog.__table__.columns}
        
        # Test primary key
        assert 'id' in columns
        assert isinstance(columns['id'].type, String)
        assert columns['id'].primary_key is True
        
        # Test operation details
        assert 'operation_type' in columns
        assert isinstance(columns['operation_type'].type, String)
        assert columns['operation_type'].nullable is False
        
        assert 'operation_subtype' in columns
        assert isinstance(columns['operation_subtype'].type, String)
        assert columns['operation_subtype'].nullable is True
        
        assert 'bill_id' in columns
        assert isinstance(columns['bill_id'].type, String)
        assert columns['bill_id'].nullable is True
        
        # Test AI model details
        assert 'model_name' in columns
        assert isinstance(columns['model_name'].type, String)
        assert columns['model_name'].nullable is False
        
        assert 'provider' in columns
        assert isinstance(columns['provider'].type, String)
        assert columns['provider'].nullable is False
        
        # Test token usage
        assert 'prompt_tokens' in columns
        assert isinstance(columns['prompt_tokens'].type, Integer)
        assert columns['prompt_tokens'].nullable is False
        
        assert 'completion_tokens' in columns
        assert isinstance(columns['completion_tokens'].type, Integer)
        assert columns['completion_tokens'].nullable is False
        
        assert 'total_tokens' in columns
        assert isinstance(columns['total_tokens'].type, Integer)
        assert columns['total_tokens'].nullable is False
        
        # Test cost tracking
        assert 'prompt_cost' in columns
        assert isinstance(columns['prompt_cost'].type, Float)
        assert columns['prompt_cost'].nullable is False
        
        assert 'completion_cost' in columns
        assert isinstance(columns['completion_cost'].type, Float)
        assert columns['completion_cost'].nullable is False
        
        assert 'total_cost' in columns
        assert isinstance(columns['total_cost'].type, Float)
        assert columns['total_cost'].nullable is False
        
        # Test performance metrics
        assert 'response_time_ms' in columns
        assert isinstance(columns['response_time_ms'].type, Float)
        assert columns['response_time_ms'].nullable is True
        
        assert 'success' in columns
        assert isinstance(columns['success'].type, Boolean)
        assert columns['success'].nullable is False
        
        assert 'error_message' in columns
        assert isinstance(columns['error_message'].type, Text)
        assert columns['error_message'].nullable is True
        
        # Test metadata
        assert 'created_at' in columns
        assert isinstance(columns['created_at'].type, DateTime)
        assert columns['created_at'].nullable is False
        
        assert 'user_id' in columns
        assert isinstance(columns['user_id'].type, String)
        assert columns['user_id'].nullable is True

    def test_ai_usage_log_defaults(self):
        """Test that default values are correctly set"""
        # Create instance without setting optional fields
        log = AIUsageLog(
            id="test-001",
            operation_type="test",
            model_name="gpt-4"
        )
        
        # Test defaults are applied
        assert log.provider == "openai"
        assert log.prompt_tokens == 0
        assert log.completion_tokens == 0
        assert log.total_tokens == 0
        assert log.prompt_cost == 0.0
        assert log.completion_cost == 0.0
        assert log.total_cost == 0.0
        assert log.success is True
        assert log.updated_at is None  # Explicitly set to None

    def test_ai_usage_log_foreign_key(self):
        """Test foreign key relationship to bills table"""
        columns = {col.name: col for col in AIUsageLog.__table__.columns}
        bill_id_col = columns['bill_id']
        
        # Check if foreign key constraint exists
        foreign_keys = list(bill_id_col.foreign_keys)
        assert len(foreign_keys) == 1
        assert foreign_keys[0].column.table.name == "bills"


class TestAIUsageSummaryStructure:
    """Test AI Usage Summary model structure and attributes"""

    def test_ai_usage_summary_table_name(self):
        """Test that table name is correct"""
        assert AIUsageSummary.__tablename__ == "ai_usage_summaries"

    def test_ai_usage_summary_columns_exist(self):
        """Test that all required columns exist with correct types"""
        columns = {col.name: col for col in AIUsageSummary.__table__.columns}
        
        # Test primary key
        assert 'id' in columns
        assert isinstance(columns['id'].type, String)
        assert columns['id'].primary_key is True
        
        # Test time period
        assert 'date_bucket' in columns
        assert isinstance(columns['date_bucket'].type, DateTime)
        assert columns['date_bucket'].nullable is False
        
        assert 'period_type' in columns
        assert isinstance(columns['period_type'].type, String)
        assert columns['period_type'].nullable is False
        
        # Test operation aggregates
        assert 'operation_type' in columns
        assert isinstance(columns['operation_type'].type, String)
        assert columns['operation_type'].nullable is True
        
        assert 'model_name' in columns
        assert isinstance(columns['model_name'].type, String)
        assert columns['model_name'].nullable is True
        
        # Test usage totals
        assert 'total_requests' in columns
        assert isinstance(columns['total_requests'].type, Integer)
        assert columns['total_requests'].nullable is False
        
        assert 'successful_requests' in columns
        assert isinstance(columns['successful_requests'].type, Integer)
        assert columns['successful_requests'].nullable is False
        
        assert 'failed_requests' in columns
        assert isinstance(columns['failed_requests'].type, Integer)
        assert columns['failed_requests'].nullable is False
        
        # Test cost totals
        assert 'total_cost' in columns
        assert isinstance(columns['total_cost'].type, Float)
        assert columns['total_cost'].nullable is False

    def test_ai_usage_summary_defaults(self):
        """Test that default values are correctly set"""
        summary = AIUsageSummary(
            id="test-summary-001",
            date_bucket=datetime(2024, 1, 1)
        )
        
        # Test defaults
        assert summary.period_type == "daily"
        assert summary.total_requests == 0
        assert summary.successful_requests == 0
        assert summary.failed_requests == 0
        assert summary.total_cost == 0.0
        assert summary.bills_processed == 0


class TestAIBudgetAlertStructure:
    """Test AI Budget Alert model structure and attributes"""

    def test_ai_budget_alert_table_name(self):
        """Test that table name is correct"""
        assert AIBudgetAlert.__tablename__ == "ai_budget_alerts"

    def test_ai_budget_alert_columns_exist(self):
        """Test that all required columns exist with correct types"""
        columns = {col.name: col for col in AIBudgetAlert.__table__.columns}
        
        # Test primary key
        assert 'id' in columns
        assert isinstance(columns['id'].type, String)
        assert columns['id'].primary_key is True
        
        # Test alert configuration
        assert 'alert_name' in columns
        assert isinstance(columns['alert_name'].type, String)
        assert columns['alert_name'].nullable is False
        
        assert 'alert_type' in columns
        assert isinstance(columns['alert_type'].type, String)
        assert columns['alert_type'].nullable is False
        
        assert 'threshold_amount' in columns
        assert isinstance(columns['threshold_amount'].type, Float)
        assert columns['threshold_amount'].nullable is False
        
        assert 'threshold_tokens' in columns
        assert isinstance(columns['threshold_tokens'].type, Integer)
        assert columns['threshold_tokens'].nullable is True
        
        # Test alert status
        assert 'is_active' in columns
        assert isinstance(columns['is_active'].type, Boolean)
        assert columns['is_active'].nullable is False
        
        assert 'times_triggered' in columns
        assert isinstance(columns['times_triggered'].type, Integer)
        assert columns['times_triggered'].nullable is False
        
        # Test notification settings
        assert 'notification_emails' in columns
        assert isinstance(columns['notification_emails'].type, postgresql.JSONB)
        assert columns['notification_emails'].nullable is True

    def test_ai_budget_alert_defaults(self):
        """Test that default values are correctly set"""
        alert = AIBudgetAlert(
            id="test-alert-001",
            alert_name="Test Alert",
            alert_type="daily",
            threshold_amount=10.0
        )
        
        # Test defaults
        assert alert.is_active is True
        assert alert.times_triggered == 0
        assert alert.last_triggered is None


class TestAIUsageModelValidation:
    """Test model validation and business logic"""

    def test_ai_usage_log_creation_with_all_fields(self):
        """Test creating AI usage log with all fields"""
        timestamp = datetime.now(timezone.utc).replace(tzinfo=None)
        
        log = AIUsageLog(
            id="comprehensive-test-001",
            operation_type="comprehensive_analysis",
            operation_subtype="support_reasons",
            bill_id="bill-123",
            model_name="gpt-4-turbo",
            provider="openai",
            prompt_tokens=2000,
            completion_tokens=800,
            total_tokens=2800,
            prompt_cost=0.02,
            completion_cost=0.024,
            total_cost=0.044,
            response_time_ms=1500.5,
            success=True,
            prompt_length=8000,
            response_length=3200,
            created_at=timestamp,
            user_id="user-456",
            session_id="session-789"
        )
        
        # Verify all fields are set correctly
        assert log.id == "comprehensive-test-001"
        assert log.operation_type == "comprehensive_analysis"
        assert log.operation_subtype == "support_reasons"
        assert log.bill_id == "bill-123"
        assert log.model_name == "gpt-4-turbo"
        assert log.provider == "openai"
        assert log.prompt_tokens == 2000
        assert log.completion_tokens == 800
        assert log.total_tokens == 2800
        assert log.prompt_cost == 0.02
        assert log.completion_cost == 0.024
        assert log.total_cost == 0.044
        assert log.response_time_ms == 1500.5
        assert log.success is True
        assert log.prompt_length == 8000
        assert log.response_length == 3200
        assert log.created_at == timestamp
        assert log.user_id == "user-456"
        assert log.session_id == "session-789"

    def test_ai_usage_summary_creation_with_all_fields(self):
        """Test creating AI usage summary with all fields"""
        summary_date = datetime(2024, 1, 15)
        
        summary = AIUsageSummary(
            id="comprehensive-summary-001",
            date_bucket=summary_date,
            period_type="daily",
            operation_type="bill_analysis",
            model_name="gpt-4-turbo",
            total_requests=25,
            successful_requests=23,
            failed_requests=2,
            total_prompt_tokens=50000,
            total_completion_tokens=25000,
            total_tokens=75000,
            total_prompt_cost=0.50,
            total_completion_cost=0.75,
            total_cost=1.25,
            avg_response_time_ms=1200.5,
            avg_tokens_per_request=3000.0,
            avg_cost_per_request=0.05,
            bills_processed=10,
            avg_cost_per_bill=0.125
        )
        
        # Verify all fields are set correctly
        assert summary.id == "comprehensive-summary-001"
        assert summary.date_bucket == summary_date
        assert summary.period_type == "daily"
        assert summary.operation_type == "bill_analysis"
        assert summary.model_name == "gpt-4-turbo"
        assert summary.total_requests == 25
        assert summary.successful_requests == 23
        assert summary.failed_requests == 2
        assert summary.total_tokens == 75000
        assert summary.total_cost == 1.25
        assert summary.avg_cost_per_bill == 0.125

    def test_ai_budget_alert_creation_with_all_fields(self):
        """Test creating AI budget alert with all fields"""
        alert = AIBudgetAlert(
            id="comprehensive-alert-001",
            alert_name="Daily Spending Limit",
            alert_type="daily",
            threshold_amount=10.0,
            threshold_tokens=100000,
            operation_type="bill_analysis",
            model_name="gpt-4-turbo",
            is_active=True,
            last_triggered=None,
            times_triggered=0,
            notification_emails=["<EMAIL>", "<EMAIL>"],
            webhook_url="https://api.example.com/alerts"
        )
        
        # Verify all fields are set correctly
        assert alert.id == "comprehensive-alert-001"
        assert alert.alert_name == "Daily Spending Limit"
        assert alert.alert_type == "daily"
        assert alert.threshold_amount == 10.0
        assert alert.threshold_tokens == 100000
        assert alert.operation_type == "bill_analysis"
        assert alert.model_name == "gpt-4-turbo"
        assert alert.is_active is True
        assert alert.times_triggered == 0
        assert "<EMAIL>" in alert.notification_emails
        assert alert.webhook_url == "https://api.example.com/alerts"


class TestAIUsageModelEdgeCases:
    """Test edge cases and boundary conditions"""

    def test_ai_usage_log_with_zero_values(self):
        """Test AI usage log with zero costs and tokens"""
        log = AIUsageLog(
            id="zero-test-001",
            operation_type="cached_response",
            model_name="gpt-4-turbo",
            prompt_tokens=0,
            completion_tokens=0,
            total_tokens=0,
            prompt_cost=0.0,
            completion_cost=0.0,
            total_cost=0.0
        )
        
        assert log.total_tokens == 0
        assert log.total_cost == 0.0

    def test_ai_usage_log_with_negative_costs(self):
        """Test AI usage log with negative costs (refunds)"""
        log = AIUsageLog(
            id="refund-test-001",
            operation_type="refund_adjustment",
            model_name="gpt-4-turbo",
            prompt_cost=-0.05,
            completion_cost=0.0,
            total_cost=-0.05
        )
        
        assert log.total_cost == -0.05
        assert log.prompt_cost == -0.05

    def test_ai_usage_log_with_unicode_content(self):
        """Test AI usage log with unicode characters"""
        log = AIUsageLog(
            id="unicode-test-001",
            operation_type="分析法案_🏛️📊",
            model_name="gpt-4-turbo",
            error_message="错误信息: API调用失败 💥"
        )
        
        assert "🏛️" in log.operation_type
        assert "💥" in log.error_message

    def test_ai_budget_alert_with_jsonb_emails(self):
        """Test AI budget alert with JSONB email list"""
        emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        
        alert = AIBudgetAlert(
            id="jsonb-test-001",
            alert_name="Test Alert",
            alert_type="monthly",
            threshold_amount=100.0,
            notification_emails=emails
        )
        
        assert len(alert.notification_emails) == 3
        assert "<EMAIL>" in alert.notification_emails
        assert "<EMAIL>" in alert.notification_emails
