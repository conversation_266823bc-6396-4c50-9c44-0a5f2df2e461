@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* shadcn/ui CSS variables */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 213 91% 46%;         /* Maps to --civic-primary #2563EB */
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;   /* Maps to --civic-error */
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 213 91% 46%;
  --radius: 0.75rem;
  --civic-primary: 213 91% 46%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;

  /* Design System Color Palette (preserved) */
  --civic-primary: #2563EB;      /* Action blue */
  --civic-secondary: #7C3AED;    /* Civic purple */
  --civic-success: #10B981;      /* Trust green */
  --civic-warning: #F59E0B;      /* Urgent amber */
  --civic-error: #EF4444;        /* Oppose red */
  --civic-neutral: #6B7280;      /* Neutral gray */
  --civic-dark: #111827;         /* Dark text */
  --civic-light: #F9FAFB;        /* Light background */
  
  /* Semantic Colors */
  --color-support: #10B981;      /* Support position */
  --color-oppose: #EF4444;       /* Oppose position */
  --color-amend: #8B5CF6;        /* Amend position */
  --color-urgent: #F59E0B;       /* Urgency indicator */
  
  /* Bill Status Colors (Fixed Semantic Set) */
  --status-introduced-bg: #F3F4F6;       /* Gray-100 */
  --status-introduced-text: #374151;     /* Gray-700 */
  --status-committee-bg: #FEF3C7;        /* Amber-100 */
  --status-committee-text: #92400E;      /* Amber-800 */
  --status-floor-bg: #FEE2E2;            /* Red-100 */
  --status-floor-text: #991B1B;          /* Red-800 */
  --status-passed-house-bg: #DBEAFE;     /* Blue-100 */
  --status-passed-house-text: #1E40AF;   /* Blue-800 */
  --status-passed-senate-bg: #E0E7FF;    /* Indigo-100 */
  --status-passed-senate-text: #3730A3;  /* Indigo-800 */
  --status-enacted-bg: #D1FAE5;          /* Green-100 */
  --status-enacted-text: #065F46;        /* Green-800 */
  --status-vetoed-bg: #FEE2E2;           /* Red-100 */
  --status-vetoed-text: #991B1B;         /* Red-800 */
  --status-failed-bg: #F3F4F6;           /* Gray-100 */
  --status-failed-text: #6B7280;         /* Gray-500 */
  
  /* Legacy variables for compatibility */
  --background: #ffffff;
  --foreground: #171717;
  
  /* Typography Scale (Design System) */
  --text-micro: 0.75rem;         /* 12px - micro copy */
  --text-small: 0.875rem;        /* 14px - body small */
  --text-base: 1rem;             /* 16px - body base */
  --text-lg: 1.125rem;           /* 18px */
  --text-xl: 1.25rem;            /* 20px */
  --text-2xl: 1.5rem;            /* 24px - H3 */
  --text-3xl: 2rem;              /* 32px - H2 */
  --text-4xl: 3rem;              /* 48px - H1 */
  
  /* Spacing Scale (8px base system) */
  --space-micro: 0.25rem;        /* 4px */
  --space-small: 0.5rem;         /* 8px */
  --space-medium: 1rem;          /* 16px */
  --space-large: 2rem;           /* 32px */
  --space-xlarge: 4rem;          /* 64px */
  --space-section: 6rem;         /* 96px */
  
  /* Border Radius */
  --radius-sm: 0.375rem;         /* 6px */
  --radius-md: 0.5rem;           /* 8px */
  --radius-lg: 0.75rem;          /* 12px */
  --radius-xl: 1rem;             /* 16px */
  --radius-2xl: 1.5rem;          /* 24px */
  
  /* Shadows (Performance optimized) */
  --shadow-card: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-card-hover: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-button: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-focus: 0 0 0 2px var(--civic-primary);
  
  /* Animation Durations */
  --duration-micro: 150ms;
  --duration-fast: 200ms;
  --duration-normal: 300ms;
  --duration-slow: 600ms;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --civic-background: #111827;
  }
}

body {
  background: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Design System Typography */
.heading-hero {
  font-size: var(--text-4xl);
  line-height: 3.5rem;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.heading-1 {
  font-size: var(--text-3xl);
  line-height: 2.5rem;
  font-weight: 600;
}

.heading-2 {
  font-size: var(--text-2xl);
  line-height: 2rem;
  font-weight: 600;
}

.heading-3 {
  font-size: var(--text-xl);
  line-height: 1.75rem;
  font-weight: 600;
}

.body-large {
  font-size: var(--text-lg);
  line-height: 1.75rem;
  font-weight: 400;
}

.body-base {
  font-size: var(--text-base);
  line-height: 1.5rem;
  font-weight: 400;
}

.body-small {
  font-size: var(--text-small);
  line-height: 1.25rem;
  font-weight: 400;
}

.text-micro {
  font-size: var(--text-micro);
  line-height: 1rem;
  font-weight: 500;
}

/* Campaign Card Components */
.campaign-card {
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-card);
  border: 1px solid #E5E7EB;
  transition: all var(--duration-normal) ease-in-out;
  overflow: hidden;
}

.campaign-card:hover {
  box-shadow: var(--shadow-card-hover);
  transform: translateY(-8px);
  border-color: #D1D5DB;
}

.campaign-card-featured {
  border-color: var(--civic-primary);
  box-shadow: 0 0 0 1px var(--civic-primary), var(--shadow-card);
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.02) 0%, rgba(124, 58, 237, 0.02) 100%);
}

.civic-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-card);
  border: 1px solid #E5E7EB;
  transition: all var(--duration-fast) ease-in-out;
}

.civic-card:hover {
  box-shadow: var(--shadow-card-hover);
  border-color: #D1D5DB;
}

.civic-card-selected {
  border-color: var(--civic-primary);
  box-shadow: var(--shadow-focus), var(--shadow-card);
  background: rgb(37 99 235 / 0.02);
}

/* Design System Button Components */
.btn-primary {
  background: var(--civic-primary);
  color: white;
  border: none;
  border-radius: var(--radius-xl);
  padding: var(--space-medium) var(--space-large);
  font-weight: 600;
  font-size: var(--text-base);
  transition: all var(--duration-fast) ease-in-out;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-small);
}

.btn-primary:hover:not(:disabled) {
  background: #1D4ED8;
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-button);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0) scale(0.98);
}

.btn-primary:disabled {
  background: #9CA3AF;
  cursor: not-allowed;
  transform: none;
  opacity: 0.5;
}

.btn-secondary {
  background: white;
  color: var(--civic-primary);
  border: 2px solid var(--civic-primary);
  border-radius: var(--radius-xl);
  padding: calc(var(--space-medium) - 2px) calc(var(--space-large) - 2px);
  font-weight: 600;
  font-size: var(--text-base);
  transition: all var(--duration-fast) ease-in-out;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-small);
}

.btn-secondary:hover:not(:disabled) {
  background: rgb(37 99 235 / 0.05);
  border-color: #1D4ED8;
  color: #1D4ED8;
}

.btn-tertiary {
  background: transparent;
  color: var(--civic-primary);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-small) var(--space-medium);
  font-weight: 500;
  font-size: var(--text-small);
  transition: all var(--duration-fast) ease-in-out;
  cursor: pointer;
  text-decoration: underline;
  text-underline-offset: 2px;
}

.btn-tertiary:hover:not(:disabled) {
  background: rgb(37 99 235 / 0.05);
  text-decoration: none;
}

/* Focus Styles for Accessibility */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus-visible {
  outline: 2px solid var(--civic-primary);
  outline-offset: 2px;
}

/* Global Focus Ring Tokens */
.focus-ring-blue {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.focus-ring-red {
  @apply focus:outline-none focus:ring-2 focus:ring-red-300 focus:ring-offset-2;
}

.focus-ring-green {
  @apply focus:outline-none focus:ring-2 focus:ring-green-300 focus:ring-offset-2;
}

.focus-ring-gray {
  @apply focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-offset-2;
}

/* Keyboard Navigation Improvements */
.keyboard-interactive {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-150;
}

.keyboard-interactive:focus-visible {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

/* Skip Link for Screen Readers */
.skip-link {
  @apply absolute -top-10 left-4 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium z-50 transition-all;
}

.skip-link:focus {
  @apply top-4;
}

/* Form Input Styles */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-xl transition-all duration-200;
  @apply text-gray-900 placeholder-gray-500;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  @apply bg-white;
}

.form-input:disabled {
  @apply bg-gray-50 text-gray-500 cursor-not-allowed;
}

.form-textarea {
  @apply w-full px-4 py-3 border border-gray-300 rounded-xl resize-none transition-all duration-200;
  @apply text-gray-900 placeholder-gray-500;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  @apply bg-white;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-error {
  @apply mt-1 text-sm text-red-600;
}

/* Performance-First Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(4px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-4px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.95); }
  to { opacity: 1; transform: scale(1); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-fade-in {
  animation: fadeIn var(--duration-fast) ease-out;
}

.animate-slide-in {
  animation: slideIn var(--duration-fast) ease-out;
}

.animate-scale-in {
  animation: scaleIn var(--duration-micro) ease-out;
}

/* Respect user motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  ::before,
  ::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Utility Classes */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Semantic Color Classes */
.text-support { color: var(--color-support); }
.text-oppose { color: var(--color-oppose); }
.text-amend { color: var(--color-amend); }
.text-urgent { color: var(--color-urgent); }

.bg-support { background-color: var(--color-support); }
.bg-oppose { background-color: var(--color-oppose); }
.bg-amend { background-color: var(--color-amend); }
.bg-urgent { background-color: var(--color-urgent); }

.border-support { border-color: var(--color-support); }
.border-oppose { border-color: var(--color-oppose); }
.border-amend { border-color: var(--color-amend); }
.border-urgent { border-color: var(--color-urgent); }

/* Bill Status Classes */
.status-introduced { 
  background-color: var(--status-introduced-bg); 
  color: var(--status-introduced-text); 
}
.status-committee { 
  background-color: var(--status-committee-bg); 
  color: var(--status-committee-text); 
}
.status-floor { 
  background-color: var(--status-floor-bg); 
  color: var(--status-floor-text); 
}
.status-passed-house { 
  background-color: var(--status-passed-house-bg); 
  color: var(--status-passed-house-text); 
}
.status-passed-senate { 
  background-color: var(--status-passed-senate-bg); 
  color: var(--status-passed-senate-text); 
}
.status-enacted { 
  background-color: var(--status-enacted-bg); 
  color: var(--status-enacted-text); 
}
.status-vetoed { 
  background-color: var(--status-vetoed-bg); 
  color: var(--status-vetoed-text); 
}
.status-failed { 
  background-color: var(--status-failed-bg); 
  color: var(--status-failed-text); 
}
