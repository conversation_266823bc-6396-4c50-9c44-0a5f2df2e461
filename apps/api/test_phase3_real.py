#!/usr/bin/env python3
"""
Real Phase 3 Evidence-First Test - Run from within API environment
Tests the Phase 3 evidence-first processing improvements with real OpenAI API calls
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Any

from app.services.balanced_analysis_service import BalancedAnalysisService
from app.services.ai_service import AIService
from app.services.enhanced_importance_scorer import get_enhanced_importance_scorer
from app.services.evidence_first_processor import get_evidence_first_processor
from app.services.intelligent_evidence_extractor import get_intelligent_extractor
from app.services.context_aware_evidence_selector import get_context_aware_selector
from app.services.evidence_analysis_mapper import get_evidence_mapper
from app.core.config import get_settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_phase3_real():
    """Test Phase 3 evidence-first improvements with real OpenAI API calls"""
    
    print("🚀 REAL Phase 3 Evidence-First Test")
    print("=" * 60)
    
    # Check configuration
    settings = get_settings()
    print(f"✅ OpenAI API Key: {'Configured' if settings.OPENAI_API_KEY else 'Missing'}")
    print(f"✅ Environment: {settings.ENVIRONMENT}")
    print()
    
    # Initialize services
    ai_service = AIService()
    balanced_service = BalancedAnalysisService(ai_service)
    enhanced_scorer = get_enhanced_importance_scorer()
    evidence_processor = get_evidence_first_processor()
    intelligent_extractor = get_intelligent_extractor()
    context_selector = get_context_aware_selector()
    evidence_mapper = get_evidence_mapper()
    
    if not ai_service.enabled:
        print("❌ AI Service not enabled - check configuration")
        return False
    
    print("✅ All Phase 3 services initialized successfully")
    print()
    
    # Test bill data - Complex bill with rich evidence
    test_bill = {
        'bill_id': 'test-phase3-real-001',
        'title': 'Comprehensive Healthcare Technology Advancement and Patient Protection Act of 2024',
        'bill_number': 'HR8847',
        'bill_type': 'hr',
        'chamber': 'house',
        'summary': 'A comprehensive bill establishing national healthcare technology standards, patient data protection requirements, and telemedicine accessibility programs with significant federal funding and enforcement mechanisms.',
        'bill_text': """
        SEC. 1. SHORT TITLE.
        This Act may be cited as the "Comprehensive Healthcare Technology Advancement and Patient Protection Act of 2024".
        
        SEC. 2. FINDINGS AND PURPOSE.
        Congress finds that healthcare technology advancement is critical to improving patient outcomes and reducing healthcare costs nationwide.
        
        SEC. 3. APPROPRIATIONS FOR HEALTHCARE TECHNOLOGY.
        (a) AUTHORIZATION.—There is authorized to be appropriated $2,500,000,000 for fiscal years 2025 through 2029 to the Department of Health and Human Services to carry out healthcare technology advancement programs under this Act.
        
        (b) TELEMEDICINE GRANTS.—Of the amounts appropriated under subsection (a), not less than $800,000,000 shall be allocated for telemedicine infrastructure grants to underserved communities.
        
        (c) RESEARCH AND DEVELOPMENT.—The Secretary shall allocate not less than $600,000,000 for artificial intelligence research in medical diagnosis and treatment.
        
        SEC. 4. HEALTHCARE DATA PROTECTION STANDARDS.
        (a) MANDATORY STANDARDS.—Not later than 180 days after the date of enactment of this Act, the Secretary of Health and Human Services shall establish mandatory healthcare data protection standards that apply to all healthcare providers, insurers, and technology vendors.
        
        (b) CERTIFICATION REQUIREMENTS.—All healthcare technology systems must obtain certification from the Department within 12 months of the effective date of the standards.
        
        (c) ANNUAL AUDITS.—Healthcare entities shall submit to annual cybersecurity audits conducted by Department-approved auditors.
        
        SEC. 5. ENFORCEMENT AND PENALTIES.
        (a) CIVIL PENALTIES.—Any healthcare entity that fails to comply with data protection standards shall be subject to a civil penalty of not less than $100,000 and not more than $5,000,000 per violation, depending on the severity and scope of the violation.
        
        (b) CRIMINAL PENALTIES.—Any person who willfully violates patient data protection requirements resulting in identity theft or financial harm shall be fined not more than $1,000,000 or imprisoned for not more than 10 years, or both.
        
        (c) IMMEDIATE SUSPENSION.—The Secretary may immediately suspend the operating license of any healthcare entity that poses an imminent threat to patient data security.
        
        SEC. 6. PATIENT RIGHTS AND ACCESS.
        (a) RIGHT TO DATA PORTABILITY.—Every patient shall have the right to obtain a complete copy of their healthcare data in a standard electronic format within 30 days of request at no cost.
        
        (b) TELEMEDICINE ACCESS.—Healthcare providers receiving federal funding must provide telemedicine services to patients in underserved areas within 24 hours of request for urgent care.
        
        (c) ACCESSIBILITY STANDARDS.—All healthcare technology platforms must comply with Section 508 accessibility standards to serve patients with disabilities.
        
        SEC. 7. IMPLEMENTATION TIMELINE AND OVERSIGHT.
        (a) PHASE 1 IMPLEMENTATION.—Within 6 months of enactment, establish data protection standards and begin certification process.
        
        (b) PHASE 2 IMPLEMENTATION.—Within 18 months of enactment, complete nationwide rollout of telemedicine infrastructure grants.
        
        (c) CONGRESSIONAL REPORTING.—The Secretary shall submit quarterly progress reports to Congress detailing implementation status, grant allocations, enforcement actions, and compliance metrics.
        
        (d) INSPECTOR GENERAL OVERSIGHT.—The Inspector General of the Department of Health and Human Services shall conduct annual reviews of program effectiveness and report findings to Congress.
        
        SEC. 8. DEFINITIONS.
        In this Act:
        (1) HEALTHCARE ENTITY.—The term 'healthcare entity' means any hospital, clinic, pharmacy, insurance provider, or technology vendor that processes protected health information.
        (2) UNDERSERVED COMMUNITY.—The term 'underserved community' means any geographic area with a shortage of healthcare professionals as designated by the Health Resources and Services Administration.
        """
    }
    
    print(f"📊 Test Data:")
    print(f"   Bill: {test_bill['title']}")
    print(f"   Bill Type: {test_bill['bill_type'].upper()}")
    print(f"   Text Length: {len(test_bill['bill_text']):,} characters")
    print()
    
    # Phase 3 Test Suite
    success = True
    
    # Test 1: Intelligent Evidence Extraction
    print("🧠 TEST 1: Intelligent Evidence Extraction")
    print("-" * 50)
    
    start_time = time.time()
    try:
        extracted_evidence = await intelligent_extractor.extract_intelligent_evidence(
            test_bill['bill_text'], test_bill
        )
        
        extraction_time = time.time() - start_time
        print(f"✅ Extracted {len(extracted_evidence)} evidence spans in {extraction_time:.2f}s")
        
        # Analyze extraction quality
        extraction_analysis = analyze_evidence_extraction(extracted_evidence)
        success &= extraction_analysis['success']
        
    except Exception as e:
        print(f"❌ Evidence extraction failed: {e}")
        success = False
    
    print()
    
    # Test 2: Evidence-First Processing
    print("🔬 TEST 2: Evidence-First Processing")
    print("-" * 50)
    
    start_time = time.time()
    try:
        # Convert extracted evidence to raw format for processing
        raw_evidence = [
            {
                'id': evidence.extraction_rule + f"_{i}",
                'quote': evidence.content,
                'heading': evidence.heading,
                'start_offset': evidence.start_offset,
                'end_offset': evidence.end_offset
            }
            for i, evidence in enumerate(extracted_evidence[:10])  # Limit for testing
        ]
        
        evidence_spans, processing_report = await evidence_processor.process_evidence_first(
            test_bill['bill_text'], test_bill, raw_evidence
        )
        
        processing_time = time.time() - start_time
        print(f"✅ Processed {len(evidence_spans)} intelligent evidence spans in {processing_time:.2f}s")
        
        # Analyze processing results
        processing_analysis = analyze_evidence_processing(evidence_spans, processing_report)
        success &= processing_analysis['success']
        
    except Exception as e:
        print(f"❌ Evidence processing failed: {e}")
        success = False
    
    print()
    
    # Test 3: Context-Aware Evidence Selection
    print("🎯 TEST 3: Context-Aware Evidence Selection")
    print("-" * 50)
    
    start_time = time.time()
    try:
        analysis_requirements = {
            'analysis_type': 'citizen_summary',
            'target_audience': 'citizens',
            'quality': 'high',
            'budget_limit': 0.25
        }
        
        evidence_bundle = await context_selector.select_context_aware_evidence(
            evidence_spans, test_bill, analysis_requirements
        )
        
        selection_time = time.time() - start_time
        print(f"✅ Selected evidence bundle in {selection_time:.2f}s")
        
        # Analyze selection quality
        selection_analysis = analyze_evidence_selection(evidence_bundle)
        success &= selection_analysis['success']
        
    except Exception as e:
        print(f"❌ Evidence selection failed: {e}")
        success = False
    
    print()
    
    # Test 4: Enhanced Importance Scoring
    print("📊 TEST 4: Enhanced Importance Scoring")
    print("-" * 50)
    
    start_time = time.time()
    try:
        enhanced_score = await enhanced_scorer.score_bill_with_evidence(
            test_bill['bill_text'], test_bill, evidence_spans
        )
        
        scoring_time = time.time() - start_time
        print(f"✅ Enhanced importance scoring completed in {scoring_time:.2f}s")
        
        # Analyze scoring results
        scoring_analysis = analyze_enhanced_scoring(enhanced_score)
        success &= scoring_analysis['success']
        
    except Exception as e:
        print(f"❌ Enhanced scoring failed: {e}")
        success = False
    
    print()
    
    # Test 5: Evidence-Analysis Mapping
    print("🗺️ TEST 5: Evidence-Analysis Mapping and Verification")
    print("-" * 50)
    
    # First run a quick analysis to get analysis content
    start_time = time.time()
    try:
        # Run balanced analysis to get analysis results
        analysis_result = await balanced_service.analyze_bill_balanced(
            bill_text=test_bill['bill_text'],
            bill_metadata=test_bill,
            evidence_spans=[{
                'id': span.id,
                'quote': span.content,
                'heading': span.heading,
                'start_offset': span.start_offset,
                'end_offset': span.end_offset
            } for span in evidence_spans[:6]]  # Limit for cost control
        )
        
        if analysis_result['success']:
            analysis = analysis_result['analysis']
            
            # Test evidence-to-analysis mapping
            mappings, mapping_report = await evidence_mapper.map_evidence_to_analysis(
                analysis, evidence_spans, test_bill
            )
            
            mapping_time = time.time() - start_time
            print(f"✅ Evidence-analysis mapping completed in {mapping_time:.2f}s")
            print(f"   Total Cost: ${analysis_result['cost_breakdown']['total_cost']:.4f}")
            
            # Analyze mapping results
            mapping_analysis = analyze_evidence_mapping(mappings, mapping_report, analysis_result)
            success &= mapping_analysis['success']
            
        else:
            print(f"❌ Analysis failed, skipping mapping test: {analysis_result.get('error')}")
            success = False
        
    except Exception as e:
        print(f"❌ Evidence mapping failed: {e}")
        success = False
    
    print()
    
    # Final Assessment
    print("🏆 PHASE 3 FINAL ASSESSMENT")
    print("=" * 60)
    
    if success:
        print("🎉 PHASE 3 SUCCESS: All evidence-first improvements working excellently!")
        print("✅ Intelligent evidence extraction functional")
        print("✅ Evidence-first processing operational")
        print("✅ Context-aware selection optimized")
        print("✅ Enhanced importance scoring improved")
        print("✅ Evidence-analysis mapping verified")
    else:
        print("❌ PHASE 3 NEEDS WORK: Some components require attention")
        print("💡 Check individual test results above for specific issues")
    
    return success

def analyze_evidence_extraction(extracted_evidence: List) -> Dict[str, Any]:
    """Analyze quality of evidence extraction"""
    
    print(f"   Evidence Spans Extracted: {len(extracted_evidence)}")
    
    # Check for diversity of extraction rules
    extraction_rules = set(ev.extraction_rule for ev in extracted_evidence)
    print(f"   Extraction Methods Used: {len(extraction_rules)}")
    
    # Check confidence scores
    if extracted_evidence:
        avg_confidence = sum(ev.confidence_score for ev in extracted_evidence) / len(extracted_evidence)
        print(f"   Average Confidence: {avg_confidence:.2f}")
    else:
        avg_confidence = 0
    
    # Success criteria
    success = (
        len(extracted_evidence) >= 8 and  # Should extract meaningful amount
        len(extraction_rules) >= 3 and   # Should use multiple methods
        avg_confidence >= 0.6             # Should have reasonable confidence
    )
    
    print(f"   Extraction Quality: {'✅ Excellent' if success else '⚠️ Needs Improvement'}")
    
    return {
        'success': success,
        'evidence_count': len(extracted_evidence),
        'method_diversity': len(extraction_rules),
        'avg_confidence': avg_confidence
    }

def analyze_evidence_processing(evidence_spans: List, processing_report: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze evidence-first processing results"""
    
    print(f"   Processed Evidence Spans: {len(evidence_spans)}")
    print(f"   Primary Topic Detected: {processing_report['context']['primary_topic']}")
    print(f"   Target Audience: {processing_report['context']['target_audience']}")
    
    if evidence_spans:
        avg_priority = sum(span.priority_score for span in evidence_spans) / len(evidence_spans)
        avg_specificity = sum(span.specificity_score for span in evidence_spans) / len(evidence_spans)
        avg_actionability = sum(span.actionability_score for span in evidence_spans) / len(evidence_spans)
        
        print(f"   Average Priority Score: {avg_priority:.2f}")
        print(f"   Average Specificity: {avg_specificity:.2f}")
        print(f"   Average Actionability: {avg_actionability:.2f}")
        
        # Check evidence type diversity
        evidence_types = set(span.evidence_type.value for span in evidence_spans)
        print(f"   Evidence Types Found: {len(evidence_types)} ({', '.join(evidence_types)})")
        
        success = (
            len(evidence_spans) >= 5 and
            avg_priority >= 0.5 and
            avg_specificity >= 0.4 and
            len(evidence_types) >= 3
        )
    else:
        success = False
    
    print(f"   Processing Quality: {'✅ Excellent' if success else '⚠️ Needs Improvement'}")
    
    return {
        'success': success,
        'span_count': len(evidence_spans),
        'avg_priority': avg_priority if evidence_spans else 0,
        'type_diversity': len(evidence_types) if evidence_spans else 0
    }

def analyze_evidence_selection(evidence_bundle) -> Dict[str, Any]:
    """Analyze context-aware evidence selection"""
    
    core_count = len(evidence_bundle.core_evidence)
    supporting_count = len(evidence_bundle.supporting_evidence)
    citizen_count = len(evidence_bundle.citizen_evidence)
    technical_count = len(evidence_bundle.technical_evidence)
    
    print(f"   Core Evidence: {core_count}")
    print(f"   Supporting Evidence: {supporting_count}")
    print(f"   Citizen Evidence: {citizen_count}")
    print(f"   Technical Evidence: {technical_count}")
    print(f"   Bundle Score: {evidence_bundle.bundle_score:.2f}")
    print(f"   Selection Rationale: {evidence_bundle.selection_rationale}")
    
    success = (
        core_count >= 3 and           # Should have solid core evidence
        evidence_bundle.bundle_score >= 0.6 and  # Should have good quality
        (core_count + supporting_count + citizen_count) >= 6  # Should have reasonable total
    )
    
    print(f"   Selection Quality: {'✅ Excellent' if success else '⚠️ Needs Improvement'}")
    
    return {
        'success': success,
        'core_count': core_count,
        'total_count': core_count + supporting_count + citizen_count + technical_count,
        'bundle_score': evidence_bundle.bundle_score
    }

def analyze_enhanced_scoring(enhanced_score) -> Dict[str, Any]:
    """Analyze enhanced importance scoring results"""
    
    print(f"   Enhanced Score: {enhanced_score.score}/100")
    print(f"   Importance Level: {enhanced_score.level.value}")
    print(f"   Auto-Process: {enhanced_score.auto_process}")
    print(f"   Evidence Quality: {enhanced_score.evidence_quality_score:.2f}")
    print(f"   Evidence Diversity: {enhanced_score.evidence_diversity_score:.2f}")
    print(f"   Critical Evidence: {enhanced_score.critical_evidence_count}")
    print(f"   Funding Strength: {enhanced_score.funding_evidence_strength:.2f}")
    print(f"   Enforcement Strength: {enhanced_score.enforcement_evidence_strength:.2f}")
    print(f"   Enhanced Reason: {enhanced_score.reason}")
    
    success = (
        enhanced_score.score >= 60 and  # Should recognize importance of comprehensive healthcare bill
        enhanced_score.evidence_quality_score >= 0.5 and
        enhanced_score.critical_evidence_count >= 2 and
        enhanced_score.funding_evidence_strength >= 0.7  # Should detect strong funding evidence
    )
    
    print(f"   Scoring Quality: {'✅ Excellent' if success else '⚠️ Needs Improvement'}")
    
    return {
        'success': success,
        'enhanced_score': enhanced_score.score,
        'evidence_quality': enhanced_score.evidence_quality_score,
        'critical_evidence': enhanced_score.critical_evidence_count
    }

def analyze_evidence_mapping(mappings: List, mapping_report, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
    """Analyze evidence-analysis mapping results"""
    
    print(f"   Evidence Mappings Created: {len(mappings)}")
    print(f"   Total Claims: {mapping_report.total_claims}")
    print(f"   Mapped Claims: {mapping_report.mapped_claims}")
    print(f"   Mapping Quality Score: {mapping_report.quality_score:.2f}")
    print(f"   Coverage Gaps: {len(mapping_report.coverage_gaps)}")
    
    # Check verification levels
    verification_dist = mapping_report.verification_distribution
    print(f"   Verification Distribution: {verification_dist}")
    
    # Check strength distribution  
    strength_dist = mapping_report.strength_distribution
    print(f"   Strength Distribution: {strength_dist}")
    
    success = (
        len(mappings) >= 5 and                    # Should create meaningful mappings
        mapping_report.quality_score >= 0.6 and  # Should have decent mapping quality
        mapping_report.mapped_claims >= mapping_report.total_claims * 0.7  # Should map most claims
    )
    
    print(f"   Mapping Quality: {'✅ Excellent' if success else '⚠️ Needs Improvement'}")
    
    return {
        'success': success,
        'mapping_count': len(mappings),
        'quality_score': mapping_report.quality_score,
        'coverage_ratio': mapping_report.mapped_claims / max(mapping_report.total_claims, 1)
    }

if __name__ == "__main__":
    asyncio.run(test_phase3_real())