import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { Toaster } from 'react-hot-toast';
import OnboardingProvider from '../components/shared/OnboardingProvider';
import Auth0Provider from '../components/auth/Auth0Provider';
import Auth0ErrorBoundary from '../components/auth/Auth0ErrorBoundary';
import Header from '../components/shared/Header';
import Footer from '../components/shared/Footer';
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  fallback: ["system-ui", "arial"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  fallback: ["ui-monospace", "Monaco", "Consolas", "monospace"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "ModernAction - Civic Engagement Platform",
  description: "Take action on the issues that matter to you. Contact your representatives and make your voice heard.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Auth0ErrorBoundary>
          <Auth0Provider>
            <OnboardingProvider>
              <div className="min-h-screen flex flex-col">
                <Header />
                <main className="flex-1">
                  {children}
                </main>
                <Footer />
              </div>
              <Toaster position="top-right" />
            </OnboardingProvider>
          </Auth0Provider>
        </Auth0ErrorBoundary>
      </body>
    </html>
  );
}
