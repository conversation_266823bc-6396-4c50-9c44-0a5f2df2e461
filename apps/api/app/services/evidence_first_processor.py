"""
Evidence-First Processor - Phase 3 Revolutionary Evidence Processing
Implements intelligent evidence extraction, prioritization, and context-aware selection
"""

import logging
import re
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import asyncio

logger = logging.getLogger(__name__)

class EvidenceType(Enum):
    """Types of evidence for intelligent categorization"""
    FUNDING = "funding"                    # Dollar amounts, appropriations
    ENFORCEMENT = "enforcement"            # Penalties, fines, compliance
    TIMELINE = "timeline"                 # Deadlines, implementation dates
    AUTHORITY = "authority"               # Who has power, responsibility
    MANDATE = "mandate"                   # Required actions, shall/must
    PROCESS = "process"                   # Procedures, how things work  
    IMPACT = "impact"                     # Effects, consequences
    SCOPE = "scope"                       # Who/what is covered
    DEFINITION = "definition"             # Terms, meanings
    REPORTING = "reporting"               # Requirements to report/notify

@dataclass
class EvidenceSpan:
    """Enhanced evidence span with intelligence metadata"""
    id: str
    content: str
    heading: str
    evidence_type: EvidenceType
    priority_score: float                 # 0-1, how important this evidence is
    specificity_score: float             # 0-1, how specific vs generic
    actionability_score: float           # 0-1, how actionable for citizens
    legal_weight: float                  # 0-1, legal/regulatory significance
    context_relevance: float             # 0-1, relevance to bill context
    extracted_values: Dict[str, Any]     # Specific values (amounts, dates, etc.)
    related_spans: List[str]             # IDs of related evidence spans
    start_offset: int
    end_offset: int

@dataclass
class EvidenceContext:
    """Context for intelligent evidence processing"""
    bill_title: str
    bill_type: str                       # HR, S, etc.
    chamber: str                         # house, senate
    primary_topic: str                   # Main subject area
    keywords: List[str]                  # Key terms to prioritize
    target_audience: str                 # citizens, experts, etc.
    analysis_purpose: str                # summary, detailed, legislative

class EvidenceFirstProcessor:
    """
    Advanced evidence-first processing system for Phase 3
    Revolutionizes how we extract, understand, and utilize evidence
    """
    
    def __init__(self):
        # Evidence type patterns for intelligent categorization
        self.evidence_patterns = {
            EvidenceType.FUNDING: [
                r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion|thousand))?',
                r'(?:appropriat|authoriz|fund)\w*[^.]*\$[\d,]+',
                r'(?:budget|fiscal|expenditure|allocation)',
                r'(?:grant|subsidy|payment|disbursement)'
            ],
            EvidenceType.ENFORCEMENT: [
                r'(?:penalty|fine|sanction)[^.]*\$[\d,]+',
                r'(?:civil|criminal)\s+(?:penalty|liability|action)',
                r'(?:enforce|violat|comply|breach)',
                r'(?:suspend|revoke|terminate|cancel)'
            ],
            EvidenceType.TIMELINE: [
                r'(?:not later than|within|by)\s+[\d\w\s]+(?:days?|months?|years?)',
                r'\d+\s*(?:days?|months?|years?)\s*(?:after|before|from)',
                r'(?:deadline|due date|effective date)',
                r'(?:annually|quarterly|monthly|daily)'
            ],
            EvidenceType.AUTHORITY: [
                r'(?:secretary|administrator|director|commissioner)\s+(?:shall|may|must)',
                r'(?:department|agency|bureau|office)\s+(?:of|for)\s+\w+',
                r'(?:federal|state|local)\s+(?:government|authority)',
                r'(?:jurisdiction|oversight|supervision)'
            ],
            EvidenceType.MANDATE: [
                r'(?:shall|must|required to|directed to)\s+[\w\s]+',
                r'(?:establish|create|implement|develop|maintain)',
                r'(?:prohibit|prevent|restrict|ban)',
                r'(?:ensure|guarantee|provide|deliver)'
            ],
            EvidenceType.PROCESS: [
                r'(?:procedure|process|method|mechanism)',
                r'(?:application|petition|request|submission)',
                r'(?:review|approval|certification|verification)',
                r'(?:step|stage|phase|requirement)'
            ],
            EvidenceType.IMPACT: [
                r'(?:affect|impact|influence|consequence)',
                r'(?:benefit|harm|help|hurt|damage)',
                r'(?:increase|decrease|reduce|expand|improve)',
                r'(?:result|outcome|effect|implication)'
            ],
            EvidenceType.SCOPE: [
                r'(?:apply to|cover|include|encompass)',
                r'(?:eligible|qualify|subject to|exempt)',
                r'(?:all|any|each|every)\s+(?:person|entity|organization)',
                r'(?:nationwide|statewide|local|regional)'
            ],
            EvidenceType.DEFINITION: [
                r'(?:means|defined as|refers to|includes)',
                r'(?:term|definition|meaning|interpretation)',
                r'(?:for purposes of this|in this (?:act|section))',
                r'(?:shall be construed|shall mean)'
            ],
            EvidenceType.REPORTING: [
                r'(?:report|notify|inform|submit)',
                r'(?:annual|quarterly|monthly)\s+(?:report|statement)',
                r'(?:congress|committee|public|secretary)',
                r'(?:disclose|publish|make available)'
            ]
        }
        
        # Priority weights for different evidence types
        self.type_priority_weights = {
            EvidenceType.FUNDING: 0.95,       # Money is almost always critical
            EvidenceType.ENFORCEMENT: 0.90,   # Legal consequences are very important
            EvidenceType.MANDATE: 0.85,       # What people must do is critical
            EvidenceType.TIMELINE: 0.80,      # When things happen matters
            EvidenceType.AUTHORITY: 0.75,     # Who has power is important
            EvidenceType.IMPACT: 0.70,        # Effects matter for citizens
            EvidenceType.SCOPE: 0.65,         # Who's covered is important
            EvidenceType.PROCESS: 0.60,       # How things work is useful
            EvidenceType.REPORTING: 0.55,     # Transparency requirements
            EvidenceType.DEFINITION: 0.45     # Definitions are supporting info
        }
        
        # Specificity indicators
        self.specificity_patterns = {
            'high': [
                r'\$[\d,]+(?:\.\d{2})?',           # Exact dollar amounts
                r'\d+\s*(?:days?|months?|years?)',  # Specific timeframes
                r'(?:section|subsection)\s+\d+',    # Specific legal references
                r'\d{1,2}/\d{1,2}/\d{2,4}',        # Specific dates
                r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b'   # Proper names
            ],
            'medium': [
                r'(?:significant|substantial|major)',
                r'(?:appropriate|reasonable|necessary)',
                r'(?:annual|periodic|regular)',
                r'(?:federal|state|local|national)'
            ],
            'low': [
                r'(?:various|multiple|several|numerous)',
                r'(?:comprehensive|thorough|complete)',
                r'(?:appropriate measures|necessary steps)',
                r'(?:stakeholders|parties|entities)'
            ]
        }
    
    async def process_evidence_first(self, bill_text: str, bill_metadata: Dict[str, Any],
                                   raw_evidence_spans: List[Dict]) -> Tuple[List[EvidenceSpan], Dict[str, Any]]:
        """
        Revolutionary evidence-first processing that transforms raw evidence into 
        intelligent, prioritized, context-aware evidence spans
        """
        
        logger.info(f"🔬 Starting evidence-first processing for {bill_metadata.get('title', 'Unknown')}")
        
        # Step 1: Create evidence context
        context = self._create_evidence_context(bill_text, bill_metadata)
        
        # Step 2: Transform raw spans into intelligent evidence spans
        intelligent_spans = await self._transform_to_intelligent_spans(
            raw_evidence_spans, context, bill_text
        )
        
        # Step 3: Calculate advanced scores and relationships
        enhanced_spans = await self._enhance_with_intelligence(intelligent_spans, context)
        
        # Step 4: Apply context-aware filtering and prioritization
        prioritized_spans = self._apply_intelligent_prioritization(enhanced_spans, context)
        
        # Step 5: Create evidence processing report
        processing_report = self._create_processing_report(
            raw_evidence_spans, prioritized_spans, context
        )
        
        logger.info(f"✅ Evidence-first processing complete: {len(prioritized_spans)}/{len(raw_evidence_spans)} spans prioritized")
        
        return prioritized_spans, processing_report
    
    def _create_evidence_context(self, bill_text: str, bill_metadata: Dict[str, Any]) -> EvidenceContext:
        """Create intelligent context for evidence processing"""
        
        title = bill_metadata.get('title', '').lower()
        bill_type = bill_metadata.get('bill_type', 'hr')
        chamber = bill_metadata.get('chamber', 'house')
        
        # Intelligent topic detection
        primary_topic = self._detect_primary_topic(title, bill_text)
        
        # Extract key terms for prioritization  
        keywords = self._extract_key_terms(title, bill_text)
        
        # Determine target audience based on bill complexity
        target_audience = self._determine_target_audience(title, bill_text)
        
        return EvidenceContext(
            bill_title=title,
            bill_type=bill_type,
            chamber=chamber,
            primary_topic=primary_topic,
            keywords=keywords,
            target_audience=target_audience,
            analysis_purpose='comprehensive'
        )
    
    async def _transform_to_intelligent_spans(self, raw_spans: List[Dict], 
                                            context: EvidenceContext,
                                            bill_text: str) -> List[EvidenceSpan]:
        """Transform raw evidence spans into intelligent evidence spans"""
        
        intelligent_spans = []
        
        for i, raw_span in enumerate(raw_spans):
            # Extract basic info
            span_id = raw_span.get('id', f"span_{i}")
            content = raw_span.get('quote', '')
            heading = raw_span.get('heading', '')
            start_offset = raw_span.get('start_offset', 0)
            end_offset = raw_span.get('end_offset', len(content))
            
            # Intelligent evidence type detection
            evidence_type = self._detect_evidence_type(content, heading)
            
            # Calculate priority score
            priority_score = self._calculate_priority_score(content, heading, evidence_type, context)
            
            # Calculate specificity score
            specificity_score = self._calculate_specificity_score(content, heading)
            
            # Calculate actionability score
            actionability_score = self._calculate_actionability_score(content, evidence_type, context)
            
            # Calculate legal weight
            legal_weight = self._calculate_legal_weight(content, heading, evidence_type)
            
            # Calculate context relevance
            context_relevance = self._calculate_context_relevance(content, heading, context)
            
            # Extract specific values
            extracted_values = self._extract_specific_values(content, evidence_type)
            
            # Create intelligent span
            intelligent_span = EvidenceSpan(
                id=span_id,
                content=content,
                heading=heading,
                evidence_type=evidence_type,
                priority_score=priority_score,
                specificity_score=specificity_score,
                actionability_score=actionability_score,
                legal_weight=legal_weight,
                context_relevance=context_relevance,
                extracted_values=extracted_values,
                related_spans=[],  # Will be populated in enhancement step
                start_offset=start_offset,
                end_offset=end_offset
            )
            
            intelligent_spans.append(intelligent_span)
        
        return intelligent_spans
    
    async def _enhance_with_intelligence(self, spans: List[EvidenceSpan], 
                                       context: EvidenceContext) -> List[EvidenceSpan]:
        """Enhance spans with relationship detection and advanced intelligence"""
        
        # Detect relationships between spans
        for i, span1 in enumerate(spans):
            related_spans = []
            
            for j, span2 in enumerate(spans):
                if i != j:
                    relationship_score = self._calculate_relationship_score(span1, span2)
                    if relationship_score > 0.6:  # Threshold for relatedness
                        related_spans.append(span2.id)
            
            span1.related_spans = related_spans
        
        # Apply context-aware score adjustments
        for span in spans:
            # Boost scores for spans highly relevant to context
            if span.context_relevance > 0.8:
                span.priority_score = min(1.0, span.priority_score * 1.1)
            
            # Boost funding and enforcement for regulatory bills
            if context.primary_topic in ['regulatory', 'enforcement', 'compliance']:
                if span.evidence_type in [EvidenceType.FUNDING, EvidenceType.ENFORCEMENT]:
                    span.priority_score = min(1.0, span.priority_score * 1.05)
        
        return spans
    
    def _apply_intelligent_prioritization(self, spans: List[EvidenceSpan], 
                                        context: EvidenceContext) -> List[EvidenceSpan]:
        """Apply intelligent prioritization to select the best evidence spans"""
        
        # Calculate composite scores for ranking
        for span in spans:
            # Weighted composite score
            composite_score = (
                span.priority_score * 0.30 +
                span.specificity_score * 0.25 +
                span.legal_weight * 0.20 +
                span.context_relevance * 0.15 +
                span.actionability_score * 0.10
            )
            span.composite_score = composite_score
        
        # Sort by composite score
        spans.sort(key=lambda s: s.composite_score, reverse=True)
        
        # Intelligent filtering based on context
        if context.target_audience == 'citizens':
            # Prioritize actionable, understandable evidence
            filtered_spans = [s for s in spans if s.actionability_score > 0.4]
        else:
            # Keep technical evidence for expert analysis
            filtered_spans = [s for s in spans if s.composite_score > 0.3]
        
        # Ensure diversity of evidence types
        prioritized_spans = self._ensure_evidence_diversity(filtered_spans)
        
        # Limit to top evidence spans to prevent overwhelm
        max_spans = 12 if context.analysis_purpose == 'comprehensive' else 8
        
        return prioritized_spans[:max_spans]
    
    def _detect_evidence_type(self, content: str, heading: str) -> EvidenceType:
        """Intelligently detect evidence type using pattern matching"""
        
        text = (content + ' ' + heading).lower()
        type_scores = {}
        
        # Score each evidence type based on pattern matches
        for evidence_type, patterns in self.evidence_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, text, re.IGNORECASE))
                score += matches
            
            type_scores[evidence_type] = score
        
        # Return type with highest score, default to MANDATE
        if not type_scores or max(type_scores.values()) == 0:
            return EvidenceType.MANDATE
        
        return max(type_scores.items(), key=lambda x: x[1])[0]
    
    def _calculate_priority_score(self, content: str, heading: str, 
                                evidence_type: EvidenceType, context: EvidenceContext) -> float:
        """Calculate priority score based on evidence type and context"""
        
        # Base score from evidence type
        base_score = self.type_priority_weights.get(evidence_type, 0.5)
        
        # Adjust based on context keywords
        text = (content + ' ' + heading).lower()
        keyword_bonus = 0
        for keyword in context.keywords:
            if keyword.lower() in text:
                keyword_bonus += 0.05
        
        # Adjust based on heading importance
        heading_bonus = 0
        if heading and any(word in heading.lower() for word in ['sec', 'section', 'funding', 'penalty']):
            heading_bonus = 0.1
        
        final_score = min(1.0, base_score + keyword_bonus + heading_bonus)
        return final_score
    
    def _calculate_specificity_score(self, content: str, heading: str) -> float:
        """Calculate how specific vs generic the evidence is"""
        
        text = content.lower()
        
        # Count high specificity patterns
        high_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) 
                        for pattern in self.specificity_patterns['high'])
        
        # Count low specificity patterns (penalty)
        low_count = sum(len(re.findall(pattern, text, re.IGNORECASE)) 
                       for pattern in self.specificity_patterns['low'])
        
        # Calculate score
        if high_count > 0:
            score = 0.7 + min(0.3, high_count * 0.1)
        elif low_count > 2:
            score = 0.2
        else:
            score = 0.5
        
        return min(1.0, score)
    
    def _calculate_actionability_score(self, content: str, evidence_type: EvidenceType, 
                                     context: EvidenceContext) -> float:
        """Calculate how actionable this evidence is for citizens"""
        
        # Base actionability by evidence type
        type_actionability = {
            EvidenceType.FUNDING: 0.8,      # Citizens care about money
            EvidenceType.ENFORCEMENT: 0.9,   # Citizens need to know penalties
            EvidenceType.MANDATE: 0.7,       # Citizens need to know requirements
            EvidenceType.TIMELINE: 0.8,      # Citizens need to know deadlines
            EvidenceType.IMPACT: 0.9,        # Citizens care about effects
            EvidenceType.SCOPE: 0.7,         # Citizens need to know if they're covered
            EvidenceType.AUTHORITY: 0.5,     # Less directly actionable
            EvidenceType.PROCESS: 0.6,       # Process info can be actionable
            EvidenceType.REPORTING: 0.4,     # Transparency is less directly actionable
            EvidenceType.DEFINITION: 0.3     # Definitions are supporting info
        }
        
        base_score = type_actionability.get(evidence_type, 0.5)
        
        # Adjust for citizen-relevant terms
        text = content.lower()
        citizen_terms = ['person', 'individual', 'citizen', 'taxpayer', 'public', 'consumer']
        citizen_bonus = sum(0.05 for term in citizen_terms if term in text)
        
        return min(1.0, base_score + citizen_bonus)
    
    def _calculate_legal_weight(self, content: str, heading: str, 
                              evidence_type: EvidenceType) -> float:
        """Calculate legal/regulatory significance"""
        
        # Base legal weight by evidence type
        type_weights = {
            EvidenceType.ENFORCEMENT: 0.95,
            EvidenceType.MANDATE: 0.90,
            EvidenceType.AUTHORITY: 0.85,
            EvidenceType.FUNDING: 0.80,
            EvidenceType.TIMELINE: 0.75,
            EvidenceType.SCOPE: 0.70,
            EvidenceType.PROCESS: 0.60,
            EvidenceType.REPORTING: 0.55,
            EvidenceType.IMPACT: 0.50,
            EvidenceType.DEFINITION: 0.40
        }
        
        base_weight = type_weights.get(evidence_type, 0.5)
        
        # Legal language indicators
        text = (content + ' ' + heading).lower()
        legal_terms = ['shall', 'must', 'required', 'violation', 'penalty', 'compliance', 'jurisdiction']
        legal_bonus = sum(0.05 for term in legal_terms if term in text)
        
        return min(1.0, base_weight + legal_bonus)
    
    def _calculate_context_relevance(self, content: str, heading: str, 
                                   context: EvidenceContext) -> float:
        """Calculate relevance to bill context"""
        
        text = (content + ' ' + heading).lower()
        title_words = context.bill_title.split()
        
        # Score based on title word overlap
        title_matches = sum(1 for word in title_words if len(word) > 3 and word in text)
        title_score = min(0.4, title_matches * 0.1)
        
        # Score based on keyword matches
        keyword_matches = sum(1 for keyword in context.keywords if keyword.lower() in text)
        keyword_score = min(0.4, keyword_matches * 0.1)
        
        # Score based on topic relevance
        topic_score = 0.2 if context.primary_topic in text else 0
        
        return title_score + keyword_score + topic_score
    
    def _extract_specific_values(self, content: str, evidence_type: EvidenceType) -> Dict[str, Any]:
        """Extract specific values from evidence based on type"""
        
        values = {}
        
        if evidence_type == EvidenceType.FUNDING:
            amounts = re.findall(r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion))?', content)
            values['amounts'] = amounts
        
        elif evidence_type == EvidenceType.TIMELINE:
            deadlines = re.findall(r'(?:not later than|within)\s+[\d\w\s]+(?:days?|months?|years?)', content, re.IGNORECASE)
            values['deadlines'] = deadlines
        
        elif evidence_type == EvidenceType.ENFORCEMENT:
            penalties = re.findall(r'(?:penalty|fine)[^.]*\$[\d,]+', content, re.IGNORECASE)
            values['penalties'] = penalties
        
        elif evidence_type == EvidenceType.AUTHORITY:
            authorities = re.findall(r'(?:secretary|administrator|director)\s+(?:of|for)\s+[\w\s]+', content, re.IGNORECASE)
            values['authorities'] = authorities
        
        return values
    
    def _calculate_relationship_score(self, span1: EvidenceSpan, span2: EvidenceSpan) -> float:
        """Calculate relationship score between two evidence spans"""
        
        # Same evidence type increases relationship
        type_bonus = 0.3 if span1.evidence_type == span2.evidence_type else 0
        
        # Similar headings increase relationship
        heading_words1 = set(span1.heading.lower().split())
        heading_words2 = set(span2.heading.lower().split())
        heading_overlap = len(heading_words1 & heading_words2) / max(len(heading_words1 | heading_words2), 1)
        
        # Content similarity
        content_words1 = set(span1.content.lower().split())
        content_words2 = set(span2.content.lower().split())
        content_overlap = len(content_words1 & content_words2) / max(len(content_words1 | content_words2), 1)
        
        # Sequential positioning (closer spans are more related)
        distance = abs(span1.start_offset - span2.start_offset)
        proximity_bonus = max(0, 0.2 - distance / 10000)  # Decrease with distance
        
        relationship_score = type_bonus + heading_overlap * 0.3 + content_overlap * 0.2 + proximity_bonus
        
        return min(1.0, relationship_score)
    
    def _ensure_evidence_diversity(self, spans: List[EvidenceSpan]) -> List[EvidenceSpan]:
        """Ensure diversity of evidence types in final selection"""
        
        # Group spans by evidence type
        type_groups = {}
        for span in spans:
            if span.evidence_type not in type_groups:
                type_groups[span.evidence_type] = []
            type_groups[span.evidence_type].append(span)
        
        # Select top spans from each type to ensure diversity
        diversified_spans = []
        
        # Prioritize critical evidence types
        critical_types = [EvidenceType.FUNDING, EvidenceType.ENFORCEMENT, EvidenceType.MANDATE, EvidenceType.TIMELINE]
        
        for evidence_type in critical_types:
            if evidence_type in type_groups:
                # Take top 2 spans from critical types
                diversified_spans.extend(type_groups[evidence_type][:2])
                del type_groups[evidence_type]
        
        # Add remaining spans from other types
        for evidence_type, type_spans in type_groups.items():
            # Take top 1 span from other types
            diversified_spans.extend(type_spans[:1])
        
        # Sort by composite score and return
        diversified_spans.sort(key=lambda s: getattr(s, 'composite_score', 0), reverse=True)
        
        return diversified_spans
    
    def _detect_primary_topic(self, title: str, bill_text: str) -> str:
        """Detect primary topic of the bill"""
        
        text = (title + ' ' + bill_text[:1000]).lower()
        
        topic_patterns = {
            'healthcare': ['health', 'medical', 'medicare', 'medicaid', 'hospital'],
            'education': ['education', 'school', 'student', 'university', 'learning'],
            'environment': ['environment', 'climate', 'energy', 'pollution', 'conservation'],
            'defense': ['defense', 'military', 'security', 'armed forces', 'veteran'],
            'economy': ['economic', 'business', 'trade', 'commerce', 'financial'],
            'regulatory': ['regulation', 'compliance', 'standard', 'requirement', 'enforce'],
            'social': ['social', 'welfare', 'benefits', 'assistance', 'community'],
            'technology': ['technology', 'digital', 'cyber', 'internet', 'data']
        }
        
        topic_scores = {}
        for topic, keywords in topic_patterns.items():
            score = sum(1 for keyword in keywords if keyword in text)
            topic_scores[topic] = score
        
        return max(topic_scores.items(), key=lambda x: x[1])[0] if topic_scores else 'general'
    
    def _extract_key_terms(self, title: str, bill_text: str) -> List[str]:
        """Extract key terms for prioritization"""
        
        # Extract meaningful words from title
        title_words = [word.lower() for word in title.split() 
                      if len(word) > 3 and word.lower() not in ['the', 'and', 'for', 'act', 'bill']]
        
        # Extract high-frequency meaningful words from bill text
        text_words = re.findall(r'\b[a-zA-Z]{4,}\b', bill_text[:2000])
        word_freq = {}
        for word in text_words:
            word_lower = word.lower()
            word_freq[word_lower] = word_freq.get(word_lower, 0) + 1
        
        # Get top frequent words
        frequent_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        frequent_words = [word for word, freq in frequent_words if freq > 2]
        
        # Combine and deduplicate
        keywords = list(set(title_words + frequent_words))
        
        return keywords[:15]  # Limit to 15 keywords
    
    def _determine_target_audience(self, title: str, bill_text: str) -> str:
        """Determine target audience based on bill complexity"""
        
        text = (title + ' ' + bill_text[:1000]).lower()
        
        # Technical indicators
        technical_terms = ['regulation', 'compliance', 'subsection', 'amendment', 'jurisdiction']
        technical_count = sum(1 for term in technical_terms if term in text)
        
        # Citizen-relevant indicators
        citizen_terms = ['citizen', 'person', 'individual', 'public', 'consumer', 'taxpayer']
        citizen_count = sum(1 for term in citizen_terms if term in text)
        
        if citizen_count > technical_count:
            return 'citizens'
        elif technical_count > 5:
            return 'experts'
        else:
            return 'general'
    
    def _create_processing_report(self, raw_spans: List[Dict], 
                                prioritized_spans: List[EvidenceSpan],
                                context: EvidenceContext) -> Dict[str, Any]:
        """Create comprehensive evidence processing report"""
        
        # Evidence type distribution
        type_distribution = {}
        for span in prioritized_spans:
            type_name = span.evidence_type.value
            type_distribution[type_name] = type_distribution.get(type_name, 0) + 1
        
        # Quality metrics
        avg_priority = sum(s.priority_score for s in prioritized_spans) / len(prioritized_spans) if prioritized_spans else 0
        avg_specificity = sum(s.specificity_score for s in prioritized_spans) / len(prioritized_spans) if prioritized_spans else 0
        avg_actionability = sum(s.actionability_score for s in prioritized_spans) / len(prioritized_spans) if prioritized_spans else 0
        
        # Processing statistics
        processing_stats = {
            'input_spans': len(raw_spans),
            'output_spans': len(prioritized_spans),
            'filtering_efficiency': 1 - (len(prioritized_spans) / len(raw_spans)) if raw_spans else 0,
            'avg_priority_score': avg_priority,
            'avg_specificity_score': avg_specificity,
            'avg_actionability_score': avg_actionability
        }
        
        return {
            'context': {
                'primary_topic': context.primary_topic,
                'target_audience': context.target_audience,
                'keywords': context.keywords[:5]  # Top 5 keywords
            },
            'evidence_distribution': type_distribution,
            'quality_metrics': processing_stats,
            'processing_summary': f"Processed {len(raw_spans)} evidence spans, selected {len(prioritized_spans)} high-quality spans with {avg_priority:.2f} avg priority score"
        }

# Global instance
evidence_first_processor = EvidenceFirstProcessor()

def get_evidence_first_processor() -> EvidenceFirstProcessor:
    """Get the global evidence-first processor instance"""
    return evidence_first_processor