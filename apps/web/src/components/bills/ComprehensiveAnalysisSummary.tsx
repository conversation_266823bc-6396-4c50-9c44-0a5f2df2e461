import React from 'react';
import { 
  CheckCircleIcon, 
  DocumentTextIcon, 
  CogIcon, 
  ListBulletIcon,
  InformationCircleIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import type { BillDetailsResponse } from '../../types/billDetails';

interface ComprehensiveAnalysisSummaryProps {
  details: BillDetailsResponse;
}

export function ComprehensiveAnalysisSummary({ details }: ComprehensiveAnalysisSummaryProps) {
  const isComprehensive = details.other_details &&
    typeof details.other_details === 'object' &&
    'processing_method' in details.other_details &&
    details.other_details.processing_method === 'comprehensive_chunk_analysis';

  if (!isComprehensive) {
    return null;
  }

  const stats = {
    heroSummaryCitations: details.hero_summary_citations?.length || 0,
    primaryMechanisms: details.overview?.primary_mechanisms?.length || 0,
    additionalDetails: details.overview?.additional_details?.length || 0,
    completeAnalysis: details.overview?.complete_analysis?.length || 0,
    coverageRatio: details.metrics?.coverage_ratio || 0,
    totalProvisions: details.overview?.additional_details?.reduce((total, section) => 
      total + (section.provisions?.length || 0), 0) || 0
  };

  return (
    <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6 mb-8">
      <div className="flex items-start gap-4">
        <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0">
          <CheckCircleIcon className="h-6 w-6 text-green-600" />
        </div>
        
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-green-900 mb-2">
            World-Class Comprehensive Analysis
          </h3>
          <p className="text-green-800 text-sm mb-4">
            This bill has been analyzed using our advanced comprehensive system that breaks down 
            every provision with specific mechanisms, enforcement details, and complete transparency.
          </p>
          
          {/* Analysis Stats */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="bg-white rounded-lg p-3 border border-green-200">
              <div className="flex items-center gap-2 mb-1">
                <DocumentTextIcon className="h-4 w-4 text-green-600" />
                <span className="text-xs font-medium text-green-700">Hero Citations</span>
              </div>
              <div className="text-lg font-bold text-green-900">{stats.heroSummaryCitations}</div>
            </div>
            
            <div className="bg-white rounded-lg p-3 border border-green-200">
              <div className="flex items-center gap-2 mb-1">
                <CogIcon className="h-4 w-4 text-green-600" />
                <span className="text-xs font-medium text-green-700">Mechanisms</span>
              </div>
              <div className="text-lg font-bold text-green-900">{stats.primaryMechanisms}</div>
            </div>
            
            <div className="bg-white rounded-lg p-3 border border-green-200">
              <div className="flex items-center gap-2 mb-1">
                <ListBulletIcon className="h-4 w-4 text-green-600" />
                <span className="text-xs font-medium text-green-700">Provisions</span>
              </div>
              <div className="text-lg font-bold text-green-900">{stats.totalProvisions}</div>
            </div>
            
            <div className="bg-white rounded-lg p-3 border border-green-200">
              <div className="flex items-center gap-2 mb-1">
                <InformationCircleIcon className="h-4 w-4 text-green-600" />
                <span className="text-xs font-medium text-green-700">Sections</span>
              </div>
              <div className="text-lg font-bold text-green-900">{stats.completeAnalysis}</div>
            </div>
            
            <div className="bg-white rounded-lg p-3 border border-green-200">
              <div className="flex items-center gap-2 mb-1">
                <ChartBarIcon className="h-4 w-4 text-green-600" />
                <span className="text-xs font-medium text-green-700">Coverage</span>
              </div>
              <div className="text-lg font-bold text-green-900">
                {Math.round(stats.coverageRatio * 100)}%
              </div>
            </div>
            
            <div className="bg-white rounded-lg p-3 border border-green-200">
              <div className="flex items-center gap-2 mb-1">
                <CheckCircleIcon className="h-4 w-4 text-green-600" />
                <span className="text-xs font-medium text-green-700">Quality</span>
              </div>
              <div className="text-lg font-bold text-green-900">A+</div>
            </div>
          </div>
          
          {/* Quality Indicators */}
          <div className="mt-4 flex flex-wrap gap-2">
            <span className="inline-flex items-center gap-1 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
              <CheckCircleIcon className="h-3 w-3" />
              Specific mechanisms identified
            </span>
            <span className="inline-flex items-center gap-1 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
              <CheckCircleIcon className="h-3 w-3" />
              Complete transparency
            </span>
            <span className="inline-flex items-center gap-1 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
              <CheckCircleIcon className="h-3 w-3" />
              Exact citations
            </span>
            <span className="inline-flex items-center gap-1 text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
              <CheckCircleIcon className="h-3 w-3" />
              Enforcement details
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
