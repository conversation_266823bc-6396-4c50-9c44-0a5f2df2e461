# Auth0 Setup Instructions

## Current Status ✅
- **Working Tenant**: `dev-vvwd64m28nwqm871.us.auth0.com`
- **Applications**: Already configured for development
- **Environment**: Development environment now uses consistent Auth0 config

## Missing Tenants (To Create Manually) ❌

### 1. **Create Staging Tenant**
1. Go to [Auth0 Dashboard](https://manage.auth0.com/)
2. Click "Create Tenant" 
3. Tenant Name: `staging-modernaction`
4. Tenant Domain: `staging-modernaction.us.auth0.com`
5. Region: US

**Create Applications:**
- **Web App (SPA)**:
  - Name: `ModernAction Staging Web`
  - Type: Single Page Application
  - Allowed Callback URLs: `https://staging.modernaction.io/api/auth/callback`
  - Allowed Logout URLs: `https://staging.modernaction.io`
  - Web Origins: `https://staging.modernaction.io`

- **API**:
  - Name: `ModernAction Staging API`
  - Identifier: `https://api.modernaction.io`

### 2. **Create Production Tenant**
1. Go to [Auth0 Dashboard](https://manage.auth0.com/)
2. Click "Create Tenant"
3. Tenant Name: `prod-modernaction`
4. Tenant Domain: `prod-modernaction.us.auth0.com`
5. Region: US

**Create Applications:**
- **Web App (SPA)**:
  - Name: `ModernAction Production Web`
  - Type: Single Page Application
  - Allowed Callback URLs: `https://modernaction.io/api/auth/callback`
  - Allowed Logout URLs: `https://modernaction.io`
  - Web Origins: `https://modernaction.io`

- **API**:
  - Name: `ModernAction Production API`
  - Identifier: `https://api.modernaction.io`

## Update Environment Files

After creating the tenants, update these files with the new credentials:

### Staging
```bash
# .env.staging (API)
AUTH0_DOMAIN=staging-modernaction.us.auth0.com
AUTH0_AUDIENCE=https://api.modernaction.io
AUTH0_CLIENT_ID=[staging_client_id]
AUTH0_CLIENT_SECRET=[staging_client_secret]

# apps/web/.env.staging (Web)
AUTH0_ISSUER_BASE_URL=https://staging-modernaction.us.auth0.com
AUTH0_CLIENT_ID=[staging_client_id]
AUTH0_CLIENT_SECRET=[staging_client_secret]
AUTH0_AUDIENCE=https://api.modernaction.io
```

### Production
```bash
# .env.production (API)
AUTH0_DOMAIN=prod-modernaction.us.auth0.com
AUTH0_AUDIENCE=https://api.modernaction.io
AUTH0_CLIENT_ID=[prod_client_id]
AUTH0_CLIENT_SECRET=[prod_client_secret]

# apps/web/.env.production (Web)
AUTH0_ISSUER_BASE_URL=https://prod-modernaction.us.auth0.com
AUTH0_CLIENT_ID=[prod_client_id]
AUTH0_CLIENT_SECRET=[prod_client_secret]
AUTH0_AUDIENCE=https://api.modernaction.io
```

## Quick Commands
```bash
# Use development (current working setup)
npm run env:dev

# Use staging (after creating tenant)
npm run env:staging

# Use production (after creating tenant)
npm run env:prod
```

## Priority Actions
1. **Immediate**: Test dashboard with current development setup
2. **Next**: Create staging tenant for staging.modernaction.io
3. **Later**: Create production tenant for launch