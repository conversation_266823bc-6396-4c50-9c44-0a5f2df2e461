'use client';

import React, { useState } from 'react';
import { Official, Bill } from '@/types';

interface SocialShareProps {
  bill?: Bill;
  official?: Official;
  officials?: Official[];
  message?: string;
  stance?: 'support' | 'oppose' | 'amend';
  userLocation?: string;
  className?: string;
}

export const SocialShare: React.FC<SocialShareProps> = ({
  bill,
  official,
  officials = [],
  message,
  stance,
  userLocation,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Create sharing messages based on context
  const createShareContent = (platform: string) => {
    let shareText = '';
    const shareUrl = typeof window !== 'undefined' ? window.location.href : '';
    const hashtags = ['TakeAction', 'CivicEngagement'];

    if (bill && stance) {
      const stanceText = stance === 'support' ? 'support' : stance === 'oppose' ? 'oppose' : 'want changes to';
      shareText = `I ${stanceText} ${bill.bill_number}: ${bill.title}`;
      
      if (userLocation) {
        shareText += ` as a constituent from ${userLocation}`;
      }
      
      hashtags.push(bill.bill_number.replace(/\s+/g, ''));
    } else if (official) {
      shareText = `Learn about ${official.full_name || official.name}, ${official.title || 'representative'}`;
      if (official.state) {
        shareText += ` from ${official.state}`;
      }
      hashtags.push('YourReps', 'Democracy');
    } else if (message) {
      shareText = message;
    } else {
      shareText = 'Take action on legislation that matters to you!';
    }

    // Add call to action based on platform
    if (platform === 'twitter') {
      shareText += ' #ModernAction';
      if (bill) {
        shareText += ` ${shareUrl}`;
      }
    }

    return { shareText, shareUrl, hashtags };
  };

  // Direct engagement with officials' social media
  const getOfficialSocialLinks = () => {
    const socialLinks: Array<{
      platform: string;
      url: string;
      official: Official;
      icon: string;
    }> = [];

    const allOfficials = official ? [official] : officials;

    allOfficials.forEach(off => {
      if (off.social_media) {
        Object.entries(off.social_media).forEach(([platform, url]) => {
          const platformLower = platform.toLowerCase();
          if (platformLower.includes('twitter')) {
            socialLinks.push({
              platform: 'Twitter',
              url,
              official: off,
              icon: '🐦'
            });
          } else if (platformLower.includes('facebook')) {
            socialLinks.push({
              platform: 'Facebook',
              url,
              official: off,
              icon: '📘'
            });
          }
        });
      }
    });

    return socialLinks;
  };

  // Generate platform-specific share URLs
  const getShareUrls = () => {
    const { shareText, shareUrl, hashtags } = createShareContent('generic');

    return {
      twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}${shareUrl ? `&url=${encodeURIComponent(shareUrl)}` : ''}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(shareText)}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}&title=${encodeURIComponent(shareText)}`,
      email: `mailto:?subject=${encodeURIComponent(`Take Action: ${bill?.title || 'Important Legislation'}`)}&body=${encodeURIComponent(`${shareText}\n\nLearn more: ${shareUrl}`)}`,
      sms: `sms:?body=${encodeURIComponent(`${shareText} ${shareUrl}`)}`
    };
  };

  const shareUrls = getShareUrls();
  const officialSocialLinks = getOfficialSocialLinks();

  const handleShare = (url: string, platform: string) => {
    if (platform === 'copy') {
      navigator.clipboard.writeText(window.location.href);
      // Could show a toast here
      return;
    }

    // Open in new window
    window.open(url, '_blank', 'width=600,height=400');
  };

  const directEngagementMessage = () => {
    if (!bill || !stance) return '';
    
    const stanceText = stance === 'support' ? 'support' : stance === 'oppose' ? 'oppose' : 'want changes to';
    return `I ${stanceText} ${bill.bill_number}: ${bill.title}. As your constituent, I hope you will consider my position on this important legislation.`;
  };

  return (
    <div className={`relative ${className}`}>
      {/* Share Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="inline-flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
        </svg>
        <span>Share & Engage</span>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />

          {/* Menu */}
          <div className="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-20 p-4">
            {/* Direct Engagement with Officials */}
            {officialSocialLinks.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-900 mb-3">
                  💬 Message Your Representatives
                </h4>
                <div className="space-y-2">
                  {officialSocialLinks.map((link, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        const message = directEngagementMessage();
                        const engagementUrl = link.platform === 'Twitter' 
                          ? `${link.url}?text=${encodeURIComponent(message)}`
                          : link.url;
                        window.open(engagementUrl, '_blank');
                      }}
                      className="flex items-center justify-between w-full p-2 text-left hover:bg-gray-50 rounded-lg transition-colors duration-200 border border-gray-100"
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{link.icon}</span>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {link.official.full_name || link.official.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {link.platform} • {link.official.title}
                          </div>
                        </div>
                      </div>
                      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* General Social Sharing */}
            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-3">
                📢 Share Your Action
              </h4>
              <div className="grid grid-cols-2 gap-2">
                <button
                  onClick={() => handleShare(shareUrls.twitter, 'twitter')}
                  className="flex items-center justify-center space-x-2 p-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors duration-200"
                >
                  <span>🐦</span>
                  <span className="text-sm font-medium">Twitter</span>
                </button>

                <button
                  onClick={() => handleShare(shareUrls.facebook, 'facebook')}
                  className="flex items-center justify-center space-x-2 p-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors duration-200"
                >
                  <span>📘</span>
                  <span className="text-sm font-medium">Facebook</span>
                </button>

                <button
                  onClick={() => handleShare(shareUrls.linkedin, 'linkedin')}
                  className="flex items-center justify-center space-x-2 p-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors duration-200"
                >
                  <span>💼</span>
                  <span className="text-sm font-medium">LinkedIn</span>
                </button>

                <button
                  onClick={() => handleShare(shareUrls.email, 'email')}
                  className="flex items-center justify-center space-x-2 p-3 bg-gray-50 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                >
                  <span>📧</span>
                  <span className="text-sm font-medium">Email</span>
                </button>
              </div>

              {/* Copy Link */}
              <button
                onClick={() => {
                  navigator.clipboard.writeText(window.location.href);
                  setIsOpen(false);
                }}
                className="w-full mt-3 flex items-center justify-center space-x-2 p-2 border border-gray-300 rounded-lg text-sm text-gray-600 hover:bg-gray-50 transition-colors duration-200"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                <span>Copy Link</span>
              </button>
            </div>

            {/* Tips */}
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <h5 className="text-xs font-semibold text-blue-900 mb-1">💡 Engagement Tips</h5>
              <ul className="text-xs text-blue-800 space-y-1">
                <li>• Be respectful and specific in your messages</li>
                <li>• Share your personal story and local impact</li>
                <li>• Use @mentions to increase visibility</li>
                <li>• Follow up with phone calls or emails</li>
              </ul>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SocialShare;