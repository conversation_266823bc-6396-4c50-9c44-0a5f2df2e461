#!/usr/bin/env python3
"""
Test script to verify that the balanced analysis fix works
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the app directory to the Python path
app_dir = Path(__file__).parent
sys.path.insert(0, str(app_dir))

from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from app.models.bill import Bill
from app.services.ai_service import AIService

async def test_balanced_analysis_fix():
    """Test that balanced analysis returns details_payload"""
    
    # Create database engine and session
    database_url = os.getenv("DATABASE_URL")
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Find the HR5-118 bill
        bill = db.query(Bill).filter(
            Bill.bill_number == "HR5",
            Bill.session_year == 118
        ).first()
        
        if not bill:
            print("❌ Bill HR5-118 not found")
            return False
        
        print(f"✅ Found bill: {bill.bill_number} - {bill.title}")
        print(f"   Full text length: {len(bill.full_text) if bill.full_text else 0}")
        
        # Create AI service
        ai_service = AIService()
        
        if not ai_service.enabled:
            print("❌ AI service is not enabled")
            return False
        
        print("✅ AI service is enabled")
        
        # Create bill metadata
        bill_metadata = {
            'bill_id': bill.id,
            'title': bill.title,
            'bill_number': bill.bill_number,
            'session_year': bill.session_year
        }
        
        # Test balanced analysis
        print("\n🔧 Testing balanced analysis...")
        
        try:
            # Use a small portion of the bill text for testing
            test_text = bill.full_text[:5000] if bill.full_text else "Test bill content"
            
            result = await ai_service.analyze_bill_balanced(
                bill_text=test_text,
                bill_metadata=bill_metadata,
                source_index=None
            )
            
            print(f"✅ Balanced analysis completed!")
            print(f"   Success: {result.get('success')}")
            print(f"   Has details_payload: {'details_payload' in result}")
            
            if 'details_payload' in result:
                details_payload = result['details_payload']
                print(f"   Details payload keys: {list(details_payload.keys())}")
                
                # Check for key components
                has_hero_summary = 'hero_summary' in details_payload
                has_overview = 'overview' in details_payload
                has_positions = 'positions' in details_payload
                
                print(f"   Has hero_summary: {has_hero_summary}")
                print(f"   Has overview: {has_overview}")
                print(f"   Has positions: {has_positions}")
                
                if has_hero_summary:
                    hero_summary = details_payload['hero_summary']
                    print(f"   Hero summary length: {len(hero_summary) if hero_summary else 0}")
                
                return True
            else:
                print("❌ No details_payload in result")
                print(f"   Available keys: {list(result.keys())}")
                return False
                
        except Exception as e:
            print(f"❌ Error in balanced analysis: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    finally:
        db.close()


async def test_simple_ai_call():
    """Test a simple AI call to verify the service works"""
    print("\n🔧 Testing simple AI call...")
    
    ai_service = AIService()
    
    if not ai_service.enabled:
        print("❌ AI service is not enabled")
        return False
    
    try:
        # Test a simple completion
        import openai
        
        client = openai.AsyncOpenAI(api_key=ai_service.api_key)
        
        response = await client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "user", "content": "Say 'Hello, AI service is working!'"}
            ],
            max_tokens=20
        )
        
        result = response.choices[0].message.content
        print(f"✅ Simple AI call successful: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Simple AI call failed: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Testing Balanced Analysis Fix")
    print("=" * 50)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    async def run_tests():
        # Test simple AI call first
        simple_test = await test_simple_ai_call()
        
        if simple_test:
            # Test balanced analysis
            balanced_test = await test_balanced_analysis_fix()
            
            if balanced_test:
                print("\n🎉 SUCCESS! The fix is working correctly")
                print("✅ Balanced analysis now returns details_payload")
                print("✅ The unified processing should now work properly")
            else:
                print("\n💥 FAILED! The fix is not working")
        else:
            print("\n💥 FAILED! AI service is not working")
    
    # Run the async tests
    asyncio.run(run_tests())
