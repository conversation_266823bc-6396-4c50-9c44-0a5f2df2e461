#!/usr/bin/env python3
"""
Unit tests for BillDetailsService - bill details processing with citations.

Tests the core functionality for:
- Creating and updating bill details
- Citation enrichment for missing citations
- Metrics computation (coverage ratio, unverified count)
- Slug generation
"""

import pytest
from unittest.mock import MagicMock, patch
from sqlalchemy.orm import Session
from app.services.bill_details_service import BillDetailsService
from app.models.bill import Bill
from app.models.bill_details import BillDetails


class TestBillDetailsService:
    
    def setup_method(self):
        """Set up test instance with mocked database."""
        self.mock_db = MagicMock(spec=Session)
        self.service = BillDetailsService(self.mock_db)
        
        # Sample bill text
        self.sample_bill_text = """
        SEC. 101. HEALTHCARE ACCESS
        
        This section expands healthcare access to underserved communities by providing additional funding for community health centers.
        
        SEC. 102. FUNDING PROVISIONS
        
        The Secretary shall allocate $5 billion annually for the next five years to support healthcare infrastructure improvements.
        """.strip()
        
        # Sample bill object
        self.sample_bill = MagicMock()
        self.sample_bill.id = "bill_123"
        self.sample_bill.bill_number = "H.R.5"
        self.sample_bill.session_year = 118
        self.sample_bill.title = "Healthcare Access Act"
        self.sample_bill.summary = "A bill to expand healthcare access"
        self.sample_bill.ai_summary = None
    
    def test_slugify_basic(self):
        """Test basic slug generation from bill number and session year."""
        bill = MagicMock()
        bill.bill_number = "H.R.5"
        bill.session_year = 118
        
        slug = self.service._slugify(bill)
        assert slug == "hr5-118"
    
    def test_slugify_with_dots_and_spaces(self):
        """Test slug generation handles dots and spaces."""
        bill = MagicMock()
        bill.bill_number = "S. 1234"
        bill.session_year = 118
        
        slug = self.service._slugify(bill)
        assert slug == "s1234-118"
    
    def test_slugify_complex_bill_number(self):
        """Test slug generation with complex bill numbers."""
        bill = MagicMock()
        bill.bill_number = "H.J.Res.45"
        bill.session_year = 117
        
        slug = self.service._slugify(bill)
        assert slug == "hjres45-117"
    
    def test_generate_candidate_quotes(self):
        """Test heuristic quote extraction from content."""
        content = "This bill expands healthcare access to underserved communities and provides additional funding for infrastructure."
        
        quotes = self.service._generate_candidate_quotes(content, self.sample_bill_text, max_quotes=2)
        
        assert isinstance(quotes, list)
        # Should find some matching phrases
        assert len(quotes) > 0
        
        # Each quote should be at least 4 words
        for quote in quotes:
            assert len(quote.split()) >= 4
            # Quote should exist in the full text
            assert quote.lower() in self.sample_bill_text.lower()
    
    def test_generate_candidate_quotes_no_matches(self):
        """Test quote generation when no matches are found."""
        content = "This content has no matches in the bill text whatsoever."
        
        quotes = self.service._generate_candidate_quotes(content, self.sample_bill_text)
        
        assert quotes == []
    
    def test_generate_candidate_quotes_empty_inputs(self):
        """Test quote generation with empty inputs."""
        # Empty content
        quotes = self.service._generate_candidate_quotes("", self.sample_bill_text)
        assert quotes == []
        
        # Empty full text
        quotes = self.service._generate_candidate_quotes("some content", "")
        assert quotes == []
    
    def test_enrich_citations_overview_sections(self):
        """Test citation enrichment for overview sections."""
        details_payload = {
            "overview": {
                "what_does": {
                    "content": "This bill expands healthcare access to underserved communities",
                    "citations": []
                },
                "who_affects": {
                    "content": "The Secretary shall allocate funding for infrastructure",
                    "citations": []
                }
            }
        }
        
        # Mock the text citation service
        with patch.object(self.service.text_service, 'build_source_index') as mock_build_index, \
             patch.object(self.service.text_service, 'bind_quote') as mock_bind_quote:
            
            mock_build_index.return_value = []
            mock_bind_quote.return_value = MagicMock(
                quote="test quote",
                start_offset=0,
                end_offset=10,
                heading="SEC. 101",
                anchor_id="sec-1"
            )
            
            enriched = self.service._enrich_citations(details_payload, self.sample_bill_text, [])
            
            # Citations should be added (mock bind_quote will be called for each quote found)
            what_does_citations = enriched["overview"]["what_does"]["citations"]
            who_affects_citations = enriched["overview"]["who_affects"]["citations"]
            
            # Since we mock bind_quote to always return a valid citation, we should get citations
            # The actual number depends on how many candidate quotes are generated
            assert isinstance(what_does_citations, list)
            assert isinstance(who_affects_citations, list)
            
            # Each citation should have required fields
            for citation in what_does_citations:
                assert "quote" in citation
                assert "start_offset" in citation
                assert "end_offset" in citation
                assert "heading" in citation
                assert "anchor_id" in citation
    
    def test_enrich_citations_positions(self):
        """Test citation enrichment for position reasons."""
        details_payload = {
            "positions": {
                "support_reasons": [
                    {
                        "claim": "This bill expands healthcare access",
                        "justification": "More people will have access to care",
                        "citations": []
                    }
                ],
                "oppose_reasons": [
                    {
                        "claim": "The funding allocation is insufficient",
                        "justification": "More money is needed",
                        "citations": []
                    }
                ]
            }
        }
        
        with patch.object(self.service.text_service, 'build_source_index') as mock_build_index, \
             patch.object(self.service.text_service, 'bind_quote') as mock_bind_quote:
            
            mock_build_index.return_value = []
            mock_bind_quote.return_value = MagicMock(
                quote="test quote",
                start_offset=0,
                end_offset=10,
                heading="SEC. 101",
                anchor_id="sec-1"
            )
            
            enriched = self.service._enrich_citations(details_payload, self.sample_bill_text, [])
            
            # Citations should be added to position reasons
            support_citations = enriched["positions"]["support_reasons"][0]["citations"]
            oppose_citations = enriched["positions"]["oppose_reasons"][0]["citations"]
            
            assert isinstance(support_citations, list)
            assert isinstance(oppose_citations, list)
    
    def test_compute_metrics_full_coverage(self):
        """Test metrics computation when all sections have citations."""
        details_payload = {
            "overview": {
                "what_does": {
                    "content": "Some content",
                    "citations": [{"quote": "test quote"}]
                },
                "who_affects": {
                    "content": "More content", 
                    "citations": [{"quote": "another quote"}]
                }
            }
        }
        
        metrics = self.service._compute_metrics(details_payload)
        
        assert metrics["coverage_ratio"] == 1.0
        assert metrics["unverified_count"] == 0
    
    def test_compute_metrics_partial_coverage(self):
        """Test metrics computation with some sections lacking citations."""
        details_payload = {
            "overview": {
                "what_does": {
                    "content": "Some content",
                    "citations": [{"quote": "test quote"}]
                },
                "who_affects": {
                    "content": "More content",
                    "citations": []  # No citations
                }
            }
        }
        
        metrics = self.service._compute_metrics(details_payload)
        
        assert metrics["coverage_ratio"] == 0.5  # 1 out of 2 sections
        assert metrics["unverified_count"] == 1
    
    def test_compute_metrics_no_content(self):
        """Test metrics computation with no content sections."""
        details_payload = {
            "overview": {},
            "positions": {}
        }
        
        metrics = self.service._compute_metrics(details_payload)
        
        assert metrics["coverage_ratio"] == 0.0
        assert metrics["unverified_count"] == 0
    
    def test_create_or_update_details_new_record(self):
        """Test creating new bill details record."""
        # Mock database query to return None (no existing record)
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        
        details_payload = {
            "hero_summary": "Test summary",
            "overview": {
                "what_does": {"content": "Test content", "citations": []}
            }
        }
        
        with patch.object(self.service.text_service, 'build_source_index') as mock_build_index:
            mock_build_index.return_value = [
                MagicMock(heading="SEC. 101", start_offset=0, end_offset=50, anchor_id="sec-1")
            ]
            
            result = self.service.create_or_update_details(
                self.sample_bill, 
                self.sample_bill_text, 
                details_payload
            )
            
            # Should create new BillDetails object
            self.mock_db.add.assert_called_once()
            self.mock_db.commit.assert_called_once()
            self.mock_db.refresh.assert_called_once()
    
    def test_create_or_update_details_existing_record(self):
        """Test updating existing bill details record."""
        # Mock existing record
        existing_details = MagicMock()
        existing_details.seo_slug = "existing-slug"
        existing_details.canonical_url = "https://example.com"
        self.mock_db.query.return_value.filter.return_value.first.return_value = existing_details
        
        details_payload = {
            "hero_summary": "Updated summary",
            "overview": {
                "what_does": {"content": "Updated content", "citations": []}
            }
        }
        
        with patch.object(self.service.text_service, 'build_source_index') as mock_build_index:
            mock_build_index.return_value = []
            
            result = self.service.create_or_update_details(
                self.sample_bill,
                self.sample_bill_text,
                details_payload
            )
            
            # Should update existing record, not create new one
            self.mock_db.add.assert_not_called()
            self.mock_db.commit.assert_called_once()
            self.mock_db.refresh.assert_called_once_with(existing_details)
            
            # Check that fields were updated
            assert existing_details.hero_summary == "Updated summary"
            assert existing_details.overview == details_payload["overview"]
    
    def test_review_flag_low_coverage(self):
        """Test that review flag is set when coverage is low."""
        details_payload = {
            "overview": {
                "what_does": {"content": "Some content", "citations": []},  # No citations
                "who_affects": {"content": "More content", "citations": []}  # No citations
            }
        }
        
        self.mock_db.query.return_value.filter.return_value.first.return_value = None
        
        with patch.object(self.service.text_service, 'build_source_index') as mock_build_index, \
             patch.object(self.service, '_enrich_citations') as mock_enrich:
            
            mock_build_index.return_value = []
            mock_enrich.return_value = details_payload  # Return unchanged (no citations added)
            
            result = self.service.create_or_update_details(
                self.sample_bill,
                self.sample_bill_text,
                details_payload
            )
            
            # Check that the BillDetails constructor was called with needs_human_review=True
            call_args = self.mock_db.add.call_args[0][0]
            assert hasattr(call_args, 'needs_human_review')


if __name__ == "__main__":
    pytest.main([__file__, "-v"])