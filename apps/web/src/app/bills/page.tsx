'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Bill, BillStatus } from '../../types';
import { BillActionModal } from '../../components/shared';
import { EnhancedBillCard, FilterBar } from '../../components/bills';
import { BillCardSkeleton } from '../../components/bills/BillCardSkeleton';
import { billApi } from '../../services/apiClient';
import { analytics } from '../../utils/analytics';
import { formatCompactNumber } from '../../utils/formatNumber';
import toast from 'react-hot-toast';

const BillsPage: React.FC = () => {
  const [bills, setBills] = useState<Bill[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedBill, setSelectedBill] = useState<Bill | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [firstActionTime] = useState(Date.now());

  // Filter and search state
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<BillStatus | 'all'>('all');
  const [urgencyFilter, setUrgencyFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  const [sortBy, setSortBy] = useState<'priority' | 'date' | 'title' | 'action_count'>('priority');

  useEffect(() => {
    loadBills();
  }, []);

  const loadBills = async () => {
    try {
      setIsLoading(true);
      console.log('Loading bills from API...');

      // Load real bills from the API using the simple endpoint
      const billsData = await billApi.getBillsSimple({ limit: 50 });
      console.log('Bills data received:', billsData);

      // Transform the simple bill data to match the Bill interface
      const transformedBills = billsData.map((bill: any) => {
        const session = bill.session_year ?? bill.session ?? 118;
        return {
          ...bill,
          bill_type: bill.chamber === 'house' ? 'house_bill' : 'senate_bill',
          session_year: session,
          state: 'federal',
          description: bill.ai_summary || bill.title,
          summary: bill.ai_summary || '',
          simple_summary: bill.ai_summary || '',
          tldr: bill.ai_summary || '',
          sponsor_name: '',
          sponsor_party: '',
          sponsor_state: '',
          introduced_date: bill.created_at,
          last_action_date: bill.created_at,
          tags: [],
          categories: [],
          reasons_for_support: [],
          reasons_for_opposition: [],
          cosponsors: [],
          vote_history: []
        };
      });

      console.log('Transformed bills:', transformedBills);
      setBills(transformedBills);
      
      // Track page view
      analytics.trackBillsPageView(transformedBills.length, transformedBills.length);
    } catch (error) {
      console.error('Failed to load bills:', error);
      toast.error('Failed to load bills. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTakeAction = (bill: Bill) => {
    // Track time to first action
    analytics.trackTimeToFirstAction(Date.now() - firstActionTime);
    analytics.trackCardAction(bill.id, 'take_action');
    
    setSelectedBill(bill);
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedBill(null);
  };

  const handleActionSubmit = (result: any) => {
    console.log('Action submitted:', result);
    // Could update UI to show action was taken
  };

  // Enhanced search handler with analytics
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    if (query) {
      // Debounce analytics tracking
      setTimeout(() => {
        const resultCount = bills.filter(bill => {
          const q = query.toLowerCase();
          return bill.title.toLowerCase().includes(q) ||
                 bill.bill_number.toLowerCase().includes(q) ||
                 bill.summary?.toLowerCase().includes(q);
        }).length;
        analytics.trackSearch(query, resultCount);
      }, 1000);
    }
  };

  // Enhanced filter handlers with analytics
  const handleStatusFilterChange = (status: BillStatus | 'all') => {
    setStatusFilter(status);
    if (status !== 'all') {
      analytics.trackFilterApply('status', status, filteredAndSortedBills.length);
    }
  };

  const handleUrgencyFilterChange = (urgency: 'all' | 'high' | 'medium' | 'low') => {
    setUrgencyFilter(urgency);
    if (urgency !== 'all') {
      analytics.trackFilterApply('urgency', urgency, filteredAndSortedBills.length);
    }
  };

  const handleSortChange = (sort: 'priority' | 'date' | 'title' | 'action_count') => {
    setSortBy(sort);
    analytics.trackFilterApply('sort', sort, filteredAndSortedBills.length);
  };

  // Filter and sort bills
  const filteredAndSortedBills = useMemo(() => {
    let filtered = bills.filter(bill => {
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch =
          bill.title.toLowerCase().includes(query) ||
          bill.bill_number.toLowerCase().includes(query) ||
          bill.summary?.toLowerCase().includes(query) ||
          bill.summary_what_does?.content?.toLowerCase().includes(query);
        if (!matchesSearch) return false;
      }

      // Status filter
      if (statusFilter !== 'all' && bill.status !== statusFilter) {
        return false;
      }

      // Urgency filter (mock logic based on status)
      if (urgencyFilter !== 'all') {
        const billUrgency = bill.status === 'floor' ? 'high' :
                           bill.status === 'committee' ? 'medium' : 'low';
        if (billUrgency !== urgencyFilter) return false;
      }

      return true;
    });

    // Sort bills
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'title':
          return a.title.localeCompare(b.title);
        case 'action_count':
          // Mock action count based on priority score
          return b.priority_score - a.priority_score;
        case 'priority':
        default:
          return b.priority_score - a.priority_score;
      }
    });

    return filtered;
  }, [bills, searchQuery, statusFilter, urgencyFilter, sortBy]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Loading Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3.5">
            <h1 className="text-xl font-semibold text-gray-900">Federal Bills</h1>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Loading Filter Bar Skeleton */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 mb-4 animate-pulse">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1 bg-gray-200 h-10 rounded-lg"></div>
              <div className="flex gap-3">
                <div className="bg-gray-200 h-10 w-28 rounded-lg"></div>
                <div className="bg-gray-200 h-10 w-24 rounded-lg"></div>
                <div className="bg-gray-200 h-10 w-20 rounded-lg"></div>
              </div>
            </div>
          </div>

          {/* Loading Bills Skeleton */}
          <BillCardSkeleton count={6} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Skip Link for Accessibility */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>
      
      {/* Urgent Banner - Temporarily hidden */}
      {false && bills.filter(b => b.status === 'floor').length > 0 && (
        <div className="bg-red-600 text-white sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-lg" aria-hidden="true">🔥</span>
                <span className="font-medium">
                  {formatCompactNumber(bills.filter(b => b.status === 'floor').length)} urgent vote{bills.filter(b => b.status === 'floor').length > 1 ? 's' : ''} this week
                </span>
              </div>
              <button
                onClick={() => {
                  analytics.trackChipToggle('urgent_banner', 'floor-high');
                  setStatusFilter('floor' as BillStatus);
                  setUrgencyFilter('high');
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    analytics.trackChipToggle('urgent_banner', 'floor-high');
                    setStatusFilter('floor' as BillStatus);
                    setUrgencyFilter('high');
                  }
                }}
                aria-pressed={statusFilter === 'floor' && urgencyFilter === 'high'}
                className="bg-red-700 hover:bg-red-800 px-3 py-1 rounded-full text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-red-300"
              >
                View urgent votes
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Optimized Header - Clean for signed-in users */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3.5">
          <h1 className="text-xl font-semibold text-gray-900">Federal Bills</h1>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6" id="main-content">

        {/* Filter Bar */}
        <FilterBar
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          statusFilter={statusFilter}
          onStatusFilterChange={handleStatusFilterChange}
          urgencyFilter={urgencyFilter}
          onUrgencyFilterChange={handleUrgencyFilterChange}
          sortBy={sortBy}
          onSortChange={handleSortChange}
          totalCount={bills.length}
          filteredCount={filteredAndSortedBills.length}
        />

        {/* Result Count */}
        <div className="mt-8 mb-6 text-sm text-gray-600">
          Showing <span className="font-medium text-gray-900">{filteredAndSortedBills.length}</span> of <span className="font-medium text-gray-900">{bills.length}</span> bills
        </div>

        {/* Bills Grid */}
        {bills.length === 0 ? (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No bills available</h3>
              <p className="text-gray-500 mb-6">We're working to load the latest legislative bills. Please check back in a moment.</p>
              <button
                onClick={loadBills}
                className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Refresh
              </button>
            </div>
          </div>
        ) : filteredAndSortedBills.length === 0 ? (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No results found</h3>
              <p className="text-gray-500 mb-6">Try adjusting your filters or search terms to find what you're looking for.</p>
              <div className="space-y-3">
                <button
                  onClick={() => {
                    analytics.trackFilterClear('all');
                    setSearchQuery('');
                    setStatusFilter('all');
                    setUrgencyFilter('all');
                    setSortBy('priority');
                  }}
                  className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Clear all filters
                </button>
                <div className="text-sm text-gray-600">
                  <p>Suggested quick filters:</p>
                  <div className="flex justify-center gap-2 mt-2">
                    <button
                      onClick={() => {
                        analytics.trackChipToggle('urgent', 'floor-high');
                        setSearchQuery('');
                        setStatusFilter('floor' as BillStatus);
                        setUrgencyFilter('high');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          analytics.trackChipToggle('urgent', 'floor-high');
                          setSearchQuery('');
                          setStatusFilter('floor' as BillStatus);
                          setUrgencyFilter('high');
                        }
                      }}
                      aria-pressed={statusFilter === 'floor' && urgencyFilter === 'high'}
                      className="inline-flex h-8 items-center gap-1.5 rounded-full px-3 text-xs font-medium bg-red-100 text-red-800 hover:bg-red-200 transition-colors focus:outline-none focus:ring-2 focus:ring-red-300 focus:ring-offset-2"
                    >
                      <span aria-hidden="true">🔥</span> Urgent votes
                    </button>
                    <button
                      onClick={() => {
                        analytics.trackChipToggle('active', 'committee-all');
                        setSearchQuery('');
                        setStatusFilter('committee' as BillStatus);
                        setUrgencyFilter('all');
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault();
                          analytics.trackChipToggle('active', 'committee-all');
                          setSearchQuery('');
                          setStatusFilter('committee' as BillStatus);
                          setUrgencyFilter('all');
                        }
                      }}
                      aria-pressed={statusFilter === 'committee'}
                      className="inline-flex h-8 items-center gap-1.5 rounded-full px-3 text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 transition-colors focus:outline-none focus:ring-2 focus:ring-green-300 focus:ring-offset-2"
                    >
                      <span aria-hidden="true">📈</span> Active bills
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="columns-1 lg:columns-2 xl:columns-3 gap-6 space-y-6">
            {filteredAndSortedBills.map((bill) => (
              <EnhancedBillCard
                key={bill.id}
                bill={bill}
                onTakeAction={handleTakeAction}
                usePageNavigation={true}
              />
            ))}
          </div>
        )}

        {/* Action Modal */}
        {selectedBill && (
          <BillActionModal
            isOpen={isModalOpen}
            onClose={handleModalClose}
            bill={selectedBill}
            onSubmit={handleActionSubmit}
          />
        )}


      </div>
    </div>
  );
};

export default BillsPage;
