#!/usr/bin/env python3
"""
Simple test of Secondary Analysis Service
"""

import asyncio
import sys
import os
sys.path.append('./apps/api')
os.environ['ENV_FILE'] = '.env.local'

async def test_secondary_simple():
    print("🧪 Simple Secondary Analysis Service Test...")
    
    try:
        from app.services.ai_service import AIService
        from app.services.secondary_analysis_service import SecondaryAnalysisService
        
        # Initialize services
        ai_service = AIService()
        secondary_service = SecondaryAnalysisService(ai_service)
        
        # Sample bill data
        bill_text = "This bill establishes a comprehensive framework for public transparency in government operations."
        bill_metadata = {
            "title": "Test Public Transparency Act",
            "bill_number": "HR9999",
            "session_year": 119
        }
        congress_summary = "A bill to promote government transparency and accountability."
        
        print("1. Testing Secondary Analysis Service...")
        result = await secondary_service.generate_secondary_analysis(
            bill_text=bill_text,
            bill_metadata=bill_metadata,
            congress_summary=congress_summary,
            evidence_spans=[]
        )
        
        print(f"✅ Success: {result.success}")
        if result.success:
            print(f"✅ What Does: {len(result.what_does.get('content', '')) if result.what_does else 0} characters")
            print(f"✅ Who Affects: {len(result.who_affects.get('content', '')) if result.who_affects else 0} characters")
            print(f"✅ Why Matters: {len(result.why_matters.get('content', '')) if result.why_matters else 0} characters")
            print(f"✅ Key Provisions: {len(result.key_provisions) if result.key_provisions else 0} items")
            print(f"✅ Processing Notes: {result.processing_notes}")
            
            # Show sample content
            if result.what_does and result.what_does.get('content'):
                sample = result.what_does['content'][:200] + "..." if len(result.what_does['content']) > 200 else result.what_does['content']
                print(f"📝 Sample content: \"{sample}\"")
        else:
            print(f"❌ Error: {result.error}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_secondary_simple())