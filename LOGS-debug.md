August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1562s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1562s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2088s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,730 INFO sqlalchemy.engine.Engine [cached since 1562s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,730 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,731 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,731 INFO sqlalchemy.engine.Engine [cached since 1562s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,732 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_summary_versions.bill_id AS bill_summary_versions_bill_id, bill_summary_versions.version_number AS bill_summary_versions_version_number, bill_summary_versions.change_reason AS bill_summary_versions_change_reason, bill_summary_versions.summary_what_does AS bill_summary_versions_summary_what_does, bill_summary_versions.summary_who_affects AS bill_summary_versions_summary_who_affects, bill_summary_versions.summary_why_matters AS bill_summary_versions_summary_why_matters, bill_summary_versions.summary_key_provisions AS bill_summary_versions_summary_key_provisions, bill_summary_versions.summary_timeline AS bill_summary_versions_summary_timeline, bill_summary_versions.summary_cost_impact AS bill_summary_versions_summary_cost_impact, bill_summary_versions.ai_summary AS bill_summary_versions_ai_summary, bill_summary_versions.simple_summary AS bill_summary_versions_simple_summary, bill_summary_versions.tldr AS bill_summary_versions_tldr, bill_summary_versions.ai_processed_at AS bill_summary_versions_ai_processed_at, bill_summary_versions.is_current AS bill_summary_versions_is_current, bill_summary_versions.changes_detected AS bill_summary_versions_changes_detected, bill_summary_versions.id AS bill_summary_versions_id, bill_summary_versions.created_at AS bill_summary_versions_created_at, bill_summary_versions.updated_at AS bill_summary_versions_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_summary_versions
********************************
web
August 07, 2025 at 01:16
WHERE bill_summary_versions.bill_id = %(bill_id_1)s ORDER BY bill_summary_versions.version_number DESC
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,729 INFO sqlalchemy.engine.Engine SELECT bill_summary_versions.bill_id AS bill_summary_versions_bill_id, bill_summary_versions.version_number AS bill_summary_versions_version_number, bill_summary_versions.change_reason AS bill_summary_versions_change_reason, bill_summary_versions.summary_what_does AS bill_summary_versions_summary_what_does, bill_summary_versions.summary_who_affects AS bill_summary_versions_summary_who_affects, bill_summary_versions.summary_why_matters AS bill_summary_versions_summary_why_matters, bill_summary_versions.summary_key_provisions AS bill_summary_versions_summary_key_provisions, bill_summary_versions.summary_timeline AS bill_summary_versions_summary_timeline, bill_summary_versions.summary_cost_impact AS bill_summary_versions_summary_cost_impact, bill_summary_versions.ai_summary AS bill_summary_versions_ai_summary, bill_summary_versions.simple_summary AS bill_summary_versions_simple_summary, bill_summary_versions.tldr AS bill_summary_versions_tldr, bill_summary_versions.ai_processed_at AS bill_summary_versions_ai_processed_at, bill_summary_versions.is_current AS bill_summary_versions_is_current, bill_summary_versions.changes_detected AS bill_summary_versions_changes_detected, bill_summary_versions.id AS bill_summary_versions_id, bill_summary_versions.created_at AS bill_summary_versions_created_at, bill_summary_versions.updated_at AS bill_summary_versions_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_summary_versions
********************************
web
August 07, 2025 at 01:16
WHERE bill_summary_versions.bill_id = %(bill_id_1)s ORDER BY bill_summary_versions.version_number DESC
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,730 INFO sqlalchemy.engine.Engine [cached since 2088s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2088s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,728 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,728 INFO sqlalchemy.engine.Engine [cached since 2088s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,728 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,728 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2088s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,727 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO: **********:62928 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3 HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,720 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,717 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,717 INFO sqlalchemy.engine.Engine [cached since 2088s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2088s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,717 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,325 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO: **********:41034 - "GET /api/v1/values/bill/1df829e1-0207-43c3-a25f-3051630d6ae3/tags HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,324 INFO sqlalchemy.engine.Engine SELECT bill_values_tags.bill_id AS bill_values_tags_bill_id, bill_values_tags.analysis_id AS bill_values_tags_analysis_id, bill_values_tags.tag_category AS bill_values_tags_tag_category, bill_values_tags.tag_type AS bill_values_tags_tag_type, bill_values_tags.tag_name AS bill_values_tags_tag_name, bill_values_tags.display_text AS bill_values_tags_display_text, bill_values_tags.description AS bill_values_tags_description, bill_values_tags.severity_level AS bill_values_tags_severity_level, bill_values_tags.display_priority AS bill_values_tags_display_priority, bill_values_tags.color_theme AS bill_values_tags_color_theme, bill_values_tags.icon_name AS bill_values_tags_icon_name, bill_values_tags.is_active AS bill_values_tags_is_active, bill_values_tags.created_at AS bill_values_tags_created_at, bill_values_tags.updated_at AS bill_values_tags_updated_at, bill_values_tags.id AS bill_values_tags_id
********************************
web
August 07, 2025 at 01:16
FROM bill_values_tags
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_tags.analysis_id = %(analysis_id_1)s AND bill_values_tags.is_active = true
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_values_tags.bill_id AS bill_values_tags_bill_id, bill_values_tags.analysis_id AS bill_values_tags_analysis_id, bill_values_tags.tag_category AS bill_values_tags_tag_category, bill_values_tags.tag_type AS bill_values_tags_tag_type, bill_values_tags.tag_name AS bill_values_tags_tag_name, bill_values_tags.display_text AS bill_values_tags_display_text, bill_values_tags.description AS bill_values_tags_description, bill_values_tags.severity_level AS bill_values_tags_severity_level, bill_values_tags.display_priority AS bill_values_tags_display_priority, bill_values_tags.color_theme AS bill_values_tags_color_theme, bill_values_tags.icon_name AS bill_values_tags_icon_name, bill_values_tags.is_active AS bill_values_tags_is_active, bill_values_tags.created_at AS bill_values_tags_created_at, bill_values_tags.updated_at AS bill_values_tags_updated_at, bill_values_tags.id AS bill_values_tags_id
********************************
web
August 07, 2025 at 01:16
FROM bill_values_tags
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_tags.analysis_id = %(analysis_id_1)s AND bill_values_tags.is_active = true
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,324 INFO sqlalchemy.engine.Engine [cached since 0.3944s ago] {'analysis_id_1': 'cb6d7704-ad9a-4b57-8be6-f42556ce02d1'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 0.3944s ago] {'analysis_id_1': 'cb6d7704-ad9a-4b57-8be6-f42556ce02d1'}
********************************
web
August 07, 2025 at 01:16
INFO: **********:62938 - "GET /api/v1/values/bill/1df829e1-0207-43c3-a25f-3051630d6ae3 HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,322 INFO sqlalchemy.engine.Engine SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,322 INFO sqlalchemy.engine.Engine [cached since 1540s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1540s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,322 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,320 INFO sqlalchemy.engine.Engine [cached since 1562s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1562s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,318 INFO sqlalchemy.engine.Engine SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO: **********:62944 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3/status-history HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO: **********:62936 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3/summary-versions?include_content=false HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,316 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,314 INFO sqlalchemy.engine.Engine [cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,314 INFO sqlalchemy.engine.Engine SELECT bill_summary_versions.bill_id AS bill_summary_versions_bill_id, bill_summary_versions.version_number AS bill_summary_versions_version_number, bill_summary_versions.change_reason AS bill_summary_versions_change_reason, bill_summary_versions.summary_what_does AS bill_summary_versions_summary_what_does, bill_summary_versions.summary_who_affects AS bill_summary_versions_summary_who_affects, bill_summary_versions.summary_why_matters AS bill_summary_versions_summary_why_matters, bill_summary_versions.summary_key_provisions AS bill_summary_versions_summary_key_provisions, bill_summary_versions.summary_timeline AS bill_summary_versions_summary_timeline, bill_summary_versions.summary_cost_impact AS bill_summary_versions_summary_cost_impact, bill_summary_versions.ai_summary AS bill_summary_versions_ai_summary, bill_summary_versions.simple_summary AS bill_summary_versions_simple_summary, bill_summary_versions.tldr AS bill_summary_versions_tldr, bill_summary_versions.ai_processed_at AS bill_summary_versions_ai_processed_at, bill_summary_versions.is_current AS bill_summary_versions_is_current, bill_summary_versions.changes_detected AS bill_summary_versions_changes_detected, bill_summary_versions.id AS bill_summary_versions_id, bill_summary_versions.created_at AS bill_summary_versions_created_at, bill_summary_versions.updated_at AS bill_summary_versions_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_summary_versions
********************************
web
August 07, 2025 at 01:16
WHERE bill_summary_versions.bill_id = %(bill_id_1)s ORDER BY bill_summary_versions.version_number DESC
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_summary_versions.bill_id AS bill_summary_versions_bill_id, bill_summary_versions.version_number AS bill_summary_versions_version_number, bill_summary_versions.change_reason AS bill_summary_versions_change_reason, bill_summary_versions.summary_what_does AS bill_summary_versions_summary_what_does, bill_summary_versions.summary_who_affects AS bill_summary_versions_summary_who_affects, bill_summary_versions.summary_why_matters AS bill_summary_versions_summary_why_matters, bill_summary_versions.summary_key_provisions AS bill_summary_versions_summary_key_provisions, bill_summary_versions.summary_timeline AS bill_summary_versions_summary_timeline, bill_summary_versions.summary_cost_impact AS bill_summary_versions_summary_cost_impact, bill_summary_versions.ai_summary AS bill_summary_versions_ai_summary, bill_summary_versions.simple_summary AS bill_summary_versions_simple_summary, bill_summary_versions.tldr AS bill_summary_versions_tldr, bill_summary_versions.ai_processed_at AS bill_summary_versions_ai_processed_at, bill_summary_versions.is_current AS bill_summary_versions_is_current, bill_summary_versions.changes_detected AS bill_summary_versions_changes_detected, bill_summary_versions.id AS bill_summary_versions_id, bill_summary_versions.created_at AS bill_summary_versions_created_at, bill_summary_versions.updated_at AS bill_summary_versions_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_summary_versions
********************************
web
August 07, 2025 at 01:16
WHERE bill_summary_versions.bill_id = %(bill_id_1)s ORDER BY bill_summary_versions.version_number DESC
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,314 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,314 INFO sqlalchemy.engine.Engine [cached since 1562s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1562s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,314 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,312 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,312 INFO sqlalchemy.engine.Engine [cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,312 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,311 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,310 INFO sqlalchemy.engine.Engine SELECT bill_status_pipeline.bill_id AS bill_status_pipeline_bill_id, bill_status_pipeline.previous_status AS bill_status_pipeline_previous_status, bill_status_pipeline.current_status AS bill_status_pipeline_current_status, bill_status_pipeline.status_changed_at AS bill_status_pipeline_status_changed_at, bill_status_pipeline.detected_at AS bill_status_pipeline_detected_at, bill_status_pipeline.external_data AS bill_status_pipeline_external_data, bill_status_pipeline.vote_details AS bill_status_pipeline_vote_details, bill_status_pipeline.notification_sent AS bill_status_pipeline_notification_sent, bill_status_pipeline.is_significant_change AS bill_status_pipeline_is_significant_change, bill_status_pipeline.notes AS bill_status_pipeline_notes, bill_status_pipeline.id AS bill_status_pipeline_id, bill_status_pipeline.created_at AS bill_status_pipeline_created_at, bill_status_pipeline.updated_at AS bill_status_pipeline_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_status_pipeline
********************************
web
August 07, 2025 at 01:16
WHERE %(param_1)s = bill_status_pipeline.bill_id ORDER BY bill_status_pipeline.detected_at DESC
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_status_pipeline.bill_id AS bill_status_pipeline_bill_id, bill_status_pipeline.previous_status AS bill_status_pipeline_previous_status, bill_status_pipeline.current_status AS bill_status_pipeline_current_status, bill_status_pipeline.status_changed_at AS bill_status_pipeline_status_changed_at, bill_status_pipeline.detected_at AS bill_status_pipeline_detected_at, bill_status_pipeline.external_data AS bill_status_pipeline_external_data, bill_status_pipeline.vote_details AS bill_status_pipeline_vote_details, bill_status_pipeline.notification_sent AS bill_status_pipeline_notification_sent, bill_status_pipeline.is_significant_change AS bill_status_pipeline_is_significant_change, bill_status_pipeline.notes AS bill_status_pipeline_notes, bill_status_pipeline.id AS bill_status_pipeline_id, bill_status_pipeline.created_at AS bill_status_pipeline_created_at, bill_status_pipeline.updated_at AS bill_status_pipeline_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_status_pipeline
********************************
web
August 07, 2025 at 01:16
WHERE %(param_1)s = bill_status_pipeline.bill_id ORDER BY bill_status_pipeline.detected_at DESC
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,310 INFO sqlalchemy.engine.Engine [cached since 1553s ago] {'param_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1553s ago] {'param_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,309 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,309 INFO sqlalchemy.engine.Engine [cached since 1562s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1562s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,307 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,308 INFO sqlalchemy.engine.Engine [cached since 1562s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1562s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,308 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,307 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO: **********:62928 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3 HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
INFO: **********:9472 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3/action-data HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,302 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,301 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,298 INFO sqlalchemy.engine.Engine [cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,298 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,299 INFO sqlalchemy.engine.Engine [cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,298 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,298 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:07,298 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO: **********:41034 - "GET /api/v1/values/bill/1df829e1-0207-43c3-a25f-3051630d6ae3/tags HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,937 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,930 INFO sqlalchemy.engine.Engine SELECT bill_values_tags.bill_id AS bill_values_tags_bill_id, bill_values_tags.analysis_id AS bill_values_tags_analysis_id, bill_values_tags.tag_category AS bill_values_tags_tag_category, bill_values_tags.tag_type AS bill_values_tags_tag_type, bill_values_tags.tag_name AS bill_values_tags_tag_name, bill_values_tags.display_text AS bill_values_tags_display_text, bill_values_tags.description AS bill_values_tags_description, bill_values_tags.severity_level AS bill_values_tags_severity_level, bill_values_tags.display_priority AS bill_values_tags_display_priority, bill_values_tags.color_theme AS bill_values_tags_color_theme, bill_values_tags.icon_name AS bill_values_tags_icon_name, bill_values_tags.is_active AS bill_values_tags_is_active, bill_values_tags.created_at AS bill_values_tags_created_at, bill_values_tags.updated_at AS bill_values_tags_updated_at, bill_values_tags.id AS bill_values_tags_id
********************************
web
August 07, 2025 at 01:16
FROM bill_values_tags
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_tags.analysis_id = %(analysis_id_1)s AND bill_values_tags.is_active = true
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_values_tags.bill_id AS bill_values_tags_bill_id, bill_values_tags.analysis_id AS bill_values_tags_analysis_id, bill_values_tags.tag_category AS bill_values_tags_tag_category, bill_values_tags.tag_type AS bill_values_tags_tag_type, bill_values_tags.tag_name AS bill_values_tags_tag_name, bill_values_tags.display_text AS bill_values_tags_display_text, bill_values_tags.description AS bill_values_tags_description, bill_values_tags.severity_level AS bill_values_tags_severity_level, bill_values_tags.display_priority AS bill_values_tags_display_priority, bill_values_tags.color_theme AS bill_values_tags_color_theme, bill_values_tags.icon_name AS bill_values_tags_icon_name, bill_values_tags.is_active AS bill_values_tags_is_active, bill_values_tags.created_at AS bill_values_tags_created_at, bill_values_tags.updated_at AS bill_values_tags_updated_at, bill_values_tags.id AS bill_values_tags_id
********************************
web
August 07, 2025 at 01:16
FROM bill_values_tags
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_tags.analysis_id = %(analysis_id_1)s AND bill_values_tags.is_active = true
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,930 INFO sqlalchemy.engine.Engine [generated in 0.00031s] {'analysis_id_1': 'cb6d7704-ad9a-4b57-8be6-f42556ce02d1'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[generated in 0.00031s] {'analysis_id_1': 'cb6d7704-ad9a-4b57-8be6-f42556ce02d1'}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,925 INFO sqlalchemy.engine.Engine SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,925 INFO sqlalchemy.engine.Engine [cached since 1539s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1539s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,918 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,918 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,918 INFO sqlalchemy.engine.Engine [cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO: **********:62936 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3/summary-versions?include_content=false HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO: **********:62928 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3 HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
INFO: **********:62938 - "GET /api/v1/values/bill/1df829e1-0207-43c3-a25f-3051630d6ae3 HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,908 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,908 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,907 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,905 INFO sqlalchemy.engine.Engine SELECT bill_summary_versions.bill_id AS bill_summary_versions_bill_id, bill_summary_versions.version_number AS bill_summary_versions_version_number, bill_summary_versions.change_reason AS bill_summary_versions_change_reason, bill_summary_versions.summary_what_does AS bill_summary_versions_summary_what_does, bill_summary_versions.summary_who_affects AS bill_summary_versions_summary_who_affects, bill_summary_versions.summary_why_matters AS bill_summary_versions_summary_why_matters, bill_summary_versions.summary_key_provisions AS bill_summary_versions_summary_key_provisions, bill_summary_versions.summary_timeline AS bill_summary_versions_summary_timeline, bill_summary_versions.summary_cost_impact AS bill_summary_versions_summary_cost_impact, bill_summary_versions.ai_summary AS bill_summary_versions_ai_summary, bill_summary_versions.simple_summary AS bill_summary_versions_simple_summary, bill_summary_versions.tldr AS bill_summary_versions_tldr, bill_summary_versions.ai_processed_at AS bill_summary_versions_ai_processed_at, bill_summary_versions.is_current AS bill_summary_versions_is_current, bill_summary_versions.changes_detected AS bill_summary_versions_changes_detected, bill_summary_versions.id AS bill_summary_versions_id, bill_summary_versions.created_at AS bill_summary_versions_created_at, bill_summary_versions.updated_at AS bill_summary_versions_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_summary_versions
********************************
web
August 07, 2025 at 01:16
WHERE bill_summary_versions.bill_id = %(bill_id_1)s ORDER BY bill_summary_versions.version_number DESC
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_summary_versions.bill_id AS bill_summary_versions_bill_id, bill_summary_versions.version_number AS bill_summary_versions_version_number, bill_summary_versions.change_reason AS bill_summary_versions_change_reason, bill_summary_versions.summary_what_does AS bill_summary_versions_summary_what_does, bill_summary_versions.summary_who_affects AS bill_summary_versions_summary_who_affects, bill_summary_versions.summary_why_matters AS bill_summary_versions_summary_why_matters, bill_summary_versions.summary_key_provisions AS bill_summary_versions_summary_key_provisions, bill_summary_versions.summary_timeline AS bill_summary_versions_summary_timeline, bill_summary_versions.summary_cost_impact AS bill_summary_versions_summary_cost_impact, bill_summary_versions.ai_summary AS bill_summary_versions_ai_summary, bill_summary_versions.simple_summary AS bill_summary_versions_simple_summary, bill_summary_versions.tldr AS bill_summary_versions_tldr, bill_summary_versions.ai_processed_at AS bill_summary_versions_ai_processed_at, bill_summary_versions.is_current AS bill_summary_versions_is_current, bill_summary_versions.changes_detected AS bill_summary_versions_changes_detected, bill_summary_versions.id AS bill_summary_versions_id, bill_summary_versions.created_at AS bill_summary_versions_created_at, bill_summary_versions.updated_at AS bill_summary_versions_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_summary_versions
********************************
web
August 07, 2025 at 01:16
WHERE bill_summary_versions.bill_id = %(bill_id_1)s ORDER BY bill_summary_versions.version_number DESC
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,905 INFO sqlalchemy.engine.Engine [cached since 1561s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1561s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,903 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,903 INFO sqlalchemy.engine.Engine [cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO: **********:62944 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3/status-history HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,902 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,901 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,898 INFO sqlalchemy.engine.Engine SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1561s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,899 INFO sqlalchemy.engine.Engine [cached since 1561s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,895 INFO sqlalchemy.engine.Engine SELECT bill_status_pipeline.bill_id AS bill_status_pipeline_bill_id, bill_status_pipeline.previous_status AS bill_status_pipeline_previous_status, bill_status_pipeline.current_status AS bill_status_pipeline_current_status, bill_status_pipeline.status_changed_at AS bill_status_pipeline_status_changed_at, bill_status_pipeline.detected_at AS bill_status_pipeline_detected_at, bill_status_pipeline.external_data AS bill_status_pipeline_external_data, bill_status_pipeline.vote_details AS bill_status_pipeline_vote_details, bill_status_pipeline.notification_sent AS bill_status_pipeline_notification_sent, bill_status_pipeline.is_significant_change AS bill_status_pipeline_is_significant_change, bill_status_pipeline.notes AS bill_status_pipeline_notes, bill_status_pipeline.id AS bill_status_pipeline_id, bill_status_pipeline.created_at AS bill_status_pipeline_created_at, bill_status_pipeline.updated_at AS bill_status_pipeline_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_status_pipeline
********************************
web
August 07, 2025 at 01:16
WHERE %(param_1)s = bill_status_pipeline.bill_id ORDER BY bill_status_pipeline.detected_at DESC
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_status_pipeline.bill_id AS bill_status_pipeline_bill_id, bill_status_pipeline.previous_status AS bill_status_pipeline_previous_status, bill_status_pipeline.current_status AS bill_status_pipeline_current_status, bill_status_pipeline.status_changed_at AS bill_status_pipeline_status_changed_at, bill_status_pipeline.detected_at AS bill_status_pipeline_detected_at, bill_status_pipeline.external_data AS bill_status_pipeline_external_data, bill_status_pipeline.vote_details AS bill_status_pipeline_vote_details, bill_status_pipeline.notification_sent AS bill_status_pipeline_notification_sent, bill_status_pipeline.is_significant_change AS bill_status_pipeline_is_significant_change, bill_status_pipeline.notes AS bill_status_pipeline_notes, bill_status_pipeline.id AS bill_status_pipeline_id, bill_status_pipeline.created_at AS bill_status_pipeline_created_at, bill_status_pipeline.updated_at AS bill_status_pipeline_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_status_pipeline
********************************
web
August 07, 2025 at 01:16
WHERE %(param_1)s = bill_status_pipeline.bill_id ORDER BY bill_status_pipeline.detected_at DESC
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,895 INFO sqlalchemy.engine.Engine [cached since 1553s ago] {'param_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1553s ago] {'param_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
INFO: **********:9472 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3/action-data HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,893 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,893 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,893 INFO sqlalchemy.engine.Engine [cached since 1561s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1561s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,892 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,892 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,892 INFO sqlalchemy.engine.Engine [cached since 1561s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1561s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,889 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,886 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,886 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,887 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,887 INFO sqlalchemy.engine.Engine [cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,887 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,887 INFO sqlalchemy.engine.Engine [cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2087s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO: **********:62936 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3/status-history HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,229 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO: **********:62928 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3 HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,228 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,226 INFO sqlalchemy.engine.Engine SELECT bill_status_pipeline.bill_id AS bill_status_pipeline_bill_id, bill_status_pipeline.previous_status AS bill_status_pipeline_previous_status, bill_status_pipeline.current_status AS bill_status_pipeline_current_status, bill_status_pipeline.status_changed_at AS bill_status_pipeline_status_changed_at, bill_status_pipeline.detected_at AS bill_status_pipeline_detected_at, bill_status_pipeline.external_data AS bill_status_pipeline_external_data, bill_status_pipeline.vote_details AS bill_status_pipeline_vote_details, bill_status_pipeline.notification_sent AS bill_status_pipeline_notification_sent, bill_status_pipeline.is_significant_change AS bill_status_pipeline_is_significant_change, bill_status_pipeline.notes AS bill_status_pipeline_notes, bill_status_pipeline.id AS bill_status_pipeline_id, bill_status_pipeline.created_at AS bill_status_pipeline_created_at, bill_status_pipeline.updated_at AS bill_status_pipeline_updated_at
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_status_pipeline.bill_id AS bill_status_pipeline_bill_id, bill_status_pipeline.previous_status AS bill_status_pipeline_previous_status, bill_status_pipeline.current_status AS bill_status_pipeline_current_status, bill_status_pipeline.status_changed_at AS bill_status_pipeline_status_changed_at, bill_status_pipeline.detected_at AS bill_status_pipeline_detected_at, bill_status_pipeline.external_data AS bill_status_pipeline_external_data, bill_status_pipeline.vote_details AS bill_status_pipeline_vote_details, bill_status_pipeline.notification_sent AS bill_status_pipeline_notification_sent, bill_status_pipeline.is_significant_change AS bill_status_pipeline_is_significant_change, bill_status_pipeline.notes AS bill_status_pipeline_notes, bill_status_pipeline.id AS bill_status_pipeline_id, bill_status_pipeline.created_at AS bill_status_pipeline_created_at, bill_status_pipeline.updated_at AS bill_status_pipeline_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_status_pipeline
********************************
web
August 07, 2025 at 01:16
WHERE %(param_1)s = bill_status_pipeline.bill_id ORDER BY bill_status_pipeline.detected_at DESC
********************************
web
August 07, 2025 at 01:16
FROM bill_status_pipeline
********************************
web
August 07, 2025 at 01:16
WHERE %(param_1)s = bill_status_pipeline.bill_id ORDER BY bill_status_pipeline.detected_at DESC
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,226 INFO sqlalchemy.engine.Engine [cached since 2086s ago] {'param_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2086s ago] {'param_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO: **********:62944 - "GET /api/v1/values/bill/1df829e1-0207-43c3-a25f-3051630d6ae3 HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,223 INFO sqlalchemy.engine.Engine [cached since 2086s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,224 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2086s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,224 INFO sqlalchemy.engine.Engine [cached since 2086s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2086s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,223 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,223 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,223 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO: **********:9472 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3/summary-versions?include_content=false HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,222 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO: **********:62938 - "GET /api/v1/values/bill/1df829e1-0207-43c3-a25f-3051630d6ae3/tags HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,221 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,221 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,220 INFO sqlalchemy.engine.Engine SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,220 INFO sqlalchemy.engine.Engine [cached since 1560s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1560s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,216 INFO sqlalchemy.engine.Engine [cached since 2086s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2086s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,216 INFO sqlalchemy.engine.Engine SELECT bill_values_tags.bill_id AS bill_values_tags_bill_id, bill_values_tags.analysis_id AS bill_values_tags_analysis_id, bill_values_tags.tag_category AS bill_values_tags_tag_category, bill_values_tags.tag_type AS bill_values_tags_tag_type, bill_values_tags.tag_name AS bill_values_tags_tag_name, bill_values_tags.display_text AS bill_values_tags_display_text, bill_values_tags.description AS bill_values_tags_description, bill_values_tags.severity_level AS bill_values_tags_severity_level, bill_values_tags.display_priority AS bill_values_tags_display_priority, bill_values_tags.color_theme AS bill_values_tags_color_theme, bill_values_tags.icon_name AS bill_values_tags_icon_name, bill_values_tags.is_active AS bill_values_tags_is_active, bill_values_tags.created_at AS bill_values_tags_created_at, bill_values_tags.updated_at AS bill_values_tags_updated_at, bill_values_tags.id AS bill_values_tags_id
********************************
web
August 07, 2025 at 01:16
FROM bill_values_tags
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_tags.analysis_id = %(analysis_id_1)s AND bill_values_tags.is_active = true
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,216 INFO sqlalchemy.engine.Engine [cached since 5.561s ago] {'analysis_id_1': 'cb6d7704-ad9a-4b57-8be6-f42556ce02d1'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_values_tags.bill_id AS bill_values_tags_bill_id, bill_values_tags.analysis_id AS bill_values_tags_analysis_id, bill_values_tags.tag_category AS bill_values_tags_tag_category, bill_values_tags.tag_type AS bill_values_tags_tag_type, bill_values_tags.tag_name AS bill_values_tags_tag_name, bill_values_tags.display_text AS bill_values_tags_display_text, bill_values_tags.description AS bill_values_tags_description, bill_values_tags.severity_level AS bill_values_tags_severity_level, bill_values_tags.display_priority AS bill_values_tags_display_priority, bill_values_tags.color_theme AS bill_values_tags_color_theme, bill_values_tags.icon_name AS bill_values_tags_icon_name, bill_values_tags.is_active AS bill_values_tags_is_active, bill_values_tags.created_at AS bill_values_tags_created_at, bill_values_tags.updated_at AS bill_values_tags_updated_at, bill_values_tags.id AS bill_values_tags_id
********************************
web
August 07, 2025 at 01:16
FROM bill_values_tags
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_tags.analysis_id = %(analysis_id_1)s AND bill_values_tags.is_active = true
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 5.561s ago] {'analysis_id_1': 'cb6d7704-ad9a-4b57-8be6-f42556ce02d1'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,215 INFO sqlalchemy.engine.Engine SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,215 INFO sqlalchemy.engine.Engine [cached since 1560s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1560s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,215 INFO sqlalchemy.engine.Engine [cached since 1560s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1560s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,215 INFO sqlalchemy.engine.Engine SELECT bill_summary_versions.bill_id AS bill_summary_versions_bill_id, bill_summary_versions.version_number AS bill_summary_versions_version_number, bill_summary_versions.change_reason AS bill_summary_versions_change_reason, bill_summary_versions.summary_what_does AS bill_summary_versions_summary_what_does, bill_summary_versions.summary_who_affects AS bill_summary_versions_summary_who_affects, bill_summary_versions.summary_why_matters AS bill_summary_versions_summary_why_matters, bill_summary_versions.summary_key_provisions AS bill_summary_versions_summary_key_provisions, bill_summary_versions.summary_timeline AS bill_summary_versions_summary_timeline, bill_summary_versions.summary_cost_impact AS bill_summary_versions_summary_cost_impact, bill_summary_versions.ai_summary AS bill_summary_versions_ai_summary, bill_summary_versions.simple_summary AS bill_summary_versions_simple_summary, bill_summary_versions.tldr AS bill_summary_versions_tldr, bill_summary_versions.ai_processed_at AS bill_summary_versions_ai_processed_at, bill_summary_versions.is_current AS bill_summary_versions_is_current, bill_summary_versions.changes_detected AS bill_summary_versions_changes_detected, bill_summary_versions.id AS bill_summary_versions_id, bill_summary_versions.created_at AS bill_summary_versions_created_at, bill_summary_versions.updated_at AS bill_summary_versions_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_summary_versions
********************************
web
August 07, 2025 at 01:16
WHERE bill_summary_versions.bill_id = %(bill_id_1)s ORDER BY bill_summary_versions.version_number DESC
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_summary_versions.bill_id AS bill_summary_versions_bill_id, bill_summary_versions.version_number AS bill_summary_versions_version_number, bill_summary_versions.change_reason AS bill_summary_versions_change_reason, bill_summary_versions.summary_what_does AS bill_summary_versions_summary_what_does, bill_summary_versions.summary_who_affects AS bill_summary_versions_summary_who_affects, bill_summary_versions.summary_why_matters AS bill_summary_versions_summary_why_matters, bill_summary_versions.summary_key_provisions AS bill_summary_versions_summary_key_provisions, bill_summary_versions.summary_timeline AS bill_summary_versions_summary_timeline, bill_summary_versions.summary_cost_impact AS bill_summary_versions_summary_cost_impact, bill_summary_versions.ai_summary AS bill_summary_versions_ai_summary, bill_summary_versions.simple_summary AS bill_summary_versions_simple_summary, bill_summary_versions.tldr AS bill_summary_versions_tldr, bill_summary_versions.ai_processed_at AS bill_summary_versions_ai_processed_at, bill_summary_versions.is_current AS bill_summary_versions_is_current, bill_summary_versions.changes_detected AS bill_summary_versions_changes_detected, bill_summary_versions.id AS bill_summary_versions_id, bill_summary_versions.created_at AS bill_summary_versions_created_at, bill_summary_versions.updated_at AS bill_summary_versions_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_summary_versions
********************************
web
August 07, 2025 at 01:16
WHERE bill_summary_versions.bill_id = %(bill_id_1)s ORDER BY bill_summary_versions.version_number DESC
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,213 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,213 INFO sqlalchemy.engine.Engine [cached since 2086s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2086s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,212 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,212 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,210 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,211 INFO sqlalchemy.engine.Engine [cached since 1560s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1560s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,211 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,210 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO: **********:9472 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3/action-data HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,203 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,200 INFO sqlalchemy.engine.Engine BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,200 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:06,200 INFO sqlalchemy.engine.Engine [cached since 2086s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2086s ago] {'id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO: **********:62944 - "GET /api/v1/values/bill/1df829e1-0207-43c3-a25f-3051630d6ae3 HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:05,348 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO: **********:62936 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3/status-history HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:05,344 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_status_pipeline.bill_id AS bill_status_pipeline_bill_id, bill_status_pipeline.previous_status AS bill_status_pipeline_previous_status, bill_status_pipeline.current_status AS bill_status_pipeline_current_status, bill_status_pipeline.status_changed_at AS bill_status_pipeline_status_changed_at, bill_status_pipeline.detected_at AS bill_status_pipeline_detected_at, bill_status_pipeline.external_data AS bill_status_pipeline_external_data, bill_status_pipeline.vote_details AS bill_status_pipeline_vote_details, bill_status_pipeline.notification_sent AS bill_status_pipeline_notification_sent, bill_status_pipeline.is_significant_change AS bill_status_pipeline_is_significant_change, bill_status_pipeline.notes AS bill_status_pipeline_notes, bill_status_pipeline.id AS bill_status_pipeline_id, bill_status_pipeline.created_at AS bill_status_pipeline_created_at, bill_status_pipeline.updated_at AS bill_status_pipeline_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_status_pipeline
********************************
web
August 07, 2025 at 01:16
WHERE %(param_1)s = bill_status_pipeline.bill_id ORDER BY bill_status_pipeline.detected_at DESC
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:05,341 INFO sqlalchemy.engine.Engine SELECT bill_status_pipeline.bill_id AS bill_status_pipeline_bill_id, bill_status_pipeline.previous_status AS bill_status_pipeline_previous_status, bill_status_pipeline.current_status AS bill_status_pipeline_current_status, bill_status_pipeline.status_changed_at AS bill_status_pipeline_status_changed_at, bill_status_pipeline.detected_at AS bill_status_pipeline_detected_at, bill_status_pipeline.external_data AS bill_status_pipeline_external_data, bill_status_pipeline.vote_details AS bill_status_pipeline_vote_details, bill_status_pipeline.notification_sent AS bill_status_pipeline_notification_sent, bill_status_pipeline.is_significant_change AS bill_status_pipeline_is_significant_change, bill_status_pipeline.notes AS bill_status_pipeline_notes, bill_status_pipeline.id AS bill_status_pipeline_id, bill_status_pipeline.created_at AS bill_status_pipeline_created_at, bill_status_pipeline.updated_at AS bill_status_pipeline_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_status_pipeline
********************************
web
August 07, 2025 at 01:16
WHERE %(param_1)s = bill_status_pipeline.bill_id ORDER BY bill_status_pipeline.detected_at DESC
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 2085s ago] {'param_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:05,342 INFO sqlalchemy.engine.Engine [cached since 2085s ago] {'param_1': '1df829e1-0207-43c3-a25f-3051630d6ae3'}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:05,338 INFO sqlalchemy.engine.Engine SELECT bill_values_analysis.bill_id AS bill_values_analysis_bill_id, bill_values_analysis.democracy_threat_score AS bill_values_analysis_democracy_threat_score, bill_values_analysis.democracy_support_score AS bill_values_analysis_democracy_support_score, bill_values_analysis.human_rights_threat_score AS bill_values_analysis_human_rights_threat_score, bill_values_analysis.human_rights_support_score AS bill_values_analysis_human_rights_support_score, bill_values_analysis.environmental_threat_score AS bill_values_analysis_environmental_threat_score, bill_values_analysis.environmental_support_score AS bill_values_analysis_environmental_support_score, bill_values_analysis.overall_threat_level AS bill_values_analysis_overall_threat_level, bill_values_analysis.overall_support_level AS bill_values_analysis_overall_support_level, bill_values_analysis.analysis_reasoning AS bill_values_analysis_analysis_reasoning, bill_values_analysis.confidence_score AS bill_values_analysis_confidence_score, bill_values_analysis.requires_human_review AS bill_values_analysis_requires_human_review, bill_values_analysis.is_flagged AS bill_values_analysis_is_flagged, bill_values_analysis.is_blocked AS bill_values_analysis_is_blocked, bill_values_analysis.reviewed_by AS bill_values_analysis_reviewed_by, bill_values_analysis.reviewed_at AS bill_values_analysis_reviewed_at, bill_values_analysis.review_notes AS bill_values_analysis_review_notes, bill_values_analysis.analyzed_at AS bill_values_analysis_analyzed_at, bill_values_analysis.ai_model_version AS bill_values_analysis_ai_model_version, bill_values_analysis.id AS bill_values_analysis_id, bill_values_analysis.created_at AS bill_values_analysis_created_at, bill_values_analysis.updated_at AS bill_values_analysis_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bill_values_analysis
********************************
web
August 07, 2025 at 01:16
WHERE bill_values_analysis.bill_id = %(bill_id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:05,338 INFO sqlalchemy.engine.Engine [cached since 1560s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:[cached since 1560s ago] {'bill_id_1': '1df829e1-0207-43c3-a25f-3051630d6ae3', 'param_1': 1}
********************************
web
August 07, 2025 at 01:16
INFO: **********:62928 - "GET /api/v1/bills/1df829e1-0207-43c3-a25f-3051630d6ae3 HTTP/1.1" 200 OK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:05,313 INFO sqlalchemy.engine.Engine ROLLBACK
********************************
web
August 07, 2025 at 01:16
2025-08-07 05:16:05,313 INFO sqlalchemy.engine.Engine SELECT bills.title AS bills_title, bills.description AS bills_description, bills.bill_number AS bills_bill_number, bills.bill_type AS bills_bill_type, bills.status AS bills_status, bills.session_year AS bills_session_year, bills.chamber AS bills_chamber, bills.state AS bills_state, bills.full_text AS bills_full_text, bills.summary AS bills_summary, bills.ai_summary AS bills_ai_summary, bills.simple_summary AS bills_simple_summary, bills.tldr AS bills_tldr, bills.summary_what_does AS bills_summary_what_does, bills.summary_who_affects AS bills_summary_who_affects, bills.summary_why_matters AS bills_summary_why_matters, bills.summary_key_provisions AS bills_summary_key_provisions, bills.summary_timeline AS bills_summary_timeline, bills.summary_cost_impact AS bills_summary_cost_impact, bills.reasons_for_support AS bills_reasons_for_support, bills.reasons_for_opposition AS bills_reasons_for_opposition, bills.support_reasons AS bills_support_reasons, bills.oppose_reasons AS bills_oppose_reasons, bills.amend_reasons AS bills_amend_reasons, bills.message_templates AS bills_message_templates, bills.ai_tags AS bills_ai_tags, bills.ai_processed_at AS bills_ai_processed_at, bills.environmental_threat_analysis AS bills_environmental_threat_analysis, bills.social_rights_threat_analysis AS bills_social_rights_threat_analysis, bills.environmental_justice_threat_analysis AS bills_environmental_justice_threat_analysis, bills.openstates_id AS bills_openstates_id, bills.congress_gov_id AS bills_congress_gov_id, bills.source_url AS bills_source_url, bills.text_url AS bills_text_url, bills.official_bill_url AS bills_official_bill_url, bills.introduced_date AS bills_introduced_date, bills.last_action_date AS bills_last_action_date, bills.sponsor_name AS bills_sponsor_name, bills.sponsor_party AS bills_sponsor_party, bills.sponsor_state AS bills_sponsor_state, bills.cosponsors AS bills_cosponsors, bills.vote_history AS bills_vote_history, bills.is_featured AS bills_is_featured, bills.priority_score AS bills_priority_score, bills.tags AS bills_tags, bills.categories AS bills_categories, bills.bill_metadata AS bills_bill_metadata, bills.id AS bills_id, bills.created_at AS bills_created_at, bills.updated_at AS bills_updated_at
********************************
web
August 07, 2025 at 01:16
FROM bills
********************************
web
August 07, 2025 at 01:16
WHERE bills.id = %(id_1)s
********************************
web
August 07, 2025 at 01:16
LIMIT %(param_1)s
********************************
web
August 07, 2025 at 01:16
INFO:sqlalchemy.engine.Engine:ROLLBACK
********************************
web
