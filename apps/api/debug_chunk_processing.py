#!/usr/bin/env python3

"""
Debug script to investigate why chunk processing stops at 1/9 chunks
"""

import asyncio
import sys
import os
sys.path.append('.')

from app.db.database import get_db
from app.models.bill import Bill
from app.services.ai_service import AIService
from app.services.balanced_analysis_service import BalancedAnalysisService
from app.services.intelligent_evidence_extractor import get_intelligent_extractor
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def debug_chunk_processing():
    """Debug the chunk processing issue"""
    print("🔍 DEBUGGING CHUNK PROCESSING ISSUE")
    print("=" * 60)
    
    # Get HR4922 bill
    db = next(get_db())
    bill = db.query(Bill).filter(Bill.bill_number == 'HR4922').first()
    
    if not bill:
        print("❌ HR4922 bill not found")
        return
    
    print(f"📋 Bill: {bill.title}")
    print(f"📄 Full text length: {len(bill.full_text)} characters")
    
    # Set up services
    ai_service = AIService()
    balanced_service = BalancedAnalysisService(ai_service)
    evidence_extractor = get_intelligent_extractor()
    
    # Extract evidence to see chunk count
    bill_metadata = {
        'title': bill.title,
        'bill_number': bill.bill_number,
        'session_year': bill.session_year,
        'bill_id': str(bill.id)
    }
    
    print("\n🔬 STEP 1: Evidence Extraction")
    evidence_result = await evidence_extractor.extract_intelligent_evidence(bill.full_text, bill_metadata)
    print(f"✅ Evidence spans extracted: {len(evidence_result)}")
    
    # Convert to expected format
    evidence_spans = []
    for i, ev in enumerate(evidence_result):
        evidence_spans.append({
            'id': f'span_{i}_{hash(ev.content) % 100000}',
            'quote': ev.content,
            'heading': ev.heading,
            'start_offset': ev.start_offset,
            'end_offset': ev.end_offset,
            'importance_score': ev.confidence_score,
            'quality_metrics': {
                'quality_level': 'high' if ev.confidence_score > 0.7 else 'medium',
                'grounding_value': ev.confidence_score
            }
        })
    
    print(f"📊 Evidence spans formatted: {len(evidence_spans)}")
    print(f"📊 Expected chunks to process: {len(evidence_spans)}")
    
    # Create a mock version that tracks chunk processing
    class DebuggingBalancedAnalysisService(BalancedAnalysisService):
        def __init__(self, ai_service):
            super().__init__(ai_service)
            self.chunks_processed = 0
            self.chunk_results = []
        
        async def _analyze_single_chunk(self, evidence_span, bill_text, bill_metadata, bill_id, chunk_num):
            """Override to track chunk processing"""
            print(f"🔍 DEBUG: Processing chunk {chunk_num}/{len(evidence_spans)}")
            print(f"🔍 DEBUG: Chunk heading: {evidence_span.get('heading', 'Unknown')[:100]}")
            
            try:
                result = await super()._analyze_single_chunk(evidence_span, bill_text, bill_metadata, bill_id, chunk_num)
                self.chunks_processed += 1
                self.chunk_results.append({
                    'chunk_num': chunk_num,
                    'success': result.get('success', False),
                    'error': result.get('error'),
                    'title': result.get('section', {}).get('title', 'No title') if result.get('success') else None
                })
                print(f"✅ DEBUG: Chunk {chunk_num} completed successfully: {result.get('success')}")
                if not result.get('success'):
                    print(f"❌ DEBUG: Chunk {chunk_num} error: {result.get('error')}")
                return result
            except Exception as e:
                print(f"💥 DEBUG: Chunk {chunk_num} exception: {e}")
                self.chunk_results.append({
                    'chunk_num': chunk_num,
                    'success': False,
                    'error': str(e),
                    'exception': True
                })
                raise
    
    print("\n🔬 STEP 2: Debug Chunk Processing")
    debug_service = DebuggingBalancedAnalysisService(ai_service)
    
    try:
        result = await debug_service.analyze_bill_balanced(bill.full_text, bill_metadata, evidence_spans)
        
        print(f"\n📊 CHUNK PROCESSING RESULTS:")
        print(f"   Expected chunks: {len(evidence_spans)}")
        print(f"   Actually processed: {debug_service.chunks_processed}")
        print(f"   Success rate: {debug_service.chunks_processed}/{len(evidence_spans)} ({(debug_service.chunks_processed/len(evidence_spans)*100):.1f}%)")
        
        print(f"\n📋 CHUNK DETAILS:")
        for chunk_result in debug_service.chunk_results:
            status = "✅" if chunk_result['success'] else "❌"
            print(f"   {status} Chunk {chunk_result['chunk_num']}: {chunk_result.get('title', 'FAILED')}")
            if not chunk_result['success']:
                print(f"      Error: {chunk_result.get('error', 'Unknown error')}")
        
        print(f"\n🎯 OVERALL RESULT:")
        print(f"   Processing success: {result.get('success')}")
        if result.get('success'):
            sections = result.get('analysis', {}).get('complete_analysis', [])
            print(f"   Sections generated: {len(sections)}")
        else:
            print(f"   Processing error: {result.get('error')}")
            
    except Exception as e:
        print(f"\n💥 PROCESSING EXCEPTION: {e}")
        print(f"   Chunks processed before exception: {debug_service.chunks_processed}")
        
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(debug_chunk_processing())