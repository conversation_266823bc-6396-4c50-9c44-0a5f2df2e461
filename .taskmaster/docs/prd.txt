ModernAction.io - Product Requirements Document (PRD)
1. Overview
Product: ModernAction.io
Status: In Development - "Make It Real" Phase
ModernAction.io is a civic engagement platform designed to bridge the gap between passive online awareness and tangible political action. The core problem it solves is citizen disempowerment; millions of people feel strongly about legislative issues but are paralyzed by a complex political process and a lack of clear, low-friction channels to make their voice heard.
Our platform is for any citizen in the United States who wants to effectively influence legislation. It is valuable because it transforms the chaotic and opaque world of politics into a simple, transparent, and actionable experience. We achieve this by combining real-time legislative data, powerful AI analysis, and a seamless, one-click interface to connect users directly with their elected officials. We are the "source of truth" and the "action button" for modern democracy.
2. Core Features
The "Source of Truth" Bill Intelligence Engine:
What it does: Automatically ingests real, live federal legislation from official sources (e.g., Congress.gov). It uses a sophisticated, multi-pass AI pipeline to analyze every bill and generate a rich, easy-to-understand "Bill Detail Page."
Why it's important: This is our primary trust-building and educational feature. It cuts through political spin and jargon, providing users with a neutral, factual, and comprehensive understanding of what a bill actually does. It is also a massive driver for SEO and organic user acquisition.
How it works: A backend service fetches bill metadata and full text. A "Chain-of-Thought" AI pipeline then chunks the bill, summarizes each section, and generates a hero summary, pro/con arguments, and relevant tags. Every claim is programmatically validated with exact citations from the bill text.
The "Action Engine":
What it does: Provides a seamless, end-to-end user journey for contacting elected officials.
Why it's important: This is the core value proposition of the entire platform. It is the feature that converts a user's informed opinion into a tangible, logged action.
How it works: A user enters their zip code. Our OfficialService uses a real-time API (e.g., OpenStates) to dynamically find their specific representatives. The user then uses our ActionModal, which leverages an AI personalization service and the Action Network API, to send a message to that official's preferred contact method (email or web form).
The Legislator Accountability System:
What it does: Creates a public, data-rich profile page for every federal legislator.
Why it's important: This closes the engagement loop. It allows users to track their representative's performance over time, creating a powerful incentive for long-term engagement with our platform.
How it works: A background data pipeline will ingest the complete voting history for each legislator. The frontend will display this voting record alongside a personalized "Alignment Score," which compares the user's past actions on bills with the official's actual votes.
3. User Experience
User Personas:
Primary: "The Concerned but Overwhelmed Citizen." (Ages 18-45). Digitally savvy, follows the news, feels strongly about specific issues (e.g., climate, healthcare) but lacks the time or knowledge to navigate the complex political system. They are frustrated and want an easy, trustworthy way to make an impact.
Secondary: "The Engaged Activist." A more experienced user who is already involved in advocacy. They are looking for better tools to amplify their message and organize their community.
Key User Flows:
Discovery & Education: A user discovers a bill via social media or Google search and lands on our SEO-optimized "Bill Detail Page." They read the AI-generated analysis and understand its impact.
Taking Action: From the Bill Detail Page, the user clicks "Take Action." They log in via Auth0, enter their zip code to find their reps, use the AI to personalize their message, and submit the action.
Accountability & Re-engagement: A month later, the user returns to the platform. They visit their representative's profile page and see their "Alignment Score" has changed based on recent votes, prompting them to take action on a new bill.
UI/UX Considerations:
Clarity and Simplicity: The UI must be ruthlessly simple, guiding the user through a clear, step-by-step process.
Trust and Transparency: Every AI-generated claim must be accompanied by a visible citation. Moderation policies must be clear.
Mobile-First: The entire experience must be flawless on a mobile device.
<PRD>
4. Technical Architecture
System Components:
Frontend: Next.js 14 App Router, TypeScript, React 19, Tailwind CSS. Hosted on AWS ECS Fargate.
Backend: Python FastAPI, SQLAlchemy. Hosted on AWS ECS Fargate.
Database: PostgreSQL on AWS RDS, located in a private VPC subnet.
AI Engine: OpenAI GPT-4/GPT-4o for analysis, Hugging Face for summarization.
Infrastructure: Defined in AWS CDK. Includes a custom VPC, Application Load Balancer with path-based routing, ECR for container storage, and Lambda/SQS for background jobs.
Authentication: Auth0, using the secure @auth0/nextjs-auth0 SDK with the BFF pattern.
Data Models:
bills: Core metadata for legislation.
bill_details: A rich, 1:1 extension of bills containing all AI-generated content, citations, and SEO fields.
officials: A cached, rich record of legislator data, including a JSONB field for all social media profiles.
user_actions: The definitive log of every action a user takes, including their stance and reasoning.
APIs and Integrations:
Congress.gov API: Primary source for all federal bill data.
OpenStates API: Primary source for all legislator data.
Action Network API: The secure, backend-integrated "delivery mechanism" for submitting user messages to official web forms.
OpenAI API: Powers the high-level analysis (pro/con reasons, etc.).
Twitter API: Used for the "Tweet from your own account" feature via OAuth 2.0.
5. Development Roadmap
This roadmap is defined by Sprints, which are logical, feature-complete units of work.
Phase 1: The "Make It Real" MVP
Sprint A: The Data Engine (Backend)
Scope: Build the complete, real data pipeline.
Deliverables: A functioning BillDataService that uses the Congress.gov API to ingest a real bill, scrapes its full text, uses the AI pipeline to generate a fully cited bill_details record, and saves it to the database. A polished seed.py administrative tool.
Sprint B: The Action Engine (Backend)
Scope: Build the complete, real action loop.
Deliverables: A functioning OfficialService that uses the OpenStates API to dynamically fetch real representatives by zip code. A secure ActionNetworkService that can successfully submit a message via their API. A simple user_actions table to log the submission.
Sprint C: The Connection Engine (Full Stack)
Scope: Implement the final user-facing features and integrations.
Deliverables: A functional, secure Auth0 login/logout flow. Implementation of the Twitter OAuth 2.0 flow for tweeting from a user's account. A polished /bills/[slug] page that displays the rich AI data. A fully integrated ActionModal that connects the user's input to the backend services.
Phase 2: The Accountability Engine (Post-Launch)
Sprint D: The Voting Record
Scope: Build the data pipeline for legislator voting history.
Deliverables: A new votes table in the database. A nightly Lambda function that ingests all roll call votes from the Congress.gov API and links them to the correct bills and officials.
Sprint E: The Alignment Score
Scope: Build the user-facing accountability features.
Deliverables: A new AlignmentService that calculates the personalized score. A new /officials/[id] profile page that displays the legislator's voting record and the user's personal Alignment Score.
6. Logical Dependency Chain
Foundation (COMPLETE): The core infrastructure (AWS), API/frontend scaffolding, and basic database schema are in place.
Authentication (Sprint C): We must implement the Auth0 login flow first. Without users, no other actions can be tracked.
Data Engine (Sprint A): We need to be able to ingest and analyze real bills before we can build the UI to display them or the action flow to act on them.
Action Engine (Sprint B): We need the Officials Lookup and Action Network integration to be functional before we can build the final Action Modal UI that uses them.
User Experience (Sprint C): With the data and action engines in place, we can build the final UI that connects them all together for the user.
Accountability Engine (Sprints D & E): This entire feature set logically depends on the MVP being complete and launched, as it relies on users having taken actions that can be compared against voting records.
7. Risks and Mitigations
Technical Challenges:
Risk: AI quality is inconsistent or produces biased/hallucinated results.
Mitigation: Our "citation-first" architecture is the primary mitigation. By forcing the AI to provide exact quotes and programmatically validating them, we dramatically reduce the risk of un-grounded claims. A human-in-the-loop review process is our final quality gate.
Risk: External APIs (Congress.gov, OpenStates) change or become unreliable.
Mitigation: Our service-oriented architecture isolates these dependencies. If an API changes, we only need to update a single service, not the entire application. Our database acts as a cache, providing resilience against temporary API outages.
Figuring out the MVP:
Risk: Scope creep could delay launch indefinitely (as demonstrated by the "Analytics" tangent).
Mitigation: This PRD now defines a ruthless, non-negotiable MVP scope. The three sprints (A, B, C) are the only things that matter for launch. All other features, including vote tracking, are officially designated as Phase 2.
Resource Constraints:
Risk: The engineering work is significant for a small team.
Mitigation: Our strategic decision to "buy" key components (authentication via Auth0, message delivery via Action Network) instead of building them from scratch saves us months of development time and allows our team to focus on our unique value proposition: the AI Intelligence Layer.
8. Appendix
Research Findings:
The ProPublica Congress API is defunct.
The Google Civic Information API's "representatives" endpoint is defunct.
Congress.gov is the authoritative source for bill data.
OpenStates is the best-in-class source for rich, multi-platform legislator data.
Action Network provides a robust, cost-effective API for submitting messages to official web forms, solving the "535 forms" problem.
Technical Specifications:
The bill_details data model, as previously specified, is the approved schema.
The "Chain-of-Thought" AI pipeline is the approved architecture for the Intelligence Engine.
The "Authoritative Local Build & Deploy" process is the approved method for manual deployments until the CI/CD pipeline is fully stabilized.