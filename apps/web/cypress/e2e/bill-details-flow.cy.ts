/**
 * E2E Tests for Bill Details Flow
 * 
 * Tests the complete user journey:
 * 1. Homepage -> Bill card
 * 2. Bill card -> Action modal
 * 3. Action modal "Learn more" -> Bill details page
 * 4. Bill details page anchor navigation
 * 5. Moderation banner display
 * 6. Citation display and source navigation
 */

describe('Bill Details Flow', () => {
  beforeEach(() => {
    // Mock API responses for consistent testing
    cy.intercept('GET', '/api/v1/bills?*', { fixture: 'bills-list.json' }).as('getBills')
    cy.intercept('GET', '/api/v1/bills/*/action-data', { fixture: 'bill-action-data.json' }).as('getBillActionData')
    cy.intercept('GET', '/api/v1/bills/details/by-slug/*', { fixture: 'bill-details.json' }).as('getBillDetails')
  })

  it('completes the full bill details flow from homepage to details page', () => {
    // Step 1: Start at homepage and find a bill
    cy.visit('/')
    cy.wait('@getBills')
    
    // Click on the first bill card to open action modal
    cy.get('[data-testid="bill-card"]').first().click()
    cy.wait('@getBillActionData')
    
    // Step 2: Verify action modal opened
    cy.get('[data-testid="bill-action-modal"]').should('be.visible')
    cy.get('[data-testid="modal-title"]').should('contain', 'Take Action on')
    
    // Step 3: Navigate through bill details sections in the modal
    cy.get('[data-testid="bill-details-section"]').should('be.visible')
    
    // Find and click a "Learn more" link
    cy.get('[data-testid="learn-more-link"]').first().then(($link) => {
      const href = $link.prop('href')
      const url = new URL(href)
      const expectedSlug = url.pathname.split('/bills/')[1].split('#')[0]
      const expectedAnchor = url.hash.substring(1)
      
      // Click the learn more link (opens in new tab)
      cy.get('[data-testid="learn-more-link"]').first().invoke('removeAttr', 'target').click()
      
      // Step 4: Verify we're on the bill details page
      cy.url().should('include', `/bills/${expectedSlug}`)
      
      // Step 5: Verify page structure and content
      cy.get('h1').should('be.visible').and('contain', 'Bill Details')
      
      // Check for navigation menu
      cy.get('nav').should('contain', 'What it does')
      cy.get('nav').should('contain', 'Who it affects') 
      cy.get('nav').should('contain', 'Why it matters')
      cy.get('nav').should('contain', 'Provisions')
      
      // Step 6: Verify moderation banner if present
      cy.get('body').then(($body) => {
        if ($body.find('[data-testid="moderation-banner"]').length > 0) {
          cy.get('[data-testid="moderation-banner"]').should('be.visible')
          cy.get('[data-testid="moderation-banner"]').should('contain', 'AI-Generated Analysis')
          cy.get('[data-testid="moderation-banner"]').should('contain', 'awaiting review by a human expert')
        }
      })
      
      // Step 7: Test anchor navigation if we have an expected anchor
      if (expectedAnchor) {
        cy.get(`#${expectedAnchor}`).should('exist')
        cy.get(`#${expectedAnchor}`).should('be.visible')
      }
      
      // Step 8: Test section navigation
      cy.get('#overview-what').should('exist')
      cy.get('#overview-who').should('exist')
      cy.get('#overview-why').should('exist')
      cy.get('#provisions').should('exist')
      
      // Click navigation links and verify scrolling
      cy.get('nav a[href="#overview-what"]').click()
      cy.get('#overview-what').should('be.visible')
      
      cy.get('nav a[href="#overview-who"]').click()
      cy.get('#overview-who').should('be.visible')
      
      // Step 9: Verify citations display
      cy.get('[data-testid="citations"]').should('exist')
      
      // Check citation structure
      cy.get('[data-testid="citations"]').first().within(() => {
        cy.get('[data-testid="citation-quote"]').should('be.visible')
        cy.get('[data-testid="citation-link"]').should('have.attr', 'href').and('include', '#sec-')
      })
      
      // Step 10: Test Additional Bill Content section
      cy.get('body').then(($body) => {
        if ($body.find('#additional-content').length > 0) {
          cy.get('#additional-content').should('be.visible')
          cy.get('#additional-content h2').should('contain', 'Additional Bill Content')
        }
      })
      
      // Step 11: Verify source index if present
      cy.get('body').then(($body) => {
        if ($body.find('#source').length > 0) {
          cy.get('#source').should('be.visible')
          cy.get('#source h2').should('contain', 'Source Index')
          cy.get('#source li').should('have.length.gt', 0)
        }
      })
    })
  })

  it('displays moderation banner with coverage metrics', () => {
    // Visit a bill details page directly
    cy.visit('/bills/hr5-118')
    cy.wait('@getBillDetails')
    
    // Check if moderation banner is present
    cy.get('body').then(($body) => {
      if ($body.find('[data-testid="moderation-banner"]').length > 0) {
        cy.get('[data-testid="moderation-banner"]').within(() => {
          // Check banner content
          cy.should('contain', 'AI-Generated Analysis')
          cy.should('contain', 'Use it as a starting point')
          
          // Check metrics display
          cy.get('[data-testid="coverage-metric"]').should('be.visible')
          cy.get('[data-testid="coverage-metric"]').should('match', /Coverage: \d+%/)
          
          // Check unverified count if present
          cy.get('body').then(($banner) => {
            if ($banner.find('[data-testid="unverified-metric"]').length > 0) {
              cy.get('[data-testid="unverified-metric"]').should('be.visible')
              cy.get('[data-testid="unverified-metric"]').should('match', /\d+ sections awaiting citations/)
            }
          })
        })
      }
    })
  })

  it('handles citation links and source navigation', () => {
    cy.visit('/bills/hr5-118')
    cy.wait('@getBillDetails')
    
    // Find a citation with a source link
    cy.get('[data-testid="citations"]').first().within(() => {
      cy.get('[data-testid="citation-link"]').first().then(($link) => {
        const href = $link.prop('href')
        const anchor = href.split('#')[1]
        
        if (anchor) {
          // Click the citation link
          cy.get('[data-testid="citation-link"]').first().click()
          
          // Verify we scrolled to the correct section
          cy.get(`#${anchor}`).should('be.visible')
        }
      })
    })
  })

  it('handles mobile responsive design', () => {
    cy.viewport('iphone-6')
    
    cy.visit('/bills/hr5-118')
    cy.wait('@getBillDetails')
    
    // Check that content is visible on mobile
    cy.get('h1').should('be.visible')
    cy.get('nav').should('be.visible')
    
    // Check that sections stack properly
    cy.get('#overview-what').should('be.visible')
    cy.get('#overview-who').should('be.visible')
    
    // Test navigation on mobile
    cy.get('nav a[href="#provisions"]').click()
    cy.get('#provisions').should('be.visible')
  })

  it('handles missing bill details gracefully', () => {
    // Mock 404 response for bill details
    cy.intercept('GET', '/api/v1/bills/details/by-slug/nonexistent-bill', { 
      statusCode: 404, 
      body: { detail: 'Bill details not found' } 
    }).as('getBillDetailsNotFound')
    
    cy.visit('/bills/nonexistent-bill')
    cy.wait('@getBillDetailsNotFound')
    
    // Should show fallback content
    cy.get('h1').should('contain', 'Bill details not available yet')
    cy.get('p').should('contain', "We couldn't load the detailed analysis")
    cy.get('p').should('contain', 'Try again shortly')
  })

  it('validates JSON-LD structured data', () => {
    cy.visit('/bills/hr5-118')
    cy.wait('@getBillDetails')
    
    // Check that JSON-LD script tag exists
    cy.get('script[type="application/ld+json"]').should('exist')
    
    // Validate JSON-LD structure
    cy.get('script[type="application/ld+json"]').then(($script) => {
      const jsonLd = JSON.parse($script.text())
      
      expect(jsonLd).to.have.property('@context', 'https://schema.org')
      expect(jsonLd).to.have.property('@type', 'Legislation')
      expect(jsonLd).to.have.property('name')
      expect(jsonLd).to.have.property('description')
      expect(jsonLd).to.have.property('url')
    })
  })

  it('validates canonical URL and SEO meta tags', () => {
    cy.visit('/bills/hr5-118')
    cy.wait('@getBillDetails')
    
    // Check canonical URL
    cy.get('link[rel="canonical"]').should('exist')
    cy.get('link[rel="canonical"]').should('have.attr', 'href').and('include', '/bills/hr5-118')
    
    // Check meta description
    cy.get('meta[name="description"]').should('exist')
    cy.get('meta[name="description"]').should('have.attr', 'content').and('not.be.empty')
    
    // Check Open Graph tags
    cy.get('meta[property="og:title"]').should('exist')
    cy.get('meta[property="og:description"]').should('exist')
    cy.get('meta[property="og:url"]').should('exist')
    cy.get('meta[property="og:type"]').should('have.attr', 'content', 'article')
  })
})