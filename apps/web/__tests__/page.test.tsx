import { render, screen } from '@testing-library/react';
import { UserProvider } from '@auth0/nextjs-auth0/client';
import Home from '../src/app/page';

// Mock Auth0 user for testing
const mockUser = {
  sub: 'auth0|test123',
  email: '<EMAIL>',
  name: 'Test User'
};

test.skip('renders a heading', () => {
  // Skip this test because async Server Components can't be tested with @testing-library/react
  // The Home component uses async/await for server-side data fetching
  render(
    <UserProvider user={mockUser}>
      <Home />
    </UserProvider>
  );
  const heading = screen.getByText(/Empower Your/i);
  expect(heading).toBeInTheDocument();
});
