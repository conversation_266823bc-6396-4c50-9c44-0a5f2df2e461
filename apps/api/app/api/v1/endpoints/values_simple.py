# app/api/v1/endpoints/values_simple.py
"""
Simplified Values Analysis Endpoints

This module provides simplified endpoints for bill values analysis
without complex task dependencies.
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any
import logging

from app.db.database import get_db
from app.models.bill import Bill
from app.services.bill_values_analysis_service import BillValuesAnalysisService
from app.core.config import Settings

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/analyze/{bill_id}")
def analyze_bill_values_simple(
    bill_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Analyze values for a specific bill (simplified version).
    
    Args:
        bill_id: ID of the bill to analyze
        db: Database session
        
    Returns:
        Values analysis results
    """
    try:
        # Get the bill
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        if not bill:
            raise HTTPException(status_code=404, detail="Bill not found")
        
        # Create values analysis service
        settings = Settings()
        values_service = BillValuesAnalysisService(db, settings)
        
        # Check if analysis already exists
        existing_analysis = values_service.get_bill_analysis(bill_id)
        if existing_analysis:
            return {
                "success": True,
                "message": "Analysis already exists",
                "bill_id": bill_id,
                "analysis": {
                    "democracy_threat_score": existing_analysis.democracy_threat_score,
                    "democracy_support_score": existing_analysis.democracy_support_score,
                    "human_rights_threat_score": existing_analysis.human_rights_threat_score,
                    "human_rights_support_score": existing_analysis.human_rights_support_score,
                    "environmental_threat_score": existing_analysis.environmental_threat_score,
                    "environmental_support_score": existing_analysis.environmental_support_score,
                    "overall_threat_level": existing_analysis.overall_threat_level,
                    "overall_support_level": existing_analysis.overall_support_level,
                    "confidence_score": existing_analysis.confidence_score,
                    "needs_human_review": existing_analysis.requires_human_review,
                    "analysis_summary": existing_analysis.analysis_reasoning.get('summary', '') if existing_analysis.analysis_reasoning else '',
                    "created_at": existing_analysis.created_at.isoformat()
                }
            }
        
        # Perform new analysis using synchronous method
        analysis = values_service.analyze_bill_values_sync(bill)
        db.commit()
        
        return {
            "success": True,
            "message": "Analysis completed successfully",
            "bill_id": bill_id,
            "analysis": {
                "democracy_threat_score": analysis.democracy_threat_score,
                "democracy_support_score": analysis.democracy_support_score,
                "human_rights_threat_score": analysis.human_rights_threat_score,
                "human_rights_support_score": analysis.human_rights_support_score,
                "environmental_threat_score": analysis.environmental_threat_score,
                "environmental_support_score": analysis.environmental_support_score,
                "overall_threat_level": analysis.overall_threat_level,
                "overall_support_level": analysis.overall_support_level,
                "confidence_score": analysis.confidence_score,
                "needs_human_review": analysis.requires_human_review,
                "analysis_summary": analysis.analysis_reasoning.get('summary', '') if analysis.analysis_reasoning else '',
                "created_at": analysis.created_at.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to analyze bill values for {bill_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


@router.get("/status/{bill_id}")
def get_bill_values_status(
    bill_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get values analysis status for a bill.
    
    Args:
        bill_id: ID of the bill
        db: Database session
        
    Returns:
        Analysis status and results if available
    """
    try:
        # Get the bill
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        if not bill:
            raise HTTPException(status_code=404, detail="Bill not found")
        
        # Create values analysis service
        settings = Settings()
        values_service = BillValuesAnalysisService(db, settings)
        
        # Check if analysis exists
        analysis = values_service.get_bill_analysis(bill_id)
        
        if not analysis:
            return {
                "bill_id": bill_id,
                "bill_number": bill.bill_number,
                "title": bill.title,
                "has_analysis": False,
                "message": "No values analysis found for this bill"
            }
        
        return {
            "bill_id": bill_id,
            "bill_number": bill.bill_number,
            "title": bill.title,
            "has_analysis": True,
            "analysis": {
                "democracy_threat_score": analysis.democracy_threat_score,
                "democracy_support_score": analysis.democracy_support_score,
                "human_rights_threat_score": analysis.human_rights_threat_score,
                "human_rights_support_score": analysis.human_rights_support_score,
                "environmental_threat_score": analysis.environmental_threat_score,
                "environmental_support_score": analysis.environmental_support_score,
                "overall_threat_level": analysis.overall_threat_level,
                "overall_support_level": analysis.overall_support_level,
                "confidence_score": analysis.confidence_score,
                "needs_human_review": analysis.requires_human_review,
                "analysis_summary": analysis.analysis_reasoning.get('summary', '') if analysis.analysis_reasoning else '',
                "created_at": analysis.created_at.isoformat(),
                "updated_at": analysis.updated_at.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get values status for {bill_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")


@router.get("/bill/{bill_id}")
def get_bill_values_analysis(
    bill_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get values analysis for a specific bill.
    
    Args:
        bill_id: ID of the bill
        db: Database session
        
    Returns:
        Values analysis results
    """
    try:
        # Get the bill
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        if not bill:
            raise HTTPException(status_code=404, detail="Bill not found")
        
        # Create values analysis service
        settings = Settings()
        values_service = BillValuesAnalysisService(db, settings)
        
        # Get analysis
        analysis = values_service.get_bill_analysis(bill_id)
        
        if not analysis:
            raise HTTPException(status_code=404, detail="No values analysis found for this bill")
        
        return {
            "bill_id": bill_id,
            "bill_number": bill.bill_number,
            "title": bill.title,
            "analysis": {
                "id": analysis.id,
                "democracy_threat_score": analysis.democracy_threat_score,
                "democracy_support_score": analysis.democracy_support_score,
                "human_rights_threat_score": analysis.human_rights_threat_score,
                "human_rights_support_score": analysis.human_rights_support_score,
                "environmental_threat_score": analysis.environmental_threat_score,
                "environmental_support_score": analysis.environmental_support_score,
                "overall_threat_level": analysis.overall_threat_level,
                "overall_support_level": analysis.overall_support_level,
                "confidence_score": analysis.confidence_score,
                "requires_human_review": analysis.requires_human_review,
                "is_flagged": analysis.is_flagged,
                "analyzed_at": analysis.analyzed_at.isoformat(),
                "reviewed_by": analysis.reviewed_by,
                "reviewed_at": analysis.reviewed_at.isoformat() if analysis.reviewed_at else None,
                "review_notes": analysis.review_notes,
                "created_at": analysis.created_at.isoformat(),
                "updated_at": analysis.updated_at.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get values analysis for {bill_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get analysis: {str(e)}")


@router.get("/bill/{bill_id}/tags")
def get_bill_values_tags(
    bill_id: str,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get values tags for a specific bill.
    
    Args:
        bill_id: ID of the bill
        db: Database session
        
    Returns:
        Values tags for the bill
    """
    try:
        # Get the bill
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        if not bill:
            raise HTTPException(status_code=404, detail="Bill not found")
        
        # Import BillValuesTag here to avoid circular imports
        from app.models.bill_values import BillValuesTag
        
        # Create values analysis service
        settings = Settings()
        values_service = BillValuesAnalysisService(db, settings)
        
        # Get analysis to get the analysis_id
        analysis = values_service.get_bill_analysis(bill_id)
        
        if not analysis:
            return {"tags": []}
        
        # Get tags for this analysis
        tags = db.query(BillValuesTag).filter(
            BillValuesTag.analysis_id == analysis.id,
            BillValuesTag.is_active == True
        ).all()
        
        return {
            "tags": [{
                "id": tag.id,
                "bill_id": tag.bill_id,
                "analysis_id": tag.analysis_id,
                "tag_category": tag.tag_category,
                "tag_type": tag.tag_type,
                "tag_name": tag.tag_name,
                "display_text": tag.display_text,
                "description": tag.description,
                "severity_level": tag.severity_level,
                "display_priority": tag.display_priority,
                "color_theme": tag.color_theme,
                "icon_name": tag.icon_name,
                "is_active": tag.is_active,
                "created_at": tag.created_at.isoformat(),
                "updated_at": tag.updated_at.isoformat()
            } for tag in tags]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get values tags for {bill_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get tags: {str(e)}")


@router.post("/batch-analyze")
def batch_analyze_bills(
    limit: int = 10,
    db: Session = Depends(get_db)
) -> Dict[str, Any]:
    """
    Analyze values for multiple bills that don't have analysis yet.
    
    Args:
        limit: Maximum number of bills to analyze
        db: Database session
        
    Returns:
        Batch analysis results
    """
    try:
        # Get bills without values analysis
        from app.models.bill_values import BillValuesAnalysis
        
        bills_without_analysis = (
            db.query(Bill)
            .outerjoin(BillValuesAnalysis)
            .filter(BillValuesAnalysis.id.is_(None))
            .limit(limit)
            .all()
        )
        
        if not bills_without_analysis:
            return {
                "success": True,
                "message": "No bills found that need values analysis",
                "analyzed_count": 0,
                "bills": []
            }
        
        # Create values analysis service
        settings = Settings()
        values_service = BillValuesAnalysisService(db, settings)
        
        results = []
        successful_count = 0
        
        for bill in bills_without_analysis:
            try:
                analysis = values_service.analyze_bill_values_sync(bill)
                results.append({
                    "bill_id": bill.id,
                    "bill_number": bill.bill_number,
                    "title": bill.title,
                    "success": True,
                    "overall_threat_level": analysis.overall_threat_level,
                    "overall_support_level": analysis.overall_support_level
                })
                successful_count += 1
                
            except Exception as e:
                logger.error(f"Failed to analyze bill {bill.id}: {e}")
                results.append({
                    "bill_id": bill.id,
                    "bill_number": bill.bill_number,
                    "title": bill.title,
                    "success": False,
                    "error": str(e)
                })
        
        # Commit all successful analyses
        db.commit()
        
        return {
            "success": True,
            "message": f"Analyzed {successful_count} out of {len(bills_without_analysis)} bills",
            "analyzed_count": successful_count,
            "total_attempted": len(bills_without_analysis),
            "bills": results
        }
        
    except Exception as e:
        logger.error(f"Failed to batch analyze bills: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Batch analysis failed: {str(e)}")
