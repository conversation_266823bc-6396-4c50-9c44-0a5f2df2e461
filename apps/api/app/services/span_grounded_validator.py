"""
Span-grounded validation gates that enforce evidence-based analysis
Blocks output with null headings/anchor_ids and requires evidence for every sentence
"""

import json
import re
from typing import Dict, Any, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class SpanGroundedValidator:
    """Validates that AI output is properly grounded in evidence spans"""
    
    def __init__(self):
        self.min_quote_length = 12
        self.max_quote_length = 140
        self.required_fields = ["tldr", "who_affects", "why_matters", "key_provisions"]
        
    def validate_analysis(self, analysis_payload: Dict[str, Any]) -> Tuple[bool, List[str], Dict[str, Any]]:
        """
        Validate analysis payload against span-grounding requirements
        
        Returns:
            (is_valid, errors, metrics)
        """
        errors = []
        metrics = {
            "evidence_coverage": 0.0,
            "invalid_citation_count": 0,
            "unverified_count": 0,
            "total_sentences": 0,
            "sentences_with_evidence": 0
        }
        
        try:
            # 1. Coverage Gate: Every sentence must have evidence
            coverage_errors, coverage_metrics = self._validate_coverage(analysis_payload)
            errors.extend(coverage_errors)
            metrics.update(coverage_metrics)
            
            # 2. Integrity Gate: All citations must be valid
            integrity_errors, integrity_metrics = self._validate_citation_integrity(analysis_payload)
            errors.extend(integrity_errors)
            metrics.update(integrity_metrics)
            
            # 3. Specificity Gate: Content must be specific, not generic
            specificity_errors = self._validate_specificity(analysis_payload)
            errors.extend(specificity_errors)
            
            # 4. Literal Check: Citations must match content
            literal_errors = self._validate_literal_match(analysis_payload)
            errors.extend(literal_errors)
            
            # Calculate final metrics
            metrics["evidence_coverage"] = (
                metrics["sentences_with_evidence"] / max(metrics["total_sentences"], 1)
            )
            
            is_valid = len(errors) == 0 and metrics["evidence_coverage"] >= 1.0
            
            return is_valid, errors, metrics
            
        except Exception as e:
            logger.error(f"Validation failed with exception: {e}")
            return False, [f"Validation exception: {e}"], metrics
    
    def _validate_coverage(self, payload: Dict[str, Any]) -> Tuple[List[str], Dict[str, Any]]:
        """Ensure every sentence has evidence"""
        errors = []
        total_sentences = 0
        sentences_with_evidence = 0
        
        # Check required fields
        for field in self.required_fields:
            if field not in payload:
                errors.append(f"Missing required field: {field}")
                continue
                
            field_data = payload[field]
            
            if field == "tldr":
                # Single content + citations
                if isinstance(field_data, dict) and "content" in field_data:
                    sentences = self._split_sentences(field_data["content"])
                    citations = field_data.get("citations", [])
                    total_sentences += len(sentences)
                    if citations:
                        sentences_with_evidence += len(sentences)
                    else:
                        errors.append(f"{field}: No citations provided for content")
                        
            elif field in ["who_affects", "key_provisions"]:
                # Array of items with content + citations
                if isinstance(field_data, list):
                    for i, item in enumerate(field_data):
                        if isinstance(item, dict) and "content" in item:
                            sentences = self._split_sentences(item["content"])
                            citations = item.get("citations", [])
                            total_sentences += len(sentences)
                            if citations:
                                sentences_with_evidence += len(sentences)
                            else:
                                errors.append(f"{field}[{i}]: No citations provided for content")
                                
            elif field == "why_matters":
                # Single content + citations
                if isinstance(field_data, dict) and "content" in field_data:
                    sentences = self._split_sentences(field_data["content"])
                    citations = field_data.get("citations", [])
                    total_sentences += len(sentences)
                    if citations:
                        sentences_with_evidence += len(sentences)
                    else:
                        errors.append(f"{field}: No citations provided for content")
        
        return errors, {
            "total_sentences": total_sentences,
            "sentences_with_evidence": sentences_with_evidence
        }
    
    def _validate_citation_integrity(self, payload: Dict[str, Any]) -> Tuple[List[str], Dict[str, Any]]:
        """Validate all citations have proper structure"""
        errors = []
        invalid_count = 0
        total_citations = 0
        
        def check_citations(citations: List[Dict], context: str):
            nonlocal invalid_count, total_citations
            
            for i, citation in enumerate(citations):
                total_citations += 1
                
                # Check required fields
                if not citation.get("heading"):
                    errors.append(f"{context}[{i}]: Citation has null/empty heading")
                    invalid_count += 1
                    
                if not citation.get("anchor_id"):
                    errors.append(f"{context}[{i}]: Citation has null/empty anchor_id")
                    invalid_count += 1
                    
                quote = citation.get("quote", "")
                if not quote:
                    errors.append(f"{context}[{i}]: Citation has empty quote")
                    invalid_count += 1
                elif len(quote) < self.min_quote_length:
                    errors.append(f"{context}[{i}]: Quote too short ({len(quote)} < {self.min_quote_length})")
                    invalid_count += 1
                elif len(quote) > self.max_quote_length:
                    errors.append(f"{context}[{i}]: Quote too long ({len(quote)} > {self.max_quote_length})")
                    invalid_count += 1
                    
                # Check offsets
                start_offset = citation.get("start_offset")
                end_offset = citation.get("end_offset")
                if start_offset is None or end_offset is None:
                    errors.append(f"{context}[{i}]: Missing start_offset or end_offset")
                    invalid_count += 1
                elif start_offset >= end_offset:
                    errors.append(f"{context}[{i}]: Invalid offsets (start >= end)")
                    invalid_count += 1
        
        # Check all citation fields
        for field in self.required_fields:
            if field not in payload:
                continue
                
            field_data = payload[field]
            
            if field == "tldr" and isinstance(field_data, dict):
                citations = field_data.get("citations", [])
                check_citations(citations, f"{field}.citations")
                
            elif field in ["who_affects", "key_provisions"] and isinstance(field_data, list):
                for i, item in enumerate(field_data):
                    if isinstance(item, dict):
                        citations = item.get("citations", [])
                        check_citations(citations, f"{field}[{i}].citations")
                        
            elif field == "why_matters" and isinstance(field_data, dict):
                citations = field_data.get("citations", [])
                check_citations(citations, f"{field}.citations")
        
        return errors, {"invalid_citation_count": invalid_count, "total_citations": total_citations}
    
    def _validate_specificity(self, payload: Dict[str, Any]) -> List[str]:
        """Check for generic/vague content"""
        errors = []
        
        # Generic phrases that indicate low quality
        generic_phrases = [
            "effective immediately upon enactment",
            "technical provisions and administrative details",
            "various provisions",
            "other requirements",
            "additional measures",
            "further details",
            "as appropriate",
            "as necessary"
        ]
        
        def check_content_specificity(content: str, context: str):
            content_lower = content.lower()
            for phrase in generic_phrases:
                if phrase in content_lower:
                    errors.append(f"{context}: Contains generic phrase '{phrase}'")
                    
            # Check for specific indicators (dates, amounts, entities)
            has_date = bool(re.search(r'\b\d{1,2}/\d{1,2}/\d{4}|\b\d{4}\b|\bdays?\b|\bmonths?\b|\byears?\b', content))
            has_amount = bool(re.search(r'\$[\d,]+|\b\d+\s*percent|\b\d+%', content))
            has_entity = bool(re.search(r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b|Department|Agency|Commission|Bureau', content))
            
            if not (has_date or has_amount or has_entity):
                errors.append(f"{context}: Content lacks specific details (no dates, amounts, or entities)")
        
        # Check content in all fields
        for field in self.required_fields:
            if field not in payload:
                continue
                
            field_data = payload[field]
            
            if field in ["tldr", "why_matters"] and isinstance(field_data, dict):
                content = field_data.get("content", "")
                check_content_specificity(content, field)
                
            elif field in ["who_affects", "key_provisions"] and isinstance(field_data, list):
                for i, item in enumerate(field_data):
                    if isinstance(item, dict):
                        content = item.get("content", "")
                        check_content_specificity(content, f"{field}[{i}]")
        
        return errors
    
    def _validate_literal_match(self, payload: Dict[str, Any]) -> List[str]:
        """Check that citations actually support the content"""
        errors = []
        
        def check_literal_match(content: str, citations: List[Dict], context: str):
            for i, citation in enumerate(citations):
                quote = citation.get("quote", "").lower()
                content_lower = content.lower()
                
                # Simple check: quote should appear in content or be closely related
                if quote and quote not in content_lower:
                    # Check for high similarity (simple word overlap)
                    quote_words = set(quote.split())
                    content_words = set(content_lower.split())
                    overlap = len(quote_words & content_words)
                    similarity = overlap / max(len(quote_words), 1)
                    
                    if similarity < 0.3:  # Less than 30% word overlap
                        errors.append(f"{context}[{i}]: Quote doesn't match content (low similarity)")
        
        # Check all fields
        for field in self.required_fields:
            if field not in payload:
                continue
                
            field_data = payload[field]
            
            if field in ["tldr", "why_matters"] and isinstance(field_data, dict):
                content = field_data.get("content", "")
                citations = field_data.get("citations", [])
                check_literal_match(content, citations, field)
                
            elif field in ["who_affects", "key_provisions"] and isinstance(field_data, list):
                for i, item in enumerate(field_data):
                    if isinstance(item, dict):
                        content = item.get("content", "")
                        citations = item.get("citations", [])
                        check_literal_match(content, citations, f"{field}[{i}]")
        
        return errors
    
    def _split_sentences(self, text: str) -> List[str]:
        """Split text into sentences"""
        # Simple sentence splitting
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
