# app/services/bill_chunking_service.py
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class BillChunk:
    """Represents a logical chunk of a bill for analysis"""
    id: str
    title: str
    content: str
    start_offset: int
    end_offset: int
    level: int  # 1=TITLE, 2=SECTION, 3=SUBSECTION
    parent_id: Optional[str]
    importance: str  # "primary", "secondary", "technical"
    affected_parties: List[str]
    chunk_type: str  # "substantive", "definitions", "enforcement", "technical"


class BillChunkingService:
    """Intelligent bill chunking for comprehensive analysis"""
    
    def __init__(self):
        # Enhanced patterns for identifying ALL bill structure elements
        self.title_pattern = re.compile(r'^TITLE\s+([IVXLCDM]+|[0-9]+)(?:\s*--\s*(.+?))?$', re.MULTILINE | re.IGNORECASE)
        self.section_pattern = re.compile(r'^SEC\.\s+(\d+)\.\s*(.+?)$', re.MULTILINE | re.IGNORECASE)
        self.subsection_pattern = re.compile(r'^\s*\([a-z]\)\s+(.+?)$', re.MULTILINE)
        self.paragraph_pattern = re.compile(r'^\s*\((\d+)\)\s+(.+?)$', re.MULTILINE)
        self.subparagraph_pattern = re.compile(r'^\s*\([A-Z]\)\s+(.+?)$', re.MULTILINE)
        self.clause_pattern = re.compile(r'^\s*\([ivx]+\)\s+(.+?)$', re.MULTILINE)

        # Enhanced keywords for comprehensive importance classification
        self.primary_keywords = [
            'requires', 'prohibits', 'mandates', 'establishes', 'creates', 'shall',
            'funding', 'penalty', 'enforcement', 'compliance', 'violation',
            'appropriated', 'authorized', 'allocated', 'grant', 'award',
            'fine', 'sanction', 'suspend', 'revoke', 'terminate',
            'report', 'submit', 'provide', 'ensure', 'implement'
        ]

        self.secondary_keywords = [
            'sense of congress', 'findings', 'purposes', 'definitions',
            'rule of construction', 'effective date', 'authorization',
            'may', 'should', 'encourage', 'support', 'promote',
            'study', 'review', 'evaluate', 'assess', 'monitor'
        ]

        self.technical_keywords = [
            'amendment', 'striking', 'inserting', 'redesignating',
            'conforming', 'clerical', 'table of contents',
            'short title', 'citation', 'severability'
        ]

        # Comprehensive affected party identification
        self.party_keywords = {
            'schools': ['school', 'educational agency', 'local educational agency', 'school district', 'lea', 'sea'],
            'parents': ['parent', 'guardian', 'family', 'caregiver'],
            'students': ['student', 'child', 'pupil', 'learner', 'minor'],
            'teachers': ['teacher', 'educator', 'personnel', 'staff', 'instructor', 'faculty'],
            'government': ['secretary', 'department', 'federal', 'state', 'agency', 'commissioner', 'administrator'],
            'contractors': ['contractor', 'vendor', 'service provider', 'grantee', 'recipient'],
            'taxpayers': ['taxpayer', 'public', 'citizen', 'community'],
            'organizations': ['organization', 'association', 'institution', 'entity', 'nonprofit']
        }

        # Content type patterns for maximum detail extraction
        self.content_patterns = {
            'deadlines': re.compile(r'(?:not later than|within|by|before)\s+(\d+\s+(?:days?|months?|years?))', re.IGNORECASE),
            'amounts': re.compile(r'\$[\d,]+(?:\.\d{2})?(?:\s+(?:million|billion|thousand))?', re.IGNORECASE),
            'percentages': re.compile(r'\d+(?:\.\d+)?%', re.IGNORECASE),
            'dates': re.compile(r'(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4}', re.IGNORECASE),
            'references': re.compile(r'section\s+\d+|subsection\s+\([a-z]\)|paragraph\s+\(\d+\)', re.IGNORECASE)
        }

    def chunk_bill(self, bill_text: str, max_chunk_size: int = 3000) -> List[BillChunk]:
        """
        Intelligently chunk a bill into logical sections for MAXIMUM detail extraction
        Smaller chunks = more detailed analysis = better coverage
        """
        chunks = []

        # First, identify ALL structural elements (including subsections, paragraphs)
        structure = self._parse_comprehensive_structure(bill_text)

        # Group into logical chunks with maximum detail preservation
        logical_chunks = self._create_logical_chunks(structure, bill_text, max_chunk_size)
        
        # Classify and enhance chunks
        for chunk_data in logical_chunks:
            chunk = self._enhance_chunk(chunk_data, bill_text)
            chunks.append(chunk)
        
        return chunks

    def _parse_comprehensive_structure(self, bill_text: str) -> List[Dict[str, Any]]:
        """Parse ALL structural elements for maximum detail extraction"""
        structure = []

        # Find all titles
        for title_match in self.title_pattern.finditer(bill_text):
            structure.append({
                'type': 'title',
                'level': 1,
                'number': title_match.group(1),
                'title': title_match.group(2) or '',
                'start': title_match.start(),
                'end': title_match.end(),
                'full_match': title_match.group(0)
            })

        # Find all sections
        for section_match in self.section_pattern.finditer(bill_text):
            structure.append({
                'type': 'section',
                'level': 2,
                'number': section_match.group(1),
                'title': section_match.group(2),
                'start': section_match.start(),
                'end': section_match.end(),
                'full_match': section_match.group(0)
            })

        # Find all subsections (a), (b), (c)
        for subsection_match in self.subsection_pattern.finditer(bill_text):
            structure.append({
                'type': 'subsection',
                'level': 3,
                'number': subsection_match.group(1),
                'title': subsection_match.group(1),
                'start': subsection_match.start(),
                'end': subsection_match.end(),
                'full_match': subsection_match.group(0)
            })

        # Find all paragraphs (1), (2), (3)
        for paragraph_match in self.paragraph_pattern.finditer(bill_text):
            structure.append({
                'type': 'paragraph',
                'level': 4,
                'number': paragraph_match.group(1),
                'title': paragraph_match.group(2)[:50] + '...' if len(paragraph_match.group(2)) > 50 else paragraph_match.group(2),
                'start': paragraph_match.start(),
                'end': paragraph_match.end(),
                'full_match': paragraph_match.group(0)
            })

        # Sort by position in text
        structure.sort(key=lambda x: x['start'])

        return structure

    def _parse_bill_structure(self, bill_text: str) -> List[Dict[str, Any]]:
        """Parse the hierarchical structure of the bill"""
        structure = []
        
        # Find all titles
        for title_match in self.title_pattern.finditer(bill_text):
            structure.append({
                'type': 'title',
                'level': 1,
                'number': title_match.group(1),
                'title': title_match.group(2) or '',
                'start': title_match.start(),
                'end': title_match.end(),
                'full_match': title_match.group(0)
            })
        
        # Find all sections
        for section_match in self.section_pattern.finditer(bill_text):
            structure.append({
                'type': 'section',
                'level': 2,
                'number': section_match.group(1),
                'title': section_match.group(2),
                'start': section_match.start(),
                'end': section_match.end(),
                'full_match': section_match.group(0)
            })
        
        # Sort by position in text
        structure.sort(key=lambda x: x['start'])
        
        return structure

    def _create_logical_chunks(self, structure: List[Dict[str, Any]], bill_text: str, max_size: int) -> List[Dict[str, Any]]:
        """Group structural elements into logical chunks with MAXIMUM detail preservation"""
        chunks = []
        current_chunk = None

        for i, element in enumerate(structure):
            # Determine content for this element
            start_pos = element['start']
            end_pos = structure[i + 1]['start'] if i + 1 < len(structure) else len(bill_text)
            content = bill_text[start_pos:end_pos].strip()

            # Create smaller, more detailed chunks for better analysis
            # Each major structural element gets its own chunk for maximum detail

            # If this is a title, start a new chunk
            if element['type'] == 'title':
                if current_chunk:
                    chunks.append(current_chunk)

                current_chunk = {
                    'id': f"title-{element['number']}",
                    'title': f"TITLE {element['number']}: {element['title']}",
                    'content': content,
                    'start_offset': start_pos,
                    'end_offset': end_pos,
                    'level': 1,
                    'elements': [element]
                }
            
            # If this is a section, create individual chunks for maximum detail
            elif element['type'] == 'section':
                # Always finalize previous chunk to ensure granular analysis
                if current_chunk:
                    chunks.append(current_chunk)

                # Create dedicated chunk for this section
                current_chunk = {
                    'id': f"section-{element['number']}",
                    'title': f"SEC. {element['number']}: {element['title']}",
                    'content': content,
                    'start_offset': start_pos,
                    'end_offset': end_pos,
                    'level': 2,
                    'elements': [element]
                }

            # If this is a subsection and content is substantial, create separate chunk
            elif element['type'] == 'subsection' and len(content) > 500:
                if current_chunk:
                    chunks.append(current_chunk)

                current_chunk = {
                    'id': f"subsection-{element['number']}-{i}",
                    'title': f"Subsection ({element['number']}): {element['title']}",
                    'content': content,
                    'start_offset': start_pos,
                    'end_offset': end_pos,
                    'level': 3,
                    'elements': [element]
                }

            # If this is a paragraph with significant content, create separate chunk
            elif element['type'] == 'paragraph' and len(content) > 300:
                if current_chunk:
                    chunks.append(current_chunk)

                current_chunk = {
                    'id': f"paragraph-{element['number']}-{i}",
                    'title': f"Paragraph ({element['number']}): {element['title']}",
                    'content': content,
                    'start_offset': start_pos,
                    'end_offset': end_pos,
                    'level': 4,
                    'elements': [element]
                }
        
        # Add final chunk
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks

    def _enhance_chunk(self, chunk_data: Dict[str, Any], bill_text: str) -> BillChunk:
        """Enhance chunk with comprehensive metadata and classification for MAXIMUM detail extraction"""
        content = chunk_data['content']
        content_lower = content.lower()

        # Classify importance with enhanced criteria
        importance = self._classify_comprehensive_importance(content_lower)

        # Identify ALL affected parties
        affected_parties = self._identify_comprehensive_affected_parties(content_lower)

        # Classify chunk type with more granular categories
        chunk_type = self._classify_comprehensive_chunk_type(content_lower)

        return BillChunk(
            id=chunk_data['id'],
            title=chunk_data['title'],
            content=content,
            start_offset=chunk_data['start_offset'],
            end_offset=chunk_data['end_offset'],
            level=chunk_data['level'],
            parent_id=None,  # TODO: Implement parent relationships
            importance=importance,
            affected_parties=affected_parties,
            chunk_type=chunk_type
        )

    def _classify_comprehensive_importance(self, content_lower: str) -> str:
        """Classify importance with enhanced criteria for maximum detail capture"""
        primary_score = sum(1 for keyword in self.primary_keywords if keyword in content_lower)
        secondary_score = sum(1 for keyword in self.secondary_keywords if keyword in content_lower)
        technical_score = sum(1 for keyword in self.technical_keywords if keyword in content_lower)

        # Enhanced scoring with more nuanced classification
        if primary_score >= 2 or 'shall' in content_lower or 'required' in content_lower:
            return "primary"
        elif primary_score >= 1 or secondary_score >= 2:
            return "secondary"
        elif technical_score >= 1 or 'amendment' in content_lower:
            return "technical"
        else:
            # Default to secondary to ensure nothing is missed
            return "secondary"

    def _identify_comprehensive_affected_parties(self, content_lower: str) -> List[str]:
        """Identify ALL affected parties with enhanced detection"""
        affected = []

        for party, keywords in self.party_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                affected.append(party)

        # Additional party detection for comprehensive coverage
        if 'individual' in content_lower or 'person' in content_lower:
            affected.append('individuals')
        if 'business' in content_lower or 'company' in content_lower:
            affected.append('businesses')
        if 'nonprofit' in content_lower or 'non-profit' in content_lower:
            affected.append('nonprofits')

        return list(set(affected))  # Remove duplicates

    def _classify_comprehensive_chunk_type(self, content_lower: str) -> str:
        """Classify chunk type with comprehensive categories for maximum detail"""
        # Definitions and terminology
        if any(word in content_lower for word in ['definition', 'means', 'term', 'shall mean']):
            return "definitions"

        # Enforcement and compliance
        elif any(word in content_lower for word in ['penalty', 'violation', 'enforcement', 'compliance', 'fine', 'sanction']):
            return "enforcement"

        # Funding and financial
        elif any(word in content_lower for word in ['appropriated', 'funding', 'grant', 'award', 'allocation', '$']):
            return "funding"

        # Reporting and oversight
        elif any(word in content_lower for word in ['report', 'submit', 'notify', 'inform', 'data collection']):
            return "reporting"

        # Implementation and procedures
        elif any(word in content_lower for word in ['implement', 'establish', 'create', 'develop', 'procedure']):
            return "implementation"

        # Technical amendments
        elif any(word in content_lower for word in ['amendment', 'striking', 'inserting', 'redesignating']):
            return "technical"

        # Congressional findings and sense
        elif any(word in content_lower for word in ['sense of congress', 'findings', 'purposes']):
            return "congressional"

        # Requirements and mandates
        elif any(word in content_lower for word in ['shall', 'must', 'required', 'mandated']):
            return "requirements"

        # Default to substantive to ensure comprehensive coverage
        else:
            return "substantive"

    def get_chunk_summary(self, chunk: BillChunk) -> str:
        """Generate a brief summary of what this chunk does"""
        # Enhanced summary with more detail
        parties_str = ', '.join(chunk.affected_parties) if chunk.affected_parties else 'various parties'
        return f"{chunk.title} - {chunk.importance} {chunk.chunk_type} provision affecting {parties_str}"

    def extract_detailed_metadata(self, content: str) -> Dict[str, Any]:
        """Extract detailed metadata from chunk content for maximum information capture"""
        metadata = {
            'deadlines': [],
            'amounts': [],
            'percentages': [],
            'dates': [],
            'references': []
        }

        # Extract all patterns for comprehensive detail
        for pattern_name, pattern in self.content_patterns.items():
            matches = pattern.findall(content)
            metadata[pattern_name] = matches

        return metadata
