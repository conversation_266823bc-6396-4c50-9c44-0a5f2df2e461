# Bill Details System Documentation

## Overview

The Bill Details System is a comprehensive, citation-based analysis platform that transforms legislative bills into transparent, user-friendly content with verifiable sources. Every claim is backed by exact quotes from the original bill text, ensuring maximum integrity and trustworthiness.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Data Models](#data-models)
3. [API Endpoints](#api-endpoints)
4. [Frontend Components](#frontend-components)
5. [AI Pipeline](#ai-pipeline)
6. [Citation Validation](#citation-validation)
7. [Testing Strategy](#testing-strategy)
8. [Deployment Guide](#deployment-guide)

## System Architecture

### Core Components

```mermaid
graph TD
    A[Bill Text Ingestion] --> B[Text Citation Service]
    B --> C[AI Analysis Service]
    C --> D[Bill Details Service]
    D --> E[Database: bill_details]
    E --> F[API Endpoints]
    F --> G[Frontend: Bill Details Page]
    G --> H[Action Modal Integration]
```

### Data Flow

1. **Ingestion**: Raw bill text is fetched and stored
2. **Indexing**: Text is segmented by sections with offset tracking
3. **AI Analysis**: Content is generated with required citations
4. **Validation**: All quotes are verified against source text
5. **Storage**: Structured data is saved to `bill_details` table
6. **Rendering**: Frontend displays content with citation links

## Key Features

### ✅ Citation-First Approach
- Every claim requires exact quotes from bill text
- Quotes include precise character offsets and section metadata
- Unverified claims are automatically dropped or flagged

### ✅ Transparency & Trust
- Moderation banner for AI-generated content
- Coverage metrics show citation completeness
- "Additional Bill Content" section for transparency
- Source index with navigable anchors

### ✅ SEO Optimization
- Clean canonical URLs (`/bills/hr5-118`)
- JSON-LD structured data
- Comprehensive meta tags
- Server-side rendering with Next.js App Router

### ✅ User Experience
- Anchor navigation from action modals
- Mobile-responsive design
- Progressive disclosure of information
- Accessible citation links

## Official Decision Points

Based on executive decisions made during implementation:

1. **Source Restriction**: Phase 1 limited to bill text only (no external sources)
2. **Transparency Label**: "Additional Bill Content" for catch-all sections
3. **Moderation Policy**: Show unreviewed content with neutral banner
4. **Banner Copy**:
   - Headline: "AI-Generated Analysis"
   - Body: "This content was generated by our AI and is awaiting review by a human expert. Use it as a starting point for your own research, not as a definitive source."

## Quick Start

### Prerequisites
- PostgreSQL database
- Python 3.9+ with Poetry
- Node.js 18+ with npm
- OpenAI API key (for AI analysis)

### Setup
1. Run database migrations: `poetry run alembic upgrade head`
2. Start API server: `poetry run uvicorn app.main:app --reload`
3. Start web server: `npm run dev`
4. Access bill details at: `http://localhost:3000/bills/{slug}`

## Next Steps

See the following documentation files for detailed implementation guides:
- [API Documentation](./api-documentation.md)
- [Frontend Guide](./frontend-guide.md)
- [AI Pipeline Details](./ai-pipeline.md)
- [Testing Guide](./testing-guide.md)
- [Deployment Instructions](./deployment.md)

## Vision: Building a world-class bill knowledge system

ModernAction’s Bill Details System aims to be the world’s most trusted, citizen-friendly source for understanding legislation. “World‑class” means:
- Source-of-truth integrity: every claim must cite the bill text with precise offsets and deep links.
- Performance and resilience: always render useful content; degrade gracefully but never break UX.
- SEO excellence: canonical URLs, structured data, and fast SSR for maximum discoverability.
- Editorial transparency: moderation banner and coverage metrics that are honest and useful.
- Developer trust: idempotent pipelines, deterministic slugs, and strong test coverage.

## August 2025 Enhancements

- Unified processing pipeline
  - End-to-end bill processing via UnifiedBillProcessingService (metadata → full text → AI → details → values)
  - Idempotent updates: reprocessing updates existing records instead of duplicating
  - Guaranteed BillDetails creation with source_index and metrics
- AI analysis resilience
  - Partial-failure tolerance; TLDR seeding ensures non-empty "what_does"
  - Normalization for key_provisions/timeline regardless of model key variants
  - Long-text handling with safe truncation for context window limits
- Canonical slugs and links
  - Canonical format: hr5-118 (letters+digits + dash + session)
  - Frontend Learn more links use shared slug util; admin page opens normalized slugs
  - API accepts both hr5-118 and 5-118; normalizes internally
- Frontend Bill Details page
  - SSR page renders hero summary, structured sections, citations, and source index
  - JSON-LD enriched with CreativeWorkSeries for the Congress session
  - Moderation banner shows coverage ratio and unverified counts
- Testing
  - Added API integration test to assert details persistence and source_index presence
  - Playwright tests validate slug formats and canonical behavior

## Operational Notes

- Coverage threshold: content flagged for human review if coverage_ratio < 0.6
- Source restriction: Phase 1 cites bill text only; external sources planned for Phase 2
- Performance goals: <2s P95 page render; <200ms API P95 for details fetch

## Next Steps

- Strengthen AI prompts to increase citation coverage; target >0.6 median
- Add editorial workflow for human review/approval with audit trail
- Expand source types (committee reports, CBO scores) under clearly labeled sections
- Richer positions with justifications and citations; add pro/con summary rollups

## Known Issues / Open Items

- Some model outputs may leave positions empty; pipeline handles gracefully but coverage can be low
- Type mismatches in web Bill types (e.g., cosponsors) should be aligned with component props
- Ensure all legacy entry points use canonical slugs to avoid 301/redirect inconsistencies
