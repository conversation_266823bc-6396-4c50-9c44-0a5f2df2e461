#!/usr/bin/env python3
"""
Test Secondary Analysis Service with HR4922
"""

import asyncio
import sys
import os
sys.path.append('./apps/api')

from app.db.database import get_db
from app.services.unified_bill_processing_service import UnifiedBillProcessingService

async def test_secondary_analysis():
    print("🧪 Testing Secondary Analysis Service with HR4922...")
    print("📝 Expected: High-quality user content with congress.gov summary integration")
    print("📚 Expected: 10th grade reading level with bill citations")
    print()
    
    try:
        # Initialize service
        db = next(get_db())
        processing_service = UnifiedBillProcessingService(db)
        
        # Process HR4922 with enhanced analysis (which will use Secondary Analysis Service)
        print("1. Processing HR4922 with Secondary Analysis Service...")
        result = await processing_service.process_bill_by_number(
            bill_number="HR4922",
            congress_session=119,
            environment="development",
            use_comprehensive=False,
            use_enhanced_analysis=True  # This will trigger Secondary Analysis Service
        )
        
        if result.get("success"):
            print(f"✅ Processing successful!")
            print(f"   Bill ID: {result.get('bill_id')}")
            print(f"   Title: {result.get('title', 'Unknown')}")
            print(f"   AI Analysis: {'✅' if result.get('processing_steps', {}).get('ai_analysis_completed') else '❌'}")
            print()
            
            # Test API endpoint to verify secondary analysis content
            print("2. Testing API endpoint for secondary analysis content...")
            
            # Wait a moment for bill_details to be created
            await asyncio.sleep(2)
            
            # Use curl to test the API endpoint
            import subprocess
            try:
                api_result = subprocess.run([
                    'curl', '-s', 'http://localhost:8000/api/v1/bills/details/by-slug/hr4922-119'
                ], capture_output=True, text=True, timeout=30)
                
                if api_result.returncode == 0:
                    import json
                    try:
                        details = json.loads(api_result.stdout)
                        
                        print(f"✅ API Status: 200 OK")
                        print(f"✅ Hero Summary: {len(details.get('hero_summary', ''))} characters")
                        
                        # Check secondary analysis quality indicators
                        overview = details.get('overview', {})
                        
                        # What does
                        what_does = overview.get('what_does', {})
                        if what_does and what_does.get('content'):
                            print(f"✅ What Does: {len(what_does['content'])} characters")
                            print(f"   Citations: {len(what_does.get('citations', []))}")
                        else:
                            print("❌ What Does: Missing or empty")
                        
                        # Who affects  
                        who_affects = overview.get('who_affects', {})
                        if who_affects and who_affects.get('content'):
                            print(f"✅ Who Affects: {len(who_affects['content'])} characters")
                            print(f"   Citations: {len(who_affects.get('citations', []))}")
                        else:
                            print("❌ Who Affects: Missing or empty")
                        
                        # Why matters
                        why_matters = overview.get('why_matters', {})
                        if why_matters and why_matters.get('content'):
                            print(f"✅ Why Matters: {len(why_matters['content'])} characters")
                            print(f"   Citations: {len(why_matters.get('citations', []))}")
                        else:
                            print("❌ Why Matters: Missing or empty")
                        
                        # Key provisions
                        key_provisions = overview.get('key_provisions', [])
                        print(f"✅ Key Provisions: {len(key_provisions)} items")
                        
                        # Cost impact
                        cost_impact = overview.get('cost_impact', {})
                        if cost_impact and cost_impact.get('content'):
                            print(f"✅ Cost Impact: {len(cost_impact['content'])} characters")
                        else:
                            print("❌ Cost Impact: Missing or empty")
                        
                        # Timeline
                        timeline = overview.get('timeline', [])
                        print(f"✅ Timeline: {len(timeline)} items")
                        
                        # Check for secondary analysis indicators
                        if details.get('secondary_analysis'):
                            print(f"✅ Secondary Analysis Flag: Present")
                        if details.get('processing_notes'):
                            print(f"✅ Processing Notes: {details['processing_notes']}")
                        
                        print()
                        print("📝 SECONDARY ANALYSIS QUALITY CHECK:")
                        
                        # Sample what_does content for readability
                        if what_does and what_does.get('content'):
                            sample_content = what_does['content'][:300] + "..." if len(what_does['content']) > 300 else what_does['content']
                            print(f"   What Does Sample: \"{sample_content}\"")
                        
                        # Check for congress.gov summary usage
                        if 'congress.gov summary' in details.get('processing_notes', ''):
                            print("✅ Congress.gov summary utilized")
                        else:
                            print("ℹ️  Processing notes: " + details.get('processing_notes', 'None'))
                        
                        # Count total citations
                        total_citations = 0
                        for section in [what_does, who_affects, why_matters, cost_impact]:
                            total_citations += len(section.get('citations', []))
                        for provision in key_provisions:
                            total_citations += len(provision.get('citations', []))
                        for timeline_item in timeline:
                            total_citations += len(timeline_item.get('citations', []))
                        
                        print(f"✅ Total citations: {total_citations}")
                        
                        if total_citations > 0:
                            print("🎉 SUCCESS: Secondary Analysis Service generated quality content with citations!")
                        else:
                            print("⚠️  WARNING: Limited citations found")
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ API JSON parse error: {e}")
                        print(f"Raw response: {api_result.stdout[:500]}...")
                else:
                    print(f"❌ API request failed: {api_result.stderr}")
            except subprocess.TimeoutExpired:
                print("❌ API request timeout")
            except Exception as e:
                print(f"❌ API test error: {e}")
        else:
            print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
            
        db.close()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_secondary_analysis())