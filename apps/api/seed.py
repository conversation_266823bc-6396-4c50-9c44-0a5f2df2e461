#!/usr/bin/env python3
"""
Administrative seeding tool for ModernAction.io

This script allows administrators to easily add new, fully processed bills
to the platform with AI-generated analysis.

Usage:
    python seed.py --bill HR5 --session 118
    python seed.py --bill S1234 --session 118 --create-campaign

This is the polished command-line tool specified in Sprint A Task 3.
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path

# Add the app directory to the Python path
app_dir = Path(__file__).parent / "app"
sys.path.insert(0, str(app_dir))

from sqlalchemy.orm import Session  # noqa: E402
from app.db.session import SessionLocal  # noqa: E402
from app.services.bill_data_service import BillDataService  # noqa: E402
from app.services.campaigns import CampaignService  # noqa: E402
from app.models.campaign import CampaignType, CampaignStatus  # noqa: E402
from app.schemas.campaign import CampaignCreate  # noqa: E402

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


def create_default_campaign(bill, db: Session) -> bool:
    """
    Create a default campaign for a newly ingested bill.

    Args:
        bill: The Bill object to create a campaign for
        db: Database session

    Returns:
        bool: True if campaign created successfully, False otherwise
    """
    try:
        campaign_service = CampaignService(db)

        # Create campaign data based on bill
        campaign_data = CampaignCreate(
            title=f"Take Action on {bill.title}",
            description=f"Contact your representatives about {bill.bill_number}: {bill.title}",
            short_description=bill.ai_summary[:200] if bill.ai_summary else "Take action on this important legislation",
            campaign_type=CampaignType.SUPPORT,  # Default to support
            status=CampaignStatus.ACTIVE,
            call_to_action=f"Contact your representatives to express your views on {bill.bill_number}",
            email_template=f"""Dear [REPRESENTATIVE_NAME],

I am writing to you as your constituent to express my views on {bill.bill_number}: {bill.title}.

{bill.ai_summary if bill.ai_summary else 'This legislation is important to me and my community.'}

I urge you to carefully consider this bill and [SUPPORT/OPPOSE] it based on the needs of our district.

Thank you for your time and service.

Sincerely,
[USER_NAME]
[USER_ADDRESS]""",
            bill_id=bill.id,
            is_featured=True,
            is_public=True,
            goal_actions=1000  # Default goal
        )

        campaign = campaign_service.create_campaign(campaign_data)
        logger.info(f"Created default campaign '{campaign.title}' with ID {campaign.id}")
        return True

    except Exception as e:
        logger.error(f"Failed to create default campaign: {e}")
        return False


async def seed_bill(bill_number: str, congress_session: int, create_campaign: bool = False) -> bool:
    """
    Seed a single bill into the database with TL;DR generation.

    Args:
        bill_number: Bill number like "HR5", "S1234"
        congress_session: Congress session number
        create_campaign: Whether to create a default campaign

    Returns:
        bool: True if successful, False otherwise
    """
    db = SessionLocal()

    try:
        logger.info(f"Starting bill seeding process for {bill_number}")

        # Initialize the BillDataService
        bill_service = BillDataService(db)

        # Perform the async ingestion (now includes TL;DR generation)
        bill = await bill_service.ingest_bill(bill_number, congress_session)

        if not bill:
            logger.error(f"Failed to ingest bill {bill_number}")
            return False

        logger.info(f"Successfully ingested bill {bill_number}")
        logger.info(f"  Title: {bill.title}")
        logger.info(f"  TL;DR: {bill.tldr[:100] if bill.tldr else 'Not generated'}...")
        logger.info(f"  AI Summary Length: {len(bill.ai_summary or '')}")
        logger.info(f"  Support Arguments: {len(bill.support_reasons or [])}")
        logger.info(f"  Opposition Arguments: {len(bill.oppose_reasons or [])}")
        logger.info(f"  Amend Arguments: {len(bill.amend_reasons or [])}")
        logger.info(f"  Tags: {bill.ai_tags}")

        # Create campaign if requested
        if create_campaign:
            logger.info("Creating default campaign...")
            campaign_created = create_default_campaign(bill, db)
            if campaign_created:
                logger.info("Default campaign created successfully")
            else:
                logger.warning("Failed to create default campaign")

        return True

    except Exception as e:
        logger.error(f"Bill seeding failed: {e}")
        if hasattr(db, 'rollback'):
            db.rollback()
        return False
    finally:
        db.close()


def health_check() -> bool:
    """
    Perform a health check of all required services.

    Returns:
        bool: True if all services are healthy, False otherwise
    """
    db = SessionLocal()

    try:
        logger.info("Performing health check...")

        bill_service = BillDataService(db)
        health_status = bill_service.health_check()

        logger.info(f"Overall Status: {health_status['status']}")

        for component, status in health_status['components'].items():
            component_status = status.get('status', 'unknown')
            logger.info(f"  {component}: {component_status}")

            if component_status == 'error':
                logger.error(f"    Error: {status.get('error', 'Unknown error')}")

        return health_status['status'] in ['healthy', 'degraded']

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return False
    finally:
        db.close()


def list_recent_bills(limit: int = 10) -> bool:
    """
    List recently ingested bills.

    Args:
        limit: Number of bills to show

    Returns:
        bool: True if successful, False otherwise
    """
    db = SessionLocal()

    try:
        from app.services.bills import BillService
        bill_service = BillService(db)

        bills = bill_service.get_bills(skip=0, limit=limit)

        if not bills:
            logger.info("No bills found in database")
            return True

        logger.info(f"Found {len(bills)} recent bills:")
        for bill in bills:
            logger.info(f"  {bill.bill_number}: {bill.title[:80]}...")
            logger.info(f"    Status: {bill.status}, Session: {bill.session_year}")
            logger.info(f"    AI Summary: {'Yes' if bill.ai_summary else 'No'}")
            logger.info("")

        return True

    except Exception as e:
        logger.error(f"Failed to list bills: {e}")
        return False
    finally:
        db.close()


def main():
    """Main entry point for the seeding tool"""
    parser = argparse.ArgumentParser(
        description="ModernAction.io Administrative Bill Seeding Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python seed.py --bill HR5 --session 118
  python seed.py --bill S1234 --session 118 --create-campaign
  python seed.py --health-check
  python seed.py --list-bills --limit 5
        """
    )

    # Bill ingestion arguments
    parser.add_argument(
        '--bill',
        type=str,
        help='Bill number to ingest (e.g., HR5, S1234)'
    )

    parser.add_argument(
        '--session',
        type=int,
        default=118,
        help='Congress session number (default: 118)'
    )

    parser.add_argument(
        '--create-campaign',
        action='store_true',
        help='Create a default campaign for the bill'
    )

    # Utility commands
    parser.add_argument(
        '--health-check',
        action='store_true',
        help='Perform a health check of all services'
    )

    parser.add_argument(
        '--list-bills',
        action='store_true',
        help='List recently ingested bills'
    )

    parser.add_argument(
        '--limit',
        type=int,
        default=10,
        help='Number of bills to list (default: 10)'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )

    args = parser.parse_args()

    # Configure logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Health check command
    if args.health_check:
        logger.info("=== ModernAction.io Health Check ===")
        success = health_check()
        sys.exit(0 if success else 1)

    # List bills command
    if args.list_bills:
        logger.info("=== Recent Bills ===")
        success = list_recent_bills(args.limit)
        sys.exit(0 if success else 1)

    # Bill ingestion command
    if args.bill:
        logger.info("=== ModernAction.io Bill Seeding Tool ===")
        logger.info(f"Ingesting bill: {args.bill}")
        logger.info(f"Congress session: {args.session}")
        logger.info(f"Create campaign: {args.create_campaign}")
        logger.info("")

        # Run the async seed_bill function
        success = asyncio.run(seed_bill(args.bill, args.session, args.create_campaign))

        if success:
            logger.info("✅ Bill seeding completed successfully!")
            logger.info("The bill is now available in the ModernAction.io platform with TL;DR!")
        else:
            logger.error("❌ Bill seeding failed!")
            sys.exit(1)
    else:
        # No command specified
        parser.print_help()
        logger.error("No command specified. Use --bill, --health-check, or --list-bills")
        sys.exit(1)


if __name__ == "__main__":
    main()
