# app/services/deep_analysis_service.py
import json
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from openai import AsyncOpenAI
from tenacity import retry, stop_after_attempt, wait_exponential
import logging

from .bill_chunking_service import BillChunk

logger = logging.getLogger(__name__)


@dataclass
class ChunkAnalysis:
    """Detailed analysis of a bill chunk"""
    chunk_id: str
    specific_actions: List[Dict[str, Any]]  # What this chunk requires/prohibits
    affected_parties: List[Dict[str, Any]]  # Who must comply
    enforcement_mechanisms: List[Dict[str, Any]]  # How it's enforced
    timelines: List[Dict[str, Any]]  # When things must happen
    funding_impacts: List[Dict[str, Any]]  # Financial implications
    legal_changes: List[Dict[str, Any]]  # Changes to existing law
    implementation_details: List[Dict[str, Any]]  # How to comply
    citations: List[Dict[str, Any]]  # Supporting quotes


class DeepAnalysisService:
    """Service for deep analysis of bill chunks"""
    
    def __init__(self, openai_api_key: str):
        self.client = AsyncOpenAI(api_key=openai_api_key)

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def analyze_chunk(self, chunk: BillChunk) -> ChunkAnalysis:
        """Perform deep analysis of a single bill chunk"""
        
        prompt = self._create_analysis_prompt(chunk)
        
        try:
            response = await self.client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=2000,
                temperature=0.1
            )
            
            content = response.choices[0].message.content.strip()
            analysis_data = self._parse_analysis_response(content)
            
            return ChunkAnalysis(
                chunk_id=chunk.id,
                specific_actions=analysis_data.get("specific_actions", []),
                affected_parties=analysis_data.get("affected_parties", []),
                enforcement_mechanisms=analysis_data.get("enforcement_mechanisms", []),
                timelines=analysis_data.get("timelines", []),
                funding_impacts=analysis_data.get("funding_impacts", []),
                legal_changes=analysis_data.get("legal_changes", []),
                implementation_details=analysis_data.get("implementation_details", []),
                citations=analysis_data.get("citations", [])
            )
            
        except Exception as e:
            logger.error(f"Failed to analyze chunk {chunk.id}: {e}")
            return self._create_fallback_analysis(chunk)

    def _create_analysis_prompt(self, chunk: BillChunk) -> str:
        """Create a comprehensive analysis prompt for MAXIMUM detail extraction"""
        return f"""
You are a world-class legislative analyst. Analyze this bill section with EXTREME precision and extract EVERY possible detail.

BILL SECTION: {chunk.title}
CHUNK TYPE: {chunk.chunk_type}
IMPORTANCE: {chunk.importance}
CONTENT: {chunk.content}

CRITICAL REQUIREMENTS FOR MAXIMUM DETAIL:
1. Extract EVERY requirement, prohibition, authorization, and procedure
2. Identify ALL affected parties, even if mentioned briefly
3. Find ALL deadlines, amounts, percentages, and specific numbers
4. Include EVERY enforcement mechanism, penalty, or consequence
5. Capture ALL implementation details and procedures
6. Extract ALL legal changes and amendments
7. Include PRECISE quotes (4-20 words each) for EVERY claim
8. Leave NO detail unexplored - comprehensive coverage is essential

Provide analysis in this EXACT JSON format with MAXIMUM detail:

{{
  "specific_actions": [
    {{
      "action": "Exact requirement, prohibition, or authorization in plain English",
      "who_must_act": "Specific entity that must comply (be very specific)",
      "what_they_must_do": "Concrete action required with all details",
      "when_required": "Any deadlines or timing requirements",
      "how_enforced": "How compliance is ensured or violations handled",
      "citations": [
        {{"quote": "exact text from bill supporting this action"}}
      ]
    }}
  ],
  "affected_parties": [
    {{
      "party": "Specific group affected (individuals, organizations, agencies)",
      "impact": "How they are specifically affected with all details",
      "obligations": "What they must do, cannot do, or are authorized to do",
      "rights_granted": "Any new rights or authorities granted",
      "restrictions_imposed": "Any limitations or restrictions placed",
      "citations": [
        {{"quote": "exact text mentioning this party"}}
      ]
    }}
  ],
  "enforcement_mechanisms": [
    {{
      "mechanism": "How compliance is enforced with all details",
      "penalty": "Specific consequences for non-compliance (fines, sanctions, etc.)",
      "enforcing_agency": "Who enforces this (specific agency or department)",
      "enforcement_process": "How enforcement proceedings work",
      "appeal_process": "Any appeal or review mechanisms",
      "citations": [
        {{"quote": "exact text about enforcement"}}
      ]
    }}
  ],
  "timelines": [
    {{
      "deadline": "Specific date, timeframe, or trigger event",
      "action_required": "What must be done by this deadline with all details",
      "responsible_party": "Who must meet this deadline (be specific)",
      "consequences_if_missed": "What happens if deadline is not met",
      "extensions_allowed": "Any provisions for extensions or delays",
      "citations": [
        {{"quote": "exact text about timing"}}
      ]
    }}
  ],
  "funding_impacts": [
    {{
      "impact_type": "funding increase/decrease/requirement/authorization",
      "amount": "Specific dollar amount, percentage, or formula if mentioned",
      "affected_entity": "Who receives/loses/manages funding (be specific)",
      "conditions": "All requirements to receive/maintain funding",
      "funding_source": "Where the money comes from",
      "duration": "How long funding lasts",
      "citations": [
        {{"quote": "exact text about funding"}}
      ]
    }}
  ],
  "legal_changes": [
    {{
      "law_being_changed": "Specific law, regulation, or code being modified",
      "type_of_change": "amendment/addition/repeal/redesignation",
      "specific_change": "Exact change being made with all details",
      "effective_date": "When the change takes effect",
      "transition_provisions": "Any transition or grandfather clauses",
      "citations": [
        {{"quote": "exact text about the legal change"}}
      ]
    }}
  ],
  "implementation_details": [
    {{
      "requirement": "What must be implemented with all details",
      "who_implements": "Specific entity responsible for implementation",
      "how_to_comply": "Detailed steps to comply with all procedures",
      "resources_needed": "All resources required (funding, staff, systems)",
      "timeline_for_implementation": "When implementation must occur",
      "success_metrics": "How success is measured or evaluated",
      "oversight_mechanisms": "How implementation is monitored",
      "citations": [
        {{"quote": "exact text about implementation"}}
      ]
    }}
  ],
  "key_definitions": [
    {{
      "term": "Important term being defined",
      "definition": "How the bill defines this term with all details",
      "significance": "Why this definition matters and its implications",
      "scope": "What is included and excluded from this definition",
      "citations": [
        {{"quote": "exact definition text"}}
      ]
    }}
  ],
  "reporting_requirements": [
    {{
      "who_reports": "Specific entity required to report",
      "what_to_report": "Detailed information that must be reported",
      "to_whom": "Who receives the report",
      "frequency": "How often reports are required",
      "format_requirements": "Any specific format or content requirements",
      "consequences_for_non_reporting": "Penalties for failing to report",
      "citations": [
        {{"quote": "exact text about reporting"}}
      ]
    }}
  ],
  "exceptions_and_waivers": [
    {{
      "exception_type": "What can be waived or excepted",
      "who_can_grant": "Authority that can grant exceptions",
      "criteria_for_granting": "Requirements to qualify for exception",
      "duration_of_exception": "How long exceptions last",
      "review_process": "How exceptions are reviewed or renewed",
      "citations": [
        {{"quote": "exact text about exceptions"}}
      ]
    }}
  ]
}}

CRITICAL: Extract EVERY detail, no matter how small. Include ALL requirements, procedures, deadlines, amounts, and affected parties. Comprehensive coverage is absolutely essential - leave nothing out!

JSON Response:
"""

    def _parse_analysis_response(self, content: str) -> Dict[str, Any]:
        """Parse the AI response into structured data"""
        try:
            # Extract JSON from response
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            json_str = content[start_idx:end_idx]
            
            return json.loads(json_str)
            
        except Exception as e:
            logger.warning(f"Failed to parse analysis JSON: {e}")
            return {}

    def _create_fallback_analysis(self, chunk: BillChunk) -> ChunkAnalysis:
        """Create a basic analysis when AI fails"""
        return ChunkAnalysis(
            chunk_id=chunk.id,
            specific_actions=[{
                "action": f"Analysis of {chunk.title} is being processed",
                "who_must_act": "Unknown",
                "what_they_must_do": "Analysis pending",
                "citations": []
            }],
            affected_parties=[],
            enforcement_mechanisms=[],
            timelines=[],
            funding_impacts=[],
            legal_changes=[],
            implementation_details=[],
            citations=[]
        )

    async def analyze_multiple_chunks(self, chunks: List[BillChunk]) -> List[ChunkAnalysis]:
        """Analyze multiple chunks in parallel"""
        import asyncio
        
        # Analyze chunks in batches to avoid rate limits
        batch_size = 3
        analyses = []
        
        for i in range(0, len(chunks), batch_size):
            batch = chunks[i:i + batch_size]
            batch_tasks = [self.analyze_chunk(chunk) for chunk in batch]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"Chunk analysis failed: {result}")
                    # Create fallback analysis
                    analyses.append(self._create_fallback_analysis(batch[0]))
                else:
                    analyses.append(result)
            
            # Small delay between batches
            await asyncio.sleep(1)
        
        return analyses

    def extract_key_mechanisms(self, analyses: List[ChunkAnalysis]) -> List[Dict[str, Any]]:
        """Extract the most important mechanisms from all analyses"""
        key_mechanisms = []
        
        for analysis in analyses:
            for action in analysis.specific_actions:
                # Score importance based on keywords
                importance_score = self._score_mechanism_importance(action)
                
                if importance_score >= 3:  # Threshold for "key" mechanisms
                    key_mechanisms.append({
                        "mechanism": action["action"],
                        "affected_parties": action["who_must_act"],
                        "implementation": action["what_they_must_do"],
                        "importance_score": importance_score,
                        "citations": action["citations"],
                        "source_chunk": analysis.chunk_id
                    })
        
        # Sort by importance
        key_mechanisms.sort(key=lambda x: x["importance_score"], reverse=True)
        
        return key_mechanisms[:10]  # Top 10 most important mechanisms

    def _score_mechanism_importance(self, action: Dict[str, Any]) -> int:
        """Score the importance of a mechanism"""
        score = 0
        action_text = action["action"].lower()
        
        # High importance keywords
        high_keywords = ["requires", "prohibits", "mandates", "establishes", "funding", "penalty"]
        score += sum(2 for keyword in high_keywords if keyword in action_text)
        
        # Medium importance keywords
        medium_keywords = ["must", "shall", "compliance", "violation", "enforcement"]
        score += sum(1 for keyword in medium_keywords if keyword in action_text)
        
        # Bonus for affecting multiple parties
        if "school" in action_text and "parent" in action_text:
            score += 2
        
        return score
