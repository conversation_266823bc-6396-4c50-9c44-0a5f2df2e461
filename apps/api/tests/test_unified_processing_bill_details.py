import os
import json
import pytest
from fastapi.testclient import TestClient

from app.main import app
from app.db.database import get_db
from app.services.unified_bill_processing_service import UnifiedBillProcessingService


@pytest.mark.integration
@pytest.mark.database
def test_unified_processing_persists_bill_details(test_client: TestClient, test_db_session):
    # Arrange: ensure OpenAI appears enabled to the service (tests use mock keys)
    os.environ['OPENAI_API_KEY'] = os.environ.get('OPENAI_API_KEY', 'test-key')

    # Override AIService methods to return deterministic content
    from app.services.ai_service import AIService

    async def fake_generate_detailed(bill_text: str, meta: dict) -> dict:
        return {
            'hero_summary': 'TLDR: Parents Bill of Rights Act overview',
            'overview': {
                'what_does': {'content': 'Expands parental rights in education', 'citations': []},
                'who_affects': {'content': 'Parents, students, LEAs', 'citations': []},
                'why_matters': {'content': 'Transparency and involvement', 'citations': []},
                'key_provisions': [{'content': 'Disclosure of curriculum'}],
                'cost_impact': {'content': 'Minimal'},
                'timeline': [{'content': 'Takes effect next fiscal year'}],
            },
            'positions': {
                'support_reasons': [{'claim': 'Increases transparency', 'justification': '', 'citations': []}],
                'oppose_reasons': [{'claim': 'May burden schools', 'justification': '', 'citations': []}],
                'amend_reasons': [{'claim': 'Clarify privacy rules', 'justification': '', 'citations': []}],
            },
            'message_templates': {},
            'tags': ['education'],
            'other_details': [],
        }

    async def fake_ai_analysis(bill_text: str, meta: dict) -> dict:
        return {
            'ai_summary': 'AI summary here',
            'tldr': 'TL;DR here',
            'support_reasons': ['reason 1','reason 2'],
            'oppose_reasons': ['o1'],
            'amend_reasons': ['a1'],
            'message_templates': {},
            'tags': ['education']
        }

    AIService.generate_detailed_bill_analysis = fake_generate_detailed  # type: ignore
    AIService.process_bill_complete = lambda self, bill_text, meta: fake_ai_analysis(bill_text, meta)  # type: ignore

    # Act: call admin endpoint
    payload = {"bill_number":"HR5","session":"118","environment":"test"}
    res = test_client.post('/api/v1/admin/process-bill-details', json=payload)
    assert res.status_code == 200, res.text
    data = res.json()
    assert data.get('success') is True

    # Assert: details exist and have content
    slug_res = test_client.get('/api/v1/bills/details/by-slug/hr5-118')
    assert slug_res.status_code == 200, slug_res.text
    details = slug_res.json()

    assert details['hero_summary'].startswith('TLDR')
    assert details['overview']['what_does']['content']
    assert isinstance(details['source_index'], list) and len(details['source_index']) > 0

    # Citations should be attached to at least one section by enrichment
    wd_cites = details['overview']['what_does'].get('citations') or []
    # Not guaranteed deterministic, but should be a list
    assert isinstance(wd_cites, list)

