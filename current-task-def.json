{"taskDefinitionArn": "arn:aws:ecs:us-east-1:308755113449:task-definition/modernaction-api-staging:39", "containerDefinitions": [{"name": "web", "image": "308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-api-staging:d5a0fa76bb82cb3a0cfe6a078fa064d4a5e0cdf2", "cpu": 0, "portMappings": [{"containerPort": 8000, "hostPort": 8000, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DB_NAME", "value": "modernaction"}, {"name": "DB_HOST", "value": "modernaction-staging-modernactiondatabasefdd241de-giohrnseld4l.csdoaiogadw5.us-east-1.rds.amazonaws.com"}, {"name": "DB_PORT", "value": "5432"}, {"name": "ENVIRONMENT", "value": "staging"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "SECRET_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:JwtSecretB8834B39-L7riLTT0OUAn:jwt_secret::"}, {"name": "DB_USERNAME", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:DatabaseCredentials8547B3E7-7zaDeXDnFrx0:username::"}, {"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:DatabaseCredentials8547B3E7-7zaDeXDnFrx0:password::"}, {"name": "OPENSTATES_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:ApiKeysSecret5A58C5CD-crnudnHX1i32:OPENSTATES_API_KEY::"}, {"name": "GOOGLE_CIVIC_INFO_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:ApiKeysSecret5A58C5CD-crnudnHX1i32:GOOGLE_CIVIC_INFO_API_KEY::"}, {"name": "CONGRESS_GOV_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:ApiKeysSecret5A58C5CD-crnudnHX1i32:CONGRESS_GOV_API_KEY::"}, {"name": "OPENAI_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:308755113449:secret:ApiKeysSecret5A58C5CD-crnudnHX1i32:OPENAI_API_KEY::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/modernaction-api-staging", "awslogs-create-group": "true", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8000/api/v1/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}, "systemControls": []}], "family": "modernaction-api-staging", "taskRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskRole30FC0FBB-YQ5wIqK35bv0", "executionRoleArn": "arn:aws:iam::308755113449:role/modernaction-staging-TaskExecutionRole250D2532-8J2CBwsOvvxh", "networkMode": "awsvpc", "revision": 39, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "ecs.capability.execution-role-awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "ecs.capability.secrets.asm.environment-variables"}, {"name": "com.amazonaws.ecs.capability.task-iam-role"}, {"name": "ecs.capability.container-health-check"}, {"name": "ecs.capability.execution-role-ecr-pull"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.29"}], "placementConstraints": [], "compatibilities": ["EC2", "FARGATE"], "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "registeredAt": "2025-08-07T00:13:38.151000-04:00", "registeredBy": "arn:aws:iam::308755113449:user/github-actions-deployer"}