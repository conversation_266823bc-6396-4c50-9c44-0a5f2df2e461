'use client';

import Image from 'next/image';

interface OfficialImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
}

export const OfficialImage: React.FC<OfficialImageProps> = ({
  src,
  alt,
  width = 128,
  height = 128,
  className = ''
}) => {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      onError={(e) => {
        (e.target as HTMLImageElement).src = '/images/default-official.png';
      }}
    />
  );
};

export default OfficialImage;