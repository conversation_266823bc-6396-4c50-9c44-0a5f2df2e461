"""
Quality Metrics Tracking Service - Phase 2 Quality Monitoring
Tracks quality metrics over time and provides insights for maintaining HR5-118 standards
"""

import json
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from app.db.database import SessionLocal
from app.models.ai_usage import AIUsageLog

logger = logging.getLogger(__name__)

@dataclass
class QualitySnapshot:
    """Snapshot of quality metrics at a point in time"""
    timestamp: datetime
    bill_id: str
    bill_title: str
    overall_quality_score: float
    quality_level: str
    specificity_score: float
    evidence_grounding_score: float
    comprehensiveness_score: float
    clarity_score: float
    actionability_score: float
    total_cost: float
    evidence_spans_count: int
    evidence_quality_avg: float
    improvement_applied: bool
    issues_count: int
    primary_issues: List[str]

@dataclass
class QualityTrend:
    """Quality trend analysis over time"""
    metric_name: str
    current_value: float
    previous_value: float
    change_percentage: float
    trend_direction: str  # 'improving', 'declining', 'stable'
    days_analyzed: int

@dataclass
class QualityInsights:
    """Quality insights and recommendations"""
    overall_assessment: str
    key_trends: List[QualityTrend]
    top_issues: List[str]
    recommendations: List[str]
    hr5118_compliance_rate: float
    cost_quality_efficiency: float

class QualityMetricsTrackingService:
    """
    Tracks and analyzes quality metrics to maintain HR5-118 standards
    Provides insights and alerts for quality degradation
    """
    
    def __init__(self):
        self.quality_snapshots: List[QualitySnapshot] = []
        self.hr5118_threshold = 0.8  # Minimum score for HR5-118 compliance
        self.tracking_window_days = 30  # Days to track for trends
    
    def record_quality_metrics(self, bill_id: str, bill_title: str, 
                             quality_metrics: Dict[str, Any],
                             evidence_quality: Dict[str, Any],
                             cost_breakdown: Dict[str, Any]) -> None:
        """Record quality metrics for a bill analysis"""
        
        try:
            snapshot = QualitySnapshot(
                timestamp=datetime.utcnow(),
                bill_id=bill_id,
                bill_title=bill_title,
                overall_quality_score=quality_metrics.get('overall_score', 0.0),
                quality_level=quality_metrics.get('quality_level', 'unknown'),
                specificity_score=quality_metrics.get('specificity_score', 0.0),
                evidence_grounding_score=quality_metrics.get('evidence_grounding_score', 0.0),
                comprehensiveness_score=quality_metrics.get('comprehensiveness_score', 0.0),
                clarity_score=quality_metrics.get('clarity_score', 0.0),
                actionability_score=quality_metrics.get('actionability_score', 0.0),
                total_cost=cost_breakdown.get('total_cost', 0.0),
                evidence_spans_count=evidence_quality.get('total_spans_validated', 0),
                evidence_quality_avg=evidence_quality.get('average_quality_score', 0.0),
                improvement_applied=evidence_quality.get('quality_improvement_applied', False),
                issues_count=len(quality_metrics.get('issues', [])),
                primary_issues=quality_metrics.get('issues', [])[:3]  # Top 3 issues
            )
            
            self.quality_snapshots.append(snapshot)
            
            # Log to database for persistence
            self._log_quality_metrics_to_db(snapshot)
            
            # Check for quality alerts
            self._check_quality_alerts(snapshot)
            
            logger.info(f"📊 Quality metrics recorded for {bill_title}: {snapshot.overall_quality_score:.2f} ({snapshot.quality_level})")
            
        except Exception as e:
            logger.error(f"Failed to record quality metrics: {e}")
    
    def get_quality_trends(self, days: int = 7) -> QualityInsights:
        """Get quality trends and insights over specified days"""
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        recent_snapshots = [s for s in self.quality_snapshots if s.timestamp >= cutoff_date]
        
        if len(recent_snapshots) < 2:
            return QualityInsights(
                overall_assessment="Insufficient data for trend analysis",
                key_trends=[],
                top_issues=[],
                recommendations=["Analyze more bills to establish quality trends"],
                hr5118_compliance_rate=0.0,
                cost_quality_efficiency=0.0
            )
        
        # Calculate trends for key metrics
        key_trends = []
        metrics_to_track = [
            ('overall_quality_score', 'Overall Quality'),
            ('specificity_score', 'Specificity'),
            ('evidence_grounding_score', 'Evidence Grounding'),
            ('comprehensiveness_score', 'Comprehensiveness'),
            ('clarity_score', 'Clarity'),
            ('actionability_score', 'Actionability')
        ]
        
        for attr, name in metrics_to_track:
            trend = self._calculate_trend(recent_snapshots, attr, name, days)
            if trend:
                key_trends.append(trend)
        
        # Calculate HR5-118 compliance rate
        hr5118_compliant = sum(1 for s in recent_snapshots if s.overall_quality_score >= self.hr5118_threshold)
        hr5118_compliance_rate = hr5118_compliant / len(recent_snapshots) if recent_snapshots else 0
        
        # Calculate cost-quality efficiency
        total_cost = sum(s.total_cost for s in recent_snapshots)
        avg_quality = sum(s.overall_quality_score for s in recent_snapshots) / len(recent_snapshots)
        cost_quality_efficiency = avg_quality / (total_cost / len(recent_snapshots)) if total_cost > 0 else 0
        
        # Identify top issues
        all_issues = []
        for snapshot in recent_snapshots:
            all_issues.extend(snapshot.primary_issues)
        
        issue_counts = {}
        for issue in all_issues:
            key = issue[:50]  # First 50 chars as key
            issue_counts[key] = issue_counts.get(key, 0) + 1
        
        top_issues = [issue for issue, count in sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)][:5]
        
        # Generate overall assessment
        overall_assessment = self._generate_overall_assessment(
            recent_snapshots, hr5118_compliance_rate, key_trends
        )
        
        # Generate recommendations
        recommendations = self._generate_quality_recommendations(
            recent_snapshots, key_trends, hr5118_compliance_rate, cost_quality_efficiency
        )
        
        return QualityInsights(
            overall_assessment=overall_assessment,
            key_trends=key_trends,
            top_issues=top_issues,
            recommendations=recommendations,
            hr5118_compliance_rate=hr5118_compliance_rate,
            cost_quality_efficiency=cost_quality_efficiency
        )
    
    def get_quality_dashboard_data(self) -> Dict[str, Any]:
        """Get quality dashboard data for monitoring"""
        
        recent_snapshots = self.quality_snapshots[-20:]  # Last 20 analyses
        
        if not recent_snapshots:
            return {
                'total_analyses': 0,
                'avg_quality_score': 0.0,
                'hr5118_compliance_rate': 0.0,
                'quality_trends': [],
                'cost_efficiency': 0.0,
                'recent_issues': []
            }
        
        # Calculate dashboard metrics
        total_analyses = len(recent_snapshots)
        avg_quality_score = sum(s.overall_quality_score for s in recent_snapshots) / total_analyses
        
        hr5118_compliant = sum(1 for s in recent_snapshots if s.overall_quality_score >= self.hr5118_threshold)
        hr5118_compliance_rate = hr5118_compliant / total_analyses
        
        # Quality score distribution
        quality_distribution = {
            'gold_standard': sum(1 for s in recent_snapshots if s.overall_quality_score >= 0.9),
            'high_quality': sum(1 for s in recent_snapshots if 0.8 <= s.overall_quality_score < 0.9),
            'acceptable': sum(1 for s in recent_snapshots if 0.65 <= s.overall_quality_score < 0.8),
            'needs_improvement': sum(1 for s in recent_snapshots if 0.4 <= s.overall_quality_score < 0.65),
            'poor': sum(1 for s in recent_snapshots if s.overall_quality_score < 0.4)
        }
        
        # Cost efficiency
        total_cost = sum(s.total_cost for s in recent_snapshots)
        cost_efficiency = avg_quality_score / (total_cost / total_analyses) if total_cost > 0 else 0
        
        # Recent issues
        recent_issues = []
        for snapshot in recent_snapshots[-5:]:  # Last 5 analyses
            recent_issues.extend(snapshot.primary_issues)
        
        return {
            'total_analyses': total_analyses,
            'avg_quality_score': avg_quality_score,
            'hr5118_compliance_rate': hr5118_compliance_rate,
            'quality_distribution': quality_distribution,
            'cost_efficiency': cost_efficiency,
            'recent_issues': list(set(recent_issues))[:10],  # Unique issues, max 10
            'improvement_rate': sum(1 for s in recent_snapshots if s.improvement_applied) / total_analyses,
            'avg_evidence_quality': sum(s.evidence_quality_avg for s in recent_snapshots) / total_analyses,
            'avg_cost_per_analysis': total_cost / total_analyses if total_analyses > 0 else 0
        }
    
    def _calculate_trend(self, snapshots: List[QualitySnapshot], 
                        attribute: str, metric_name: str, days: int) -> Optional[QualityTrend]:
        """Calculate trend for a specific metric"""
        
        if len(snapshots) < 2:
            return None
        
        # Sort by timestamp
        sorted_snapshots = sorted(snapshots, key=lambda x: x.timestamp)
        
        # Get current and previous values
        current_values = [getattr(s, attribute) for s in sorted_snapshots[-5:]]  # Last 5
        previous_values = [getattr(s, attribute) for s in sorted_snapshots[:-5]] if len(sorted_snapshots) > 5 else [getattr(s, attribute) for s in sorted_snapshots[:1]]
        
        current_avg = sum(current_values) / len(current_values)
        previous_avg = sum(previous_values) / len(previous_values) if previous_values else current_avg
        
        # Calculate change
        change_percentage = ((current_avg - previous_avg) / previous_avg * 100) if previous_avg != 0 else 0
        
        # Determine trend direction
        if abs(change_percentage) < 2:  # Less than 2% change
            trend_direction = 'stable'
        elif change_percentage > 0:
            trend_direction = 'improving'
        else:
            trend_direction = 'declining'
        
        return QualityTrend(
            metric_name=metric_name,
            current_value=current_avg,
            previous_value=previous_avg,
            change_percentage=change_percentage,
            trend_direction=trend_direction,
            days_analyzed=days
        )
    
    def _generate_overall_assessment(self, snapshots: List[QualitySnapshot], 
                                   compliance_rate: float, trends: List[QualityTrend]) -> str:
        """Generate overall quality assessment"""
        
        avg_score = sum(s.overall_quality_score for s in snapshots) / len(snapshots)
        
        if avg_score >= 0.9 and compliance_rate >= 0.8:
            assessment = "Quality is excellent and consistently meeting HR5-118 gold standards"
        elif avg_score >= 0.8 and compliance_rate >= 0.7:
            assessment = "Quality is good with strong HR5-118 compliance"
        elif avg_score >= 0.65 and compliance_rate >= 0.5:
            assessment = "Quality is acceptable but has room for improvement"
        elif avg_score >= 0.4:
            assessment = "Quality needs significant improvement to meet standards"
        else:
            assessment = "Quality is below acceptable levels and requires immediate attention"
        
        # Add trend information
        improving_trends = [t for t in trends if t.trend_direction == 'improving']
        declining_trends = [t for t in trends if t.trend_direction == 'declining']
        
        if len(improving_trends) > len(declining_trends):
            assessment += ". Quality trends are generally improving."
        elif len(declining_trends) > len(improving_trends):
            assessment += ". Quality trends show concerning decline."
        else:
            assessment += ". Quality trends are stable."
        
        return assessment
    
    def _generate_quality_recommendations(self, snapshots: List[QualitySnapshot],
                                        trends: List[QualityTrend],
                                        compliance_rate: float,
                                        cost_efficiency: float) -> List[str]:
        """Generate quality improvement recommendations"""
        
        recommendations = []
        
        # HR5-118 compliance recommendations
        if compliance_rate < 0.7:
            recommendations.append("Increase focus on HR5-118 compliance - less than 70% of analyses meet gold standard")
        
        # Trend-based recommendations
        for trend in trends:
            if trend.trend_direction == 'declining' and trend.change_percentage < -5:
                recommendations.append(f"Address declining {trend.metric_name} - down {abs(trend.change_percentage):.1f}% recently")
        
        # Cost efficiency recommendations
        if cost_efficiency < 50:  # Quality per dollar threshold
            recommendations.append("Improve cost efficiency - quality per dollar is below optimal range")
        
        # Issue-based recommendations
        all_issues = []
        for snapshot in snapshots:
            all_issues.extend(snapshot.primary_issues)
        
        common_issues = {}
        for issue in all_issues:
            key = issue[:30]  # First 30 chars
            common_issues[key] = common_issues.get(key, 0) + 1
        
        top_issue = max(common_issues.items(), key=lambda x: x[1]) if common_issues else None
        if top_issue and top_issue[1] > len(snapshots) * 0.3:  # Appears in >30% of analyses
            recommendations.append(f"Address recurring issue: {top_issue[0]}...")
        
        # Evidence quality recommendations
        avg_evidence_quality = sum(s.evidence_quality_avg for s in snapshots) / len(snapshots)
        if avg_evidence_quality < 0.7:
            recommendations.append("Improve evidence extraction quality - current average below target")
        
        return recommendations[:5]  # Limit to top 5 recommendations
    
    def _check_quality_alerts(self, snapshot: QualitySnapshot) -> None:
        """Check for quality alerts and log warnings"""
        
        alerts = []
        
        # Critical quality alert
        if snapshot.overall_quality_score < 0.4:
            alerts.append(f"CRITICAL: Quality score {snapshot.overall_quality_score:.2f} is below acceptable threshold")
        
        # HR5-118 compliance alert
        if snapshot.overall_quality_score < self.hr5118_threshold:
            alerts.append(f"WARNING: Analysis does not meet HR5-118 standard ({snapshot.overall_quality_score:.2f} < {self.hr5118_threshold})")
        
        # Evidence quality alert
        if snapshot.evidence_quality_avg < 0.6:
            alerts.append(f"WARNING: Evidence quality is low ({snapshot.evidence_quality_avg:.2f})")
        
        # High cost with low quality alert
        if snapshot.total_cost > 0.20 and snapshot.overall_quality_score < 0.7:
            alerts.append(f"ALERT: High cost (${snapshot.total_cost:.3f}) with suboptimal quality ({snapshot.overall_quality_score:.2f})")
        
        for alert in alerts:
            logger.warning(f"🚨 Quality Alert for {snapshot.bill_title}: {alert}")
    
    def _log_quality_metrics_to_db(self, snapshot: QualitySnapshot) -> None:
        """Log quality metrics to database for persistence"""
        
        try:
            db = SessionLocal()
            try:
                # Create quality metrics log entry
                quality_log = AIUsageLog(
                    id=str(uuid.uuid4()),
                    operation_type="quality_tracking",
                    operation_subtype="quality_snapshot",
                    bill_id=snapshot.bill_id,
                    model_name="quality_validator",
                    provider="internal",
                    prompt_tokens=0,
                    completion_tokens=0,
                    total_tokens=0,
                    prompt_cost=0.0,
                    completion_cost=0.0,
                    total_cost=snapshot.total_cost,
                    response_time_ms=0.0,
                    success=True,
                    prompt_length=0,
                    response_length=0,
                    metadata=json.dumps(asdict(snapshot), default=str)  # Store full snapshot as metadata
                )
                
                db.add(quality_log)
                db.commit()
                
                logger.debug(f"✅ Quality metrics logged to database for {snapshot.bill_title}")
                
            except Exception as e:
                logger.error(f"Failed to log quality metrics to database: {e}")
                db.rollback()
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Failed to create database session for quality logging: {e}")

# Global instance
quality_metrics_tracker = QualityMetricsTrackingService()

def get_quality_metrics_tracker() -> QualityMetricsTrackingService:
    """Get the global quality metrics tracking service instance"""
    return quality_metrics_tracker