import { test, expect } from '@playwright/test';

/**
 * Bill Details Navigation E2E
 * - Opens a campaign-like journey
 * - Opens Action Modal, clicks "Learn more"
 * - Lands on /bills/hr5-118#[anchor]
 * - Clicks a citation link and verifies scroll to the source anchor
 */

test.describe('Bill Details - Action to Details to Citation', () => {
  test.beforeEach(async ({ page }) => {
    // Mock campaigns page minimal content to reach an action modal quickly
    await page.route('**/api/v1/campaigns*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([{ id: 'cmp-hr5', title: 'Support HR5', status: 'active' }]),
      });
    });

    // Mock bill details (hr5-118)
    await page.route('**/api/v1/bills/details/by-slug/*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'bill_123',
          bill_id: 'bill_123',
          seo_slug: 'hr5-118',
          seo_title: 'Healthcare Access Act - Bill Details',
          hero_summary: 'Summary',
          needs_human_review: true,
          metrics: { coverage_ratio: 0.7, unverified_count: 1 },
          overview: {
            what_does: {
              content: 'Expands Medicaid eligibility...',
              citations: [
                { quote: 'Expands Medicaid eligibility', start_offset: 0, end_offset: 20, heading: 'SEC. 101', anchor_id: 'sec-1' }
              ]
            }
          },
          positions: {
            support_reasons: [
              {
                claim: 'Expands coverage',
                justification: 'More people covered',
                citations: [
                  { quote: '12 million previously uninsured', start_offset: 50, end_offset: 80, heading: 'SEC. 101', anchor_id: 'sec-1' }
                ]
              }
            ]
          },
          source_index: [
            { heading: 'SEC. 101', start_offset: 0, end_offset: 100, anchor_id: 'sec-1' }
          ]
        }),
      });
    });

    // Mock bills list used by homepage/cards
    await page.route('**/api/v1/bills?*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          { id: 'bill_123', title: 'Healthcare Access Act', bill_number: 'H.R.5', session_year: 118, status: 'introduced', chamber: 'house', is_featured: true, priority_score: 90, seo_slug: 'hr5-118' }
        ]),
      });
    });

    // Mock bill action data to ensure modal renders details section and learn-more links
    await page.route('**/api/v1/bills/*/action-data', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          bill_id: 'bill_123',
          title: 'Healthcare Access Act',
          bill_number: 'H.R.5',
          ai_summary: 'AI summary',
          support_reasons: ['Reason A', 'Reason B'],
          oppose_reasons: ['Oppose A'],
          amend_reasons: ['Amend A'],
          ai_tags: ['healthcare'],
          message_templates: {},
        }),
      });
    });
  });

  test('from action modal to details to citation anchor', async ({ page }) => {
    // Start at homepage and wait for bill cards
    await page.goto('/');
    await page.waitForSelector('[data-testid="bill-card"]');

    // Open the bill action modal
    const firstCard = page.locator('[data-testid="bill-card"]').first();
    await firstCard.click();
    await expect(page.locator('[data-testid="bill-action-modal"]')).toBeVisible();

    // Click Learn more
    const learnMore = page.locator('[data-testid="learn-more-link"]').first();
    await expect(learnMore).toBeVisible();
    // Stay in same tab
    await learnMore.evaluate((el: HTMLAnchorElement) => el.removeAttribute('target'));
    const targetHref = await learnMore.getAttribute('href');
    expect(targetHref).toMatch(/\/bills\/hr5-118#/);
    await learnMore.click();

    // Landed on details page with anchor
    await expect(page).toHaveURL(/\/bills\/hr5-118/);
    // Find a citation in Support Reasons area (or any citation) and click its link
    const citationLink = page.locator('[data-testid="citation-link"]').first();
    await expect(citationLink).toBeVisible();
    const href = await citationLink.getAttribute('href');
    expect(href).toMatch(/#sec-\d+/);
    const anchor = href!.slice(href!.indexOf('#') + 1);

    // Click and verify scroll/visibility of source anchor
    await citationLink.click();
    const target = page.locator(`#${anchor}`);
    await expect(target).toBeVisible();
    await expect(target).toBeInViewport();
  });
});

