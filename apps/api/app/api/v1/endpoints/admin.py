# app/api/v1/endpoints/admin.py
"""
Admin endpoints for testing and management
"""

from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel
import asyncio
import logging

logger = logging.getLogger(__name__)

from app.db.database import get_db
from app.services.congress_gov_api import CongressGovAP<PERSON>
from app.services.ai import summarize_bill
from app.services.ai_service import AIService
from app.services.enhanced_bill_service import EnhancedBillService
from app.models.bill import Bill, BillType, BillStatus
from app.models.bill_values import BillValuesAnalysis, BillValuesTag
from app.schemas.bill import BillResponse, BillCreate
from app.services.bill_values_analysis_service import BillValuesAnalysisService
import time
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

router = APIRouter()

class ProcessBillDetailsRequest(BaseModel):
    bill_number: str
    session: Union[int, str]
    environment: Optional[str] = "development"
    use_enhanced_analysis: Optional[bool] = True  # Default to new enriched analysis

@router.post("/process-bill-details")
async def process_bill_details(payload: ProcessBillDetailsRequest, db: Session = Depends(get_db)):
    """Run the complete unified pipeline for a single bill and persist BillDetails.
    Expects: {"bill_number":"HR5", "session":"118"}
    """
    from app.services.unified_bill_processing_service import UnifiedBillProcessingService

    try:
        congress_session = int(payload.session) if isinstance(payload.session, str) else payload.session
        logger.info(f"🔍 ADMIN DEBUG: About to call UnifiedBillProcessingService")
        logger.info(f"🔍 ADMIN DEBUG: payload.use_enhanced_analysis={payload.use_enhanced_analysis}")
        service = UnifiedBillProcessingService(db)
        logger.info(f"🔍 ADMIN DEBUG: Service created, calling process_bill_by_number")
        result = await service.process_bill_by_number(
            bill_number=payload.bill_number,
            congress_session=congress_session,
            environment=payload.environment or "development",
            use_enhanced_analysis=payload.use_enhanced_analysis
        )
        logger.info(f"🔍 ADMIN DEBUG: process_bill_by_number returned: {result.get('success')}")
        if not result.get("success"):
            raise HTTPException(status_code=400, detail=result)
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to process bill {payload.bill_number}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to process bill: {e}")


@router.post("/batch-process-bill-details")
async def batch_process_bill_details(
    bill_ids: List[str],
    environment: str = "development", 
    db: Session = Depends(get_db)
):
    """
    Run process-bill-details workflow on multiple existing bills by their IDs.
    
    This endpoint takes a list of bill IDs from the database and runs the 
    complete AI analysis and bill details generation for each one.
    
    Args:
        bill_ids: List of bill IDs to process
        environment: Environment context (development, staging, production)
        
    Returns:
        Processing results for each bill
    """
    from app.services.unified_bill_processing_service import UnifiedBillProcessingService
    from app.models.bill import Bill
    
    try:
        results = {
            "success": True,
            "environment": environment,
            "total_requested": len(bill_ids),
            "processed_bills": [],
            "failed_bills": [],
            "skipped_bills": [],
            "errors": []
        }
        
        service = UnifiedBillProcessingService(db)
        
        for bill_id in bill_ids:
            try:
                # Get the bill from database
                bill = db.query(Bill).filter(Bill.id == bill_id).first()
                if not bill:
                    results["failed_bills"].append(bill_id)
                    results["errors"].append(f"{bill_id}: Bill not found")
                    continue
                
                # Check if already processed
                if bill.details and len(bill.details) > 0:
                    results["skipped_bills"].append({
                        "bill_id": bill_id,
                        "bill_number": bill.bill_number,
                        "reason": "Already has bill details"
                    })
                    continue
                
                # Run the manual AI analysis
                logger.info(f"Running process-bill-details workflow for {bill.bill_number}")
                result = await service._run_manual_ai_analysis(bill)
                
                if result.get("success"):
                    results["processed_bills"].append({
                        "bill_id": bill_id,
                        "bill_number": bill.bill_number,
                        "title": bill.title,
                        "cost": result.get("cost", 0),
                        "processing_time": result.get("processing_time", 0)
                    })
                else:
                    results["failed_bills"].append(bill_id)
                    results["errors"].append(f"{bill.bill_number}: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                results["failed_bills"].append(bill_id)
                results["errors"].append(f"{bill_id}: {str(e)}")
                logger.error(f"Error processing bill {bill_id}: {e}")
        
        # Update success status
        if len(results["failed_bills"]) == len(bill_ids):
            results["success"] = False
            results["message"] = "All bills failed to process"
        elif len(results["failed_bills"]) > 0:
            results["message"] = f"Processed {len(results['processed_bills'])} of {len(bill_ids)} bills with {len(results['failed_bills'])} failures"
        else:
            results["message"] = f"Successfully processed all {len(results['processed_bills'])} bills"
        
        return results
        
    except Exception as e:
        logger.error(f"Batch processing error: {e}")
        raise HTTPException(status_code=500, detail=f"Batch processing failed: {str(e)}")

@router.get("/ai-usage/recent")
async def get_recent_ai_usage(hours: int = 1, db: Session = Depends(get_db)):
    """Get recent AI usage and costs"""
    from app.models.ai_usage import AIUsageLog
    from datetime import datetime, timedelta

    cutoff = datetime.utcnow() - timedelta(hours=hours)
    recent_usage = db.query(AIUsageLog).filter(
        AIUsageLog.created_at >= cutoff
    ).order_by(AIUsageLog.created_at.desc()).limit(50).all()

    total_cost = sum(usage.total_cost for usage in recent_usage)

    return {
        "total_cost": round(total_cost, 4),
        "usage_count": len(recent_usage),
        "time_period_hours": hours,
        "usage_details": [
            {
                "timestamp": usage.created_at.isoformat(),
                "operation": usage.operation_type,
                "cost": round(usage.total_cost, 4),
                "tokens": usage.total_tokens,
                "model": usage.model_name
            }
            for usage in recent_usage
        ]
    }

@router.get("/bills-processing-status")
async def get_bills_processing_status(
    limit: int = 50,
    include_scores: bool = True,
    db: Session = Depends(get_db)
):
    """
    Get processing status for all bills with importance scores and workflow tracking.
    
    This shows which bills have been pulled, which have importance scores,
    which have been through AI processing, and which need manual processing.
    """
    from app.models.bill import Bill
    from app.models.bill_details import BillDetails
    from app.models.ai_usage import AIUsageLog
    from app.services.bill_importance_scorer import BillImportanceScorer
    from sqlalchemy.orm import joinedload
    from sqlalchemy import desc, func
    
    try:
        # Get bills with related data
        bills_query = (
            db.query(Bill)
            .options(
                joinedload(Bill.details),
                joinedload(Bill.ai_usage_logs)
            )
            .order_by(desc(Bill.created_at))
            .limit(limit)
        )
        
        bills = bills_query.all()
        
        # Initialize importance scorer if needed
        scorer = BillImportanceScorer() if include_scores else None
        
        results = []
        for bill in bills:
            # Calculate importance score if requested
            importance_data = None
            if include_scores and scorer:
                importance_score = scorer.score_bill(
                    title=bill.title or "",
                    summary=bill.summary or "",
                    bill_number=bill.bill_number or ""
                )
                importance_data = {
                    "score": importance_score.score,
                    "level": importance_score.level.value,
                    "auto_process": importance_score.auto_process,
                    "reason": importance_score.reason,
                    "key_indicators": importance_score.key_indicators[:3]
                }
            
            # Check processing status
            has_ai_summary = bool(bill.ai_summary and bill.ai_summary.strip())
            has_details = bool(bill.details and len(bill.details) > 0)
            
            # Calculate AI costs
            total_ai_cost = sum(log.total_cost or 0 for log in bill.ai_usage_logs) if bill.ai_usage_logs else 0
            
            # Determine workflow status
            workflow_status = "not_started"
            if has_details and has_ai_summary:
                workflow_status = "fully_processed"
            elif has_ai_summary:
                workflow_status = "ai_complete"
            elif bill.priority_score and bill.priority_score > 0:
                workflow_status = "scored"
            else:
                workflow_status = "pulled_only"
            
            bill_data = {
                "id": bill.id,
                "bill_number": bill.bill_number,
                "title": bill.title,
                "created_at": bill.created_at.isoformat() if bill.created_at else None,
                "updated_at": bill.updated_at.isoformat() if bill.updated_at else None,
                "priority_score": bill.priority_score,
                "is_featured": bill.is_featured,
                "status": bill.status.value if bill.status else "unknown",
                
                # Processing status
                "workflow_status": workflow_status,
                "has_ai_summary": has_ai_summary,
                "has_details": has_details,
                "can_request_details": not has_details,
                
                # AI costs
                "total_ai_cost": round(total_ai_cost, 4),
                "ai_usage_count": len(bill.ai_usage_logs) if bill.ai_usage_logs else 0,
                
                # Importance scoring
                "importance": importance_data,
                
                # Last processing info
                "last_ai_processing": None,
                "details_created_at": None
            }
            
            # Add timing info if available
            if bill.ai_usage_logs:
                latest_ai_log = max(bill.ai_usage_logs, key=lambda x: x.created_at)
                bill_data["last_ai_processing"] = latest_ai_log.created_at.isoformat()
            
            if has_details:
                details = bill.details[0] if isinstance(bill.details, list) else bill.details
                bill_data["details_created_at"] = details.created_at.isoformat() if details.created_at else None
            
            results.append(bill_data)
        
        # Summary stats
        total_bills = len(results)
        fully_processed = len([b for b in results if b["workflow_status"] == "fully_processed"])
        ai_complete = len([b for b in results if b["workflow_status"] == "ai_complete"])
        scored_only = len([b for b in results if b["workflow_status"] == "scored"])
        pulled_only = len([b for b in results if b["workflow_status"] == "pulled_only"])
        
        auto_process_eligible = 0
        manual_process_available = 0
        if include_scores:
            auto_process_eligible = len([b for b in results if b["importance"] and b["importance"]["auto_process"]])
            manual_process_available = len([b for b in results if b["importance"] and not b["importance"]["auto_process"] and not b["has_ai_summary"]])
        
        summary = {
            "total_bills": total_bills,
            "workflow_breakdown": {
                "fully_processed": fully_processed,
                "ai_complete": ai_complete, 
                "scored_only": scored_only,
                "pulled_only": pulled_only
            },
            "importance_breakdown": {
                "auto_process_eligible": auto_process_eligible,
                "manual_process_available": manual_process_available
            } if include_scores else None,
            "total_ai_costs": round(sum(b["total_ai_cost"] for b in results), 4)
        }
        
        return {
            "success": True,
            "bills": results,
            "summary": summary,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting bills processing status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get processing status: {str(e)}")


@router.get("/ai-usage/cost-per-bill")
async def get_cost_per_bill(limit: int = 20, db: Session = Depends(get_db)):
    """Get COST PER BILL PROCESSED - exactly what you asked for!"""
    from app.models.ai_usage import AIUsageLog
    from app.models.bill import Bill
    from sqlalchemy import func
    from datetime import datetime, timedelta

    # Get bills processed in last 24 hours with their AI costs
    cutoff = datetime.utcnow() - timedelta(hours=24)

    # Group AI usage by bill_id and sum costs
    bill_costs = db.query(
        AIUsageLog.bill_id,
        func.sum(AIUsageLog.total_cost).label('total_cost'),
        func.count(AIUsageLog.id).label('ai_calls'),
        func.max(AIUsageLog.created_at).label('last_processed')
    ).filter(
        AIUsageLog.created_at >= cutoff,
        AIUsageLog.bill_id.isnot(None)
    ).group_by(AIUsageLog.bill_id).order_by(func.max(AIUsageLog.created_at).desc()).limit(limit).all()

    # Get bill details
    results = []
    for bill_cost in bill_costs:
        bill = db.query(Bill).filter(Bill.id == bill_cost.bill_id).first()
        results.append({
            "bill_id": bill_cost.bill_id,
            "bill_number": bill.bill_number if bill else "Unknown",
            "title": bill.title[:100] + "..." if bill and bill.title and len(bill.title) > 100 else (bill.title if bill else "Unknown"),
            "cost_per_bill": round(float(bill_cost.total_cost), 4),
            "ai_calls": bill_cost.ai_calls,
            "processed_at": bill_cost.last_processed.isoformat(),
            "cost_per_call": round(float(bill_cost.total_cost) / bill_cost.ai_calls, 4) if bill_cost.ai_calls > 0 else 0
        })

    total_bills = len(results)
    total_cost = sum(r["cost_per_bill"] for r in results)
    avg_cost_per_bill = round(total_cost / total_bills, 4) if total_bills > 0 else 0

    return {
        "cost_per_bill_breakdown": results,
        "summary": {
            "total_bills_processed": total_bills,
            "total_cost": round(total_cost, 4),
            "average_cost_per_bill": avg_cost_per_bill,
            "time_period": "Last 24 hours"
        }
    }

@router.post("/test-enriched-analysis")
async def test_enriched_analysis():
    """Test the enriched analysis system with a small sample"""
    from app.services.ai_service import AIService

    ai_service = AIService()
    if not ai_service.enabled:
        return {"error": "AI service not enabled"}

    # Small test bill text
    test_bill = """
    SEC. 1. SHORT TITLE.
    This Act may be cited as the "Test Act".

    SEC. 2. AUTHORIZATION.
    There are authorized to be appropriated $1,000 for this Act.
    Each agency shall comply within 30 days.
    """

    test_metadata = {
        'bill_id': 'test-123',
        'title': 'Test Act',
        'bill_number': 'TEST1'
    }

    try:
        result = await ai_service.analyze_bill_balanced(test_bill, test_metadata)

        cost = result.get('_metadata', {}).get('cost', 0) if result.get('success') else 0

        return {
            "success": result.get('success', False),
            "cost": round(cost, 4),
            "error": result.get('error') if not result.get('success') else None,
            "processing_level": result.get('processing_level'),
            "sections_analyzed": result.get('_metadata', {}).get('sections_analyzed', 0),
            "tokens_used": result.get('_metadata', {}).get('tokens_used', 0),
            "message": f"Test completed - cost: ${cost:.4f}"
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "message": "Test failed"
        }

@router.get("/status")
async def get_system_status():
    """Get system status for all services"""
    try:
        # Check Congress API
        congress_api = CongressGovAPI()
        congress_status = {
            "enabled": congress_api.enabled,
            "message": "API key configured" if congress_api.enabled else "Missing CONGRESS_GOV_API_KEY"
        }

        # Check Hugging Face AI
        try:
            from app.services.ai import get_summarizer
            summarizer = get_summarizer()
            hf_status = {
                "enabled": summarizer is not None,
                "message": "Transformers available" if summarizer else "Transformers not installed"
            }
        except Exception as e:
            hf_status = {"enabled": False, "message": f"Error: {str(e)}"}

        # Check OpenAI AI
        try:
            ai_service = AIService()
            openai_status = {
                "enabled": ai_service.enabled,
                "message": "API key configured" if ai_service.enabled else "Missing OPENAI_API_KEY"
            }
        except Exception as e:
            openai_status = {"enabled": False, "message": f"Error: {str(e)}"}

        return {
            "Congress.gov API": congress_status,
            "Hugging Face AI": hf_status,
            "OpenAI AI": openai_status
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error checking system status: {str(e)}")

@router.get("/test-bill-lookup")
async def test_bill_lookup(congress: int, bill_type: str, bill_number: int):
    """Test bill lookup from Congress.gov API"""
    try:
        congress_api = CongressGovAPI()
        if not congress_api.enabled:
            return {"success": False, "error": "Congress.gov API not enabled"}

        bill_data = congress_api.get_bill_by_number(congress, bill_type, bill_number)
        if bill_data:
            return {"success": True, "bill": bill_data}
        else:
            return {"success": False, "error": "Bill not found"}
    except Exception as e:
        return {"success": False, "error": str(e)}

@router.post("/test-ai-summary")
async def test_ai_summary(request: dict):
    """Test AI summarization"""
    try:
        bill_text = request.get("bill_text", "")
        bill_title = request.get("bill_title", "")

        if not bill_text.strip():
            return {"success": False, "error": "Bill text is required"}

        start_time = time.time()

        # Try OpenAI first, then fall back to Hugging Face
        try:
            ai_service = AIService()
            if ai_service.enabled:
                result = await ai_service.process_bill_complete(bill_text, {"title": bill_title})
                summary = result.get("ai_summary", "No summary generated")
            else:
                raise Exception("OpenAI not available")
        except Exception:
            # Fall back to Hugging Face
            from app.services.ai import summarize_bill
            summary = summarize_bill(bill_text, bill_title)

        processing_time = round(time.time() - start_time, 2)

        return {
            "success": True,
            "summary": summary,
            "processing_time": processing_time
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@router.get("/recent-bills")
async def get_recent_bills():
    """Get recent bills from Congress.gov API"""
    try:
        congress_api = CongressGovAPI()
        if not congress_api.enabled:
            return {"success": False, "error": "Congress.gov API not enabled"}

        bills = congress_api.get_recent_bills(limit=10)
        return {"success": True, "bills": bills}
    except Exception as e:
        return {"success": False, "error": str(e)}

@router.delete("/clear-bills")
async def clear_bills(db: Session = Depends(get_db)):
    """Clear all bills from database"""
    try:
        deleted_count = db.query(Bill).count()
        db.query(Bill).delete()
        db.commit()
        return {"success": True, "deleted_count": deleted_count}
    except Exception as e:
        db.rollback()
        return {"success": False, "error": str(e)}

@router.delete("/clear-test-bills")
async def clear_test_bills(db: Session = Depends(get_db)):
    """Clear specific test bills created during E2E testing"""
    try:
        # Delete specific test bills by bill number
        test_bills = ["HR10", "HR15", "HR20", "S1"]
        deleted_count = 0

        for bill_number in test_bills:
            bill = db.query(Bill).filter(Bill.bill_number == bill_number).first()
            if bill:
                db.delete(bill)
                deleted_count += 1

        db.commit()
        return {"success": True, "deleted_count": deleted_count, "test_bills_removed": test_bills}
    except Exception as e:
        db.rollback()
        return {"success": False, "error": str(e)}

@router.post("/summarize-bill")
async def summarize_bill_from_congress(congress: int, bill_type: str, bill_number: int):
    """Fetch a bill from Congress.gov and generate AI summary"""
    try:
        # Fetch bill data
        congress_api = CongressGovAPI()
        if not congress_api.enabled:
            return {"success": False, "error": "Congress.gov API not enabled"}

        bill_data = congress_api.get_bill_by_number(congress, bill_type, bill_number)
        if not bill_data:
            return {"success": False, "error": "Bill not found"}

        # Get comprehensive bill text for AI processing
        bill_text = f"BILL TITLE: {bill_data.get('title', '')}\n\n"

        # Add bill summary if available
        if bill_data.get('summary'):
            bill_text += f"OFFICIAL SUMMARY: {bill_data['summary']}\n\n"

        # Add sponsor information
        if bill_data.get('sponsors') and len(bill_data['sponsors']) > 0:
            sponsor = bill_data['sponsors'][0]
            bill_text += f"SPONSOR: {sponsor.get('fullName', 'Unknown')} [{sponsor.get('party', '')}-{sponsor.get('state', '')}]\n\n"

        # Add latest action
        if bill_data.get('latestAction'):
            bill_text += f"LATEST ACTION: {bill_data['latestAction'].get('text', '')}\n\n"

        # Get the ACTUAL FULL TEXT of the bill
        try:
            logger.info(f"Fetching full text for {bill_type.upper()}.{bill_number}")
            full_text = await congress_api.get_bill_full_text(congress, bill_type, bill_number)

            if full_text and len(full_text.strip()) > 100:  # Ensure we got substantial content
                bill_text += f"FULL BILL TEXT:\n{full_text}"
                logger.info(f"Successfully retrieved {len(full_text)} characters of full bill text")
            else:
                # Fallback to metadata-based analysis
                bill_text += "BILL CONTENT: [Full text not available - analysis based on title, summary, and metadata]"
                logger.warning("Full text not available, using metadata only")

        except Exception as e:
            logger.error(f"Error fetching full bill text: {e}")
            bill_text += f"BILL CONTENT: [Text retrieval error: {str(e)} - analysis based on available metadata]"

        start_time = time.time()

        # Try OpenAI first, then fall back to Hugging Face
        ai_result = None
        ai_method = "unknown"

        try:
            ai_service = AIService()
            if ai_service.enabled:
                # For now, just get the summary directly to debug
                summary = await ai_service._generate_summary(bill_text, bill_data)
                ai_method = "OpenAI GPT-4"

                # Try to get full analysis
                try:
                    ai_result = await ai_service.process_bill_complete(bill_text, bill_data)
                except Exception as e:
                    logger.warning(f"Full AI analysis failed, using summary only: {e}")
                    ai_result = {"ai_summary": summary}
            else:
                raise Exception("OpenAI not available")
        except Exception as e:
            logger.error(f"OpenAI processing failed: {e}")
            # Fall back to Hugging Face
            from app.services.ai import summarize_bill
            summary = summarize_bill(bill_text, bill_data.get('title', ''))
            ai_method = "Hugging Face Transformers"

        processing_time = round(time.time() - start_time, 2)

        return {
            "success": True,
            "bill": {
                "congress": congress,
                "bill_type": bill_type.upper(),
                "bill_number": bill_number,
                "title": bill_data.get('title', 'Unknown'),
                "introduced_date": bill_data.get('introducedDate', 'Unknown'),
                "latest_action": bill_data.get('latestAction', {}).get('text', 'No status'),
                "sponsor": bill_data.get('sponsors', [{}])[0].get('fullName', 'Unknown') if bill_data.get('sponsors') else 'Unknown'
            },
            "ai_summary": summary,
            "ai_method": ai_method,
            "processing_time": processing_time,
            "full_ai_result": ai_result if ai_result else None
        }
    except Exception as e:
        return {"success": False, "error": str(e)}


@router.post("/process-and-save-bill")
async def process_and_save_bill(
    congress: int = 118,
    bill_type: str = "hr",
    bill_number: int = 1,
    db: Session = Depends(get_db)
):
    """
    Complete bill processing pipeline: fetch, analyze, and save to database.

    This endpoint tests the entire flow:
    1. Fetch bill data from Congress.gov API
    2. Get full bill text
    3. Generate AI analysis
    4. Save everything to database
    5. Return database record
    """
    start_time = time.time()

    try:
        logger.info(f"Starting complete bill processing for {bill_type.upper()}.{bill_number}")

        # Initialize enhanced bill service
        enhanced_bill_service = EnhancedBillService()

        # Use the enhanced service to process the complete bill
        bill_number_str = f"{bill_type.upper()}{bill_number}"
        result = await enhanced_bill_service.process_bill(
            bill_number_str, str(congress)
        )

        processing_time = time.time() - start_time

        # Check if processing was successful
        if result.get("status") == "success":
            return {
                "success": True,
                "message": result.get("message", f"Successfully processed and saved bill {bill_type.upper()}.{bill_number}"),
                "bill_id": result["bill_id"],
                "processing_time": round(processing_time, 2),
                "steps_completed": {
                    "metadata_fetched": True,
                    "full_text_fetched": True,
                    "ai_analysis_generated": True,
                    "database_saved": True
                },
                "bill_data": {
                    "id": result["bill_id"],
                    "title": result.get("title", ""),
                    "bill_number": result["bill_number"],
                    "ai_summary": result.get("ai_summary", ""),
                    "support_reasons": result.get("support_reasons", []),
                    "oppose_reasons": result.get("oppose_reasons", []),
                    "ai_tags": result.get("tags", []),
                    "full_text_length": result.get("full_text_length", 0),
                    "official_bill_url": None,
                    "ai_processed_at": None
                }
            }
        elif result.get("status") == "already_processed":
            return {
                "success": True,
                "message": result.get("message", f"Bill {bill_type.upper()}.{bill_number} was already processed"),
                "bill_id": result["bill_id"],
                "processing_time": round(processing_time, 2),
                "steps_completed": {
                    "metadata_fetched": True,
                    "full_text_fetched": True,
                    "ai_analysis_generated": True,
                    "database_saved": True
                },
                "bill_data": {
                    "id": result["bill_id"],
                    "title": "",
                    "bill_number": result.get("bill_number", f"{bill_type.upper()}.{bill_number}"),
                    "ai_summary": "",
                    "support_reasons": None,
                    "oppose_reasons": None,
                    "ai_tags": [],
                    "full_text_length": 0,
                    "official_bill_url": None,
                    "ai_processed_at": None
                }
            }
        else:
            # Error case
            return {
                "success": False,
                "error": result.get("error", "Unknown error occurred"),
                "message": result.get("message", f"Failed to process bill {bill_type.upper()}.{bill_number}"),
                "processing_time": round(processing_time, 2)
            }

    except Exception as e:
        logger.error(f"Complete bill processing failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"success": False, "error": str(e)}


@router.get("/database-bills")
async def get_database_bills(db: Session = Depends(get_db)):
    """Get all bills from the database to verify storage"""
    try:
        bills = db.query(Bill).order_by(Bill.created_at.desc()).limit(10).all()

        bill_data = []
        for bill in bills:
            bill_data.append({
                "id": str(bill.id),
                "title": bill.title,
                "bill_number": bill.bill_number,
                "congress_gov_id": bill.congress_gov_id,
                "official_bill_url": bill.official_bill_url,
                "ai_summary": bill.ai_summary,
                "support_reasons": bill.support_reasons,
                "oppose_reasons": bill.oppose_reasons,
                "amend_reasons": bill.amend_reasons,
                "ai_tags": bill.ai_tags,
                "environmental_threat_analysis": bill.environmental_threat_analysis,
                "social_rights_threat_analysis": bill.social_rights_threat_analysis,
                "environmental_justice_threat_analysis": bill.environmental_justice_threat_analysis,
                "full_text_length": len(bill.full_text) if bill.full_text else 0,
                "ai_processed_at": bill.ai_processed_at.isoformat() if bill.ai_processed_at else None,
                "created_at": bill.created_at.isoformat() if bill.created_at else None
            })

        return {
            "success": True,
            "count": len(bill_data),
            "bills": bill_data
        }

    except Exception as e:
        logger.error(f"Database query failed: {e}")
        return {"success": False, "error": str(e)}


@router.get("/", response_class=HTMLResponse)
async def admin_dashboard():
    """Admin dashboard for testing bill processing"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>ModernAction.io - Admin Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
            h2 { color: #34495e; margin-top: 30px; }
            .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #fafafa; }
            .form-group { margin: 15px 0; }
            label { display: block; margin-bottom: 5px; font-weight: bold; color: #555; }
            input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
            button { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; margin: 5px; }
            button:hover { background: #2980b9; }
            .success { color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 4px; margin: 10px 0; }
            .error { color: #e74c3c; background: #fdf2f2; padding: 10px; border-radius: 4px; margin: 10px 0; }
            .result { background: #ecf0f1; padding: 15px; border-radius: 4px; margin: 10px 0; white-space: pre-wrap; }
            .status { padding: 5px 10px; border-radius: 3px; font-size: 12px; font-weight: bold; }
            .status.working { background: #f39c12; color: white; }
            .status.success { background: #27ae60; color: white; }
            .status.error { background: #e74c3c; color: white; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🏛️ ModernAction.io - Admin Dashboard</h1>
            <p>Test and manage bill processing functionality</p>
            
            <div class="section">
                <h2>📊 System Status</h2>
                <div id="system-status">Loading...</div>
                <button onclick="checkSystemStatus()">Refresh Status</button>
            </div>
            
            <div class="section">
                <h2>🔍 Test Bill Lookup</h2>
                <div class="form-group">
                    <label>Congress Number:</label>
                    <input type="number" id="congress" value="118" min="100" max="120">
                </div>
                <div class="form-group">
                    <label>Bill Type:</label>
                    <select id="bill-type">
                        <option value="hr">House Bill (HR)</option>
                        <option value="s">Senate Bill (S)</option>
                        <option value="hjres">House Joint Resolution (HJRES)</option>
                        <option value="sjres">Senate Joint Resolution (SJRES)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Bill Number:</label>
                    <input type="number" id="bill-number" value="1" min="1">
                </div>
                <button onclick="testBillLookup()">Fetch Bill Data</button>
                <div id="bill-lookup-result"></div>
            </div>

            <div class="section">
                <h2>🔄 Complete Bill Processing Pipeline</h2>
                <p>Test the complete flow: fetch bill → get full text → AI analysis → database storage</p>
                <button onclick="testCompletePipeline()" style="background: #e74c3c; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;">🚀 Test Complete Pipeline</button>
                <div id="complete-pipeline-result"></div>
            </div>

            <div class="section">
                <h2>💾 Database Records</h2>
                <p>View bills stored in the database</p>
                <button onclick="loadDatabaseRecords()" style="background: #27ae60; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold;">📊 Load Database Records</button>
                <div id="database-records"></div>
            </div>

            <div class="section">
                <h2>🤖 Test AI Summarization</h2>
                <div class="form-group">
                    <label>Bill Text to Summarize:</label>
                    <textarea id="bill-text" rows="6" placeholder="Enter bill text here or use 'Fetch Bill Data' above first..."></textarea>
                </div>
                <div class="form-group">
                    <label>Bill Title (optional):</label>
                    <input type="text" id="bill-title" placeholder="Bill title for context">
                </div>
                <button onclick="testAISummarization()">Generate AI Summary</button>
                <div id="ai-summary-result"></div>
            </div>
            
            <div class="section">
                <h2>📋 Recent Bills</h2>
                <button onclick="fetchRecentBills()">Fetch Recent Bills</button>
                <div id="recent-bills-result"></div>
            </div>

            <div class="section">
                <h2>📝 AI Summary Results</h2>
                <div id="ai-summary-results" style="max-height: 400px; overflow-y: auto;">
                    <p style="color: #666; font-style: italic;">AI summaries will appear here when you click "Summarize" on bills above.</p>
                </div>
                <button onclick="clearSummaryResults()" style="background: #95a5a6;">Clear Results</button>
            </div>
            
            <div class="section">
                <h2>💾 Database Bills</h2>
                <button onclick="fetchDatabaseBills()">View Database Bills</button>
                <button onclick="clearDatabase()" style="background: #e74c3c;">Clear All Bills</button>
                <div id="database-bills-result"></div>
            </div>
        </div>
        
        <script>
            async function checkSystemStatus() {
                const statusDiv = document.getElementById('system-status');
                statusDiv.innerHTML = 'Checking...';
                
                try {
                    const response = await fetch('/api/v1/admin/status', {
                        signal: AbortSignal.timeout(30000) // 30 seconds for status check
                    });
                    const data = await response.json();
                    
                    let html = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">';
                    
                    for (const [service, status] of Object.entries(data)) {
                        const statusClass = status.enabled ? 'success' : 'error';
                        html += `<div><strong>${service}:</strong> <span class="status ${statusClass}">${status.enabled ? 'ENABLED' : 'DISABLED'}</span>`;
                        if (status.message) html += `<br><small>${status.message}</small>`;
                        html += '</div>';
                    }
                    
                    html += '</div>';
                    statusDiv.innerHTML = html;
                } catch (error) {
                    statusDiv.innerHTML = `<div class="error">Error checking status: ${error.message}</div>`;
                }
            }
            
            async function testBillLookup() {
                const congress = document.getElementById('congress').value;
                const billType = document.getElementById('bill-type').value;
                const billNumber = document.getElementById('bill-number').value;
                const resultDiv = document.getElementById('bill-lookup-result');
                
                resultDiv.innerHTML = 'Fetching bill data...';
                
                try {
                    const response = await fetch(`/api/v1/admin/test-bill-lookup?congress=${congress}&bill_type=${billType}&bill_number=${billNumber}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        const bill = data.bill;
                        document.getElementById('bill-text').value = bill.title + '\\n\\n' + (bill.summary || 'No summary available');
                        document.getElementById('bill-title').value = bill.title;
                        
                        resultDiv.innerHTML = `
                            <div class="success">✅ Bill found successfully!</div>
                            <div class="result">
                                <strong>Title:</strong> ${bill.title}
                                <strong>Status:</strong> ${bill.latestAction?.text || 'No status'}
                                <strong>Introduced:</strong> ${bill.introducedDate || 'Unknown'}
                                <strong>Summary:</strong> ${bill.summary || 'No summary available'}
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }
            
            async function testAISummarization() {
                const billText = document.getElementById('bill-text').value;
                const billTitle = document.getElementById('bill-title').value;
                const resultDiv = document.getElementById('ai-summary-result');
                
                if (!billText.trim()) {
                    resultDiv.innerHTML = '<div class="error">❌ Please enter bill text to summarize</div>';
                    return;
                }
                
                resultDiv.innerHTML = 'Generating AI summary...';
                
                try {
                    const response = await fetch('/api/v1/admin/test-ai-summary', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ bill_text: billText, bill_title: billTitle }),
                        signal: AbortSignal.timeout(120000) // 2 minutes for AI summary generation
                    });
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ AI summary generated successfully!</div>
                            <div class="result">
                                <strong>Summary:</strong>
                                ${data.summary}
                                
                                <strong>Processing Time:</strong> ${data.processing_time}s
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }
            
            async function fetchRecentBills() {
                const resultDiv = document.getElementById('recent-bills-result');
                resultDiv.innerHTML = 'Fetching recent bills...';

                try {
                    const response = await fetch('/api/v1/admin/recent-bills');
                    const data = await response.json();

                    if (data.success) {
                        let html = `<div class="success">✅ Found ${data.bills.length} recent bills</div>`;
                        html += '<div style="display: grid; gap: 15px; margin-top: 15px;">';

                        data.bills.forEach((bill, index) => {
                            // Extract bill info from the number (e.g., "HR1234" -> type="hr", number="1234")
                            const billMatch = bill.number.match(/^([A-Z]+)(\\d+)$/);
                            const billType = billMatch ? billMatch[1].toLowerCase() : 'hr';
                            const billNumber = billMatch ? billMatch[2] : index + 1;

                            html += `
                                <div style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; background: white;">
                                    <div style="display: flex; justify-content: between; align-items: start; gap: 10px;">
                                        <div style="flex: 1;">
                                            <strong style="color: #2c3e50;">${bill.number}</strong>: ${bill.title}
                                            <br><small style="color: #666;">Congress: 118 | Type: ${billType.toUpperCase()}</small>
                                        </div>
                                        <button onclick="summarizeBill(118, '${billType}', ${billNumber}, '${bill.number}', this)"
                                                style="background: #27ae60; white-space: nowrap; min-width: 100px;">
                                            📝 Summarize
                                        </button>
                                    </div>
                                </div>
                            `;
                        });

                        html += '</div>';
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }
            
            async function fetchDatabaseBills() {
                const resultDiv = document.getElementById('database-bills-result');
                resultDiv.innerHTML = 'Fetching database bills...';
                
                try {
                    const response = await fetch('/api/v1/admin/database-bills');
                    const data = await response.json();
                    
                    if (data.success) {
                        let html = `<div class="success">✅ Found ${data.bills.length} bills in database</div>`;
                        if (data.bills.length > 0) {
                            html += '<div class="result">';
                            data.bills.forEach((bill, index) => {
                                html += `${index + 1}. <strong>${bill.bill_number}</strong>: ${bill.title}\\n`;
                                if (bill.ai_summary) html += `   📝 AI Summary: ${bill.ai_summary.substring(0, 100)}...\\n`;
                            });
                            html += '</div>';
                        }
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }
            
            async function clearDatabase() {
                if (!confirm('Are you sure you want to clear all bills from the database?')) return;

                const resultDiv = document.getElementById('database-bills-result');
                resultDiv.innerHTML = 'Clearing database...';

                try {
                    const response = await fetch('/api/v1/admin/clear-bills', { method: 'DELETE' });
                    const data = await response.json();

                    if (data.success) {
                        resultDiv.innerHTML = `<div class="success">✅ Cleared ${data.deleted_count} bills from database</div>`;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }

            async function summarizeBill(congress, billType, billNumber, displayName, buttonElement) {
                const originalText = buttonElement.innerHTML;
                buttonElement.innerHTML = '⏳ Processing...';
                buttonElement.disabled = true;

                try {
                    const response = await fetch(`/api/v1/admin/summarize-bill?congress=${congress}&bill_type=${billType}&bill_number=${billNumber}`, {
                        method: 'POST',
                        signal: AbortSignal.timeout(120000) // 2 minutes for bill summarization
                    });
                    const data = await response.json();

                    if (data.success) {
                        // Add result to the AI Summary Results section
                        addSummaryResult(data);

                        buttonElement.innerHTML = '✅ Done';
                        buttonElement.style.background = '#27ae60';

                        // Reset button after 3 seconds
                        setTimeout(() => {
                            buttonElement.innerHTML = originalText;
                            buttonElement.style.background = '#27ae60';
                            buttonElement.disabled = false;
                        }, 3000);
                    } else {
                        buttonElement.innerHTML = '❌ Failed';
                        buttonElement.style.background = '#e74c3c';

                        // Show error in results
                        addSummaryResult({
                            success: false,
                            error: data.error,
                            bill: { title: displayName }
                        });

                        setTimeout(() => {
                            buttonElement.innerHTML = originalText;
                            buttonElement.style.background = '#27ae60';
                            buttonElement.disabled = false;
                        }, 3000);
                    }
                } catch (error) {
                    buttonElement.innerHTML = '❌ Error';
                    buttonElement.style.background = '#e74c3c';

                    addSummaryResult({
                        success: false,
                        error: error.message,
                        bill: { title: displayName }
                    });

                    setTimeout(() => {
                        buttonElement.innerHTML = originalText;
                        buttonElement.style.background = '#27ae60';
                        buttonElement.disabled = false;
                    }, 3000);
                }
            }

            function addSummaryResult(data) {
                const resultsDiv = document.getElementById('ai-summary-results');
                const timestamp = new Date().toLocaleTimeString();

                let resultHtml = `
                    <div style="border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; background: ${data.success ? '#f8f9fa' : '#fdf2f2'};">
                        <div style="display: flex; justify-content: between; align-items: start; margin-bottom: 10px;">
                            <h4 style="margin: 0; color: ${data.success ? '#2c3e50' : '#e74c3c'};">
                                ${data.success ? '✅' : '❌'} ${data.bill.title}
                            </h4>
                            <small style="color: #666;">${timestamp}</small>
                        </div>
                `;

                if (data.success) {
                    resultHtml += `
                        <div style="margin: 10px 0;">
                            <strong>Bill:</strong> ${data.bill.bill_type}${data.bill.bill_number} (${data.bill.congress}th Congress)<br>
                            <strong>Sponsor:</strong> ${data.bill.sponsor}<br>
                            <strong>Introduced:</strong> ${data.bill.introduced_date}<br>
                            <strong>Status:</strong> ${data.bill.latest_action.substring(0, 100)}...
                        </div>

                        <div style="background: #e8f5e8; padding: 10px; border-radius: 4px; margin: 10px 0;">
                            <strong>🤖 AI Summary (${data.ai_method}):</strong><br>
                            <div style="margin-top: 8px; line-height: 1.5;">
                                ${data.ai_summary}
                            </div>
                        </div>

                        <div style="font-size: 12px; color: #666;">
                            Processing time: ${data.processing_time}s
                        </div>
                    `;

                    // Add full AI result if available (OpenAI)
                    if (data.full_ai_result) {
                        resultHtml += `
                            <details style="margin-top: 10px;">
                                <summary style="cursor: pointer; color: #3498db;">📊 View Full AI Analysis</summary>
                                <div style="background: #f8f9fa; padding: 10px; margin-top: 5px; border-radius: 4px; font-size: 12px;">
                                    <pre style="white-space: pre-wrap; margin: 0;">${JSON.stringify(data.full_ai_result, null, 2)}</pre>
                                </div>
                            </details>
                        `;
                    }
                } else {
                    resultHtml += `
                        <div style="color: #e74c3c;">
                            <strong>Error:</strong> ${data.error}
                        </div>
                    `;
                }

                resultHtml += '</div>';

                // Add to top of results
                if (resultsDiv.innerHTML.includes('AI summaries will appear here')) {
                    resultsDiv.innerHTML = resultHtml;
                } else {
                    resultsDiv.innerHTML = resultHtml + resultsDiv.innerHTML;
                }

                // Scroll to show the new result
                resultsDiv.scrollTop = 0;
            }

            function clearSummaryResults() {
                const resultsDiv = document.getElementById('ai-summary-results');
                resultsDiv.innerHTML = '<p style="color: #666; font-style: italic;">AI summaries will appear here when you click "Summarize" on bills above.</p>';
            }
            
            async function testCompletePipeline() {
                const congress = document.getElementById('congress').value;
                const billType = document.getElementById('bill-type').value;
                const billNumber = document.getElementById('bill-number').value;
                const resultDiv = document.getElementById('complete-pipeline-result');

                resultDiv.innerHTML = '<div class="loading">🔄 Running complete pipeline... This may take 1-3 minutes for full AI analysis...</div>';

                try {
                    const response = await fetch(`/api/v1/admin/process-and-save-bill?congress=${congress}&bill_type=${billType}&bill_number=${billNumber}`, {
                        method: 'POST',
                        signal: AbortSignal.timeout(180000) // 3 minutes timeout for bill processing
                    });
                    const data = await response.json();

                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h3>✅ Complete Pipeline Success!</h3>
                                <p><strong>Message:</strong> ${data.message}</p>
                                <p><strong>Processing Time:</strong> ${data.processing_time} seconds</p>

                                <h4>📊 Pipeline Steps:</h4>
                                <ul>
                                    <li>✅ Metadata Fetched: ${data.steps_completed.metadata_fetched}</li>
                                    <li>✅ Full Text Fetched: ${data.steps_completed.full_text_fetched}</li>
                                    <li>✅ AI Analysis Generated: ${data.steps_completed.ai_analysis_generated}</li>
                                    <li>✅ Ready for Database: ${data.steps_completed.ready_for_database}</li>
                                </ul>

                                <h4>📋 Bill Information:</h4>
                                <ul>
                                    <li><strong>Title:</strong> ${data.bill_data.title}</li>
                                    <li><strong>Bill Number:</strong> ${data.bill_data.bill_number}</li>
                                    <li><strong>Bill ID:</strong> ${data.bill_id}</li>
                                    <li><strong>Full Text Length:</strong> ${data.bill_data.full_text_length.toLocaleString()} characters</li>
                                    <li><strong>AI Summary:</strong> ${data.bill_data.ai_summary}</li>
                                    <li><strong>Official URL:</strong> <a href="${data.bill_data.official_bill_url}" target="_blank">${data.bill_data.official_bill_url}</a></li>
                                    <li><strong>AI Processed:</strong> ${data.bill_data.ai_processed_at}</li>
                                </ul>

                                <h4>🤖 AI Analysis Results:</h4>
                                <ul>
                                    <li><strong>Support Reasons:</strong> ${data.bill_data.support_reasons ? data.bill_data.support_reasons.join(', ') : 'None'}</li>
                                    <li><strong>Oppose Reasons:</strong> ${data.bill_data.oppose_reasons ? data.bill_data.oppose_reasons.join(', ') : 'None'}</li>
                                    <li><strong>AI Tags:</strong> ${data.bill_data.ai_tags ? data.bill_data.ai_tags.join(', ') : 'None'}</li>
                                </ul>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Pipeline Failed: ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }

            async function loadDatabaseRecords() {
                const resultDiv = document.getElementById('database-records');
                resultDiv.innerHTML = '<div class="loading">🔄 Loading database records...</div>';

                try {
                    const response = await fetch('/api/v1/admin/database-bills');
                    const data = await response.json();

                    if (data.success) {
                        let html = `
                            <div class="success">
                                <h3>📊 Database Records (${data.count} bills)</h3>
                                <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-top: 10px;">
                        `;

                        data.bills.forEach(bill => {
                            html += `
                                <div style="border-bottom: 1px solid #eee; padding: 10px 0; margin-bottom: 10px;">
                                    <h4>${bill.title}</h4>
                                    <p><strong>Bill Number:</strong> ${bill.bill_number}</p>
                                    <p><strong>ID:</strong> ${bill.id}</p>
                                    <p><strong>AI Summary:</strong> ${bill.ai_summary || 'Not processed'}</p>
                                    <p><strong>Full Text Length:</strong> ${bill.full_text_length.toLocaleString()} characters</p>
                                    <p><strong>Official URL:</strong> ${bill.official_bill_url || 'Not set'}</p>
                                    <p><strong>Support Reasons:</strong> ${bill.support_reasons ? JSON.stringify(bill.support_reasons) : 'None'}</p>
                                    <p><strong>Oppose Reasons:</strong> ${bill.oppose_reasons ? JSON.stringify(bill.oppose_reasons) : 'None'}</p>
                                    <p><strong>AI Tags:</strong> ${bill.ai_tags ? JSON.stringify(bill.ai_tags) : 'None'}</p>
                                    <p><strong>Created:</strong> ${bill.created_at}</p>
                                    <p><strong>AI Processed:</strong> ${bill.ai_processed_at || 'Not processed'}</p>
                                </div>
                            `;
                        });

                        html += '</div></div>';
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Failed to load records: ${data.error}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                }
            }

            // Load system status on page load
            window.onload = checkSystemStatus;
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@router.post("/test-action-network")
async def test_action_network():
    """
    Test Action Network integration with mock data
    """
    try:
        from app.services.action_network_service import ActionNetworkService

        action_network = ActionNetworkService()

        if not action_network.enabled:
            return {
                "success": False,
                "error": "Action Network service is not enabled (missing API key)"
            }

        # Test with mock data for both chambers
        test_senate_data = {
            'chamber': 'senate',
            'bill_number': 'S.123',
            'position': 'support'
        }

        test_house_data = {
            'chamber': 'house',
            'bill_number': 'H.R.456',
            'position': 'oppose'
        }

        senate_result = await action_network.get_campaign_embed_info(test_senate_data)
        house_result = await action_network.get_campaign_embed_info(test_house_data)
        health_check = action_network.health_check()

        return {
            "success": True,
            "action_network_enabled": action_network.enabled,
            "health_check": health_check,
            "senate_campaign": senate_result,
            "house_campaign": house_result,
            "message": "Action Network embed integration ready. Users can complete forms to send messages to officials."
        }

    except Exception as e:
        logger.error(f"Error testing Action Network: {e}")
        return {
            "success": False,
            "error": f"Action Network test failed: {str(e)}"
        }

@router.post("/test-message-personalization")
async def test_message_personalization():
    """
    Test message personalization service with mock data
    """
    try:
        from app.services.message_personalization_service import MessagePersonalizationService
        from app.services.bills import BillService
        from app.db.database import get_db

        # Get a bill from the database for testing
        db = next(get_db())
        bill_service = BillService(db)
        bills = bill_service.get_bills(limit=1)

        if not bills:
            return {
                "success": False,
                "error": "No bills found in database. Please process a bill first."
            }

        bill = bills[0]

        # Test message personalization
        personalization_service = MessagePersonalizationService()

        test_request_data = {
            'bill': bill,
            'position': 'support',
            'user_info': {
                'first_name': 'Test',
                'last_name': 'User',
                'email': '<EMAIL>',
                'zip_code': '60302'
            },
            'representatives': [
                {
                    'full_name': 'Test Representative',
                    'title': 'Representative',
                    'chamber': 'house',
                    'state': 'IL',
                    'party': 'Democratic',
                    'email': '<EMAIL>',
                    'last_name': 'Representative'
                },
                {
                    'full_name': 'Test Senator',
                    'title': 'Senator',
                    'chamber': 'senate',
                    'state': 'IL',
                    'party': 'Democratic',
                    'email': '<EMAIL>',
                    'last_name': 'Senator'
                }
            ],
            'custom_message': 'This is important to me because it affects my community.',
            'selected_reasons': ['It will improve public services', 'It supports economic growth']
        }

        logger.info(f"Testing message personalization with bill: {bill.bill_number}")

        result = await personalization_service.create_personalized_messages(test_request_data)

        return {
            "success": True,
            "bill_title": bill.title,
            "bill_number": bill.bill_number,
            "personalization_result": result
        }

    except Exception as e:
        logger.error(f"Error testing message personalization: {e}")
        return {
            "success": False,
            "error": f"Message personalization test failed: {str(e)}"
        }

@router.post("/test-complete-action-flow")
async def test_complete_action_flow(db: Session = Depends(get_db)):
    """Test the complete action submission flow without authentication"""
    try:
        # Test data
        test_data = {
            "bill_id": "25177ae6-9916-456b-aec2-7d3b84f87647",
            "stance": "support",
            "selected_reasons": ["This bill addresses an important issue", "The provisions make sense for our community"],
            "custom_message": "This is important to me because it affects my community.",
            "zip_code": "60302",
            "address": "123 Main St",
            "city": "Oak Park",
            "state": "IL"
        }

        # Mock user data
        mock_user = {
            "sub": "test-user-123",
            "email": "<EMAIL>",
            "name": "Test User",
            "given_name": "Test",
            "family_name": "User"
        }

        logger.info(f"Testing complete action flow with bill ID: {test_data['bill_id']}")

        # Step 1: Get bill data
        from app.services.bills import BillService
        bill_service = BillService(db)
        bill = bill_service.get_bill(test_data["bill_id"])

        if not bill:
            return {"success": False, "error": "Bill not found"}

        logger.info(f"Found bill: {bill.bill_number} - {bill.title}")

        # Step 2: Look up representatives
        from app.services.officials_service import OfficialsService
        officials_service = OfficialsService()
        officials_result = await officials_service.lookup_representatives_by_zip(test_data["zip_code"])

        if officials_result["status"] != "success":
            return {"success": False, "error": f"Officials lookup failed: {officials_result.get('message')}"}

        # Combine senators and representative
        representatives = officials_result["senators"] + ([officials_result["representative"]] if officials_result["representative"] else [])
        logger.info(f"Found {len(representatives)} representatives")

        # Step 3: Generate personalized messages
        from app.services.message_personalization_service import MessagePersonalizationService
        personalization_service = MessagePersonalizationService()

        user_info = {
            "first_name": mock_user["given_name"],
            "last_name": mock_user["family_name"],
            "email": mock_user["email"],
            "zip_code": test_data["zip_code"],
            "address": test_data.get("address", ""),
            "city": test_data.get("city", ""),
            "state": test_data.get("state", "")
        }

        personalization_request = {
            "bill": bill,
            "position": test_data["stance"],
            "user_info": user_info,
            "representatives": representatives,
            "custom_message": test_data["custom_message"],
            "selected_reasons": test_data["selected_reasons"]
        }

        personalization_result = await personalization_service.create_personalized_messages(personalization_request)

        if personalization_result["status"] != "success":
            return {"success": False, "error": f"Message personalization failed: {personalization_result.get('message')}"}

        logger.info(f"Generated {personalization_result['total_messages']} personalized messages")

        # Step 4: Test Action Network submission (mock for now)
        from app.services.action_network_service import ActionNetworkService
        action_network_service = ActionNetworkService()

        # Test Action Network submission with mock data
        action_network_results = []
        for message in personalization_result["messages"]:
            # In production, this would call action_network_service.submit_message()
            # For testing, we'll simulate a successful submission
            mock_result = {
                "status": "success",
                "message_id": f"mock-{message['representative']['full_name'].replace(' ', '-').lower()}",
                "representative": message['representative']['full_name'],
                "subject": message['subject'][:50] + "..." if len(message['subject']) > 50 else message['subject']
            }
            action_network_results.append(mock_result)

        return {
            "success": True,
            "bill": {
                "id": bill.id,
                "number": bill.bill_number,
                "title": bill.title
            },
            "officials_lookup": {
                "status": officials_result["status"],
                "total_representatives": len(representatives),
                "representatives": [rep["full_name"] for rep in representatives]
            },
            "message_personalization": {
                "status": personalization_result["status"],
                "total_messages": personalization_result["total_messages"],
                "sample_subject": personalization_result["messages"][0]["subject"] if personalization_result["messages"] else None
            },
            "action_network": {
                "status": "success",
                "total_submissions": len(action_network_results),
                "submissions": action_network_results,
                "note": "Mock submissions completed successfully"
            },
            "test_data": test_data
        }

    except Exception as e:
        logger.error(f"Error testing complete action flow: {e}")
        return {
            "success": False,
            "error": str(e)
        }


# =============================================================================
# UNIFIED BILL PROCESSING ENDPOINTS
# =============================================================================

@router.post("/process-bills")
async def process_bills_unified(
    request: Request,
    bill_numbers: Optional[List[str]] = None,
    limit: int = 5,
    environment: str = "development",
    db: Session = Depends(get_db)
):
    """
    Unified bill processing endpoint with environment controls.
    
    This endpoint uses the new unified processing service to handle
    the complete pipeline: Congress.gov API → AI Analysis → Values Scoring
    
    Args:
        bill_numbers: Optional specific bill numbers to process
        limit: Number of recent bills to process if no specific bills provided
        environment: Environment context (development, staging, production)
        
    Returns:
        Processing results with detailed status
    """
    try:
        from app.services.unified_bill_processing_service import get_unified_bill_processing_service
        
        # Environment validation
        valid_environments = ["development", "staging", "production"]
        if environment not in valid_environments:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid environment. Must be one of: {', '.join(valid_environments)}"
            )
        
        # Initialize processing service
        processing_service = get_unified_bill_processing_service(db)
        
        logger.info(f"Starting unified bill processing in {environment} environment")
        
        if bill_numbers:
            # Process specific bills
            results = {
                "success": True,
                "environment": environment,
                "processing_type": "specific_bills",
                "total_requested": len(bill_numbers),
                "processed_bills": [],
                "failed_bills": [],
                "errors": []
            }
            
            for bill_number in bill_numbers:
                try:
                    result = await processing_service.process_bill_by_number(
                        bill_number=bill_number,
                        congress_session=118,
                        environment=environment
                    )
                    
                    if result["success"]:
                        results["processed_bills"].append({
                            "bill_number": result["bill_number"],
                            "bill_id": result["bill_id"],
                            "title": result["title"],
                            "processing_steps": result.get("processing_steps"),
                            "values_scores": result.get("values_scores")
                        })
                    else:
                        results["failed_bills"].append(bill_number)
                        results["errors"].append(f"{bill_number}: {result['error']}")
                        
                except Exception as e:
                    results["failed_bills"].append(bill_number)
                    results["errors"].append(f"{bill_number}: {str(e)}")
            
            results["message"] = f"Processed {len(results['processed_bills'])} of {len(bill_numbers)} bills in {environment}"
            return results
            
        else:
            # Process recent bills
            result = await processing_service.process_recent_bills(
                limit=limit,
                congress_session=118,
                environment=environment
            )
            result["processing_type"] = "recent_bills"
            return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unified bill processing failed: {e}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")


@router.get("/processing-status")
async def get_processing_status(db: Session = Depends(get_db)):
    """
    Get overall bill processing status and statistics.
    
    Returns:
        Statistics about bill processing, AI analysis, and values analysis
    """
    try:
        from app.models.bill import Bill
        from app.models.bill_values import BillValuesAnalysis
        
        # Get bill statistics
        total_bills = db.query(Bill).count()
        bills_with_ai = db.query(Bill).filter(Bill.ai_processed_at.is_not(None)).count()
        bills_with_values = db.query(BillValuesAnalysis).count()
        bills_needing_review = db.query(BillValuesAnalysis).filter(
            BillValuesAnalysis.requires_human_review == True,
            BillValuesAnalysis.reviewed_at.is_(None)
        ).count()
        
        # Get recent processing activity
        recent_bills = db.query(Bill).filter(
            Bill.created_at >= datetime.utcnow() - timedelta(days=7)
        ).count()
        
        recent_ai_processed = db.query(Bill).filter(
            Bill.ai_processed_at >= datetime.utcnow() - timedelta(days=7)
        ).count()
        
        return {
            "success": True,
            "statistics": {
                "total_bills": total_bills,
                "bills_with_ai_analysis": bills_with_ai,
                "bills_with_values_analysis": bills_with_values,
                "bills_needing_review": bills_needing_review,
                "ai_processing_coverage": round((bills_with_ai / total_bills * 100) if total_bills > 0 else 0, 1),
                "values_analysis_coverage": round((bills_with_values / total_bills * 100) if total_bills > 0 else 0, 1)
            },
            "recent_activity": {
                "bills_added_last_7_days": recent_bills,
                "bills_ai_processed_last_7_days": recent_ai_processed
            },
            "system_health": {
                "unified_processing_available": True,
                "values_analysis_available": True,
                "review_queue_size": bills_needing_review
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting processing status: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")


@router.post("/trigger-values-analysis")
async def trigger_values_analysis_batch(
    request: Request,
    limit: int = 10,
    force_reanalysis: bool = False,
    db: Session = Depends(get_db)
):
    """
    Trigger values analysis for bills that don't have it yet.
    
    Args:
        limit: Maximum number of bills to analyze
        force_reanalysis: Whether to re-analyze bills that already have analysis
        
    Returns:
        Results of the values analysis batch operation
    """
    try:
        from app.services.bill_values_analysis_service import BillValuesAnalysisService
        from app.models.bill import Bill
        from app.models.bill_values import BillValuesAnalysis
        
        values_service = BillValuesAnalysisService(db)
        
        # Find bills that need values analysis
        if force_reanalysis:
            bills_query = db.query(Bill).limit(limit)
        else:
            bills_query = db.query(Bill).outerjoin(BillValuesAnalysis).filter(
                BillValuesAnalysis.id.is_(None)
            ).limit(limit)
        
        bills_to_analyze = bills_query.all()
        
        if not bills_to_analyze:
            return {
                "success": True,
                "message": "No bills found that need values analysis",
                "analyzed_count": 0,
                "failed_count": 0
            }
        
        # Process each bill
        results = {
            "success": True,
            "total_bills": len(bills_to_analyze),
            "analyzed_bills": [],
            "failed_bills": [],
            "errors": []
        }
        
        for bill in bills_to_analyze:
            try:
                analysis = await values_service.analyze_bill_values(bill)
                db.commit()
                
                results["analyzed_bills"].append({
                    "bill_id": bill.id,
                    "bill_number": bill.bill_number,
                    "title": bill.title,
                    "needs_review": analysis.requires_human_review if analysis else False
                })
                
            except Exception as e:
                logger.error(f"Values analysis failed for bill {bill.id}: {e}")
                results["failed_bills"].append(bill.bill_number)
                results["errors"].append(f"{bill.bill_number}: {str(e)}")
                db.rollback()
        
        results["message"] = f"Analyzed {len(results['analyzed_bills'])} bills, {len(results['failed_bills'])} failed"
        return results
        
    except Exception as e:
        logger.error(f"Batch values analysis failed: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")


# =============================================================================
# VALUES ANALYSIS ADMIN ENDPOINTS
# =============================================================================

def verify_admin_api_key(request: Request) -> bool:
    """
    Simple API key verification for admin endpoints.
    TODO: Replace with proper admin authentication system.
    """
    api_key = request.headers.get("X-Admin-API-Key")
    # For now, use a simple check - replace with proper auth later
    expected_key = "admin-dev-key-2024"  # TODO: Move to environment variable
    return api_key == expected_key


@router.get("/review-queue")
async def get_review_queue(
    request: Request,
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """
    Get bills that require human review for values analysis.

    This endpoint returns bills that have been flagged by the AI for human review,
    allowing our policy team to review and approve the final tags and scores.

    Security: Protected by admin API key.
    """
    # Verify admin access
    if not verify_admin_api_key(request):
        raise HTTPException(status_code=401, detail="Invalid admin API key")

    try:
        # Query bills that require human review
        query = db.query(BillValuesAnalysis).filter(
            BillValuesAnalysis.requires_human_review == True,
            BillValuesAnalysis.reviewed_at.is_(None)
        ).order_by(BillValuesAnalysis.analyzed_at.desc())

        # Apply pagination
        total_count = query.count()
        analyses = query.offset(offset).limit(limit).all()

        # Build response with bill details and analysis
        review_items = []
        for analysis in analyses:
            bill = db.query(Bill).filter(Bill.id == analysis.bill_id).first()
            if not bill:
                continue

            # Get associated tags
            tags = db.query(BillValuesTag).filter(
                BillValuesTag.analysis_id == analysis.id,
                BillValuesTag.is_active == True
            ).all()

            review_items.append({
                "bill_id": bill.id,
                "bill_title": bill.title,
                "bill_number": bill.bill_number,
                "bill_type": bill.bill_type,
                "bill_status": bill.status,
                "analysis": {
                    "id": analysis.id,
                    "democracy_threat_score": analysis.democracy_threat_score,
                    "democracy_support_score": analysis.democracy_support_score,
                    "human_rights_threat_score": analysis.human_rights_threat_score,
                    "human_rights_support_score": analysis.human_rights_support_score,
                    "environmental_threat_score": analysis.environmental_threat_score,
                    "environmental_support_score": analysis.environmental_support_score,
                    "overall_threat_level": analysis.overall_threat_level,
                    "overall_support_level": analysis.overall_support_level,
                    "confidence_score": float(analysis.confidence_score) if analysis.confidence_score else None,
                    "analyzed_at": analysis.analyzed_at.isoformat(),
                    "ai_model_version": analysis.ai_model_version,
                    "reasoning": analysis.analysis_reasoning
                },
                "suggested_tags": [{
                    "id": tag.id,
                    "category": tag.tag_category,
                    "type": tag.tag_type,
                    "display_text": tag.display_text,
                    "description": tag.description,
                    "severity_level": tag.severity_level,
                    "color_theme": tag.color_theme,
                    "icon_name": tag.icon_name
                } for tag in tags],
                "flagged_reason": "Low confidence score" if analysis.confidence_score and analysis.confidence_score < 0.7 else "High impact detected"
            })

        return {
            "success": True,
            "data": {
                "items": review_items,
                "pagination": {
                    "total": total_count,
                    "limit": limit,
                    "offset": offset,
                    "has_more": offset + limit < total_count
                }
            }
        }

    except Exception as e:
        logger.error(f"Error fetching review queue: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch review queue: {str(e)}")


class AdminReviewDecision(BaseModel):
    """Model for admin review decisions."""
    bill_id: str
    reviewer_id: str  # Placeholder for now
    final_scores: Dict[str, int]  # e.g., {"democracy_threat": 5, "democracy_support": 2, ...}
    final_tags: List[Dict[str, Any]]  # Final approved tags
    review_notes: Optional[str] = None
    approved: bool = True  # Whether the analysis is approved or rejected


@router.post("/reviews")
async def submit_review_decision(
    request: Request,
    decision: AdminReviewDecision,
    db: Session = Depends(get_db)
):
    """
    Submit admin review decision for a bill's values analysis.

    This endpoint allows admins to approve, reject, or modify the AI's analysis
    and set the final tags and scores that users will see.

    Security: Protected by admin API key.
    """
    # Verify admin access
    if not verify_admin_api_key(request):
        raise HTTPException(status_code=401, detail="Invalid admin API key")

    try:
        # Get the analysis record
        analysis = db.query(BillValuesAnalysis).filter(
            BillValuesAnalysis.bill_id == decision.bill_id
        ).first()

        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found for this bill")

        # Check if already reviewed
        if analysis.reviewed_at:
            raise HTTPException(status_code=400, detail="This bill has already been reviewed")

        # Update analysis with final scores
        if decision.approved:
            analysis.democracy_threat_score = decision.final_scores.get("democracy_threat", analysis.democracy_threat_score)
            analysis.democracy_support_score = decision.final_scores.get("democracy_support", analysis.democracy_support_score)
            analysis.human_rights_threat_score = decision.final_scores.get("human_rights_threat", analysis.human_rights_threat_score)
            analysis.human_rights_support_score = decision.final_scores.get("human_rights_support", analysis.human_rights_support_score)
            analysis.environmental_threat_score = decision.final_scores.get("environmental_threat", analysis.environmental_threat_score)
            analysis.environmental_support_score = decision.final_scores.get("environmental_support", analysis.environmental_support_score)

            # Recalculate overall levels based on final scores
            max_threat = max(analysis.democracy_threat_score, analysis.human_rights_threat_score, analysis.environmental_threat_score)
            max_support = max(analysis.democracy_support_score, analysis.human_rights_support_score, analysis.environmental_support_score)

            analysis.overall_threat_level = _score_to_level(max_threat)
            analysis.overall_support_level = _score_to_level(max_support)

        # Mark as reviewed
        analysis.reviewed_by = decision.reviewer_id
        analysis.reviewed_at = datetime.utcnow()
        analysis.review_notes = decision.review_notes
        analysis.requires_human_review = False
        analysis.is_flagged = not decision.approved

        # Update tags if provided
        if decision.approved and decision.final_tags:
            # Deactivate existing tags
            db.query(BillValuesTag).filter(
                BillValuesTag.analysis_id == analysis.id
            ).update({"is_active": False})

            # Create new approved tags
            for tag_data in decision.final_tags:
                new_tag = BillValuesTag(
                    bill_id=analysis.bill_id,
                    analysis_id=analysis.id,
                    tag_category=tag_data.get("category", "general"),
                    tag_type=tag_data.get("type", "neutral"),
                    tag_name=tag_data.get("name", "admin_approved"),
                    display_text=tag_data.get("display_text", "Admin Approved"),
                    description=tag_data.get("description", "Approved by admin review"),
                    severity_level=tag_data.get("severity_level", 5),
                    display_priority=tag_data.get("display_priority", 5),
                    color_theme=tag_data.get("color_theme", "blue"),
                    icon_name=tag_data.get("icon_name", "check"),
                    is_active=True
                )
                db.add(new_tag)

        db.commit()

        logger.info(f"Admin review completed for bill {decision.bill_id} by {decision.reviewer_id}")

        return {
            "success": True,
            "message": "Review decision submitted successfully",
            "data": {
                "bill_id": decision.bill_id,
                "reviewed_at": analysis.reviewed_at.isoformat(),
                "approved": decision.approved,
                "final_threat_level": analysis.overall_threat_level,
                "final_support_level": analysis.overall_support_level
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error submitting review decision: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to submit review: {str(e)}")


def _score_to_level(score: int) -> str:
    """Convert numeric score to categorical level."""
    if score == 0:
        return 'none'
    elif score <= 3:
        return 'low'
    elif score <= 6:
        return 'medium'
    elif score <= 8:
        return 'high'
    else:
        return 'critical'


@router.get("/review-stats")
async def get_review_stats(
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Get statistics about the review queue and analysis coverage.

    Security: Protected by admin API key.
    """
    # Verify admin access
    if not verify_admin_api_key(request):
        raise HTTPException(status_code=401, detail="Invalid admin API key")

    try:
        # Count bills requiring review
        pending_reviews = db.query(BillValuesAnalysis).filter(
            BillValuesAnalysis.requires_human_review == True,
            BillValuesAnalysis.reviewed_at.is_(None)
        ).count()

        # Count completed reviews
        completed_reviews = db.query(BillValuesAnalysis).filter(
            BillValuesAnalysis.reviewed_at.is_not(None)
        ).count()

        # Count total analyses
        total_analyses = db.query(BillValuesAnalysis).count()

        # Count flagged bills
        flagged_bills = db.query(BillValuesAnalysis).filter(
            BillValuesAnalysis.is_flagged == True
        ).count()

        # Count by threat level
        threat_levels = db.query(
            BillValuesAnalysis.overall_threat_level,
            db.func.count(BillValuesAnalysis.id)
        ).group_by(BillValuesAnalysis.overall_threat_level).all()

        return {
            "success": True,
            "data": {
                "pending_reviews": pending_reviews,
                "completed_reviews": completed_reviews,
                "total_analyses": total_analyses,
                "flagged_bills": flagged_bills,
                "review_completion_rate": round((completed_reviews / total_analyses * 100) if total_analyses > 0 else 0, 2),
                "threat_level_distribution": {level: count for level, count in threat_levels}
            }
        }

    except Exception as e:
        logger.error(f"Error fetching review stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch stats: {str(e)}")


@router.post("/process-existing-bills-with-ai")
async def process_existing_bills_with_ai(
    limit: int = 5,
    db: Session = Depends(get_db)
):
    """
    Process existing bills in the database with OpenAI AI service.

    This endpoint finds bills that don't have AI summaries and processes them
    with the new OpenAI service to generate comprehensive AI analysis.

    Args:
        limit: Maximum number of bills to process (default: 5)
    """
    try:
        from app.models.bill import Bill
        from app.services.ai_service import AIService
        from datetime import datetime, timezone
        import asyncio

        # Initialize AI service
        ai_service = AIService()
        if not ai_service.enabled:
            return {
                "success": False,
                "error": "OpenAI service is not enabled. Please check API key configuration.",
                "processed_bills": [],
                "failed_bills": []
            }

        # Find bills without AI summaries
        bills_to_process = db.query(Bill).filter(
            Bill.ai_summary.is_(None)
        ).limit(limit).all()

        if not bills_to_process:
            return {
                "success": True,
                "message": "No bills found that need AI processing",
                "processed_bills": [],
                "failed_bills": [],
                "total_processed": 0
            }

        logger.info(f"Processing {len(bills_to_process)} bills with OpenAI AI service")

        processed_bills = []
        failed_bills = []

        for bill in bills_to_process:
            try:
                logger.info(f"Processing bill {bill.bill_number}: {bill.title[:50]}...")

                # Prepare bill metadata
                bill_metadata = {
                    "title": bill.title,
                    "bill_number": bill.bill_number,
                    "session_year": bill.session_year,
                    "chamber": bill.chamber,
                    "status": bill.status.value if bill.status else "unknown"
                }

                # Use bill text or title if no full text available
                bill_text = bill.full_text if bill.full_text else f"BILL TITLE: {bill.title}"

                # Generate comprehensive AI analysis
                ai_result = await ai_service.process_bill_complete(
                    bill_text=bill_text,
                    bill_metadata=bill_metadata
                )

                if ai_result and ai_result.get('ai_summary'):
                    # Update bill with AI analysis
                    bill.ai_summary = ai_result.get('ai_summary')
                    bill.ai_support_reasons = ai_result.get('support_reasons', [])
                    bill.ai_oppose_reasons = ai_result.get('oppose_reasons', [])
                    bill.ai_processed_at = datetime.now(timezone.utc)

                    # Update additional fields if available
                    if 'social_justice_score' in ai_result:
                        bill.social_justice_score = ai_result['social_justice_score']
                    if 'environmental_score' in ai_result:
                        bill.environmental_score = ai_result['environmental_score']
                    if 'democratic_score' in ai_result:
                        bill.democratic_score = ai_result['democratic_score']

                    processed_bills.append({
                        "bill_id": str(bill.id),
                        "bill_number": bill.bill_number,
                        "title": bill.title,
                        "ai_summary_length": len(ai_result.get('ai_summary', '')),
                        "support_reasons_count": len(ai_result.get('support_reasons', [])),
                        "oppose_reasons_count": len(ai_result.get('oppose_reasons', []))
                    })

                    logger.info(f"Successfully processed bill {bill.bill_number}")
                else:
                    failed_bills.append({
                        "bill_id": str(bill.id),
                        "bill_number": bill.bill_number,
                        "error": "AI service returned no results"
                    })
                    logger.error(f"AI service returned no results for bill {bill.bill_number}")

                # Small delay to avoid rate limits
                await asyncio.sleep(1)

            except Exception as e:
                failed_bills.append({
                    "bill_id": str(bill.id),
                    "bill_number": bill.bill_number,
                    "error": str(e)
                })
                logger.error(f"Failed to process bill {bill.bill_number}: {e}")

        # Commit all changes
        db.commit()

        return {
            "success": True,
            "message": f"Processed {len(processed_bills)} bills successfully",
            "total_processed": len(processed_bills) + len(failed_bills),
            "processed_bills": processed_bills,
            "failed_bills": failed_bills,
            "ai_service_enabled": ai_service.enabled
        }

    except Exception as e:
        logger.error(f"Error processing existing bills with AI: {e}")
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")
