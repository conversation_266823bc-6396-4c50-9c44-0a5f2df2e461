# app/services/action_network_service.py
"""
Action Network embedded forms service for professional message delivery to representatives.

This service integrates with Action Network's embedded forms to submit personalized messages
to federal representatives on behalf of users. Uses UI-created campaigns that actually
send messages to representatives (unlike API-created campaigns).
"""

import logging
import os
from typing import Dict, Any
from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class ActionNetworkService:
    """Service for integrating with Action Network UI-created campaigns for actual message delivery"""

    def __init__(self):
        self.api_key = getattr(settings, 'ACTION_NETWORK_API_KEY', None) or os.getenv('ACTION_NETWORK_API_KEY')
        self.base_url = "https://actionnetwork.org"

        # UI-created campaign IDs for actual message delivery
        # These must be created manually through Action Network's UI to actually send messages
        self.senate_campaign_id = os.getenv('ACTION_NETWORK_SENATE_CAMPAIGN_ID', 'modernaction-senate-2024')
        self.house_campaign_id = os.getenv('ACTION_NETWORK_HOUSE_CAMPAIGN_ID', 'modernaction-house-2024')

        if not self.api_key:
            logger.warning("ACTION_NETWORK_API_KEY not configured. Action Network features will be disabled.")
            self.enabled = False
        else:
            self.enabled = True
            logger.info(f"Action Network service initialized with Senate: {self.senate_campaign_id}, House: {self.house_campaign_id}")
            logger.info("Using UI-created campaigns for actual message delivery to officials")
    
    def determine_target_chamber_and_form(self, bill_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Determine which Action Network form to use based on bill status and chamber
        
        MVP Logic for maximum impact:
        1. House bills being voted on -> Contact House representatives
        2. Senate bills being voted on -> Contact Senators  
        3. Bills that passed one chamber -> Contact the chamber currently reviewing it
        4. Default: Contact chamber of origin
        
        Args:
            bill_data: Dict containing bill status, chamber, and bill_type info
            
        Returns:
            Dict with target_chamber, form_type, and routing_reason
        """
        bill_status = bill_data.get('status', 'introduced').lower()
        bill_chamber = bill_data.get('chamber', 'house').lower()  # Origin chamber
        bill_type = bill_data.get('bill_type', 'house_bill').lower()
        bill_number = bill_data.get('bill_number', 'Unknown')
        
        logger.info(f"Determining MVP routing for {bill_number}: status={bill_status}, chamber={bill_chamber}, type={bill_type}")
        
        # Enhanced MVP routing logic
        if bill_status in ['draft', 'introduced', 'committee']:
            # Bill is in early stages - contact origin chamber where it's being developed
            target_chamber = bill_chamber
            routing_reason = f"Bill in {bill_status} stage in origin {bill_chamber}"
            
        elif bill_status == 'floor':
            # Bill is on floor for voting - contact the chamber voting on it
            target_chamber = bill_chamber
            routing_reason = f"Bill on {bill_chamber} floor for voting - maximum impact"
            
        elif bill_status == 'passed':
            # Bill passed origin chamber, now in other chamber - contact new chamber
            if bill_chamber == 'house':
                target_chamber = 'senate'
                routing_reason = "Bill passed House, now in Senate for consideration"
            else:
                target_chamber = 'house' 
                routing_reason = "Bill passed Senate, now in House for consideration"
                
        elif bill_status in ['signed', 'vetoed']:
            # Bill completed - for accountability, contact both chambers
            target_chamber = 'both'
            routing_reason = f"Bill {bill_status} - contact all representatives for accountability"
            
        elif bill_status == 'failed':
            # Bill failed - contact chamber that failed it for feedback
            target_chamber = bill_chamber
            routing_reason = f"Bill failed in {bill_chamber} - advocate for reconsideration"
            
        else:
            # Default: route to origin chamber
            target_chamber = bill_chamber
            routing_reason = f"Default routing to origin chamber ({bill_chamber})"
        
        # Determine form type - simplified for MVP
        if target_chamber == 'both':
            # For 'both', start with Senate (typically has broader forms)
            form_type = 'senate'  
            routing_reason += " (using Senate form for broader coverage)"
        else:
            form_type = target_chamber
            
        logger.info(f"MVP Routing: {bill_number} -> {form_type.upper()} form ({routing_reason})")
        
        return {
            'target_chamber': target_chamber,
            'form_type': form_type,
            'routing_reason': routing_reason,
            'bill_number': bill_number
        }

    async def get_campaign_embed_info(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get Action Network campaign embed information with smart bill-status-based routing

        Args:
            message_data: Dict containing:
                - chamber: Bill's origin chamber
                - bill_status: Current status of bill 
                - bill_type: Type of bill (house_bill, senate_bill, etc.)
                - bill_number: Bill being addressed
                - position: 'support', 'oppose', or 'amend'

        Returns:
            Dict with campaign embed information for frontend
        """
        if not self.enabled:
            logger.warning("Action Network service is not enabled")
            return {
                'status': 'disabled',
                'message': 'Action Network service is not configured'
            }

        try:
            # Determine smart routing based on bill status
            routing_info = self.determine_target_chamber_and_form(message_data)
            target_chamber = routing_info['target_chamber']
            form_type = routing_info['form_type']
            
            # Select appropriate campaign ID based on routing decision
            if form_type == 'senate':
                campaign_id = self.senate_campaign_id
                chamber_label = 'Senate'
            elif form_type == 'house':
                campaign_id = self.house_campaign_id
                chamber_label = 'House'
            elif form_type == 'unified':
                # For bills that passed both chambers, prefer senate form (typically handles both)
                campaign_id = self.senate_campaign_id
                chamber_label = 'Congress'
            else:
                # Default fallback
                campaign_id = self.house_campaign_id
                chamber_label = 'House'

            logger.info(f"Selected {chamber_label} campaign: {campaign_id}")
            logger.info(f"Routing reason: {routing_info['routing_reason']}")

            # Return enhanced embed information with routing details
            return {
                'status': 'success',
                'campaign_id': campaign_id,
                'chamber': form_type,
                'target_chamber': target_chamber,
                'embed_url': f"{self.base_url}/letters/{campaign_id}",
                'iframe_url': f"{self.base_url}/letters/{campaign_id}/embed",
                'message': f"Action Network {chamber_label} campaign ready",
                'routing_info': routing_info
            }

        except Exception as e:
            logger.error(f"Failed to get Action Network campaign info: {e}")
            return {
                'status': 'error',
                'message': 'Failed to get campaign information',
                'error': str(e)
            }

    async def submit_message(self, message_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Submit message to Action Network API for actual delivery to representatives
        
        Args:
            message_data: Dict containing:
                - person: Dict with user info (first_name, last_name, email, address_line1, city, state, zip_code)
                - targets: List of representatives to contact
                - subject: Email subject
                - body: Message content
                - bill_number: Bill being addressed
                - position: 'support', 'oppose', or 'amend'
                
        Returns:
            Dict with submission results
        """
        if not self.enabled:
            logger.warning("Action Network service is not enabled")
            return {
                'status': 'disabled',
                'message': 'Action Network service is not configured'
            }

        try:
            # Determine routing and get appropriate campaign
            routing_info = self.determine_target_chamber_and_form(message_data)
            target_chamber = routing_info['target_chamber']
            form_type = routing_info['form_type']
            
            # Select appropriate campaign ID
            if form_type == 'senate':
                campaign_id = self.senate_campaign_id
            elif form_type == 'house':
                campaign_id = self.house_campaign_id
            else:
                # Default to house
                campaign_id = self.house_campaign_id
                
            logger.info(f"Submitting to Action Network campaign: {campaign_id}")
            
            # Extract person and message data
            person = message_data.get('person', {})
            targets = message_data.get('targets', [])
            subject = message_data.get('subject', '')
            body = message_data.get('body', '')
            bill_number = message_data.get('bill_number', '')
            position = message_data.get('position', 'support')
            
            # Import aiohttp for async HTTP requests
            import aiohttp
            import json
            
            # Prepare Action Network submission data
            submission_data = {
                "person": {
                    "given_name": person.get('first_name', ''),
                    "family_name": person.get('last_name', ''),
                    "email_addresses": [{"address": person.get('email', '')}],
                    "postal_addresses": [{
                        "address_lines": [person.get('address_line1', '')],
                        "locality": person.get('city', ''),
                        "region": person.get('state', ''),
                        "postal_code": person.get('zip_code', ''),
                        "country": "US"
                    }]
                },
                "message": {
                    "subject": subject,
                    "body": body
                },
                "targets": targets,
                "add_tags": [
                    f"bill_{bill_number.replace(' ', '_')}",
                    f"position_{position}",
                    "modernaction_platform"
                ]
            }
            
            # Submit to Action Network API
            headers = {
                'Content-Type': 'application/json',
                'OSDI-API-Token': self.api_key
            }
            
            url = f"https://actionnetwork.org/api/v2/messages"
            
            successful_submissions = 0
            failed_submissions = 0
            results = []
            
            # For now, simulate a successful submission since Action Network's API
            # requires specific setup that may not be available in development
            if os.getenv('DEVELOPMENT_MODE', 'false').lower() == 'true':
                logger.info("Development mode: simulating Action Network submission")
                
                for target in targets:
                    target_name = target.get('name', 'Unknown Representative')
                    results.append({
                        'target_name': target_name,
                        'success': True,
                        'message_id': f"dev_msg_{hash(f'{target_name}_{subject}')}"[:12],
                        'status': 'simulated_success'
                    })
                    successful_submissions += 1
                    
                return {
                    'status': 'success',
                    'message': f'Development: Simulated {successful_submissions} message submissions',
                    'total_targets': len(targets),
                    'successful_submissions': successful_submissions,
                    'failed_submissions': failed_submissions,
                    'results': results,
                    'development_mode': True
                }
            
            else:
                # Production: actual Action Network API call
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, headers=headers, json=submission_data) as response:
                        if response.status == 201:  # Created
                            response_data = await response.json()
                            message_id = response_data.get('identifiers', [{}])[0].get('action_network:identifier')
                            
                            # Mark all targets as successful for a single message submission
                            for target in targets:
                                results.append({
                                    'target_name': target.get('name', 'Unknown Representative'),
                                    'success': True,
                                    'message_id': message_id,
                                    'status': 'delivered'
                                })
                                successful_submissions += 1
                            
                            logger.info(f"Successfully submitted message to Action Network: {message_id}")
                            
                            return {
                                'status': 'success',
                                'message': f'Successfully submitted message to {len(targets)} representatives',
                                'total_targets': len(targets),
                                'successful_submissions': successful_submissions,
                                'failed_submissions': failed_submissions,
                                'results': results,
                                'action_network_id': message_id
                            }
                            
                        else:
                            error_text = await response.text()
                            logger.error(f"Action Network submission failed: {response.status} - {error_text}")
                            
                            # Mark all targets as failed
                            for target in targets:
                                results.append({
                                    'target_name': target.get('name', 'Unknown Representative'),
                                    'success': False,
                                    'error': f'HTTP {response.status}: {error_text}',
                                    'status': 'failed'
                                })
                                failed_submissions += 1
                            
                            return {
                                'status': 'error',
                                'message': f'Action Network submission failed: HTTP {response.status}',
                                'total_targets': len(targets),
                                'successful_submissions': successful_submissions,
                                'failed_submissions': failed_submissions,
                                'results': results,
                                'error': error_text
                            }

        except Exception as e:
            logger.error(f"Error submitting message to Action Network: {e}")
            
            # Mark all targets as failed due to exception
            targets = message_data.get('targets', [])
            results = []
            for target in targets:
                results.append({
                    'target_name': target.get('name', 'Unknown Representative'),
                    'success': False,
                    'error': str(e),
                    'status': 'failed'
                })
            
            return {
                'status': 'error',
                'message': f'Submission failed: {str(e)}',
                'total_targets': len(targets),
                'successful_submissions': 0,
                'failed_submissions': len(targets),
                'results': results,
                'error': str(e)
            }
    
    async def track_delivery_attempt(self, campaign_id: str, person_data: Dict[str, Any],
                                    bill_number: str = None, position: str = None) -> Dict[str, Any]:
        """
        Track a delivery attempt for analytics (called when user completes Action Network form)
        This method can be called via webhook or frontend callback when user completes the form
        """
        try:
            logger.info(f"Tracking delivery attempt for campaign {campaign_id}")

            # In a real implementation, this would:
            # 1. Update database delivery tracking
            # 2. Send analytics events
            # 3. Update user action history

            return {
                'success': True,
                'campaign_id': campaign_id,
                'message': 'Delivery attempt tracked successfully'
            }

        except Exception as e:
            logger.error(f"Error tracking delivery attempt: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    

    
    def health_check(self) -> Dict[str, Any]:
        """Check the health of the Action Network service configuration"""
        if not self.enabled:
            return {
                'status': 'disabled',
                'message': 'Action Network API key not configured'
            }

        try:
            # Check that campaign IDs are configured
            if not self.senate_campaign_id or not self.house_campaign_id:
                return {
                    'status': 'error',
                    'message': 'Action Network campaign IDs not configured'
                }

            return {
                'status': 'healthy',
                'message': f'Action Network service ready (Senate: {self.senate_campaign_id}, House: {self.house_campaign_id})',
                'senate_url': f"{self.base_url}/letters/{self.senate_campaign_id}",
                'house_url': f"{self.base_url}/letters/{self.house_campaign_id}"
            }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'Action Network service error: {e}'
            }


# Convenience function for easy import
def get_action_network_service() -> ActionNetworkService:
    """Get an Action Network service instance"""
    return ActionNetworkService()
