import Link from 'next/link';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Official Not Found | ModernAction.io',
  description: 'The requested official profile could not be found. Search for other elected representatives.',
  robots: {
    index: false,
    follow: true
  }
};

export default function OfficialNotFound() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center py-12">
          {/* Large icon */}
          <div className="mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-blue-100 mb-6">
            <svg className="h-16 w-16 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>

          <h1 className="text-3xl font-bold text-gray-900 mb-4">Official Not Found</h1>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            The official profile you're looking for doesn't exist or may have been removed. 
            This could happen if the official ID is incorrect or if the representative is no longer in office.
          </p>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/officials"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Search All Officials
            </Link>
            
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              Back to Home
            </Link>
          </div>

          {/* Help section */}
          <div className="mt-12 bg-blue-50 rounded-lg p-6 max-w-2xl mx-auto">
            <h2 className="text-lg font-semibold text-blue-900 mb-3">Looking for Your Representatives?</h2>
            <p className="text-blue-800 text-sm mb-4">
              Enter your ZIP code to find all your federal and state representatives, including senators and house members.
            </p>
            <Link
              href="/officials#zip-lookup"
              className="inline-flex items-center px-4 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-800 bg-white hover:bg-blue-100 transition-colors duration-200"
            >
              Find by ZIP Code
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}