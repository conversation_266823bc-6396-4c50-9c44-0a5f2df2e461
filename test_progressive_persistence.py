#!/usr/bin/env python3
"""
Direct test of the progressive persistence system in BalancedAnalysisService
"""
import sys
import os
sys.path.append('./apps/api')
os.environ['ENV_FILE'] = '.env.local'

import asyncio
from app.db.database import get_db
from app.services.ai_service import AIService
from app.models.bill import Bill

async def test_progressive_persistence():
    print("🧪 DIRECT TEST: Progressive Persistence in BalancedAnalysisService")
    
    try:
        db = next(get_db())
        
        # Get HR4922 bill
        bill = db.query(Bill).filter(Bill.bill_number == 'HR4922').first()
        if not bill:
            print("❌ HR4922 bill not found")
            return
        
        print(f"📋 Testing bill: {bill.title}")
        print(f"📄 Full text length: {len(bill.full_text or '')} characters")
        
        # Clear AI processing flag to force reprocessing
        bill.ai_processed_at = None
        db.commit()
        
        # Create AI service and get the balanced analyzer directly
        ai_service = AIService()
        balanced_analyzer = ai_service.balanced_analyzer
        
        bill_metadata = {
            'title': bill.title,
            'bill_number': bill.bill_number,
            'session_year': bill.session_year,
            'bill_id': str(bill.id)
        }
        
        print(f"🎯 Calling balanced_analyzer.analyze_bill_balanced directly...")
        print(f"   This should trigger progressive persistence with 💾 logs")
        
        # Call the balanced analyzer directly (this should have progressive persistence)
        result = await balanced_analyzer.analyze_bill_balanced(
            bill_text=bill.full_text or '',
            bill_metadata=bill_metadata,
            evidence_spans=[]  # Empty evidence spans for now - service will extract them
        )
        
        print(f"📊 Result success: {result.get('success', False)}")
        
        if result.get('success'):
            details_payload = result.get('details_payload', {})
            print(f"📦 Details payload size: {len(str(details_payload))} chars")
            
            overview = details_payload.get('overview', {})
            complete_analysis = overview.get('complete_analysis', [])
            print(f"📑 Complete Analysis sections: {len(complete_analysis)}")
            
            # Check for progressive persistence metadata
            chunks_completed = overview.get('chunks_completed', 0)
            total_chunks = overview.get('total_chunks', 0)
            print(f"🔄 Progressive status: {chunks_completed}/{total_chunks} chunks")
            
            if len(complete_analysis) > 0:
                print("✅ SUCCESS: Complete Analysis generated with progressive persistence!")
                sample = complete_analysis[0]
                print(f"   Sample title: {sample.get('title', 'No title')[:60]}...")
                print(f"   Sample content: {len(sample.get('detailed_summary', ''))} chars")
                print(f"   Evidence IDs: {sample.get('ev_ids', [])}")
            else:
                print("❌ FAILED: No Complete Analysis sections generated")
        else:
            print(f"❌ FAILED: {result.get('error', 'Unknown error')}")
            
        db.close()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_progressive_persistence())