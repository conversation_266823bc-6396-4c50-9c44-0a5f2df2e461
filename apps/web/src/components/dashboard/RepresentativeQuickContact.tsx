'use client';

import React, { useState, useEffect } from 'react';
import { Official } from '@/types';
import Link from 'next/link';
import { trackDashboardContact } from '@/services/analytics';

interface RepresentativeQuickContactProps {
  representatives: Official[];
  userZip?: string;
  className?: string;
  showAllContacts?: boolean;
}

interface QuickContactAction {
  type: 'social' | 'traditional';
  platform: string;
  icon: string;
  label: string;
  url: string;
  color: string;
}

export const RepresentativeQuickContact: React.FC<RepresentativeQuickContactProps> = ({
  representatives,
  userZip,
  className = '',
  showAllContacts = true
}) => {
  const [selectedOfficial, setSelectedOfficial] = useState<Official | null>(null);
  const [showModal, setShowModal] = useState(false);

  // Get quick contact actions for an official
  const getQuickActions = (official: Official): QuickContactAction[] => {
    const actions: QuickContactAction[] = [];
    
    // Social media actions
    if (official.social_media) {
      Object.entries(official.social_media).forEach(([platform, url]) => {
        const platformLower = platform.toLowerCase();
        
        if (platformLower.includes('twitter')) {
          actions.push({
            type: 'social',
            platform: 'twitter',
            icon: '🐦',
            label: 'Twitter',
            url: url,
            color: 'bg-blue-500'
          });
        } else if (platformLower.includes('facebook')) {
          actions.push({
            type: 'social',
            platform: 'facebook',
            icon: '📘',
            label: 'Facebook',
            url: url,
            color: 'bg-blue-600'
          });
        } else if (platformLower.includes('instagram')) {
          actions.push({
            type: 'social',
            platform: 'instagram',
            icon: '📷',
            label: 'Instagram',
            url: url,
            color: 'bg-pink-500'
          });
        }
      });
    }
    
    // Traditional contact methods
    if (official.email && showAllContacts) {
      actions.push({
        type: 'traditional',
        platform: 'email',
        icon: '📧',
        label: 'Email',
        url: `mailto:${official.email}`,
        color: 'bg-gray-500'
      });
    }
    
    if (official.phone && showAllContacts) {
      actions.push({
        type: 'traditional',
        platform: 'phone',
        icon: '📞',
        label: 'Call',
        url: `tel:${official.phone}`,
        color: 'bg-green-500'
      });
    }
    
    if ((official.website || official.homepage_url) && showAllContacts) {
      actions.push({
        type: 'traditional',
        platform: 'website',
        icon: '🌐',
        label: 'Website',
        url: official.website || official.homepage_url || '',
        color: 'bg-purple-500'
      });
    }
    
    return actions;
  };

  // Quick message template for social media
  const createQuickMessage = (official: Official, context: string = 'general'): string => {
    const displayName = official.full_name || official.name;
    const handle = getTwitterHandle(official);
    
    if (context === 'general') {
      return `${handle ? `${handle} ` : `Dear ${displayName}, `}Thank you for your service representing${userZip ? ` ${userZip}` : ' our community'}. I wanted to reach out about an issue that's important to me and many of your constituents...`;
    }
    
    return '';
  };

  // Extract Twitter handle
  const getTwitterHandle = (official: Official): string => {
    if (!official.social_media) return '';
    
    const twitterKey = Object.keys(official.social_media).find(key => 
      key.toLowerCase().includes('twitter')
    );
    
    if (!twitterKey) return '';
    
    const url = official.social_media[twitterKey];
    const match = url.match(/(?:twitter\.com|x\.com)\/([A-Za-z0-9_]+)/);
    return match ? `@${match[1]}` : '';
  };

  // Handle quick contact action
  const handleQuickAction = (official: Official, action: QuickContactAction) => {
    if (action.type === 'social' && action.platform === 'twitter') {
      const message = createQuickMessage(official);
      const tweetUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}`;
      window.open(tweetUrl, '_blank', 'width=600,height=400');
    } else {
      window.open(action.url, '_blank');
    }
    
    // Track the engagement
    trackDashboardContact(
      { id: official.id, name: official.name },
      action.platform,
      userZip
    );
  };

  if (representatives.length === 0) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-6 text-center ${className}`}>
        <div className="text-4xl mb-2">🏛️</div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Find Your Representatives</h3>
        <p className="text-gray-600 mb-4">
          Enter your ZIP code to see your elected officials and contact them easily.
        </p>
        <Link
          href="/officials"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
        >
          Search Officials
        </Link>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <span className="mr-2">👥</span>
          Your Representatives
        </h3>
        {userZip && (
          <span className="text-sm text-gray-500">📍 {userZip}</span>
        )}
      </div>

      <div className="grid gap-4">
        {representatives.map(official => {
          const quickActions = getQuickActions(official);
          const socialActions = quickActions.filter(a => a.type === 'social');
          const traditionalActions = quickActions.filter(a => a.type === 'traditional');
          
          return (
            <div key={official.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                      {official.official_photo_url ? (
                        <img
                          src={official.official_photo_url}
                          alt={official.name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-lg font-semibold text-gray-600">
                          {official.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      )}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {official.full_name || official.name}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {official.title}
                        {official.party && (
                          <span className="ml-2 text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded">
                            {official.party}
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                  
                  {/* Quick Social Actions */}
                  {socialActions.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-3">
                      {socialActions.slice(0, 3).map(action => (
                        <button
                          key={action.platform}
                          onClick={() => handleQuickAction(official, action)}
                          className={`inline-flex items-center px-3 py-1.5 text-sm font-medium text-white rounded-full ${action.color} hover:opacity-90 transition-opacity`}
                          title={`Message on ${action.label}`}
                        >
                          <span className="mr-1">{action.icon}</span>
                          {action.label}
                        </button>
                      ))}
                    </div>
                  )}
                  
                  {/* Traditional Contact Options (Compact) */}
                  {traditionalActions.length > 0 && (
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      {traditionalActions.slice(0, 2).map(action => (
                        <button
                          key={action.platform}
                          onClick={() => handleQuickAction(official, action)}
                          className="flex items-center space-x-1 hover:text-gray-900 transition-colors"
                          title={action.label}
                        >
                          <span>{action.icon}</span>
                          <span>{action.label}</span>
                        </button>
                      ))}
                      
                      {quickActions.length > 5 && (
                        <button
                          onClick={() => {
                            setSelectedOfficial(official);
                            setShowModal(true);
                          }}
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          +{quickActions.length - 5} more
                        </button>
                      )}
                    </div>
                  )}
                </div>
                
                <div className="flex items-center space-x-2">
                  <Link
                    href={`/officials/${official.id}`}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
                    title="View full profile"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Actions Summary */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-semibold text-blue-900 mb-2 flex items-center">
          <span className="mr-2">💡</span>
          Quick Contact Tips
        </h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Social media messages often get faster responses</li>
          <li>• Be specific about your ZIP code and how issues affect you</li>
          <li>• Follow up social contacts with formal emails or calls</li>
          <li>• Tag relevant committees and other representatives for visibility</li>
        </ul>
      </div>

      {/* All Contact Options Modal */}
      {showModal && selectedOfficial && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  Contact {selectedOfficial.name}
                </h3>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="space-y-3">
                {getQuickActions(selectedOfficial).map(action => (
                  <button
                    key={action.platform}
                    onClick={() => {
                      handleQuickAction(selectedOfficial, action);
                      setShowModal(false);
                    }}
                    className={`flex items-center space-x-3 w-full p-3 text-left rounded-lg text-white hover:opacity-90 transition-opacity ${action.color}`}
                  >
                    <span className="text-xl">{action.icon}</span>
                    <div>
                      <div className="font-medium">{action.label}</div>
                      <div className="text-sm opacity-90">
                        {action.type === 'social' ? 'Send message' : 'Direct contact'}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
              
              <div className="mt-4 pt-4 border-t border-gray-200">
                <Link
                  href={`/officials/${selectedOfficial.id}`}
                  className="block text-center py-2 text-blue-600 hover:text-blue-800 font-medium"
                  onClick={() => setShowModal(false)}
                >
                  View Full Profile & Legislative Record →
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RepresentativeQuickContact;