import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { <PERSON>, <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';
import { Campaign, Official } from '../../types';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import axios from 'axios';

interface ActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  campaign: Campaign;
  officials: Official[];
  onSubmit: (data: ActionFormData) => Promise<void>;
  isLoading?: boolean;
}

export interface ActionFormData {
  email: string;
  message: string;
  action_types?: string[];
}

const ActionModal: React.FC<ActionModalProps> = ({
  isOpen,
  onClose,
  campaign,
  officials,
  onSubmit,
  isLoading = false
}) => {
  // Default message combining campaign info and talking points
  const defaultMessage = React.useMemo(() => {
    let message = campaign.call_to_action || 'I am writing to express my position on this important issue.';

    if (campaign.talking_points && campaign.talking_points.length > 0) {
      message += '\n\nKey points:\n';
      campaign.talking_points.forEach((point, index) => {
        message += `${index + 1}. ${point}\n`;
      });
    }

    message += '\n\nI urge you to take action on this matter. Thank you for your time and consideration.';

    return message;
  }, [campaign]);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    watch,
    setValue
  } = useForm<ActionFormData>({
    mode: 'onChange',
    defaultValues: {
      email: '',
      message: defaultMessage
    }
  });

  const watchedEmail = watch('email');

  // AI assistance state
  const [isLoadingAI, setIsLoadingAI] = useState(false);
  const [aiInput, setAiInput] = useState('');
  const [aiProgress, setAiProgress] = useState(0);
  const [aiError, setAiError] = useState<string | null>(null);

  // Twitter toggle state
  const [includeTwitter, setIncludeTwitter] = useState(false);

  const handleClose = () => {
    reset();
    onClose();
  };

  const onFormSubmit = async (data: ActionFormData) => {
    try {
      // Build action_types array based on user selections
      const actionTypes = ['EMAIL']; // Always include email
      if (includeTwitter) {
        actionTypes.push('TWITTER');
      }

      // Add action_types to the form data
      const submissionData = {
        ...data,
        action_types: actionTypes
      };

      await onSubmit(submissionData);
      handleClose();
    } catch (error) {
      console.error('Failed to submit action:', error);
      // Error handling is managed by the parent component
    }
  };

  const handleAIGenerate = async () => {
    if (!aiInput.trim()) {
      return;
    }

    setIsLoadingAI(true);
    setAiProgress(0);
    setAiError(null);

    // Simulate progress for better UX
    const progressInterval = setInterval(() => {
      setAiProgress(prev => Math.min(prev + 10, 90));
    }, 300);

    try {
      const response = await axios.post('/api/v1/ai/personalize-message', {
        raw_text: aiInput,
        context: `${campaign.title} - ${campaign.description}`,
        tone: 'professional'
      });

      setAiProgress(100);
      const personalizedMessage = response.data.personalized_message;
      setValue('message', personalizedMessage, { shouldValidate: true });
      setAiInput(''); // Clear the AI input after successful generation

      // Brief delay to show completion
      setTimeout(() => {
        setAiProgress(0);
      }, 500);
    } catch (error) {
      console.error('Failed to generate AI message:', error);
      setAiError(error instanceof Error ? error.message : 'Failed to generate message. Please try again.');
    } finally {
      clearInterval(progressInterval);
      setIsLoadingAI(false);
    }
  };



  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Contact Your Representatives</DialogTitle>
          <DialogDescription>
            Send a personalized message to your elected officials about this important issue.
          </DialogDescription>
        </DialogHeader>

        {/* Campaign Context */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Badge variant="civic" className="mt-1">Campaign</Badge>
              <div>
                <h4 className="font-medium text-gray-900 mb-1">{campaign.title}</h4>
                <p className="text-sm text-gray-600">{campaign.description}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Officials Preview */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 mb-3">Your message will be sent to:</h4>
          <div className="space-y-2">
            {officials.map((official) => (
              <Card key={official.id} className="bg-gray-50">
                <CardContent className="p-3">
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0">
                      {official.photo_url ? (
                        /* eslint-disable-next-line @next/next/no-img-element */
                        <img
                          className="h-10 w-10 rounded-full object-cover"
                          src={official.photo_url}
                          alt={official.name}
                        />
                      ) : (
                        <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                          <span className="text-sm font-medium text-gray-600">
                            {official.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{official.name}</p>
                      <p className="text-sm text-gray-600">
                        {official.level === 'federal' ? 'U.S. ' : ''}
                        {official.chamber === 'house' ? 'Representative' : 'Senator'}
                        {official.party && ` (${official.party})`}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email">
              Your Email Address *
            </Label>
            <Input
              type="email"
              id="email"
              data-testid="action-modal-email-input"
              {...register('email', {
                required: 'Email address is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Please enter a valid email address'
                }
              })}
              className={errors.email ? 'border-destructive' : ''}
              placeholder="<EMAIL>"
              disabled={isLoading}
            />
            {errors.email && (
              <p className="text-sm text-destructive">{errors.email.message}</p>
            )}
          </div>

          {/* AI Message Assistant */}
          <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
            <CardContent className="p-4">
              <div className="flex items-center mb-3">
                <Sparkles className="h-5 w-5 text-purple-600 mr-2" />
                <h4 className="font-medium text-purple-900">AI Message Assistant</h4>
              </div>
              <p className="text-sm text-purple-700 mb-3">
                Share your personal story or key concerns, and our AI will help craft a more persuasive message.
              </p>
              <div className="space-y-3">
                <Textarea
                  data-testid="ai-assist-input"
                  value={aiInput}
                  onChange={(e) => setAiInput(e.target.value)}
                  placeholder="e.g., I'm a parent concerned about climate change affecting my children's future..."
                  className="border-purple-300 focus:border-purple-500 focus:ring-purple-500"
                  rows={3}
                  disabled={isLoading || isLoadingAI}
                />
                <Button
                  type="button"
                  data-testid="ai-assist-generate-button"
                  onClick={handleAIGenerate}
                  disabled={!aiInput.trim() || isLoading || isLoadingAI}
                  className="bg-purple-600 hover:bg-purple-700 focus:ring-purple-500"
                  size="sm"
                >
                  {isLoadingAI ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Crafting your message...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate My Message
                    </>
                  )}
                </Button>

                {/* Progress bar */}
                {isLoadingAI && (
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-purple-600 h-2 rounded-full transition-all duration-300 ease-out"
                      style={{ width: `${aiProgress}%` }}
                    ></div>
                  </div>
                )}

                {/* Error message */}
                {aiError && (
                  <p className="text-sm text-destructive bg-destructive/10 p-2 rounded mt-2">
                    {aiError}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Message Field */}
          <div className="space-y-2">
            <Label htmlFor="message">
              Your Message
            </Label>
            <Textarea
              id="message"
              rows={8}
              data-testid="action-modal-message-textarea"
              {...register('message', {
                required: 'Message is required',
                minLength: {
                  value: 10,
                  message: 'Message must be at least 10 characters long'
                }
              })}
              className={errors.message ? 'border-destructive' : ''}
              placeholder="Your message to your representatives..."
              disabled={isLoading}
              defaultValue={defaultMessage}
            />
            {errors.message && (
              <p className="text-sm text-destructive">{errors.message.message}</p>
            )}
            <p className="text-xs text-gray-500">
              Personal messages are more effective than form letters. Feel free to edit this message.
            </p>
          </div>

          {/* Twitter Toggle */}
          <Card className="bg-blue-50/50 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="twitter-toggle"
                  data-testid="action-modal-tweet-toggle"
                  checked={includeTwitter}
                  onChange={(e) => setIncludeTwitter(e.target.checked)}
                  disabled={isLoading}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-input rounded"
                />
                <Label htmlFor="twitter-toggle" className="flex-1 cursor-pointer">
                  <div className="text-sm font-medium text-blue-900">
                    Also post a public Tweet to this official
                  </div>
                  <div className="text-xs text-blue-700">
                    Your message will be shared publicly on Twitter to increase visibility and pressure
                  </div>
                </Label>
                <div className="text-blue-600">
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              data-testid="action-modal-cancel-button"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="action"
              data-testid="action-modal-send-button"
              disabled={!isValid || !watchedEmail || isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                'Send Message'
              )}
            </Button>
          </div>
        </form>

        {/* Privacy Notice */}
        <Card className="mt-6 bg-gray-50">
          <CardContent className="p-3">
            <p className="text-xs text-gray-500">
              <strong>Privacy Notice:</strong> Your email and message will only be used to contact your representatives about this campaign. 
              We respect your privacy and will never share your personal information with third parties.
            </p>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );
};

export default ActionModal;