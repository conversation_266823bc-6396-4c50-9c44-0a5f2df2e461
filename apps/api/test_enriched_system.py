#!/usr/bin/env python3
"""
Test the enriched analysis system with budget controls
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

async def test_enriched_analysis():
    """Test the enriched analysis system"""
    try:
        from app.services.ai_service import AIService
        from app.services.enriched_analysis_service import EnrichedAnalysisService
        
        print("🧪 Testing Enriched Analysis System")
        print("=" * 60)
        
        ai_service = AIService()
        if not ai_service.enabled:
            print("⚠️ AI service not enabled - skipping test")
            return False
        
        # Test sample bill with money and mandates (should route to high priority)
        sample_bill_text = """
        SEC. 1. SHORT TITLE.
        This Act may be cited as the "Comprehensive Test Act of 2024".
        
        SEC. 2. AUTHORIZATION OF APPROPRIATIONS.
        (a) In General.--There are authorized to be appropriated $100,000,000 for fiscal year 2024 to carry out this Act.
        (b) Limitations.--No funds authorized under this section shall be used for administrative costs exceeding 5 percent.
        (c) Allocation.--Of the amounts appropriated under subsection (a), not less than 60 percent shall be allocated to direct services.
        
        SEC. 3. MANDATORY COMPLIANCE REQUIREMENTS.
        (a) Agency Requirements.--Each Federal agency shall comply with the requirements of this Act not later than 180 days after enactment.
        (b) Reporting.--Each agency shall submit annual reports to Congress detailing compliance efforts.
        (c) Enforcement.--The Secretary shall enforce compliance through regular audits and may impose civil penalties of up to $50,000 per violation.
        
        SEC. 4. PROHIBITED ACTIVITIES.
        (a) General Prohibition.--No Federal funds may be used for activities that conflict with the purposes of this Act.
        (b) Specific Prohibitions.--Federal agencies may not use funds authorized under this Act for lobbying activities or political campaigns.
        
        SEC. 5. DEFINITIONS.
        For purposes of this Act:
        (1) AGENCY.--The term "agency" has the meaning given such term in section 551 of title 5, United States Code.
        (2) COMPLIANCE.--The term "compliance" means adherence to all requirements specified in this Act.
        
        SEC. 6. EFFECTIVE DATE.
        This Act shall take effect on January 1, 2025.
        """
        
        bill_metadata = {
            'bill_id': 'test-enriched-123',
            'title': 'Comprehensive Test Act of 2024',
            'bill_number': 'HR123'
        }
        
        print(f"📤 Testing enriched analysis for: {bill_metadata['title']}")
        
        # Test enriched analysis
        result = await ai_service.analyze_bill_enriched(sample_bill_text, bill_metadata)
        
        if result.get('success'):
            print("✅ Enriched analysis completed successfully")
            
            # Check cost
            metadata = result.get('_metadata', {})
            cost = metadata.get('cost', 0)
            cost_breakdown = metadata.get('cost_breakdown', {})
            
            print(f"💰 Total cost: ${cost:.4f}")
            print(f"💰 Pass A cost: ${cost_breakdown.get('pass_a_cost', 0):.4f}")
            print(f"💰 Pass B cost: ${cost_breakdown.get('pass_b_cost', 0):.4f}")
            print(f"📊 Sections analyzed: {metadata.get('sections_analyzed', 0)}")
            print(f"📊 Sections enriched: {metadata.get('sections_enriched', 0)}")
            print(f"📊 Free enrichments: {metadata.get('free_enrichments', 0)}")
            
            # Check budget compliance
            budget_exhausted = cost_breakdown.get('budget_exhausted', False)
            under_budget = cost <= 0.30
            
            print(f"💸 Under $0.30 budget: {'✅' if under_budget else '❌'}")
            print(f"💸 Budget exhausted: {'⚠️' if budget_exhausted else '✅'}")
            
            # Check enriched content
            extraction = result.get('extraction', {})
            complete_analysis = extraction.get('complete_analysis', [])
            additional_details = extraction.get('additional_details', {})
            
            print(f"\n📋 Content Quality:")
            print(f"Complete analysis sections: {len(complete_analysis)}")
            
            if complete_analysis:
                first_section = complete_analysis[0]
                print(f"First section title: {first_section.get('title', 'Unknown')}")
                print(f"Detailed summary length: {len(first_section.get('detailed_summary', ''))}")
                print(f"Key actions: {len(first_section.get('key_actions', []))}")
                print(f"Affected parties: {len(first_section.get('affected_parties', []))}")
                print(f"Potential impacts: {len(first_section.get('potential_impact', []))}")
                print(f"Compliance requirements: {len(first_section.get('compliance_requirements', []))}")
            
            print(f"\n📊 Additional Details:")
            for key, items in additional_details.items():
                if isinstance(items, list):
                    print(f"{key}: {len(items)} items")
            
            # Test budget controls
            if under_budget and not budget_exhausted:
                print("\n✅ BUDGET CONTROLS WORKING")
                return True
            else:
                print(f"\n❌ BUDGET CONTROLS FAILED: cost=${cost:.4f}, exhausted={budget_exhausted}")
                return False
                
        else:
            error = result.get('error', 'Unknown error')
            print(f"❌ Enriched analysis failed: {error}")
            
            # Check if it's a validation failure (acceptable)
            if 'validation' in error.lower():
                print("✅ Validation gates working (blocked bad content)")
                return True
            else:
                return False
        
    except Exception as e:
        print(f"❌ Enriched analysis test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_budget_limits():
    """Test that budget limits are enforced"""
    try:
        from app.services.enriched_analysis_service import EnrichedAnalysisService
        from app.services.ai_service import AIService
        
        print("\n🧪 Testing Budget Limits")
        print("=" * 60)
        
        ai_service = AIService()
        if not ai_service.enabled:
            print("⚠️ AI service not enabled - skipping test")
            return True
        
        enriched_service = EnrichedAnalysisService(ai_service)
        
        # Test budget calculations
        print(f"Max bill budget: ${enriched_service.max_bill_budget:.2f}")
        print(f"Pass A limits: {enriched_service.pass_a_input_limit} in, {enriched_service.pass_a_output_limit} out tokens")
        print(f"Pass B limits: {enriched_service.pass_b_input_limit} in, {enriched_service.pass_b_output_limit} out tokens")
        print(f"Max enriched sections: {enriched_service.max_enriched_sections}")
        
        # Estimate costs
        pass_a_cost = (
            enriched_service.pass_a_input_limit * enriched_service.gpt4o_mini_input_cost / 1000 +
            enriched_service.pass_a_output_limit * enriched_service.gpt4o_mini_output_cost / 1000
        )
        
        pass_b_cost_per_section = (
            enriched_service.pass_b_input_limit * enriched_service.gpt4o_input_cost / 1000 +
            enriched_service.pass_b_output_limit * enriched_service.gpt4o_output_cost / 1000
        )
        
        max_pass_b_cost = pass_b_cost_per_section * enriched_service.max_enriched_sections
        total_max_cost = pass_a_cost + max_pass_b_cost
        
        print(f"\n💰 Cost Estimates:")
        print(f"Pass A (4o-mini): ${pass_a_cost:.4f}")
        print(f"Pass B per section (4o): ${pass_b_cost_per_section:.4f}")
        print(f"Max Pass B cost: ${max_pass_b_cost:.4f}")
        print(f"Total max cost: ${total_max_cost:.4f}")
        
        # Check budget compliance
        under_budget = total_max_cost <= enriched_service.max_bill_budget
        safety_margin = enriched_service.max_bill_budget - total_max_cost
        
        print(f"\n📊 Budget Analysis:")
        print(f"Under budget: {'✅' if under_budget else '❌'}")
        print(f"Safety margin: ${safety_margin:.4f}")
        print(f"Budget utilization: {total_max_cost/enriched_service.max_bill_budget*100:.1f}%")
        
        if under_budget and safety_margin > 0.01:  # At least 1 cent margin
            print("✅ BUDGET LIMITS PROPERLY CONFIGURED")
            return True
        else:
            print("❌ BUDGET LIMITS TOO TIGHT")
            return False
        
    except Exception as e:
        print(f"❌ Budget limits test failed: {e}")
        return False

async def main():
    """Run all enriched analysis tests"""
    print("🧪 Testing Enriched Analysis System")
    print("=" * 60)
    
    # Test enriched analysis
    analysis_success = await test_enriched_analysis()
    
    # Test budget limits
    budget_success = await test_budget_limits()
    
    print("\n" + "=" * 60)
    print("📋 Overall Test Results:")
    print(f"✅ Enriched analysis: {'PASS' if analysis_success else 'FAIL'}")
    print(f"✅ Budget controls: {'PASS' if budget_success else 'FAIL'}")
    
    if analysis_success and budget_success:
        print("\n🎉 ENRICHED ANALYSIS SYSTEM READY!")
        print("💰 Stays under $0.30 budget")
        print("📊 Comprehensive two-pass analysis")
        print("🔒 Quality gates enforced")
        print("🚀 Free deterministic enrichments")
        print("⚡ Smart section routing")
    else:
        print("\n❌ ENRICHED ANALYSIS SYSTEM NEEDS FIXES")
        print("🔧 Check budget controls and analysis quality")

if __name__ == "__main__":
    asyncio.run(main())
