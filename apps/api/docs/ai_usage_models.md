# AI Usage Tracking Models

This document describes the AI usage tracking models that monitor token usage, costs, and performance metrics for all AI operations in the Modern Action platform.

## Overview

The AI usage tracking system consists of three main models:

1. **AIUsageLog** - Individual AI API call tracking
2. **AIUsageSummary** - Aggregated usage statistics
3. **AIBudgetAlert** - Budget monitoring and alerts

## Models

### AIUsageLog

Tracks individual AI API calls with detailed token usage and cost information.

**Table:** `ai_usage_logs`

**Key Fields:**
- `id` (String, PK) - Unique identifier for the log entry
- `operation_type` (String, Required) - Type of AI operation (e.g., 'bill_analysis', 'reason_generation')
- `operation_subtype` (String, Optional) - Subtype of operation (e.g., 'support_reasons', 'oppose_reasons')
- `bill_id` (String, FK, Optional) - Associated bill ID if applicable
- `model_name` (String, Required) - AI model used (e.g., 'gpt-4-turbo')
- `provider` (String, Required, Default: 'openai') - AI provider
- `prompt_tokens` (Integer, Default: 0) - Number of tokens in prompt
- `completion_tokens` (Integer, Default: 0) - Number of tokens in completion
- `total_tokens` (Integer, Default: 0) - Total tokens used
- `prompt_cost` (Float, Default: 0.0) - Cost of prompt tokens (USD)
- `completion_cost` (Float, Default: 0.0) - Cost of completion tokens (USD)
- `total_cost` (Float, Default: 0.0) - Total cost (USD)
- `response_time_ms` (Float, Optional) - Response time in milliseconds
- `success` (Boolean, Default: True) - Whether the operation succeeded
- `error_message` (Text, Optional) - Error message if operation failed
- `created_at` (DateTime, Required) - Timestamp of the operation
- `user_id` (String, Optional) - User who triggered the operation
- `session_id` (String, Optional) - Session identifier

**Relationships:**
- `bill` - Many-to-one relationship with Bill model

**Usage Example:**
```python
from app.models.ai_usage import AIUsageLog

# Log a successful AI operation
log = AIUsageLog(
    id="log-001",
    operation_type="bill_analysis",
    operation_subtype="support_reasons",
    bill_id="bill-123",
    model_name="gpt-4-turbo",
    prompt_tokens=1500,
    completion_tokens=800,
    total_tokens=2300,
    prompt_cost=0.015,
    completion_cost=0.024,
    total_cost=0.039,
    response_time_ms=1200.5,
    user_id="user-456"
)
```

### AIUsageSummary

Aggregated usage statistics for cost monitoring and analysis.

**Table:** `ai_usage_summaries`

**Key Fields:**
- `id` (String, PK) - Unique identifier
- `date_bucket` (DateTime, Required) - Date for this summary
- `period_type` (String, Default: 'daily') - Period type ('daily', 'monthly')
- `operation_type` (String, Optional) - Specific operation or 'all' for total
- `model_name` (String, Optional) - Specific model or 'all' for total
- `total_requests` (Integer, Default: 0) - Total number of requests
- `successful_requests` (Integer, Default: 0) - Number of successful requests
- `failed_requests` (Integer, Default: 0) - Number of failed requests
- `total_tokens` (Integer, Default: 0) - Total tokens used
- `total_cost` (Float, Default: 0.0) - Total cost (USD)
- `avg_response_time_ms` (Float, Optional) - Average response time
- `avg_cost_per_request` (Float, Optional) - Average cost per request
- `bills_processed` (Integer, Default: 0) - Number of bills processed
- `avg_cost_per_bill` (Float, Optional) - Average cost per bill

**Usage Example:**
```python
from app.models.ai_usage import AIUsageSummary
from datetime import datetime

# Create daily summary
summary = AIUsageSummary(
    id="summary-2024-01-15",
    date_bucket=datetime(2024, 1, 15),
    period_type="daily",
    operation_type="bill_analysis",
    model_name="gpt-4-turbo",
    total_requests=25,
    successful_requests=23,
    failed_requests=2,
    total_cost=1.25,
    bills_processed=10,
    avg_cost_per_bill=0.125
)
```

### AIBudgetAlert

Budget monitoring and alert configuration.

**Table:** `ai_budget_alerts`

**Key Fields:**
- `id` (String, PK) - Unique identifier
- `alert_name` (String, Required) - Human-readable alert name
- `alert_type` (String, Required) - Alert type ('daily', 'monthly', 'per_bill', 'total')
- `threshold_amount` (Float, Required) - USD threshold amount
- `threshold_tokens` (Integer, Optional) - Optional token threshold
- `operation_type` (String, Optional) - Specific operation or null for all
- `model_name` (String, Optional) - Specific model or null for all
- `is_active` (Boolean, Default: True) - Whether alert is active
- `last_triggered` (DateTime, Optional) - When alert was last triggered
- `times_triggered` (Integer, Default: 0) - Number of times triggered
- `notification_emails` (JSONB, Optional) - List of emails to notify
- `webhook_url` (String, Optional) - Optional webhook for alerts

**Usage Example:**
```python
from app.models.ai_usage import AIBudgetAlert

# Create daily spending alert
alert = AIBudgetAlert(
    id="daily-limit-001",
    alert_name="Daily Spending Limit",
    alert_type="daily",
    threshold_amount=10.0,
    operation_type="bill_analysis",
    notification_emails=["<EMAIL>", "<EMAIL>"]
)
```

## Best Practices

### 1. Logging AI Operations

Always log AI operations immediately after completion:

```python
async def log_ai_operation(operation_type: str, model_name: str, 
                          prompt_tokens: int, completion_tokens: int,
                          cost: float, success: bool = True, 
                          error: str = None, **kwargs):
    """Log an AI operation with usage metrics"""
    log = AIUsageLog(
        id=f"{operation_type}-{datetime.utcnow().isoformat()}",
        operation_type=operation_type,
        model_name=model_name,
        prompt_tokens=prompt_tokens,
        completion_tokens=completion_tokens,
        total_tokens=prompt_tokens + completion_tokens,
        total_cost=cost,
        success=success,
        error_message=error,
        **kwargs
    )
    db.add(log)
    await db.commit()
```

### 2. Cost Calculation

Ensure accurate cost calculation based on current pricing:

```python
def calculate_cost(model_name: str, prompt_tokens: int, completion_tokens: int) -> dict:
    """Calculate cost based on current pricing"""
    pricing = {
        'gpt-4-turbo': {'prompt': 0.00001, 'completion': 0.00003},
        'gpt-3.5-turbo': {'prompt': 0.0000005, 'completion': 0.0000015}
    }
    
    rates = pricing.get(model_name, pricing['gpt-4-turbo'])
    prompt_cost = prompt_tokens * rates['prompt']
    completion_cost = completion_tokens * rates['completion']
    
    return {
        'prompt_cost': prompt_cost,
        'completion_cost': completion_cost,
        'total_cost': prompt_cost + completion_cost
    }
```

### 3. Budget Monitoring

Implement budget checks before expensive operations:

```python
async def check_budget_before_operation(operation_type: str, estimated_cost: float) -> bool:
    """Check if operation would exceed budget limits"""
    # Check daily budget
    today = datetime.utcnow().date()
    daily_usage = await db.query(AIUsageLog).filter(
        func.date(AIUsageLog.created_at) == today,
        AIUsageLog.operation_type == operation_type
    ).with_entities(func.sum(AIUsageLog.total_cost)).scalar() or 0
    
    # Check against alerts
    alerts = await db.query(AIBudgetAlert).filter(
        AIBudgetAlert.alert_type == 'daily',
        AIBudgetAlert.operation_type == operation_type,
        AIBudgetAlert.is_active == True
    ).all()
    
    for alert in alerts:
        if daily_usage + estimated_cost > alert.threshold_amount:
            return False
    
    return True
```

## Database Migration

To create the AI usage tables, run the following migration:

```sql
-- Create ai_usage_logs table
CREATE TABLE ai_usage_logs (
    id VARCHAR PRIMARY KEY,
    operation_type VARCHAR NOT NULL,
    operation_subtype VARCHAR,
    bill_id VARCHAR REFERENCES bills(id),
    model_name VARCHAR NOT NULL,
    provider VARCHAR NOT NULL DEFAULT 'openai',
    prompt_tokens INTEGER NOT NULL DEFAULT 0,
    completion_tokens INTEGER NOT NULL DEFAULT 0,
    total_tokens INTEGER NOT NULL DEFAULT 0,
    prompt_cost FLOAT NOT NULL DEFAULT 0.0,
    completion_cost FLOAT NOT NULL DEFAULT 0.0,
    total_cost FLOAT NOT NULL DEFAULT 0.0,
    response_time_ms FLOAT,
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    prompt_length INTEGER,
    response_length INTEGER,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    user_id VARCHAR,
    session_id VARCHAR
);

-- Create indexes for performance
CREATE INDEX idx_ai_usage_logs_created_at ON ai_usage_logs(created_at);
CREATE INDEX idx_ai_usage_logs_operation_type ON ai_usage_logs(operation_type);
CREATE INDEX idx_ai_usage_logs_bill_id ON ai_usage_logs(bill_id);
CREATE INDEX idx_ai_usage_logs_model_name ON ai_usage_logs(model_name);

-- Create ai_usage_summaries table
CREATE TABLE ai_usage_summaries (
    id VARCHAR PRIMARY KEY,
    date_bucket TIMESTAMP NOT NULL,
    period_type VARCHAR NOT NULL DEFAULT 'daily',
    operation_type VARCHAR,
    model_name VARCHAR,
    total_requests INTEGER NOT NULL DEFAULT 0,
    successful_requests INTEGER NOT NULL DEFAULT 0,
    failed_requests INTEGER NOT NULL DEFAULT 0,
    total_prompt_tokens INTEGER NOT NULL DEFAULT 0,
    total_completion_tokens INTEGER NOT NULL DEFAULT 0,
    total_tokens INTEGER NOT NULL DEFAULT 0,
    total_prompt_cost FLOAT NOT NULL DEFAULT 0.0,
    total_completion_cost FLOAT NOT NULL DEFAULT 0.0,
    total_cost FLOAT NOT NULL DEFAULT 0.0,
    avg_response_time_ms FLOAT,
    avg_tokens_per_request FLOAT,
    avg_cost_per_request FLOAT,
    bills_processed INTEGER NOT NULL DEFAULT 0,
    avg_cost_per_bill FLOAT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Create ai_budget_alerts table
CREATE TABLE ai_budget_alerts (
    id VARCHAR PRIMARY KEY,
    alert_name VARCHAR NOT NULL,
    alert_type VARCHAR NOT NULL,
    threshold_amount FLOAT NOT NULL,
    threshold_tokens INTEGER,
    operation_type VARCHAR,
    model_name VARCHAR,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_triggered TIMESTAMP,
    times_triggered INTEGER NOT NULL DEFAULT 0,
    notification_emails JSONB,
    webhook_url VARCHAR,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

## Testing

The AI usage models include comprehensive tests covering:

- **Unit Tests** (`test_ai_usage_models_unit.py`) - Model structure and validation
- **Integration Tests** (`test_ai_usage_models.py`) - Database operations and relationships

Run tests with:
```bash
# Unit tests (no database required)
pytest tests/test_ai_usage_models_unit.py -v

# Integration tests (requires test database)
pytest tests/test_ai_usage_models.py -v
```

## Monitoring and Analytics

Use the AI usage data for:

1. **Cost Analysis** - Track spending by operation, model, and time period
2. **Performance Monitoring** - Monitor response times and success rates
3. **Budget Management** - Set up alerts for spending thresholds
4. **Usage Optimization** - Identify expensive operations for optimization
5. **Capacity Planning** - Forecast future usage and costs
