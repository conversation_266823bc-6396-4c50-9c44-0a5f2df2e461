'use client';

import React, { useState, useEffect } from 'react';
import { Official } from '@/types';
import { officialApi } from '@/services/apiClient';
import OfficialCard from './OfficialCard';

interface OfficialsListProps {
  zipCode?: string;
  level?: string;
  chamber?: string;
  state?: string;
  party?: string;
  searchQuery?: string;
  limit?: number;
  className?: string;
}

export const OfficialsList: React.FC<OfficialsListProps> = ({
  zipCode,
  level,
  chamber,
  state,
  party,
  searchQuery,
  limit = 20,
  className = ''
}) => {
  const [officials, setOfficials] = useState<Official[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOfficials = async () => {
      try {
        setLoading(true);
        setError(null);

        let fetchedOfficials: Official[] = [];

        if (zipCode) {
          // Primary use case: Get officials by ZIP code
          fetchedOfficials = await officialApi.getOfficialsByZip(zipCode, { limit });
        } else if (searchQuery || level || chamber || state || party) {
          // Secondary use case: Search with filters
          fetchedOfficials = await officialApi.searchOfficials({
            query: searchQuery,
            level: level as any,
            chamber: chamber as any,
            state,
            party,
            limit
          });
        } else if (level) {
          // Get officials by level
          fetchedOfficials = await officialApi.getOfficialsByLevel(level, { limit });
        } else if (chamber) {
          // Get officials by chamber
          fetchedOfficials = await officialApi.getOfficialsByChamber(chamber, { limit });
        } else {
          // Default: Get all officials
          fetchedOfficials = await officialApi.getOfficials({ limit });
        }

        setOfficials(fetchedOfficials);
      } catch (err) {
        console.error('Error fetching officials:', err);
        setError('Failed to load officials. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchOfficials();
  }, [zipCode, level, chamber, state, party, searchQuery, limit]);

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {/* Loading skeleton */}
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-start space-x-4">
              <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="text-red-800 font-medium">Error Loading Officials</div>
          <div className="text-red-600 text-sm mt-1">{error}</div>
          <button
            onClick={() => window.location.reload()}
            className="mt-3 inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (officials.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <div className="text-gray-800 font-medium">No Officials Found</div>
          <div className="text-gray-600 text-sm mt-1">
            {zipCode 
              ? `No representatives found for ZIP code ${zipCode}`
              : 'No officials match your search criteria'
            }
          </div>
          {zipCode && (
            <div className="text-gray-500 text-xs mt-2">
              Try entering a different ZIP code or check that it's a valid 5-digit US postal code.
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Results Summary */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-600">
          {zipCode && (
            <span className="font-medium">
              Your representatives for ZIP code {zipCode}
            </span>
          )}
          {!zipCode && (
            <span>
              Found {officials.length} official{officials.length !== 1 ? 's' : ''}
              {searchQuery && ` matching "${searchQuery}"`}
            </span>
          )}
        </div>

        {officials.length > 0 && (
          <div className="text-xs text-gray-500">
            Last updated: {new Date().toLocaleDateString()}
          </div>
        )}
      </div>

      {/* Officials Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {officials.map((official) => (
          <OfficialCard
            key={official.id}
            official={official}
            showFullDetails={false}
          />
        ))}
      </div>

      {/* Load More Button (if needed) */}
      {officials.length >= limit && (
        <div className="text-center">
          <button
            onClick={() => {
              // Implement load more functionality
              console.log('Load more officials');
            }}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Load More Officials
          </button>
        </div>
      )}
    </div>
  );
};

export default OfficialsList;