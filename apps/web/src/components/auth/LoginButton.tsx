'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';

interface LoginButtonProps {
  className?: string;
  children?: React.ReactNode;
  returnTo?: string; // Optional override for return URL
  preserveState?: boolean; // Whether to preserve form state in localStorage
  stateKey?: string; // Key for storing form state
  onSaveState?: () => void; // Callback to trigger form state saving
}

const LoginButton: React.FC<LoginButtonProps> = ({
  className = "bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors",
  children = "Log In",
  returnTo,
  preserveState = false,
  stateKey = 'form-state-before-login',
  onSaveState
}) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // Construct the current URL including search params
  const currentUrl = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
  
  // Use provided returnTo or current URL
  const redirectUrl = returnTo || currentUrl;
  
  // Encode the return URL for the Auth0 login
  const loginUrl = `/api/auth/login?returnTo=${encodeURIComponent(redirectUrl)}`;

  const handleClick = () => {
    if (preserveState && stateKey) {
      // Trigger external save callback if provided
      if (onSaveState) {
        onSaveState();
      }
      
      // Store a flag to indicate that form state should be preserved
      localStorage.setItem('preserve-form-state', 'true');
      localStorage.setItem('form-state-key', stateKey);
      localStorage.setItem('return-url', redirectUrl);
    }
  };

  return (
    <Link
      href={loginUrl}
      className={className}
      onClick={handleClick}
    >
      {children}
    </Link>
  );
};

export default LoginButton;
