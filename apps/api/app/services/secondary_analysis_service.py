"""
Secondary Analysis Service

Generates user-friendly bill summaries for other bill_details fields:
- what_does, who_affects, why_matters, etc.
- 10th grade reading level content
- Utilizes congress.gov summaries when available
- Quality-first approach with bill citations
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import re

from .ai_service import AIService

logger = logging.getLogger(__name__)

@dataclass
class SecondaryAnalysisResult:
    """Result of secondary analysis generation"""
    success: bool
    what_does: Optional[Dict[str, Any]] = None
    who_affects: Optional[Dict[str, Any]] = None
    why_matters: Optional[Dict[str, Any]] = None
    cost_impact: Optional[Dict[str, Any]] = None
    key_provisions: Optional[List[Dict[str, Any]]] = None
    timeline: Optional[List[Dict[str, Any]]] = None
    positions: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_notes: Optional[str] = None

class SecondaryAnalysisService:
    """
    Service for generating user-friendly secondary analysis content
    """
    
    def __init__(self, ai_service: AIService):
        self.ai_service = ai_service
        
    async def generate_secondary_analysis(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        analysis_context: Optional[Dict[str, Any]] = None
    ) -> SecondaryAnalysisResult:
        """
        Generate user-friendly secondary analysis content
        
        Args:
            bill_text: Full text of the bill
            bill_metadata: Bill metadata (title, number, etc.)
            congress_summary: Summary from congress.gov if available
            evidence_spans: Evidence spans for citations
            analysis_context: Additional context from balanced analysis
            
        Returns:
            SecondaryAnalysisResult with generated content including positions
        """
        try:
            logger.info(f"Starting secondary analysis for {bill_metadata.get('bill_number', 'unknown')}")
            
            # Generate each section in parallel for efficiency
            tasks = []
            
            # What does this bill do?
            tasks.append(self._generate_what_does(bill_text, bill_metadata, congress_summary, evidence_spans))
            
            # Who does this affect?
            tasks.append(self._generate_who_affects(bill_text, bill_metadata, congress_summary, evidence_spans))
            
            # Why does this matter?
            tasks.append(self._generate_why_matters(bill_text, bill_metadata, congress_summary, evidence_spans))
            
            # Cost impact
            tasks.append(self._generate_cost_impact(bill_text, bill_metadata, congress_summary, evidence_spans))
            
            # Key provisions
            tasks.append(self._generate_key_provisions(bill_text, bill_metadata, congress_summary, evidence_spans))
            
            # Timeline
            tasks.append(self._generate_timeline(bill_text, bill_metadata, congress_summary, evidence_spans))
            
            # Positions (support/oppose/amend reasons)
            tasks.append(self._generate_positions(bill_text, bill_metadata, congress_summary, evidence_spans, analysis_context))
            
            # Execute all analysis tasks
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            what_does, who_affects, why_matters, cost_impact, key_provisions, timeline, positions = results
            
            # Check for exceptions
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    section_names = ["what_does", "who_affects", "why_matters", "cost_impact", "key_provisions", "timeline", "positions"]
                    logger.error(f"Error in {section_names[i]}: {result}")
                    # Set to None for failed sections
                    results[i] = None
            
            return SecondaryAnalysisResult(
                success=True,
                what_does=what_does,
                who_affects=who_affects,
                why_matters=why_matters,
                cost_impact=cost_impact,
                key_provisions=key_provisions,
                timeline=timeline,
                positions=positions,
                processing_notes=f"Generated secondary analysis with positions and {'congress.gov summary' if congress_summary else 'bill text only'}"
            )
            
        except Exception as e:
            logger.error(f"Secondary analysis failed: {e}")
            return SecondaryAnalysisResult(
                success=False,
                error=str(e)
            )
    
    async def _generate_what_does(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate 'What does this bill do?' content"""
        
        prompt = self._build_what_does_prompt(bill_text, bill_metadata, congress_summary, evidence_spans)
        
        try:
            response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": prompt}],
                max_tokens=800,
                temperature=0.3
            )
            
            # Parse response and extract citations
            content, citations = self._parse_response_with_citations(response, evidence_spans or [])
            
            return {
                "content": content,
                "citations": citations
            }
            
        except Exception as e:
            logger.error(f"Error generating what_does: {e}")
            return None
    
    async def _generate_who_affects(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate 'Who does this affect?' content"""
        
        prompt = self._build_who_affects_prompt(bill_text, bill_metadata, congress_summary, evidence_spans)
        
        try:
            response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": prompt}],
                max_tokens=600,
                temperature=0.3
            )
            
            content, citations = self._parse_response_with_citations(response, evidence_spans or [])
            
            return {
                "content": content,
                "citations": citations
            }
            
        except Exception as e:
            logger.error(f"Error generating who_affects: {e}")
            return None
    
    async def _generate_why_matters(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate 'Why does this matter?' content"""
        
        prompt = self._build_why_matters_prompt(bill_text, bill_metadata, congress_summary, evidence_spans)
        
        try:
            response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": prompt}],
                max_tokens=700,
                temperature=0.3
            )
            
            content, citations = self._parse_response_with_citations(response, evidence_spans or [])
            
            return {
                "content": content,
                "citations": citations
            }
            
        except Exception as e:
            logger.error(f"Error generating why_matters: {e}")
            return None
    
    async def _generate_cost_impact(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate cost impact analysis"""
        
        prompt = self._build_cost_impact_prompt(bill_text, bill_metadata, congress_summary, evidence_spans)
        
        try:
            response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": prompt}],
                max_tokens=600,
                temperature=0.3
            )
            
            content, citations = self._parse_response_with_citations(response, evidence_spans or [])
            
            return {
                "content": content,
                "citations": citations
            }
            
        except Exception as e:
            logger.error(f"Error generating cost_impact: {e}")
            return None
    
    async def _generate_key_provisions(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[List[Dict[str, Any]]]:
        """Generate key provisions list"""
        
        prompt = self._build_key_provisions_prompt(bill_text, bill_metadata, congress_summary, evidence_spans)
        
        try:
            response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": prompt}],
                max_tokens=1000,
                temperature=0.3
            )
            
            # Parse multiple provisions from response
            provisions = self._parse_provisions_response(response, evidence_spans or [])
            
            return provisions
            
        except Exception as e:
            logger.error(f"Error generating key_provisions: {e}")
            return None
    
    async def _generate_timeline(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[List[Dict[str, Any]]]:
        """Generate timeline of key dates/events"""
        
        prompt = self._build_timeline_prompt(bill_text, bill_metadata, congress_summary, evidence_spans)
        
        try:
            response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": prompt}],
                max_tokens=800,
                temperature=0.3
            )
            
            # Parse timeline events from response
            timeline_items = self._parse_timeline_response(response, evidence_spans or [])
            
            return timeline_items
            
        except Exception as e:
            logger.error(f"Error generating timeline: {e}")
            return None
    
    async def _generate_positions(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        analysis_context: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """Generate support/oppose/amend positions with evidence grounding"""
        
        logger.info(f"🎯 Generating positions in secondary analysis for {bill_metadata.get('bill_number', 'unknown')}")
        
        try:
            # Generate support reasons
            support_prompt = self._build_support_reasons_prompt(bill_text, bill_metadata, congress_summary, evidence_spans, analysis_context)
            support_response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": support_prompt}],
                max_tokens=1000,
                temperature=0.3
            )
            support_reasons = self._parse_positions_response(support_response, evidence_spans or [])
            
            # Generate oppose reasons
            oppose_prompt = self._build_oppose_reasons_prompt(bill_text, bill_metadata, congress_summary, evidence_spans, analysis_context)
            oppose_response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": oppose_prompt}],
                max_tokens=1000,
                temperature=0.3
            )
            oppose_reasons = self._parse_positions_response(oppose_response, evidence_spans or [])
            
            # Generate amendment suggestions
            amend_prompt = self._build_amend_reasons_prompt(bill_text, bill_metadata, congress_summary, evidence_spans, analysis_context)
            amend_response = await self.ai_service._make_openai_request(
                [{"role": "user", "content": amend_prompt}],
                max_tokens=1000,
                temperature=0.3
            )
            amend_reasons = self._parse_positions_response(amend_response, evidence_spans or [])
            
            positions = {
                "support_reasons": support_reasons,
                "oppose_reasons": oppose_reasons,
                "amend_reasons": amend_reasons
            }
            
            logger.info(f"✅ Generated positions: {len(support_reasons)} support, {len(oppose_reasons)} oppose, {len(amend_reasons)} amend")
            return positions
            
        except Exception as e:
            logger.error(f"Error generating positions: {e}")
            return None
    
    def _build_what_does_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build prompt for 'What does this bill do?' section"""
        
        base_prompt = f"""You are writing a clear, accessible explanation of what this bill does for everyday citizens.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Focus on practical impacts and real-world changes
- Use simple, clear language that anyone can understand
- Be factual and specific, not generic
- 2-3 paragraphs, around 300-500 words

CITATION REQUIREMENTS:
- Include exact quotes from the bill to support key points
- Use this format: [CITATION: "exact quote from bill"]
- Only cite actual text that appears in the bill"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (use as foundation):
{congress_summary}

Build upon this official summary but make it more accessible and detailed."""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL (use for citations):
{self._format_evidence_spans(evidence_spans[:10])}"""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Write a clear, accessible explanation of what this bill actually does. Focus on the practical changes it would make to people's lives, businesses, or government operations. Use specific examples and cite exact quotes from the bill."""

        return base_prompt
    
    def _build_who_affects_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build prompt for 'Who does this affect?' section"""
        
        base_prompt = f"""You are identifying who would be affected by this legislation in clear, accessible language.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Identify specific groups of people, businesses, organizations
- Explain HOW each group would be affected
- Be concrete and specific, not vague
- 2-3 paragraphs, around 200-400 words

CITATION REQUIREMENTS:
- Include exact quotes from the bill that mention affected parties
- Use this format: [CITATION: "exact quote from bill"]
- Focus on text that specifically names or describes affected groups"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference for affected parties):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:8])}"""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Identify who this bill would affect and explain how. Be specific about different groups and the nature of the impact on each."""

        return base_prompt
    
    def _build_why_matters_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build prompt for 'Why does this matter?' section"""
        
        base_prompt = f"""You are explaining why this legislation matters to everyday citizens in accessible language.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Focus on the significance and importance of this legislation
- Explain the problems it addresses or opportunities it creates
- Connect to real-world impacts people care about
- 2-3 paragraphs, around 300-500 words

CITATION REQUIREMENTS:
- Include exact quotes that show the bill's purpose or findings
- Use this format: [CITATION: "exact quote from bill"]
- Quote findings, purposes, or key provisions that show importance"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference for context):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:8])}"""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Explain why this bill matters. What problems does it address? What opportunities does it create? Why should citizens care about this legislation?"""

        return base_prompt
    
    def _build_cost_impact_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build prompt for cost impact analysis"""
        
        base_prompt = f"""You are analyzing the financial impact of this legislation in clear, accessible language.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Identify costs, savings, or financial impacts
- Be specific about amounts when mentioned in the bill
- Explain who pays and who benefits financially
- 1-2 paragraphs, around 200-350 words

CITATION REQUIREMENTS:
- Include exact quotes about funding, costs, or financial provisions
- Use this format: [CITATION: "exact quote from bill"]
- Quote specific dollar amounts or cost-related language"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:6])}"""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Analyze the cost and financial impact of this bill. Look for funding provisions, cost estimates, fees, penalties, or other financial impacts. If no specific costs are mentioned, explain that and focus on the general financial approach."""

        return base_prompt
    
    def _build_key_provisions_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build prompt for key provisions list"""
        
        base_prompt = f"""You are identifying the key provisions of this legislation as a bulleted list for everyday citizens.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Create 4-8 key provisions as separate bullet points
- Each provision should be 1-2 sentences
- Focus on the most important changes or requirements
- Be specific and actionable

CITATION REQUIREMENTS:
- Include exact quotes that support each provision
- Use this format after each provision: [CITATION: "exact quote from bill"]
- Each provision should have at least one supporting quote

FORMAT:
Provision 1: [Description] [CITATION: "quote"]
Provision 2: [Description] [CITATION: "quote"]
...etc"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:10])}"""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Identify the key provisions of this bill. What are the most important things this legislation would do? Focus on concrete actions, requirements, or changes."""

        return base_prompt
    
    def _build_timeline_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Build prompt for timeline analysis"""
        
        base_prompt = f"""You are identifying key dates and timeline information from this legislation.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Identify specific dates, deadlines, or timeframes mentioned in the bill
- Create 3-6 timeline items
- Each item should be clear about when something happens
- If no specific dates, focus on the sequence of events

CITATION REQUIREMENTS:
- Include exact quotes that mention dates or timeframes
- Use this format: [CITATION: "exact quote from bill"]
- Focus on text with specific dates or timing language

FORMAT:
Timeline Item 1: [Date/Timeframe] - [What happens] [CITATION: "quote"]
Timeline Item 2: [Date/Timeframe] - [What happens] [CITATION: "quote"]
...etc"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:6])}"""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Identify the timeline for this bill. When do things happen? What are the key dates, deadlines, or implementation phases? If no specific dates are mentioned, explain the general sequence of how the bill would be implemented."""

        return base_prompt
    
    def _build_support_reasons_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        analysis_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Build prompt for support reasons"""
        
        base_prompt = f"""You are generating reasons why citizens might SUPPORT this legislation.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Generate 3-5 compelling reasons to support this bill
- Each reason should be specific and evidence-based
- Focus on positive impacts and benefits
- Be balanced and factual, not promotional

CITATION REQUIREMENTS:
- Include exact quotes from the bill that support each reason
- Use this format: [CITATION: "exact quote from bill"]
- Each reason must have at least one supporting quote

FORMAT:
Reason 1: [Clear statement of support reason] [CITATION: "quote"]
Justification: [1-2 sentences explaining why this matters]

Reason 2: [Clear statement of support reason] [CITATION: "quote"]  
Justification: [1-2 sentences explaining why this matters]

...etc"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:10])}"""

        if analysis_context:
            sections = analysis_context.get('complete_analysis', [])
            if sections:
                base_prompt += f"""

BILL ANALYSIS CONTEXT:
Based on detailed analysis, this bill has {len(sections)} major provisions. Use this context to identify the most compelling support reasons."""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Generate reasons why citizens and advocacy groups might support this legislation. Focus on concrete benefits and positive changes."""

        return base_prompt
    
    def _build_oppose_reasons_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        analysis_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Build prompt for oppose reasons"""
        
        base_prompt = f"""You are generating reasons why citizens might OPPOSE this legislation.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Generate 3-5 substantive reasons to oppose this bill
- Each reason should be specific and evidence-based
- Focus on potential negative impacts or concerns
- Be balanced and factual, not inflammatory

CITATION REQUIREMENTS:
- Include exact quotes from the bill that support each concern
- Use this format: [CITATION: "exact quote from bill"]
- Each reason must have at least one supporting quote

FORMAT:
Reason 1: [Clear statement of opposition reason] [CITATION: "quote"]
Justification: [1-2 sentences explaining the concern]

Reason 2: [Clear statement of opposition reason] [CITATION: "quote"]
Justification: [1-2 sentences explaining the concern]

...etc"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:10])}"""

        if analysis_context:
            sections = analysis_context.get('complete_analysis', [])
            if sections:
                base_prompt += f"""

BILL ANALYSIS CONTEXT:
Based on detailed analysis, this bill has {len(sections)} major provisions. Use this context to identify the most significant concerns."""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Generate reasons why citizens and advocacy groups might oppose this legislation. Focus on concrete concerns and potential negative impacts."""

        return base_prompt
    
    def _build_amend_reasons_prompt(
        self, 
        bill_text: str, 
        bill_metadata: Dict[str, Any],
        congress_summary: Optional[str] = None,
        evidence_spans: Optional[List[Dict[str, Any]]] = None,
        analysis_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Build prompt for amendment suggestions"""
        
        base_prompt = f"""You are generating suggestions for how this legislation could be AMENDED or IMPROVED.

BILL: {bill_metadata.get('title', 'Unknown Bill')} ({bill_metadata.get('bill_number', 'Unknown Number')})

WRITING REQUIREMENTS:
- Write at a 10th grade reading level
- Generate 3-5 constructive amendment suggestions
- Each suggestion should be specific and actionable
- Focus on improvements that would address concerns or enhance benefits
- Be constructive and solution-oriented

CITATION REQUIREMENTS:
- Include exact quotes from specific bill sections that could be amended
- Use this format: [CITATION: "exact quote from bill"]
- Each suggestion must reference specific bill text

FORMAT:
Suggestion 1: [Clear amendment suggestion] [CITATION: "quote of section to amend"]
Rationale: [1-2 sentences explaining why this improvement is needed]

Suggestion 2: [Clear amendment suggestion] [CITATION: "quote of section to amend"]
Rationale: [1-2 sentences explaining why this improvement is needed]

...etc"""

        if congress_summary:
            base_prompt += f"""

CONGRESS.GOV SUMMARY (reference):
{congress_summary}"""

        if evidence_spans:
            base_prompt += f"""

EVIDENCE SPANS FROM BILL:
{self._format_evidence_spans(evidence_spans[:10])}"""

        if analysis_context:
            sections = analysis_context.get('complete_analysis', [])
            if sections:
                base_prompt += f"""

BILL ANALYSIS CONTEXT:
Based on detailed analysis, this bill has {len(sections)} major provisions. Use this context to identify areas that could be strengthened or refined."""

        base_prompt += f"""

BILL TEXT:
{bill_text[:15000]}

Generate constructive suggestions for how this legislation could be amended or improved. Focus on specific changes that would address concerns or enhance effectiveness."""

        return base_prompt
    
    def _format_evidence_spans(self, evidence_spans: List[Dict[str, Any]]) -> str:
        """Format evidence spans for prompt inclusion"""
        formatted = []
        for i, span in enumerate(evidence_spans):
            formatted.append(f"[{i+1}] {span.get('heading', 'Section')}: \"{span.get('quote', '')}\"")
        return "\n".join(formatted)
    
    def _parse_response_with_citations(self, response: str, evidence_spans: List[Dict[str, Any]]) -> Tuple[str, List[Dict[str, Any]]]:
        """Parse AI response and extract citations"""
        
        # Extract citations from response
        citation_pattern = r'\[CITATION:\s*"([^"]+)"\]'
        citations = []
        
        # Find all citations in the response
        for match in re.finditer(citation_pattern, response):
            citation_text = match.group(1)
            
            # Try to find matching evidence span
            best_match = None
            best_score = 0
            
            for span in evidence_spans:
                span_quote = span.get('quote', '')
                if span_quote and citation_text in span_quote:
                    score = len(citation_text) / len(span_quote)
                    if score > best_score:
                        best_score = score
                        best_match = span
            
            if best_match:
                citations.append({
                    'quote': citation_text,
                    'start_offset': best_match.get('start_offset', 0),
                    'end_offset': best_match.get('end_offset', 0),
                    'heading': best_match.get('heading'),
                    'anchor_id': best_match.get('anchor_id')
                })
            else:
                # Add citation without specific anchoring
                citations.append({
                    'quote': citation_text,
                    'start_offset': 0,
                    'end_offset': 0,
                    'heading': None,
                    'anchor_id': None
                })
        
        # Remove citation markup from content
        content = re.sub(citation_pattern, '', response).strip()
        
        return content, citations
    
    def _parse_provisions_response(self, response: str, evidence_spans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Parse provisions list response"""
        
        provisions = []
        
        # Split by lines and process each provision
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # Extract provision and citation
            citation_pattern = r'\[CITATION:\s*"([^"]+)"\]'
            citations = []
            
            for match in re.finditer(citation_pattern, line):
                citation_text = match.group(1)
                citations.append({
                    'quote': citation_text,
                    'start_offset': 0,
                    'end_offset': 0,
                    'heading': None,
                    'anchor_id': None
                })
            
            # Remove citation markup
            content = re.sub(citation_pattern, '', line).strip()
            
            # Remove provision numbering/bullet points
            content = re.sub(r'^(Provision\s*\d+:?\s*|[\d\w]+\.\s*|\*\s*|-\s*)', '', content).strip()
            
            if content:
                provisions.append({
                    'content': content,
                    'citations': citations
                })
        
        return provisions[:8]  # Limit to 8 provisions
    
    def _parse_timeline_response(self, response: str, evidence_spans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Parse timeline response"""
        
        timeline_items = []
        
        # Split by lines and process each timeline item
        lines = response.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # Extract timeline item and citation
            citation_pattern = r'\[CITATION:\s*"([^"]+)"\]'
            citations = []
            
            for match in re.finditer(citation_pattern, line):
                citation_text = match.group(1)
                citations.append({
                    'quote': citation_text,
                    'start_offset': 0,
                    'end_offset': 0,
                    'heading': None,
                    'anchor_id': None
                })
            
            # Remove citation markup
            content = re.sub(citation_pattern, '', line).strip()
            
            # Remove timeline item numbering
            content = re.sub(r'^(Timeline\s*Item\s*\d+:?\s*|[\d\w]+\.\s*|\*\s*|-\s*)', '', content).strip()
            
            if content:
                timeline_items.append({
                    'content': content,
                    'citations': citations
                })
        
        return timeline_items[:6]  # Limit to 6 timeline items
    
    def _parse_positions_response(self, response: str, evidence_spans: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Parse positions response into structured format"""
        
        positions = []
        
        # Split by lines and process each position
        lines = response.split('\n')
        current_reason = None
        current_justification = None
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            # Check if this is a reason line
            if line.startswith('Reason ') or line.startswith('Suggestion '):
                # Process previous reason if exists
                if current_reason:
                    positions.append(self._build_position_dict(current_reason, current_justification, evidence_spans))
                
                # Extract new reason
                current_reason = line
                current_justification = None
                
            elif line.startswith('Justification:') or line.startswith('Rationale:'):
                # Extract justification
                current_justification = line
        
        # Process final reason
        if current_reason:
            positions.append(self._build_position_dict(current_reason, current_justification, evidence_spans))
        
        return positions[:5]  # Limit to 5 positions
    
    def _build_position_dict(self, reason_line: str, justification_line: Optional[str], evidence_spans: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Build position dictionary from parsed lines"""
        
        # Extract citations from reason line
        citation_pattern = r'\[CITATION:\s*"([^"]+)"\]'
        citations = []
        
        for match in re.finditer(citation_pattern, reason_line):
            citation_text = match.group(1)
            
            # Try to find matching evidence span
            best_match = None
            best_score = 0
            
            for span in evidence_spans:
                span_quote = span.get('quote', '')
                if span_quote and citation_text in span_quote:
                    score = len(citation_text) / len(span_quote)
                    if score > best_score:
                        best_score = score
                        best_match = span
            
            if best_match:
                citations.append({
                    'quote': citation_text,
                    'start_offset': best_match.get('start_offset', 0),
                    'end_offset': best_match.get('end_offset', 0),
                    'heading': best_match.get('heading'),
                    'anchor_id': best_match.get('anchor_id')
                })
            else:
                # Add citation without specific anchoring
                citations.append({
                    'quote': citation_text,
                    'start_offset': 0,
                    'end_offset': 0,
                    'heading': None,
                    'anchor_id': None
                })
        
        # Remove citation markup from reason
        reason_clean = re.sub(citation_pattern, '', reason_line).strip()
        reason_clean = re.sub(r'^(Reason\s*\d+:?\s*|Suggestion\s*\d+:?\s*)', '', reason_clean).strip()
        
        # Extract justification content
        justification_clean = ""
        if justification_line:
            justification_clean = re.sub(r'^(Justification:\s*|Rationale:\s*)', '', justification_line).strip()
        
        return {
            'reason': reason_clean,
            'justification': justification_clean,
            'citations': citations
        }