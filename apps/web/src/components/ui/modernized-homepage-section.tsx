'use client'

import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card'
import { Button } from './button'
import { Badge } from './badge'
import { MegaphoneIcon } from 'lucide-react'

interface Bill {
  id: number
  bill_number: string
  title: string
  status: string
  chamber: string
  session_year: number
  sponsor_party?: string
  tldr?: string
  ai_summary?: string
  summary?: string
  popular_title?: string
  short_title?: string
}

interface ModernizedHomepageSectionProps {
  bills: Bill[]
}

export function ModernizedHomepageSection({ bills }: ModernizedHomepageSectionProps) {
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'committee': return 'committee' as const
      case 'floor': return 'floor' as const
      case 'passed': return 'passed' as const
      default: return 'status' as const
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'committee': return '⏳ In Committee'
      case 'floor': return '🗳️ Floor Vote Soon'
      case 'passed': return '✅ Passed Chamber'
      default: return '🔄 Active'
    }
  }

  const getStatusDetail = (status: string) => {
    switch (status) {
      case 'committee': return 'House Judiciary - Hearing scheduled'
      case 'floor': return 'Scheduled for House floor vote'
      case 'passed': return 'Awaiting Senate consideration'
      default: return 'Recently introduced'
    }
  }

  return (
    <section className="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-24">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6 leading-tight">
            Bills Moving Through Congress
          </h2>
          <p className="text-xl text-slate-700 max-w-3xl mx-auto font-medium">
            Track the most urgent legislation and make your voice heard on the bills that matter
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {bills.map((bill) => (
            <Card key={bill.id} className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-0 shadow-lg bg-white/70 backdrop-blur-sm">
              <CardHeader className="space-y-4">
                {/* Status Badge */}
                <div className="flex items-center justify-between">
                  <Badge variant={getStatusVariant(bill.status)} className="h-8 px-3 text-sm font-bold">
                    {getStatusText(bill.status)}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {bill.chamber === 'house' ? 'House' : 'Senate'} • {bill.session_year}
                  </Badge>
                </div>

                {/* Bill Number & Title */}
                <div className="space-y-3">
                  <Badge variant="secondary" className="font-mono font-semibold">
                    {bill.bill_number}
                  </Badge>
                  <CardTitle className="text-xl font-bold text-slate-900 leading-tight group-hover:text-blue-600 transition-colors">
                    {bill.title.length > 80 ? `${bill.title.substring(0, 80)}...` : bill.title}
                  </CardTitle>
                  <CardDescription className="text-sm font-medium">
                    Popular name: {bill.popular_title || bill.short_title || "Processing..."}
                  </CardDescription>
                </div>

                {/* Status Detail */}
                <div className="bg-slate-50 rounded-lg p-3 border border-slate-200">
                  <p className="text-xs font-medium text-slate-600">{getStatusDetail(bill.status)}</p>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                {/* AI Summary */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-700">
                      🤖 AI Summary
                    </Badge>
                  </div>
                  <p className="text-slate-700 text-sm leading-relaxed line-clamp-3">
                    {bill.tldr || bill.ai_summary || bill.summary || 'Legislative summary being processed...'}
                  </p>
                </div>

                {/* Bill Progress Indicator */}
                <div className="bg-slate-50 rounded-lg p-4 border border-slate-200">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      <span className="text-sm font-semibold text-slate-800">Bill Progress</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {bill.session_year} Session
                    </Badge>
                  </div>
                  <div className="flex justify-between text-xs font-medium text-slate-700 mb-3">
                    <span className={bill.status === 'introduced' ? 'font-bold text-blue-700' : 'text-slate-500'}>
                      Introduced
                    </span>
                    <span className={bill.status === 'committee' ? 'font-bold text-amber-700' : 'text-slate-500'}>
                      Committee
                    </span>
                    <span className={bill.status === 'floor' ? 'font-bold text-red-700' : 'text-slate-500'}>
                      Floor
                    </span>
                    <span className={bill.status === 'passed' ? 'font-bold text-green-700' : 'text-slate-500'}>
                      President
                    </span>
                  </div>
                  <div className="w-full bg-slate-200 rounded-full h-3 shadow-inner">
                    <div className={`h-3 rounded-full transition-all duration-500 ${
                      bill.status === 'introduced' ? 'w-1/4 bg-gradient-to-r from-blue-500 to-blue-600' :
                      bill.status === 'committee' ? 'w-2/4 bg-gradient-to-r from-amber-500 to-orange-500' :
                      bill.status === 'floor' ? 'w-3/4 bg-gradient-to-r from-red-500 to-red-600' :
                      'w-full bg-gradient-to-r from-green-500 to-green-600'
                    }`}></div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-2">
                  <Button
                    variant="action"
                    size="lg"
                    className="flex-1"
                    asChild
                  >
                    <Link href={`/bills/${bill.id}/action`}>
                      <MegaphoneIcon className="w-4 h-4" />
                      Send Message to Congress
                    </Link>
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    asChild
                  >
                    <Link href={`/bills/${bill.id}`}>
                      Read Analysis
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Button
            variant="civic"
            size="xl"
            asChild
          >
            <Link href="/bills">
              View All 147 Bills Active This Week
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}