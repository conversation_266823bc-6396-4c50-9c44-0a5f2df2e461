'use client';

import React, { useState, useEffect } from 'react';
import { Official, Bill } from '@/types';
import { trackSocialEngagement } from '@/services/analytics';

interface SocialMediaContactWidgetProps {
  officials: Official[];
  bill?: Bill;
  stance?: 'support' | 'oppose' | 'amend';
  userZip?: string;
  customMessage?: string;
  className?: string;
  variant?: 'compact' | 'full' | 'inline';
  context?: 'bill-action' | 'official-profile' | 'dashboard' | 'campaign';
}

interface SocialPlatform {
  name: string;
  icon: string;
  color: string;
  charLimit?: number;
  features: string[];
}

const SOCIAL_PLATFORMS: Record<string, SocialPlatform> = {
  twitter: {
    name: 'Twitter/X',
    icon: '🐦',
    color: 'bg-blue-500',
    charLimit: 280,
    features: ['hashtags', 'mentions', 'threads']
  },
  facebook: {
    name: 'Facebook',
    icon: '📘', 
    color: 'bg-blue-600',
    features: ['mentions', 'longform', 'links']
  },
  instagram: {
    name: 'Instagram',
    icon: '📷',
    color: 'bg-pink-500',
    features: ['visual', 'stories', 'hashtags']
  },
  linkedin: {
    name: 'LinkedIn',
    icon: '💼',
    color: 'bg-blue-700',
    features: ['professional', 'longform', 'articles']
  }
};

export const SocialMediaContactWidget: React.FC<SocialMediaContactWidgetProps> = ({
  officials,
  bill,
  stance,
  userZip,
  customMessage,
  className = '',
  variant = 'full',
  context = 'bill-action'
}) => {
  const [selectedOfficial, setSelectedOfficial] = useState<Official | null>(null);
  const [selectedPlatform, setSelectedPlatform] = useState<string>('twitter');
  const [messageTemplate, setMessageTemplate] = useState<string>('');
  const [isExpanded, setIsExpanded] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Get available social platforms for officials
  const getAvailablePlatforms = (official: Official): string[] => {
    if (!official.social_media) return [];
    
    return Object.keys(official.social_media)
      .map(platform => platform.toLowerCase())
      .filter(platform => 
        platform.includes('twitter') || 
        platform.includes('facebook') || 
        platform.includes('instagram') || 
        platform.includes('linkedin')
      )
      .map(platform => {
        if (platform.includes('twitter')) return 'twitter';
        if (platform.includes('facebook')) return 'facebook';  
        if (platform.includes('instagram')) return 'instagram';
        if (platform.includes('linkedin')) return 'linkedin';
        return platform;
      });
  };

  // Generate platform-specific message templates
  const generateMessageTemplate = (
    official: Official, 
    platform: string, 
    context: string
  ): string => {
    const displayName = official.full_name || official.name;
    const handle = getOfficialHandle(official, platform);
    const isTwitter = platform === 'twitter';
    const isLinkedIn = platform === 'linkedin';
    
    let message = '';
    
    if (bill && stance) {
      const stanceText = stance === 'support' ? 'support' : 
                        stance === 'oppose' ? 'oppose' : 
                        'want changes to';
      
      if (isTwitter) {
        message = `${handle ? `${handle} ` : `Dear ${displayName}, `}As your constituent${userZip ? ` from ${userZip}` : ''}, I ${stanceText} ${bill.bill_number}`;
        
        if (bill.title.length < 100) {
          message += `: ${bill.title}`;
        }
        
        message += `. ${customMessage || 'Please consider my position on this important legislation.'}`;
        
        // Add relevant hashtags
        const hashtags = [
          '#' + bill.bill_number.replace(/\s+/g, ''),
          stance === 'support' ? '#SupportThis' : stance === 'oppose' ? '#OposeThis' : '#AmendThis',
          '#YourVote'
        ];
        
        const remainingChars = 280 - message.length;
        const hashtagString = hashtags.join(' ');
        
        if (remainingChars > hashtagString.length + 5) {
          message += ' ' + hashtagString;
        }
      } else if (isLinkedIn) {
        message = `Dear ${displayName},

As your constituent${userZip ? ` representing ${userZip}` : ''}, I am writing to express that I ${stanceText} ${bill.bill_number}: ${bill.title}.

${customMessage || 'This legislation is important to me and my community for several reasons:'}

• [Add your specific reasons here]
• [Explain local impact]
• [Share personal story if relevant]

I hope you will consider constituent feedback like mine when this bill comes to a vote. Your leadership on this issue would be greatly appreciated.

Thank you for your time and service.

Sincerely,
[Your name]
[Your location]`;
      } else {
        // Facebook/Instagram - Medium length
        message = `${handle ? `${handle} ` : `Dear ${displayName}, `}

As your constituent${userZip ? ` from ${userZip}` : ''}, I wanted to reach out about ${bill.bill_number}: ${bill.title}.

I ${stanceText} this legislation because ${customMessage || '[explain your reasoning here]'}.

${stance === 'support' ? 
  'I hope you will vote in favor of this important bill.' :
  stance === 'oppose' ?
  'I hope you will vote against this bill.' :
  'I hope you will work to improve this bill before it comes to a vote.'
}

Thank you for representing our community in Congress.`;
      }
    } else {
      // General contact message
      if (isTwitter) {
        message = `${handle ? `${handle} ` : `Dear ${displayName}, `}Thank you for your service representing${userZip ? ` ${userZip}` : ' our community'}. ${customMessage || 'I wanted to share my thoughts on an important issue.'}`;
      } else {
        message = `Dear ${displayName},

Thank you for your dedicated service representing${userZip ? ` the ${userZip} area` : ' our community'} in ${official.chamber === 'senate' ? 'the Senate' : 'Congress'}.

${customMessage || 'I wanted to reach out to share my thoughts on an issue that\'s important to me and many of your constituents.'}

[Add your specific message here]

Thank you for your time and consideration.

Sincerely,
[Your name]`;
      }
    }
    
    return message;
  };

  // Get official's handle for a platform
  const getOfficialHandle = (official: Official, platform: string): string => {
    if (!official.social_media) return '';
    
    const socialMedia = official.social_media;
    const platformKey = Object.keys(socialMedia).find(key => 
      key.toLowerCase().includes(platform)
    );
    
    if (!platformKey) return '';
    
    const url = socialMedia[platformKey];
    
    if (platform === 'twitter' && url) {
      const match = url.match(/(?:twitter\.com|x\.com)\/([A-Za-z0-9_]+)/);
      return match ? `@${match[1]}` : '';
    }
    
    return '';
  };

  // Get social media URL for official and platform
  const getSocialMediaUrl = (official: Official, platform: string): string => {
    if (!official.social_media) return '';
    
    const socialMedia = official.social_media;
    const platformKey = Object.keys(socialMedia).find(key => 
      key.toLowerCase().includes(platform)
    );
    
    return platformKey ? socialMedia[platformKey] : '';
  };

  // Handle message customization
  const handleSendMessage = (official: Official, platform: string, message: string) => {
    const socialUrl = getSocialMediaUrl(official, platform);
    if (!socialUrl) return;
    
    let engagementUrl = socialUrl;
    
    if (platform === 'twitter') {
      const tweetText = encodeURIComponent(message);
      engagementUrl = `https://twitter.com/intent/tweet?text=${tweetText}`;
    } else if (platform === 'facebook') {
      // For Facebook, we can't pre-fill text, so just open their page
      engagementUrl = socialUrl;
    }
    
    // Track engagement analytics
    trackSocialEngagement({
      actionType: 'direct-message',
      platform: platform as any,
      official: official.name,
      officialId: official.id,
      bill: bill?.bill_number,
      stance,
      userLocation: userZip || 'unknown',
      messageLength: message.length,
      customization: message !== generateMessageTemplate(official, platform, context),
      context: context as any
    });
    
    window.open(engagementUrl, '_blank', 'width=600,height=600');
  };

  useEffect(() => {
    if (officials.length > 0 && !selectedOfficial) {
      setSelectedOfficial(officials[0]);
    }
  }, [officials]);

  useEffect(() => {
    if (selectedOfficial) {
      const template = generateMessageTemplate(selectedOfficial, selectedPlatform, context);
      setMessageTemplate(template);
    }
  }, [selectedOfficial, selectedPlatform, bill, stance, customMessage, userZip, context]);

  if (officials.length === 0) return null;

  // Compact variant - just quick action buttons
  if (variant === 'compact') {
    return (
      <div className={`flex flex-wrap gap-2 ${className}`}>
        {officials.slice(0, 2).map(official => {
          const availablePlatforms = getAvailablePlatforms(official);
          return availablePlatforms.slice(0, 2).map(platform => {
            const platformInfo = SOCIAL_PLATFORMS[platform];
            if (!platformInfo) return null;
            
            return (
              <button
                key={`${official.id}-${platform}`}
                onClick={() => {
                  const message = generateMessageTemplate(official, platform, context);
                  handleSendMessage(official, platform, message);
                }}
                className={`inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-full text-white ${platformInfo.color} hover:opacity-90 transition-opacity`}
                title={`Message ${official.name} on ${platformInfo.name}`}
              >
                <span className="mr-1">{platformInfo.icon}</span>
                {platform}
              </button>
            );
          });
        })}
      </div>
    );
  }

  // Inline variant - single row with dropdown
  if (variant === 'inline') {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <span className="text-sm font-medium text-gray-700">Quick Contact:</span>
        <div className="flex space-x-2">
          {officials.slice(0, 3).map(official => {
            const availablePlatforms = getAvailablePlatforms(official);
            const primaryPlatform = availablePlatforms[0];
            
            if (!primaryPlatform) return null;
            
            const platformInfo = SOCIAL_PLATFORMS[primaryPlatform];
            
            return (
              <button
                key={official.id}
                onClick={() => {
                  const message = generateMessageTemplate(official, primaryPlatform, context);
                  handleSendMessage(official, primaryPlatform, message);
                }}
                className="inline-flex items-center px-2 py-1 text-xs font-medium rounded border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                title={`Message ${official.name} on ${platformInfo.name}`}
              >
                <span className="mr-1">{platformInfo.icon}</span>
                {official.name.split(' ').slice(-1)[0]}
              </button>
            );
          })}
        </div>
      </div>
    );
  }

  // Full variant - comprehensive interface
  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <span className="mr-2">📱</span>
            Contact Your Representatives
          </h3>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            {isExpanded ? 'Show Less' : 'Customize Message'}
          </button>
        </div>
        
        {bill && stance && (
          <p className="mt-2 text-sm text-gray-600">
            Share your {stance === 'support' ? 'support for' : stance === 'oppose' ? 'opposition to' : 'suggested changes to'} <strong>{bill.bill_number}</strong>
          </p>
        )}
      </div>

      {/* Quick Actions */}
      <div className="p-4">
        <div className="grid gap-3">
          {officials.map(official => {
            const availablePlatforms = getAvailablePlatforms(official);
            
            if (availablePlatforms.length === 0) return null;
            
            return (
              <div key={official.id} className="border border-gray-100 rounded-lg p-3">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900">
                      {official.full_name || official.name}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {official.title} {official.state && `• ${official.state}`}
                    </p>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {availablePlatforms.map(platform => {
                    const platformInfo = SOCIAL_PLATFORMS[platform];
                    if (!platformInfo) return null;
                    
                    return (
                      <button
                        key={platform}
                        onClick={() => {
                          if (isExpanded) {
                            setSelectedOfficial(official);
                            setSelectedPlatform(platform);
                            setShowPreview(true);
                          } else {
                            const message = generateMessageTemplate(official, platform, context);
                            handleSendMessage(official, platform, message);
                          }
                        }}
                        className={`inline-flex items-center px-3 py-1.5 text-sm font-medium rounded-md text-white ${platformInfo.color} hover:opacity-90 transition-opacity`}
                      >
                        <span className="mr-2">{platformInfo.icon}</span>
                        {platformInfo.name}
                      </button>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Message Preview Modal */}
      {showPreview && selectedOfficial && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">
                  Message Preview - {selectedOfficial.name}
                </h3>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Platform: {SOCIAL_PLATFORMS[selectedPlatform]?.name}
                  </label>
                  <div className="flex space-x-2">
                    {getAvailablePlatforms(selectedOfficial).map(platform => (
                      <button
                        key={platform}
                        onClick={() => setSelectedPlatform(platform)}
                        className={`px-3 py-1 text-sm rounded ${
                          selectedPlatform === platform
                            ? 'bg-blue-100 text-blue-800'
                            : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                        }`}
                      >
                        {SOCIAL_PLATFORMS[platform]?.icon} {SOCIAL_PLATFORMS[platform]?.name}
                      </button>
                    ))}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your Message:
                  </label>
                  <textarea
                    value={messageTemplate}
                    onChange={(e) => setMessageTemplate(e.target.value)}
                    className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Customize your message..."
                  />
                  {SOCIAL_PLATFORMS[selectedPlatform]?.charLimit && (
                    <p className="mt-1 text-xs text-gray-500">
                      {messageTemplate.length}/{SOCIAL_PLATFORMS[selectedPlatform].charLimit} characters
                    </p>
                  )}
                </div>
                
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setShowPreview(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      handleSendMessage(selectedOfficial, selectedPlatform, messageTemplate);
                      setShowPreview(false);
                    }}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
                  >
                    Send Message
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SocialMediaContactWidget;