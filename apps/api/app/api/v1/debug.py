"""
Debug endpoints for development and staging environments.
These endpoints provide health checks and diagnostic information.
"""

from typing import Any, Dict
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.db.database import get_db
from app.services.openstates_officials_api import get_openstates_officials_client
from app.models.official import Official

router = APIRouter()


@router.get("/health")
def health_check() -> Dict[str, Any]:
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "message": "ModernAction API is running",
        "environment": "development"  # Should be updated per environment
    }


@router.get("/openstates-health")
def openstates_health_check() -> Dict[str, Any]:
    """Check the health of the OpenStates API integration."""
    client = get_openstates_officials_client()
    return client.health_check()


@router.get("/database-stats")
def database_stats(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Get basic database statistics for debugging."""
    try:
        # Count officials
        total_officials = db.query(Official).count()
        active_officials = db.query(Official).filter(Official.is_active == True).count()
        federal_officials = db.query(Official).filter(Official.level == 'federal').count()
        state_officials = db.query(Official).filter(Official.level == 'state').count()
        
        # Count officials with social media
        officials_with_social = db.query(Official).filter(
            Official.social_media.isnot(None)
        ).count()
        
        # Count officials with photos
        officials_with_photos = db.query(Official).filter(
            Official.official_photo_url.isnot(None)
        ).count()
        
        return {
            "status": "success",
            "statistics": {
                "total_officials": total_officials,
                "active_officials": active_officials,
                "federal_officials": federal_officials,
                "state_officials": state_officials,
                "officials_with_social_media": officials_with_social,
                "officials_with_photos": officials_with_photos,
                "social_media_coverage": f"{(officials_with_social / max(total_officials, 1) * 100):.1f}%",
                "photo_coverage": f"{(officials_with_photos / max(total_officials, 1) * 100):.1f}%"
            }
        }
    
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


@router.get("/test-zip/{zip_code}")
def test_zip_lookup(zip_code: str) -> Dict[str, Any]:
    """Test ZIP code lookup using OpenStates API directly."""
    if len(zip_code) != 5 or not zip_code.isdigit():
        return {
            "status": "error",
            "error": "ZIP code must be 5 digits"
        }
    
    try:
        client = get_openstates_officials_client()
        officials = client.get_officials_by_zip(zip_code)
        
        return {
            "status": "success",
            "zip_code": zip_code,
            "officials_found": len(officials),
            "officials": [
                {
                    "name": official.name,
                    "title": official.title,
                    "party": official.party,
                    "level": official.level,
                    "state": official.state,
                    "has_social_media": official.social_media is not None,
                    "has_photo": official.photo_url is not None,
                    "has_email": official.email is not None,
                    "has_phone": official.phone is not None
                }
                for official in officials[:10]  # Limit to first 10 for readability
            ]
        }
    
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "zip_code": zip_code
        }


@router.get("/official/{official_id}")
def debug_get_official(official_id: str, db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Debug endpoint to check if a specific official exists and get basic info."""
    try:
        from app.services.officials import OfficialService
        import time
        
        start_time = time.time()
        
        service = OfficialService(db)
        official = service.get_official(official_id)
        
        duration = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        if not official:
            return {
                "status": "not_found",
                "official_id": official_id,
                "query_duration_ms": duration,
                "message": "Official not found in database"
            }
        
        return {
            "status": "found",
            "official_id": official_id,
            "query_duration_ms": duration,
            "official": {
                "id": str(official.id),
                "name": official.name,
                "title": official.title,
                "party": official.party,
                "state": official.state,
                "is_active": official.is_active,
                "created_at": official.created_at.isoformat() if official.created_at else None,
                "updated_at": official.updated_at.isoformat() if official.updated_at else None
            }
        }
    
    except Exception as e:
        return {
            "status": "error",
            "official_id": official_id,
            "error": str(e),
            "error_type": type(e).__name__
        }

@router.get("/sample-official")
def get_sample_official(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """Get a sample official record to verify data structure."""
    try:
        # Get an official with social media data if possible
        official = db.query(Official).filter(
            Official.social_media.isnot(None),
            Official.is_active == True
        ).first()
        
        if not official:
            # Fallback to any active official
            official = db.query(Official).filter(Official.is_active == True).first()
        
        if not official:
            return {
                "status": "error",
                "error": "No officials found in database"
            }
        
        return {
            "status": "success",
            "official": {
                "id": str(official.id),
                "name": official.name,
                "full_name": official.full_name,
                "title": official.title,
                "party": official.party,
                "level": official.level,
                "state": official.state,
                "district": official.district,
                "email": official.email,
                "phone": official.phone,
                "website": official.website,
                "social_media": official.social_media,
                "has_photo": official.official_photo_url is not None,
                "photo_url": official.official_photo_url,
                "bio_length": len(official.bio) if official.bio else 0,
                "created_at": official.created_at.isoformat() if official.created_at else None,
                "updated_at": official.updated_at.isoformat() if official.updated_at else None
            }
        }
    
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }