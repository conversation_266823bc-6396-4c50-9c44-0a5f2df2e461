#!/usr/bin/env python3
"""
Test the span-grounded validation system
"""

import asyncio
import sys
import os
import json

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

async def test_validation_gates():
    """Test the validation gates with good and bad examples"""
    try:
        from app.services.span_grounded_validator import SpanGroundedValidator
        
        validator = SpanGroundedValidator()
        
        print("🧪 Testing Span-Grounded Validation Gates")
        print("=" * 60)
        
        # Test 1: BAD example (like HR7 current output)
        print("\n1️⃣ Testing BAD example (null headings/anchor_ids)")
        bad_example = {
            "tldr": {
                "content": "The bill prohibits federal funding for abortions and requires disclosure.",
                "citations": [
                    {
                        "quote": "Funding for Abortion and Abortion Insurance Full Disclosure",
                        "heading": None,  # ❌ NULL HEADING
                        "anchor_id": None,  # ❌ NULL ANCHOR_ID
                        "start_offset": 2639,
                        "end_offset": 2698
                    }
                ]
            },
            "who_affects": [
                {
                    "content": "All provisions are effective immediately upon enactment.",  # ❌ GENERIC
                    "citations": []  # ❌ NO CITATIONS
                }
            ],
            "why_matters": {
                "content": "This is important legislation.",  # ❌ VAGUE
                "citations": []  # ❌ NO CITATIONS
            },
            "key_provisions": [
                {
                    "content": "Technical provisions and administrative details",  # ❌ GENERIC PHRASE
                    "citations": []  # ❌ NO CITATIONS
                }
            ]
        }
        
        is_valid, errors, metrics = validator.validate_analysis(bad_example)
        print(f"Valid: {is_valid}")
        print(f"Errors: {len(errors)}")
        print(f"Evidence coverage: {metrics['evidence_coverage']:.2f}")
        print(f"Invalid citations: {metrics['invalid_citation_count']}")
        
        if not is_valid:
            print("✅ CORRECTLY REJECTED bad example")
            for error in errors[:5]:  # Show first 5 errors
                print(f"  - {error}")
        else:
            print("❌ FAILED to reject bad example")
        
        # Test 2: GOOD example (like HR5 old output)
        print("\n2️⃣ Testing GOOD example (proper headings/anchor_ids)")
        good_example = {
            "tldr": {
                "content": "The Parents Bill of Rights Act requires local educational agencies to post curriculum information online by September 30, 2024.",
                "citations": [
                    {
                        "quote": "not later than September 30, 2024",
                        "heading": "SEC. 1111. STATE PLANS.",
                        "anchor_id": "sec-3",
                        "start_offset": 2100,
                        "end_offset": 2135
                    }
                ]
            },
            "who_affects": [
                {
                    "content": "Local educational agencies must comply with new curriculum disclosure requirements.",
                    "citations": [
                        {
                            "quote": "each local educational agency",
                            "heading": "SEC. 1111. STATE PLANS.",
                            "anchor_id": "sec-3",
                            "start_offset": 1950,
                            "end_offset": 1980
                        }
                    ]
                }
            ],
            "why_matters": {
                "content": "Parents will have access to detailed curriculum information for the first time.",
                "citations": [
                    {
                        "quote": "curriculum or description of such curriculum",
                        "heading": "SEC. 1111. STATE PLANS.",
                        "anchor_id": "sec-3",
                        "start_offset": 2000,
                        "end_offset": 2040
                    }
                ]
            },
            "key_provisions": [
                {
                    "content": "Agencies must post curriculum information on their website by September 30, 2024.",
                    "citations": [
                        {
                            "quote": "post on the website of the local educational agency",
                            "heading": "SEC. 1111. STATE PLANS.",
                            "anchor_id": "sec-3",
                            "start_offset": 1800,
                            "end_offset": 1850
                        }
                    ]
                }
            ]
        }
        
        is_valid, errors, metrics = validator.validate_analysis(good_example)
        print(f"Valid: {is_valid}")
        print(f"Errors: {len(errors)}")
        print(f"Evidence coverage: {metrics['evidence_coverage']:.2f}")
        print(f"Invalid citations: {metrics['invalid_citation_count']}")
        
        if is_valid:
            print("✅ CORRECTLY ACCEPTED good example")
        else:
            print("❌ FAILED to accept good example")
            for error in errors[:3]:  # Show first 3 errors
                print(f"  - {error}")
        
        print("\n" + "=" * 60)
        print("📋 Validation Test Results:")
        print(f"✅ Bad example rejected: {'PASS' if not validator.validate_analysis(bad_example)[0] else 'FAIL'}")
        print(f"✅ Good example accepted: {'PASS' if validator.validate_analysis(good_example)[0] else 'FAIL'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_service_integration():
    """Test that AI service uses the validator"""
    try:
        from app.services.ai_service import AIService
        
        print("\n🔗 Testing AI Service Integration")
        print("=" * 60)
        
        ai_service = AIService()
        if not ai_service.enabled:
            print("⚠️ AI service not enabled - skipping integration test")
            return True
        
        print("✅ AI service has validator:", hasattr(ai_service, 'validator'))
        print("✅ Validator is SpanGroundedValidator:", type(ai_service.validator).__name__)
        
        return True
        
    except Exception as e:
        print(f"❌ AI service integration test failed: {e}")
        return False

async def main():
    """Run all validation tests"""
    print("🧪 Testing Span-Grounded Validation System")
    print("=" * 60)
    
    # Test validation gates
    validation_success = await test_validation_gates()
    
    # Test AI service integration
    integration_success = await test_ai_service_integration()
    
    print("\n" + "=" * 60)
    print("📋 Overall Test Results:")
    print(f"✅ Validation gates: {'PASS' if validation_success else 'FAIL'}")
    print(f"✅ AI service integration: {'PASS' if integration_success else 'FAIL'}")
    
    if validation_success and integration_success:
        print("\n🎉 ALL VALIDATION TESTS PASSED!")
        print("🚫 Bad output will now be blocked")
        print("✅ Only quality content will be published")
    else:
        print("\n❌ VALIDATION TESTS FAILED")
        print("🔧 Need to fix validation system")

if __name__ == "__main__":
    asyncio.run(main())
