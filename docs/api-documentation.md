# Bill Details API Documentation
> Last updated: 2025-08-11


## Overview

The Bill Details API provides endpoints for retrieving structured, citation-based analysis of legislative bills. All responses include verifiable quotes from the original bill text.

## Base URL

- Development: `http://localhost:8000/api/v1`
- Production: `https://api.modernaction.io/api/v1`

## Authentication

Most bill details endpoints are public. Authentication is handled via JWT tokens for administrative functions.

## Endpoints

### GET /bills/{bill_id}/details

Retrieve bill details by bill ID.

**Parameters:**
- `bill_id` (string): The unique identifier for the bill

**Response:** `BillDetailsResponse`

### GET /bills/details/by-slug/{slug}

Retrieve bill details by SEO slug.

- Accepts both hr5-118 and 5-118 style slugs by normalizing letters+digits
- Returns 404 when not present; avoids 500 for missing data

**Parameters:**
- `slug` (string): Canonical SEO slug (e.g., "hr5-118")

**Response:** `BillDetailsResponse`

**Example:**
```
GET /api/v1/bills/details/by-slug/hr5-118
```

```json
{
  "id": "bill_123",
  "bill_id": "bill_123",
  "seo_slug": "hr5-118",
  "seo_title": "Healthcare Access Act - Bill Details",
  "seo_meta_description": "Analysis of healthcare legislation...",
  "canonical_url": "https://modernaction.io/bills/hr5-118",
  "hero_summary": "Brief overview of the bill...",
  "needs_human_review": true,
  "metrics": {
    "coverage_ratio": 0.75,
    "unverified_count": 2
  },
  "overview": {
    "what_does": {
      "content": "This bill expands...",
      "citations": [
        {
          "quote": "expands Medicaid eligibility",
          "start_offset": 45,
          "end_offset": 68,
          "heading": "SEC. 101. MEDICAID EXPANSION",
          "anchor_id": "sec-1"
        }
      ]
    }
  }
}
```

### GET /bills/details/by-slug/{slug}

Retrieve bill details by SEO slug. Supports both `hr5-118` and `5-118` formats.

**Parameters:**
- `slug` (string): The SEO slug for the bill

**Response:** `BillDetailsResponse` (same as above)

**Error Responses:**
- `404`: Bill details not found
- `500`: Server error

## Data Models

### BillDetailsResponse

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Unique identifier |
| `bill_id` | string | Associated bill ID |
| `seo_slug` | string | URL-friendly identifier |
| `seo_title` | string | Page title for SEO |
| `seo_meta_description` | string | Meta description |
| `canonical_url` | string | Canonical URL |
| `hero_summary` | string | Brief overview |
| `overview` | Overview | Structured sections |
| `positions` | Positions | Support/oppose/amend reasons |
| `other_details` | Array | Additional content |
| `source_index` | Array | Text segment metadata |
| `needs_human_review` | boolean | Moderation flag |
| `metrics` | Metrics | Coverage and quality stats |

### Overview

| Field | Type | Description |
|-------|------|-------------|
| `what_does` | SectionWithCitations | What the bill does |
| `who_affects` | SectionWithCitations | Who is affected |
| `why_matters` | SectionWithCitations | Why it matters |
| `key_provisions` | Array<ProvisionItem> | Key provisions |
| `cost_impact` | SectionWithCitations | Cost analysis |
| `timeline` | Array<TimelineItem> | Implementation timeline |

### SectionWithCitations

| Field | Type | Description |
|-------|------|-------------|
| `content` | string | Section content |
| `citations` | Array<Citation> | Supporting quotes |

### Citation

| Field | Type | Description |
|-------|------|-------------|
| `quote` | string | Exact quote from bill text |
| `start_offset` | integer | Character start position |
| `end_offset` | integer | Character end position |
| `heading` | string | Section heading |
| `anchor_id` | string | HTML anchor ID |

### Positions

| Field | Type | Description |
|-------|------|-------------|
| `support_reasons` | Array<PositionReason> | Reasons to support |
| `oppose_reasons` | Array<PositionReason> | Reasons to oppose |
| `amend_reasons` | Array<PositionReason> | Reasons to amend |

### PositionReason

| Field | Type | Description |
|-------|------|-------------|
| `claim` | string | Position statement |
| `justification` | string | Supporting rationale |
| `citations` | Array<Citation> | Supporting quotes |

### Metrics

| Field | Type | Description |
|-------|------|-------------|
| `coverage_ratio` | float | Percentage of sections with citations |
| `unverified_count` | integer | Number of sections lacking citations |

## Implementation Notes

### Slug Normalization

The API accepts multiple slug formats:
- Preferred: `hr5-118` (full bill number with session)
- Alternative: `5-118` (number only with session)
- Legacy: `hr-5-118` (with hyphens)

### Citation Validation
### Admin Pipeline Endpoint

POST /admin/process-bill-details
- Description: Runs unified pipeline (metadata → full_text → AI → bill_details → values)
- Body: { "bill_number": "HR5", "session": "118", "environment": "development" }
- Response: { success: true, bill_id, message, updated: { ai_analysis, details_persisted, values_analysis } }
- Notes:
  - Idempotent: re-processing updates existing bill and details safely
  - Requires OpenAI key for full AI analysis; falls back to HF when not available

### Slug Formats
- Canonical: hr5-118 (letters+digits + dash + session)
- Alternate: 5-118 (digits-only prefix)
- API accepts both; frontend normalizes to canonical via utils/slug.ts


All citations are validated against the source bill text:
- Quotes must match exactly (after whitespace normalization)
- Offsets are calculated from the original text
- Invalid quotes are automatically dropped

### Error Handling

- Missing bill details return 404 with helpful error message
- Invalid slugs are normalized before lookup
- Server errors include correlation IDs for debugging

### Rate Limiting

- Public endpoints: 1000 requests/hour per IP
- Authenticated endpoints: 5000 requests/hour per user

## Example Usage

### JavaScript/TypeScript

```typescript
// Fetch bill details by slug
const response = await fetch('/api/v1/bills/details/by-slug/hr5-118');
const billDetails = await response.json();

// Access citations
const citations = billDetails.overview.what_does.citations;
citations.forEach(citation => {
  console.log(`"${citation.quote}" - ${citation.heading}`);
});
```

### Python

```python
import requests

# Fetch bill details
response = requests.get('/api/v1/bills/details/by-slug/hr5-118')
bill_details = response.json()

# Access overview content
overview = bill_details['overview']
print(f"What it does: {overview['what_does']['content']}")
```

### cURL

```bash
# Fetch bill details
curl -X GET "http://localhost:8000/api/v1/bills/details/by-slug/hr5-118" \
  -H "Accept: application/json"
```

## Testing

Use the provided test fixtures for consistent testing:
- `cypress/fixtures/bill-details.json` - Sample response
- `apps/api/tests/test_bill_details_api.py` - API tests

## See Also

- [Frontend Integration Guide](./frontend-guide.md)
- [AI Pipeline Documentation](./ai-pipeline.md)
- [Testing Guide](./testing-guide.md)