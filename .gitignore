# Environment files - ALL .env files
.env
.env.*
*.env
*.env.*
.env.local
.env.development
.env.test
.env.staging
.env.production

# Application-specific environment files
apps/**/.env
apps/**/.env.*
apps/**/env
apps/**/env.*

# Lambda environment files
apps/lambda/**/.env
apps/lambda/**/.env.*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDEs
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Database
*.db
*.sqlite

# CDK
cdk.out/

# Test results
test-results/
playwright-report/
coverage/

# Build artifacts
*.zip
apps/lambda/**/build/

logs
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/ 
