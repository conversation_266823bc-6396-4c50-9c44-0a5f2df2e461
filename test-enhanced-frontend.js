const axios = require('axios');

async function testEnhancedFrontend() {
  console.log('🧪 Testing Enhanced Complete Analysis Frontend...\n');
  
  try {
    // Test the API endpoint first
    console.log('1. Testing API endpoint...');
    const apiResponse = await axios.get('http://localhost:8000/api/v1/bills/details/by-slug/hr4922-119');
    const details = apiResponse.data;
    
    console.log(`✅ API Status: ${apiResponse.status}`);
    console.log(`✅ Sections: ${details.overview?.complete_analysis?.length || 0}`);
    console.log(`✅ Progressive: ${details.overview?.chunks_completed || 0}/${details.overview?.total_chunks || 0} chunks`);
    console.log(`✅ Processing Status: ${details.overview?.processing_status || 'unknown'}`);
    
    // Test enhanced sections
    if (details.overview?.complete_analysis?.length > 0) {
      const sampleSection = details.overview.complete_analysis[0];
      console.log(`✅ Enhanced section detected:`);
      console.log(`   - Title: "${sampleSection.title}"`);
      console.log(`   - Enhanced content: ${!!(sampleSection.detailed_summary)}`) ;
      console.log(`   - Evidence IDs: ${sampleSection.ev_ids?.length || 0}`);
      console.log(`   - Content length: ${(sampleSection.detailed_summary || sampleSection.summary || '').length} chars`);
    }
    
    // Test frontend page
    console.log('\n2. Testing frontend page...');
    const frontendResponse = await axios.get('http://localhost:3000/bills/hr4922-119');
    console.log(`✅ Frontend Status: ${frontendResponse.status}`);
    
    // Check for key indicators in the HTML
    const html = frontendResponse.data;
    const hasProgressiveAnalysis = html.includes('World-Class Analysis') || html.includes('Progressive Analysis');
    const hasEnhancedComponent = html.includes('EnhancedCompleteAnalysis') || html.includes('Enhanced with premium AI');
    const hasSections = html.includes('sections') && html.includes('chunks');
    
    console.log(`✅ Contains progressive analysis indicators: ${hasProgressiveAnalysis}`);
    console.log(`✅ Contains enhanced analysis content: ${hasSections}`);
    
    console.log('\n🎉 Enhanced Frontend Test Results:');
    console.log(`   • API providing ${details.overview?.complete_analysis?.length || 0} enhanced sections`);
    console.log(`   • Progressive analysis: ${details.overview?.processing_status === 'in_progress' ? 'IN PROGRESS' : 'COMPLETED'}`);
    console.log(`   • Frontend rendering: ${frontendResponse.status === 200 ? 'SUCCESS' : 'FAILED'}`);
    console.log(`   • Enhanced UI components: ${hasProgressiveAnalysis ? 'ACTIVE' : 'MISSING'}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testEnhancedFrontend();