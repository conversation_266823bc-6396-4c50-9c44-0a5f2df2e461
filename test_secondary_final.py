#!/usr/bin/env python3
"""
Final test of Secondary Analysis Service integration
"""

import asyncio
import sys
import os
sys.path.append('./apps/api')
os.environ['ENV_FILE'] = '.env.local'

async def test_final_secondary():
    print("🧪 Final Secondary Analysis Integration Test...")
    
    try:
        from app.db.database import get_db
        from app.services.unified_bill_processing_service import UnifiedBillProcessingService
        
        db = next(get_db())
        service = UnifiedBillProcessingService(db)
        
        print("Processing HR4922 with Secondary Analysis Service...")
        result = await service.process_bill_by_number(
            bill_number="HR4922",
            congress_session=119,
            environment="development",
            use_comprehensive=False,
            use_enhanced_analysis=True
        )
        
        print(f"Processing result: {result.get('success', False)}")
        if result.get('success'):
            print(f"✅ Bill ID: {result.get('bill_id')}")
            print("✅ SUCCESS: Secondary Analysis integration working!")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown')}")
            
        db.close()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_final_secondary())