'use client';

import { useEffect } from 'react';
import { Official } from '@/types';
import { trackOfficialView } from '@/services/analytics';

interface TrackOfficialViewProps {
  official: Official;
  userLocation?: string;
}

export const TrackOfficialView: React.FC<TrackOfficialViewProps> = ({
  official,
  userLocation
}) => {
  useEffect(() => {
    // Track the official profile view
    trackOfficialView(
      { id: official.id, name: official.name },
      userLocation
    );
  }, [official.id, official.name, userLocation]);

  // This component doesn't render anything
  return null;
};

export default TrackOfficialView;