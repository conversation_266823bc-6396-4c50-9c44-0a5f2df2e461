# app/services/text_citation_service.py
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
import re


@dataclass
class SourceIndexItem:
    heading: Optional[str]
    start_offset: int
    end_offset: int
    anchor_id: Optional[str]


@dataclass
class CitationBinding:
    quote: str
    start_offset: int
    end_offset: int
    heading: Optional[str]
    anchor_id: Optional[str]


class TextCitationService:
    """Segments bill text and validates citations (exact quotes)."""

    def __init__(self, heading_pattern: str = r"^SEC\.\s*[^\n]+|^Section\s+\d+|^TITLE\s+[IVXLC]+|^Subtitle\s+[A-Z]", flags: int = re.MULTILINE):
        self.heading_regex = re.compile(heading_pattern, flags)

    def build_source_index(self, full_text: str) -> List[SourceIndexItem]:
        """Build an index of sections by heading with character offsets."""
        items: List[SourceIndexItem] = []
        if not full_text:
            return items

        matches = list(self.heading_regex.finditer(full_text))
        if not matches:
            # Single section spanning all
            return [SourceIndexItem(heading=None, start_offset=0, end_offset=len(full_text), anchor_id="sec-0")]

        for i, m in enumerate(matches):
            start = m.start()
            end = matches[i + 1].start() if i + 1 < len(matches) else len(full_text)
            heading = m.group(0).strip()
            items.append(SourceIndexItem(
                heading=heading,
                start_offset=start,
                end_offset=end,
                anchor_id=f"sec-{i+1}"
            ))
        return items

    def bind_quote(self, full_text: str, source_index: List[SourceIndexItem], quote: str) -> Optional[CitationBinding]:
        """Find exact quote in text and return binding with offsets and section metadata."""
        if not quote or not full_text:
            return None

        # Normalize whitespace for robust matching
        q = re.sub(r"\s+", " ", quote.strip())

        # Try direct find first
        pos = full_text.find(q)
        if pos == -1:
            # Try a more flexible search: collapse whitespace in text
            collapsed = re.sub(r"\s+", " ", full_text)
            pos = collapsed.find(q)
            if pos == -1:
                return None
            # Map back to original offsets approximately by searching start fragment
            start_fragment = q[: min(20, len(q))]
            pos = full_text.find(start_fragment)
            if pos == -1:
                return None
            end = pos + len(q)
        else:
            end = pos + len(q)

        # Determine section
        section = None
        for item in source_index:
            if item.start_offset <= pos < item.end_offset:
                section = item
                break

        return CitationBinding(
            quote=quote,
            start_offset=pos,
            end_offset=end,
            heading=section.heading if section else None,
            anchor_id=section.anchor_id if section else None,
        )

    def bind_citations_bulk(self, full_text: str, source_index: List[SourceIndexItem], quotes: List[str]) -> List[CitationBinding]:
        bindings: List[CitationBinding] = []
        for q in quotes:
            b = self.bind_quote(full_text, source_index, q)
            if b:
                bindings.append(b)
        return bindings

