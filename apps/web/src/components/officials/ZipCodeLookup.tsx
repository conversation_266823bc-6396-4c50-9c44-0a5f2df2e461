'use client';

import React, { useState } from 'react';
import { OfficialsList } from './OfficialsList';

interface ZipCodeLookupProps {
  className?: string;
  onOfficialsFound?: (count: number) => void;
}

export const ZipCodeLookup: React.FC<ZipCodeLookupProps> = ({
  className = '',
  onOfficialsFound
}) => {
  const [zipCode, setZipCode] = useState('');
  const [submittedZipCode, setSubmittedZipCode] = useState('');
  const [error, setError] = useState('');

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}$/;
    return zipRegex.test(zip);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!zipCode.trim()) {
      setError('Please enter a ZIP code');
      return;
    }

    if (!validateZipCode(zipCode.trim())) {
      setError('Please enter a valid 5-digit ZIP code');
      return;
    }

    setError('');
    setSubmittedZipCode(zipCode.trim());
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 5); // Only digits, max 5
    setZipCode(value);
    if (error) setError(''); // Clear error when user starts typing
  };

  const handleClear = () => {
    setZipCode('');
    setSubmittedZipCode('');
    setError('');
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* ZIP Code Input Form */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="max-w-md mx-auto">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 text-center">
            Find Your Representatives
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 mb-2">
                Enter your ZIP code
              </label>
              <div className="relative">
                <input
                  type="text"
                  id="zipCode"
                  value={zipCode}
                  onChange={handleInputChange}
                  placeholder="e.g., 90210"
                  className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                    error ? 'border-red-300' : 'border-gray-300'
                  }`}
                  maxLength={5}
                />
                {zipCode && (
                  <button
                    type="button"
                    onClick={handleClear}
                    className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                  >
                    <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>
              
              {error && (
                <p className="mt-2 text-sm text-red-600 flex items-center">
                  <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {error}
                </p>
              )}
            </div>

            <button
              type="submit"
              disabled={!zipCode || !validateZipCode(zipCode)}
              className={`w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200 ${
                !zipCode || !validateZipCode(zipCode)
                  ? 'bg-gray-300 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
              }`}
            >
              Find My Representatives
            </button>
          </form>

          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              We'll show you your federal senators and house representative, plus relevant state officials.
            </p>
          </div>
        </div>
      </div>

      {/* Popular ZIP Codes for Quick Testing */}
      {!submittedZipCode && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Try these examples:</h3>
          <div className="flex flex-wrap gap-2">
            {['90210', '10001', '60302', '20001', '75201'].map((exampleZip) => (
              <button
                key={exampleZip}
                onClick={() => {
                  setZipCode(exampleZip);
                  setSubmittedZipCode(exampleZip);
                  setError('');
                }}
                className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {exampleZip}
              </button>
            ))}
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Beverly Hills, NYC, Oak Park, Washington DC, Dallas
          </p>
        </div>
      )}

      {/* Results Section */}
      {submittedZipCode && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">
              Your Representatives
            </h2>
            <button
              onClick={handleClear}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Search Again
            </button>
          </div>

          <OfficialsList zipCode={submittedZipCode} />
        </div>
      )}
    </div>
  );
};

export default ZipCodeLookup;