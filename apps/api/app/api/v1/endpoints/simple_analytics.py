# app/api/v1/endpoints/simple_analytics.py


"""
Simple analytics endpoints - KISS approach.

These endpoints provide immediate value with minimal complexity:
- Basic support/oppose stats
- Top reasons for stances
- Recent custom reasons
- Simple action tracking

No complex aggregations, just direct queries that provide insights.
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.services.simple_action_tracking import get_simple_tracking
from app.models.action_tracking import ActionStance
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/bills/{bill_id}/stats")
async def get_bill_stats(
    bill_id: str,
    db: Session = Depends(get_db)
):
    """
    Get simple support/oppose/amend stats for a bill

    Returns basic counts - no complex analytics needed
    """
    try:
        tracker = get_simple_tracking(db)
        stats = tracker.get_bill_stats(bill_id)

        return {
            "success": True,
            "bill_id": bill_id,
            "stats": stats
        }

    except Exception as e:
        logger.error(f"Failed to get bill stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get bill stats")


@router.get("/bills/{bill_id}/reasoning-options")
async def get_reasoning_options(
    bill_id: str,
    stance: str = Query(..., description="support, oppose, or amend"),
    db: Session = Depends(get_db)
):
    """
    Get predefined reasoning options for a bill and stance

    Simple list of reasons users can select from
    """
    try:
        if stance not in ['support', 'oppose', 'amend']:
            raise HTTPException(status_code=400, detail="Stance must be support, oppose, or amend")

        tracker = get_simple_tracking(db)
        options = tracker.get_reasoning_options(bill_id, stance)

        return {
            "success": True,
            "bill_id": bill_id,
            "stance": stance,
            "reasoning_options": options
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get reasoning options: {e}")
        raise HTTPException(status_code=500, detail="Failed to get reasoning options")


@router.get("/bills/{bill_id}/top-reasons")
async def get_top_reasons(
    bill_id: str,
    stance: str = Query(..., description="support, oppose, or amend"),
    limit: int = Query(5, ge=1, le=20, description="Number of top reasons to return"),
    db: Session = Depends(get_db)
):
    """
    Get top predefined reasons for a bill/stance

    Shows which reasons are most popular among users
    """
    try:
        if stance not in ['support', 'oppose', 'amend']:
            raise HTTPException(status_code=400, detail="Stance must be support, oppose, or amend")

        tracker = get_simple_tracking(db)
        reasons = tracker.get_top_reasons(bill_id, stance, limit)

        return {
            "success": True,
            "bill_id": bill_id,
            "stance": stance,
            "top_reasons": reasons
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get top reasons: {e}")
        raise HTTPException(status_code=500, detail="Failed to get top reasons")


@router.get("/bills/{bill_id}/custom-reasons")
async def get_custom_reasons(
    bill_id: str,
    stance: str = Query(..., description="support, oppose, or amend"),
    limit: int = Query(10, ge=1, le=50, description="Number of custom reasons to return"),
    db: Session = Depends(get_db)
):
    """
    Get recent custom reasons submitted by users

    Shows what users are saying in their own words
    """
    try:
        if stance not in ['support', 'oppose', 'amend']:
            raise HTTPException(status_code=400, detail="Stance must be support, oppose, or amend")

        tracker = get_simple_tracking(db)
        reasons = tracker.get_custom_reasons(bill_id, stance, limit)

        return {
            "success": True,
            "bill_id": bill_id,
            "stance": stance,
            "custom_reasons": reasons,
            "count": len(reasons)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get custom reasons: {e}")
        raise HTTPException(status_code=500, detail="Failed to get custom reasons")


@router.post("/bills/{bill_id}/reasoning-options")
async def add_reasoning_options(
    bill_id: str,
    stance: str,
    reasons: List[str],
    db: Session = Depends(get_db)
):
    """
    Add predefined reasoning options for a bill (Admin utility)

    Simple way to populate reasoning options for bills
    """
    try:
        if stance not in ['support', 'oppose', 'amend']:
            raise HTTPException(status_code=400, detail="Stance must be support, oppose, or amend")

        if not reasons or len(reasons) == 0:
            raise HTTPException(status_code=400, detail="At least one reason is required")

        tracker = get_simple_tracking(db)
        success = tracker.add_reasoning_options(bill_id, stance, reasons)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to add reasoning options")

        return {
            "success": True,
            "bill_id": bill_id,
            "stance": stance,
            "added_count": len(reasons),
            "message": f"Added {len(reasons)} reasoning options"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to add reasoning options: {e}")
        raise HTTPException(status_code=500, detail="Failed to add reasoning options")


@router.get("/actions/{action_id}/summary")
async def get_action_summary(
    action_id: str,
    db: Session = Depends(get_db)
):
    """
    Get simple summary of an action with its reasoning

    Shows what stance the user took and why
    """
    try:
        tracker = get_simple_tracking(db)
        summary = tracker.get_action_summary(action_id)

        if not summary:
            raise HTTPException(status_code=404, detail="Action not found")

        return {
            "success": True,
            "action_summary": summary
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get action summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get action summary")


@router.post("/actions/{action_id}/track-reasoning")
async def track_action_reasoning(
    action_id: str,
    reasoning_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """
    Track user's reasoning for an action (simple version)

    Expected format:
    {
        "selected_reasons": ["reason_id_1", "reason_id_2"],
        "custom_reason": "My custom reason"
    }
    """
    try:
        tracker = get_simple_tracking(db)

        selected_reasons = reasoning_data.get('selected_reasons', [])
        custom_reason = reasoning_data.get('custom_reason')

        success = tracker.track_user_reasoning(
            action_id=action_id,
            selected_reasons=selected_reasons,
            custom_reason=custom_reason
        )

        if not success:
            raise HTTPException(status_code=500, detail="Failed to track reasoning")

        return {
            "success": True,
            "action_id": action_id,
            "tracked_selected_reasons": len(selected_reasons),
            "tracked_custom_reason": bool(custom_reason),
            "message": "Reasoning tracked successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to track reasoning: {e}")
        raise HTTPException(status_code=500, detail="Failed to track reasoning")


@router.get("/debug/user-lookup/{auth0_user_id}")
async def debug_user_lookup(
    auth0_user_id: str,
    db: Session = Depends(get_db)
):
    """
    Debug endpoint to help troubleshoot user lookup issues
    """
    try:
        from app.models.user import User
        from app.models.action import Action

        # Look for user by Auth0 ID
        user = db.query(User).filter(User.auth0_user_id == auth0_user_id).first()

        # Also show all users to help with debugging
        all_users = db.query(User).all()

        # If we found a user, also get their action count
        action_count = 0
        if user:
            action_count = db.query(Action).filter(Action.user_id == user.id).count()

        return {
            "searched_auth0_id": auth0_user_id,
            "found_user": {
                "id": user.id if user else None,
                "email": user.email if user else None,
                "auth0_user_id": user.auth0_user_id if user else None,
                "name": user.name if user else None,
                "action_count": action_count
            } if user else None,
            "all_users_in_db": [
                {
                    "id": u.id,
                    "email": u.email,
                    "auth0_user_id": u.auth0_user_id,
                    "name": u.name,
                    "action_count": db.query(Action).filter(Action.user_id == u.id).count()
                }
                for u in all_users
            ]
        }

    except Exception as e:
        logger.error(f"Debug user lookup failed: {e}")
        return {
            "error": str(e),
            "searched_auth0_id": auth0_user_id
        }

@router.get("/debug/user-by-email/{email}")
async def debug_user_by_email(
    email: str,
    db: Session = Depends(get_db)
):
    """
    Debug endpoint to find user by email address
    """
    try:
        from app.models.user import User
        from app.models.action import Action

        # Look for user by email
        user = db.query(User).filter(User.email == email).first()

        # If we found a user, also get their action count
        action_count = 0
        if user:
            action_count = db.query(Action).filter(Action.user_id == user.id).count()

        return {
            "searched_email": email,
            "found_user": {
                "id": user.id if user else None,
                "email": user.email if user else None,
                "auth0_user_id": user.auth0_user_id if user else None,
                "name": user.name if user else None,
                "action_count": action_count
            } if user else None,
        }

    except Exception as e:
        logger.error(f"Debug user lookup by email failed: {e}")
        return {
            "error": str(e),
            "searched_email": email
        }

@router.get("/health/simple")
async def simple_health_check(db: Session = Depends(get_db)):
    """
    Simple health check - just verify core tables exist
    """
    try:
        from sqlalchemy import text

        # Check core tables
        db.execute(text("SELECT COUNT(*) FROM actions"))
        db.execute(text("SELECT COUNT(*) FROM reasoning_options"))
        db.execute(text("SELECT COUNT(*) FROM action_reasoning"))
        db.execute(text("SELECT COUNT(*) FROM custom_reasons_pool"))

        return {
            "status": "healthy",
            "message": "Simple action tracking is ready"
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }


@router.get("/admin/overview")
async def get_admin_overview(db: Session = Depends(get_db)):
    """
    Get comprehensive overview for admin dashboard

    Returns overall statistics and recent activity
    """
    try:
        from sqlalchemy import text, desc
        from app.models.action import Action
        from app.models.bill import Bill

        # Get overall action counts
        total_actions = db.execute(text("SELECT COUNT(*) FROM actions")).scalar()

        # Get actions by stance
        stance_counts = db.execute(text("""
            SELECT position, COUNT(*) as count
            FROM actions
            WHERE position IS NOT NULL
            GROUP BY position
        """)).fetchall()

        by_stance = {row[0]: row[1] for row in stance_counts}

        # Get actions by status
        status_counts = db.execute(text("""
            SELECT status, COUNT(*) as count
            FROM actions
            GROUP BY status
        """)).fetchall()

        by_status = {row[0]: row[1] for row in status_counts}

        # Get recent actions with bill titles
        recent_actions_query = db.query(Action, Bill.title).join(
            Bill, Action.bill_id == Bill.id
        ).order_by(desc(Action.created_at)).limit(10)

        recent_actions = []
        for action, bill_title in recent_actions_query:
            recent_actions.append({
                "id": action.id,
                "bill_title": bill_title or "Unknown Bill",
                "user_email": action.user_email,
                "stance": action.position or "unknown",
                "created_at": action.created_at.isoformat() if action.created_at else None,
                "selected_reasons": action.selected_reasons or [],
                "custom_reason": None  # Would need to join with custom_reasons_pool to get this
            })

        # Get reasoning options count
        reasoning_options_count = db.execute(text("SELECT COUNT(*) FROM reasoning_options")).scalar()

        # Get custom reasons count
        custom_reasons_count = db.execute(text("SELECT COUNT(*) FROM custom_reasons_pool")).scalar()

        return {
            "total_actions": total_actions,
            "by_stance": by_stance,
            "by_status": by_status,
            "recent_actions": recent_actions,
            "reasoning_options_count": reasoning_options_count,
            "custom_reasons_count": custom_reasons_count,
            "summary": {
                "total_engagement": total_actions,
                "reasoning_tracking": reasoning_options_count + custom_reasons_count,
                "active_bills": len(set(action.bill_id for action, _ in recent_actions_query))
            }
        }

    except Exception as e:
        logger.error(f"Failed to get admin overview: {e}")
        raise HTTPException(status_code=500, detail="Failed to get admin overview")


@router.get("/user/{auth0_user_id}/actions")
async def get_user_actions(
    auth0_user_id: str,
    limit: int = 20,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """
    Get user's action history with bill details

    Returns user-specific actions with:
    - Bills they've taken action on
    - Messages they sent
    - Current bill status
    - Their reasoning/stance
    """
    try:
        from app.models.action import Action
        from app.models.bill import Bill
        from app.models.user import User
        from app.models.action_tracking import ReasoningOption, ActionReasoning, CustomReasonsPool
        from sqlalchemy import desc, and_

        # First, find the user by their Auth0 user ID
        user = db.query(User).filter(User.auth0_user_id == auth0_user_id).first()
        if not user:
            # Also try to find by email if the Auth0 ID doesn't match
            # This handles cases where users were created with different Auth0 configs
            from app.models.action import Action
            actions_by_email = db.query(Action).filter(Action.user_email.ilike(f"%{auth0_user_id}%")).all()

            if actions_by_email:
                # If we find actions by email, try to find the user by that email
                user_email = actions_by_email[0].user_email
                user = db.query(User).filter(User.email == user_email).first()
                logger.info(f"Found user by email lookup: {user_email}")

            if not user:
                logger.warning(f"No user found with Auth0 ID: {auth0_user_id}")
                return {
                    "actions": [],
                    "total_count": 0,
                    "limit": limit,
                    "offset": offset,
                    "debug_info": {
                        "searched_auth0_id": auth0_user_id,
                        "suggestion": f"Try: http://localhost:8000/api/v1/simple/debug/user-by-email/YOUR_EMAIL"
                    }
                }

        logger.info(f"Found user {user.id} for Auth0 ID {auth0_user_id}")

        # Get user's actions with bill info
        query = db.query(Action, Bill.title, Bill.status, Bill.summary).join(
            Bill, Action.bill_id == Bill.id
        ).filter(Action.user_id == user.id).order_by(desc(Action.created_at))

        # Apply pagination
        actions_data = query.offset(offset).limit(limit).all()

        user_actions = []

        for action, bill_title, bill_status, bill_summary in actions_data:
            # Get selected reasons with full text
            selected_reasons_query = db.query(
                ReasoningOption.id, ReasoningOption.reason_text
            ).join(
                ActionReasoning, ReasoningOption.id == ActionReasoning.reasoning_option_id
            ).filter(ActionReasoning.action_id == action.id)

            selected_reasons = [
                {"id": reason_id, "reason_text": reason_text}
                for reason_id, reason_text in selected_reasons_query.all()
            ]

            # Get custom reason if exists
            custom_reason_query = db.query(CustomReasonsPool.custom_reason).filter(
                CustomReasonsPool.action_id == action.id
            ).first()

            custom_reason = custom_reason_query[0] if custom_reason_query else None

            # Parse representative info
            representatives = []
            if action.representative_info:
                try:
                    import json
                    rep_data = action.representative_info
                    if isinstance(rep_data, str):
                        rep_data = json.loads(rep_data)

                    if isinstance(rep_data, list):
                        representatives = [
                            {
                                "name": rep.get("name", "Unknown"),
                                "title": rep.get("title", "Unknown"),
                                "party": rep.get("party", "Unknown")
                            }
                            for rep in rep_data
                        ]
                except Exception as e:
                    logger.warning(f"Failed to parse representative info for action {action.id}: {e}")

            user_actions.append({
                "id": action.id,
                "bill_id": action.bill_id,
                "bill_title": bill_title,
                "bill_status": bill_status,
                "bill_summary": bill_summary,
                "stance": action.position or "unknown",
                "action_status": action.status.value if action.status else "unknown",
                "subject": action.subject,
                "message": action.message,
                "created_at": action.created_at.isoformat() if action.created_at else None,
                "sent_at": action.sent_at.isoformat() if action.sent_at else None,
                "delivered_at": action.delivered_at.isoformat() if action.delivered_at else None,
                "selected_reasons": selected_reasons,
                "custom_reason": custom_reason,
                "representatives_contacted": representatives,
                "delivery_method": action.delivery_method,
                "error_message": action.error_message
            })

        return {
            "actions": user_actions,
            "total_count": query.count(),
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        logger.error(f"Failed to get user actions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get user actions")


@router.get("/admin/actions/detailed")
async def get_detailed_actions(
    limit: int = 50,
    offset: int = 0,
    bill_id: str | None = None,
    db: Session = Depends(get_db)
):
    """
    Get detailed action data for admin review

    Returns comprehensive information about each action including:
    - Full message content
    - User details
    - Selected reasons with full text
    - Custom reasons
    - Representative information
    - Location data
    - Delivery status
    """
    try:
        from app.models.action import Action
        from app.models.bill import Bill
        from app.models.user import User
        from app.models.action_tracking import ReasoningOption, ActionReasoning, CustomReasonsPool
        from sqlalchemy import desc, and_

        # Base query with joins
        query = db.query(Action, Bill.title, User.name).join(
            Bill, Action.bill_id == Bill.id
        ).join(
            User, Action.user_id == User.id
        ).order_by(desc(Action.created_at))

        # Filter by bill if specified
        if bill_id:
            query = query.filter(Action.bill_id == bill_id)

        # Apply pagination
        actions_data = query.offset(offset).limit(limit).all()

        detailed_actions = []

        for action, bill_title, user_name in actions_data:
            # Get selected reasons with full text
            selected_reasons_query = db.query(
                ReasoningOption.id, ReasoningOption.reason_text
            ).join(
                ActionReasoning, ReasoningOption.id == ActionReasoning.reasoning_option_id
            ).filter(ActionReasoning.action_id == action.id)

            selected_reasons = [
                {"id": reason_id, "reason_text": reason_text}
                for reason_id, reason_text in selected_reasons_query.all()
            ]

            # Get custom reason if exists
            custom_reason_query = db.query(CustomReasonsPool.custom_reason).filter(
                CustomReasonsPool.action_id == action.id
            ).first()

            custom_reason = custom_reason_query[0] if custom_reason_query else None

            # Parse representative info from JSON
            representatives = []
            if action.representative_info:
                try:
                    import json
                    rep_data = action.representative_info
                    if isinstance(rep_data, str):
                        rep_data = json.loads(rep_data)

                    if isinstance(rep_data, list):
                        representatives = [
                            {
                                "name": rep.get("name", "Unknown"),
                                "title": rep.get("title", "Unknown"),
                                "party": rep.get("party", "Unknown")
                            }
                            for rep in rep_data
                        ]
                except Exception as e:
                    logger.warning(f"Failed to parse representative info for action {action.id}: {e}")

            # Parse user location
            user_location = {}
            if action.user_location:
                try:
                    import json
                    location_data = action.user_location
                    if isinstance(location_data, str):
                        location_data = json.loads(location_data)
                    user_location = location_data
                except Exception as e:
                    logger.warning(f"Failed to parse user location for action {action.id}: {e}")

            # Parse action metadata
            action_metadata = {}
            if action.action_metadata:
                try:
                    import json
                    if isinstance(action.action_metadata, str):
                        action_metadata = json.loads(action.action_metadata)
                    else:
                        action_metadata = action.action_metadata
                except Exception as e:
                    logger.warning(f"Failed to parse action metadata for action {action.id}: {e}")

            detailed_actions.append({
                "id": action.id,
                "bill_id": action.bill_id,
                "bill_title": bill_title,
                "user_id": action.user_id,
                "user_email": action.user_email,
                "user_name": user_name,
                "stance": action.position or "unknown",
                "status": action.status.value if action.status else "unknown",
                "subject": action.subject,
                "message": action.message,
                "created_at": action.created_at.isoformat() if action.created_at else None,
                "sent_at": action.sent_at.isoformat() if action.sent_at else None,
                "delivered_at": action.delivered_at.isoformat() if action.delivered_at else None,
                "selected_reasons": selected_reasons,
                "custom_reason": custom_reason,
                "user_location": user_location,
                "representatives_contacted": representatives,
                "action_metadata": action_metadata,
                "delivery_method": action.delivery_method,
                "delivery_id": action.delivery_id,
                "error_message": action.error_message,
                "retry_count": action.retry_count
            })

        return {
            "actions": detailed_actions,
            "total_count": query.count(),
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        logger.error(f"Failed to get detailed actions: {e}")
        raise HTTPException(status_code=500, detail="Failed to get detailed actions")


@router.get("/bills/{bill_id}/status-history")
async def get_bill_status_history(
    bill_id: str,
    db: Session = Depends(get_db)
):
    """
    Get bill status history for tracking legislative progress

    Returns chronological status changes for a bill
    """
    try:
        from app.models.bill import BillStatusPipeline, Bill
        from sqlalchemy import desc

        # Verify bill exists
        bill = db.query(Bill).filter(Bill.id == bill_id).first()
        if not bill:
            raise HTTPException(status_code=404, detail="Bill not found")

        # Get status history
        status_history = db.query(BillStatusPipeline).filter(
            BillStatusPipeline.bill_id == bill_id
        ).order_by(desc(BillStatusPipeline.status_changed_at)).all()

        history_data = []
        for status_record in status_history:
            history_data.append({
                "id": status_record.id,
                "previous_status": status_record.previous_status.value if status_record.previous_status else None,
                "current_status": status_record.current_status.value,
                "status_changed_at": status_record.status_changed_at.isoformat() if status_record.status_changed_at else None,
                "detected_at": status_record.detected_at.isoformat() if status_record.detected_at else None,
                "is_significant_change": status_record.is_significant_change,
                "notes": status_record.notes
            })

        return {
            "bill_id": bill_id,
            "bill_title": bill.title,
            "current_status": bill.status.value if bill.status else "unknown",
            "status_history": history_data,
            "total_changes": len(history_data)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get bill status history: {e}")
        raise HTTPException(status_code=500, detail="Failed to get bill status history")