#!/usr/bin/env python3
"""
Run golden quality tests without pytest
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

async def run_golden_tests():
    """Run all golden quality tests"""
    try:
        from app.services.span_grounded_validator import SpanGroundedValidator
        from app.services.enhanced_span_retriever import EnhancedSpanRetriever
        from app.services.bill_details_service import BillDetailsService
        from app.db.database import get_db
        
        print("🧪 Running Golden Quality Tests")
        print("=" * 60)
        
        validator = SpanGroundedValidator()
        retriever = EnhancedSpanRetriever()
        
        tests_passed = 0
        tests_total = 0
        
        # Test 1: HR5 Golden Example (Must Pass)
        print("\n1️⃣ Testing HR5 Golden Example...")
        tests_total += 1
        
        hr5_analysis = {
            "tldr": {
                "content": "The Parents Bill of Rights Act requires local educational agencies to post curriculum information online by September 30, 2024.",
                "citations": [
                    {
                        "quote": "not later than September 30, 2024",
                        "heading": "SEC. 1111. STATE PLANS.",
                        "anchor_id": "sec-3",
                        "start_offset": 2100,
                        "end_offset": 2135
                    }
                ]
            },
            "who_affects": [
                {
                    "content": "Local educational agencies must comply with new curriculum disclosure requirements.",
                    "citations": [
                        {
                            "quote": "each local educational agency",
                            "heading": "SEC. 1111. STATE PLANS.",
                            "anchor_id": "sec-3",
                            "start_offset": 1950,
                            "end_offset": 1980
                        }
                    ]
                }
            ],
            "why_matters": {
                "content": "Parents will have access to detailed curriculum information for the first time.",
                "citations": [
                    {
                        "quote": "curriculum or description of such curriculum",
                        "heading": "SEC. 1111. STATE PLANS.",
                        "anchor_id": "sec-3",
                        "start_offset": 2000,
                        "end_offset": 2040
                    }
                ]
            },
            "key_provisions": [
                {
                    "content": "Agencies must post curriculum information on their website by September 30, 2024.",
                    "citations": [
                        {
                            "quote": "post on the website of the local educational agency",
                            "heading": "SEC. 1111. STATE PLANS.",
                            "anchor_id": "sec-3",
                            "start_offset": 1800,
                            "end_offset": 1850
                        }
                    ]
                }
            ]
        }
        
        is_valid, errors, metrics = validator.validate_analysis(hr5_analysis)
        
        if is_valid and metrics['evidence_coverage'] == 1.0 and metrics['invalid_citation_count'] == 0:
            print("✅ HR5 golden example PASSED")
            tests_passed += 1
        else:
            print(f"❌ HR5 golden example FAILED: {errors}")
        
        # Test 2: HR7 Bad Example (Must Fail)
        print("\n2️⃣ Testing HR7 Bad Example...")
        tests_total += 1
        
        hr7_bad_analysis = {
            "tldr": {
                "content": "The bill prohibits federal funding for abortions and requires disclosure.",
                "citations": [
                    {
                        "quote": "Funding for Abortion and Abortion Insurance Full Disclosure",
                        "heading": None,  # ❌ NULL HEADING
                        "anchor_id": None,  # ❌ NULL ANCHOR_ID
                        "start_offset": 2639,
                        "end_offset": 2698
                    }
                ]
            },
            "who_affects": [
                {
                    "content": "All provisions are effective immediately upon enactment.",  # ❌ GENERIC
                    "citations": []  # ❌ NO CITATIONS
                }
            ],
            "why_matters": {
                "content": "This is important legislation.",  # ❌ VAGUE
                "citations": []  # ❌ NO CITATIONS
            },
            "key_provisions": [
                {
                    "content": "Technical provisions and administrative details",  # ❌ GENERIC PHRASE
                    "citations": []  # ❌ NO CITATIONS
                }
            ]
        }
        
        is_valid, errors, metrics = validator.validate_analysis(hr7_bad_analysis)
        
        if not is_valid and metrics['invalid_citation_count'] > 0:
            print("✅ HR7 bad example CORRECTLY REJECTED")
            tests_passed += 1
        else:
            print(f"❌ HR7 bad example INCORRECTLY ACCEPTED")
        
        # Test 3: Budget Classification
        print("\n3️⃣ Testing Budget Classification...")
        tests_total += 1
        
        budget_test_text = """
        SEC. 2. AUTHORIZATION OF APPROPRIATIONS.
        There are authorized to be appropriated $50,000,000 for fiscal year 2024.
        
        SEC. 3. APPROPRIATIONS.
        There are appropriated out of any money in the Treasury $25,000,000.
        
        SEC. 4. PENALTIES.
        Any violation shall be subject to a civil penalty of not more than $10,000.
        """
        
        metadata = {'bill_id': 'test', 'title': 'Budget Test Bill'}
        result = retriever.extract_enhanced_spans(budget_test_text, metadata)
        
        budget_spans = [s for s in result.get('evidence', []) if s.get('type') == 'budget']
        categories_found = set(s.get('budget_category') for s in budget_spans if s.get('budget_category'))
        
        if 'authorization' in categories_found and 'appropriation' in categories_found and 'penalty' in categories_found:
            print("✅ Budget classification PASSED")
            tests_passed += 1
        else:
            print(f"❌ Budget classification FAILED: {categories_found}")
        
        # Test 4: Mandate Detection
        print("\n4️⃣ Testing Mandate Detection...")
        tests_total += 1
        
        mandate_test_text = """
        SEC. 1. REQUIREMENTS.
        Each agency shall comply with this Act not later than 180 days.
        No funds may be used for administrative costs exceeding 10 percent.
        The Secretary must enforce these provisions through regular monitoring.
        """
        
        result = retriever.extract_enhanced_spans(mandate_test_text, metadata)
        mandate_spans = [s for s in result.get('evidence', []) if s.get('type') == 'mandate']
        
        all_quotes = ' '.join(s.get('quote', '') for s in mandate_spans)
        
        if len(mandate_spans) >= 2 and 'shall' in all_quotes.lower() and 'must' in all_quotes.lower():
            print("✅ Mandate detection PASSED")
            tests_passed += 1
        else:
            print(f"❌ Mandate detection FAILED: {len(mandate_spans)} spans, quotes: {all_quotes[:100]}")
        
        # Test 5: Quality Gates
        print("\n5️⃣ Testing Quality Gates...")
        tests_total += 1
        
        try:
            db = next(get_db())
            details_service = BillDetailsService(db)
            
            bad_payload = {
                "hero_summary": "Generic bill summary with no specifics.",
                "hero_summary_citations": [],  # ❌ NO CITATIONS
                "overview": {
                    "what_does": {
                        "content": "Various provisions and technical details",  # ❌ GENERIC
                        "citations": []  # ❌ NO CITATIONS
                    }
                }
            }
            
            result = details_service._apply_quality_gates(bad_payload)
            
            if not result['passed'] and result['quality_score'] < 0.8:
                print("✅ Quality gates PASSED")
                tests_passed += 1
            else:
                print(f"❌ Quality gates FAILED: passed={result['passed']}, score={result['quality_score']}")
            
            db.close()
            
        except Exception as e:
            print(f"❌ Quality gates test ERROR: {e}")
        
        # Test 6: Routing Logic
        print("\n6️⃣ Testing Routing Logic...")
        tests_total += 1
        
        high_priority_text = """
        SEC. 1. There are authorized to be appropriated $1,000,000.
        SEC. 2. Each agency shall comply with these requirements.
        """
        
        result = retriever.extract_enhanced_spans(high_priority_text, metadata)
        
        if (result.get('routing') == 'high' and 
            result.get('has_money') and 
            result.get('has_mandates')):
            print("✅ Routing logic PASSED")
            tests_passed += 1
        else:
            print(f"❌ Routing logic FAILED: routing={result.get('routing')}, money={result.get('has_money')}, mandates={result.get('has_mandates')}")
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 Golden Test Results:")
        print(f"✅ Tests passed: {tests_passed}/{tests_total}")
        print(f"📊 Success rate: {tests_passed/tests_total*100:.1f}%")
        
        if tests_passed == tests_total:
            print("\n🎉 ALL GOLDEN TESTS PASSED!")
            print("🚫 Bad content will be blocked")
            print("✅ Quality standards enforced")
            print("🔒 System ready for production")
            return True
        else:
            print(f"\n❌ {tests_total - tests_passed} GOLDEN TESTS FAILED!")
            print("🔧 Quality system needs fixes")
            print("⚠️ Do not deploy until all tests pass")
            return False
        
    except Exception as e:
        print(f"❌ Golden tests failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run golden tests"""
    success = await run_golden_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())
