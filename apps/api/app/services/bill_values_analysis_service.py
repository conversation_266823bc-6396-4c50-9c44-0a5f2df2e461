# app/services/bill_values_analysis_service.py
"""
Service for analyzing bills against core values: democracy, human rights, and environmental justice.

This service provides AI-powered analysis of bills to identify potential threats or support
for our core values, using neutral language and providing human oversight capabilities.
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session

from app.models.bill import Bill
from app.models.bill_values import BillValuesAnalysis, BillValuesTag
from app.services.ai_values_analyzer import AIValuesAnalyzer
from app.core.config import Settings

logger = logging.getLogger(__name__)


class BillValuesAnalysisService:
    """Service for analyzing bills against core values and generating neutral tags."""
    
    def __init__(self, db: Session, settings: Settings = None):
        self.db = db
        self.settings = settings or Settings()
        self.ai_analyzer = AIValuesAnalyzer(self.settings)
    
    async def analyze_bill_values(self, bill: Bill) -> BillValuesAnalysis:
        """
        Analyze a bill against our core values and create analysis record.
        
        Args:
            bill: The bill to analyze
            
        Returns:
            BillValuesAnalysis record with scores and reasoning
        """
        logger.info(f"Starting values analysis for bill {bill.id}: {bill.title}")
        
        try:
            # For now, use mock analysis while we build the infrastructure
            # TODO: Replace with actual AI analysis once scoring criteria is refined
            analysis_result = await self._mock_analyze_bill_content(bill)
            
            # Create or update analysis record
            analysis = self._create_analysis_record(bill, analysis_result)
            
            # Generate neutral tags for frontend display
            await self._generate_values_tags(analysis, analysis_result)
            
            logger.info(f"Completed values analysis for bill {bill.id}")
            return analysis
            
        except Exception as e:
            logger.error(f"Failed to analyze bill {bill.id}: {e}")
            # Create minimal analysis record to prevent blocking
            return self._create_fallback_analysis(bill)
    
    async def _mock_analyze_bill_content(self, bill: Bill) -> Dict:
        """
        Mock analysis function that returns realistic test data.
        This will be replaced with actual AI analysis once criteria is refined.
        """
        # Simulate analysis based on bill title keywords for realistic mock data
        title_lower = bill.title.lower() if bill.title else ""
        
        # Mock scoring based on keywords (this is temporary for development)
        democracy_threat = 0
        democracy_support = 0
        human_rights_threat = 0
        human_rights_support = 0
        environmental_threat = 0
        environmental_support = 0
        
        # Democracy keywords
        if any(word in title_lower for word in ['voting', 'election', 'ballot', 'democracy']):
            democracy_support = 6
        elif any(word in title_lower for word in ['restrict', 'limit', 'reduce']):
            democracy_threat = 4
        
        # Human rights keywords
        if any(word in title_lower for word in ['healthcare', 'education', 'equality', 'rights']):
            human_rights_support = 5
        elif any(word in title_lower for word in ['discriminat', 'restrict', 'ban']):
            human_rights_threat = 3
        
        # Environmental keywords
        if any(word in title_lower for word in ['environment', 'climate', 'clean', 'renewable']):
            environmental_support = 7
        elif any(word in title_lower for word in ['deregulat', 'pollut', 'drill']):
            environmental_threat = 5
        
        # Determine overall levels
        max_threat = max(democracy_threat, human_rights_threat, environmental_threat)
        max_support = max(democracy_support, human_rights_support, environmental_support)
        
        threat_level = self._score_to_level(max_threat)
        support_level = self._score_to_level(max_support)
        
        return {
            'democracy_threat_score': democracy_threat,
            'democracy_support_score': democracy_support,
            'human_rights_threat_score': human_rights_threat,
            'human_rights_support_score': human_rights_support,
            'environmental_threat_score': environmental_threat,
            'environmental_support_score': environmental_support,
            'overall_threat_level': threat_level,
            'overall_support_level': support_level,
            'confidence_score': 0.75,  # Mock confidence
            'reasoning': {
                'democracy': f"Analysis of democratic processes and institutions in bill content",
                'human_rights': f"Assessment of civil liberties and equality implications",
                'environment': f"Evaluation of environmental and sustainability impacts",
                'methodology': 'AI analysis using neutral criteria framework v1.0'
            }
        }
    
    def _score_to_level(self, score: int) -> str:
        """Convert numeric score to categorical level."""
        if score == 0:
            return 'none'
        elif score <= 3:
            return 'low'
        elif score <= 6:
            return 'medium'
        elif score <= 8:
            return 'high'
        else:
            return 'critical'
    
    def _create_analysis_record(self, bill: Bill, analysis_result: Dict) -> BillValuesAnalysis:
        """Create or update the analysis record in the database."""
        
        # Check if analysis already exists
        existing = self.db.query(BillValuesAnalysis).filter(
            BillValuesAnalysis.bill_id == bill.id
        ).first()
        
        if existing:
            # Update existing analysis
            for key, value in analysis_result.items():
                if key == 'reasoning':
                    # Store JSON-serializable string in Text column
                    import json
                    existing.analysis_reasoning = json.dumps(value) if isinstance(value, (dict, list)) else str(value)
                elif hasattr(existing, key):
                    setattr(existing, key, value)
            existing.analyzed_at = datetime.utcnow()
            existing.ai_model_version = "mock_v1.0"
            analysis = existing
        else:
            # Create new analysis; ensure analysis_reasoning is stored as JSON string
            import json
            reasoning_value = analysis_result.get('reasoning')
            analysis = BillValuesAnalysis(
                bill_id=bill.id,
                democracy_threat_score=analysis_result['democracy_threat_score'],
                democracy_support_score=analysis_result['democracy_support_score'],
                human_rights_threat_score=analysis_result['human_rights_threat_score'],
                human_rights_support_score=analysis_result['human_rights_support_score'],
                environmental_threat_score=analysis_result['environmental_threat_score'],
                environmental_support_score=analysis_result['environmental_support_score'],
                overall_threat_level=analysis_result['overall_threat_level'],
                overall_support_level=analysis_result['overall_support_level'],
                analysis_reasoning=(json.dumps(reasoning_value) if isinstance(reasoning_value, (dict, list)) else str(reasoning_value) if reasoning_value is not None else None),
                confidence_score=analysis_result['confidence_score'],
                analyzed_at=datetime.utcnow(),
                ai_model_version="mock_v1.0",
                requires_human_review=(analysis_result.get('confidence_score', 1.0) < 0.7),
                # Explicitly set timestamps to work around database constraint issue
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            self.db.add(analysis)

        self.db.flush()  # Get the ID
        return analysis

    def analyze_bill_values_sync(self, bill: Bill) -> BillValuesAnalysis:
        """
        Synchronous version of bill values analysis.

        Args:
            bill: Bill to analyze

        Returns:
            BillValuesAnalysis object
        """
        # Check if analysis already exists
        existing_analysis = self.get_bill_analysis(bill.id)
        if existing_analysis:
            return existing_analysis

        # For now, use mock analysis (can be replaced with actual AI analysis later)
        analysis_data = self._generate_mock_analysis(bill)

        # Create new analysis record with explicit timestamps
        import json
        now = datetime.utcnow()
        analysis = BillValuesAnalysis(
            bill_id=bill.id,
            democracy_threat_score=analysis_data['democracy_threat_score'],
            democracy_support_score=analysis_data['democracy_support_score'],
            human_rights_threat_score=analysis_data['human_rights_threat_score'],
            human_rights_support_score=analysis_data['human_rights_support_score'],
            environmental_threat_score=analysis_data['environmental_threat_score'],
            environmental_support_score=analysis_data['environmental_support_score'],
            overall_threat_level=analysis_data['overall_threat_level'],
            overall_support_level=analysis_data['overall_support_level'],
            confidence_score=analysis_data['confidence_score'],
            requires_human_review=analysis_data['needs_human_review'],
            analysis_reasoning=json.dumps({'summary': analysis_data['analysis_summary']}),
            analyzed_at=now,
            ai_model_version="mock_v1.0",
            created_at=now,
            updated_at=now
        )

        # Save to database
        self.db.add(analysis)
        self.db.flush()  # Get the ID without committing

        return analysis

    def _generate_mock_analysis(self, bill: Bill) -> Dict:
        """
        Generate values analysis using AI or fallback to enhanced keyword analysis.

        Args:
            bill: Bill to analyze

        Returns:
            Dictionary with analysis scores
        """
        try:
            # Prepare bill content for analysis
            bill_text = self._prepare_bill_text(bill)
            bill_title = bill.title or "Untitled Bill"
            bill_summary = getattr(bill, 'summary', None) or getattr(bill, 'ai_summary', None)
            
            # Use AI analyzer
            analysis_result = self.ai_analyzer.analyze_bill_values(
                bill_text=bill_text,
                bill_title=bill_title,
                bill_summary=bill_summary
            )
            
            logger.info(f"AI analysis completed for bill {bill.id} with confidence {analysis_result.get('confidence_score', 0)}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"AI analysis failed for bill {bill.id}: {e}, falling back to keyword analysis")
            # Fallback to enhanced keyword analysis
            return self.ai_analyzer._generate_fallback_analysis(bill.title or "Untitled Bill")
    
    def _prepare_bill_text(self, bill: Bill) -> str:
        """Prepare bill text for AI analysis."""
        # Try to get the most comprehensive text available
        bill_text = ""
        
        if hasattr(bill, 'full_text') and bill.full_text:
            bill_text = bill.full_text
        elif hasattr(bill, 'text') and bill.text:
            bill_text = bill.text
        elif hasattr(bill, 'summary') and bill.summary:
            bill_text = bill.summary
        elif hasattr(bill, 'ai_summary') and bill.ai_summary:
            bill_text = bill.ai_summary
        elif bill.title:
            bill_text = bill.title
        else:
            bill_text = "No bill text available"
        
        return bill_text
    
    async def _generate_values_tags(self, analysis: BillValuesAnalysis, analysis_result: Dict):
        """Generate neutral-language tags for frontend display."""
        
        # Clear existing tags
        self.db.query(BillValuesTag).filter(
            BillValuesTag.analysis_id == analysis.id
        ).delete()
        
        tags = []
        
        # Democracy tags
        if analysis.democracy_support_score >= 5:
            tags.append(self._create_tag(
                analysis, 'democracy', 'support', 'civic_participation',
                'Affects Civic Participation', analysis.democracy_support_score,
                'blue', 'vote'
            ))
        elif analysis.democracy_threat_score >= 5:
            tags.append(self._create_tag(
                analysis, 'democracy', 'impact', 'democratic_processes',
                'Impacts Democratic Processes', analysis.democracy_threat_score,
                'orange', 'alert-circle'
            ))
        
        # Human rights tags
        if analysis.human_rights_support_score >= 5:
            tags.append(self._create_tag(
                analysis, 'human_rights', 'support', 'community_rights',
                'Supports Community Rights', analysis.human_rights_support_score,
                'green', 'users'
            ))
        elif analysis.human_rights_threat_score >= 5:
            tags.append(self._create_tag(
                analysis, 'human_rights', 'impact', 'civil_liberties',
                'Affects Civil Liberties', analysis.human_rights_threat_score,
                'orange', 'shield'
            ))
        
        # Environmental tags
        if analysis.environmental_support_score >= 5:
            tags.append(self._create_tag(
                analysis, 'environment', 'support', 'environmental_protection',
                'Supports Environmental Protection', analysis.environmental_support_score,
                'green', 'leaf'
            ))
        elif analysis.environmental_threat_score >= 5:
            tags.append(self._create_tag(
                analysis, 'environment', 'impact', 'environmental_impact',
                'Environmental Impact Considerations', analysis.environmental_threat_score,
                'orange', 'globe'
            ))
        
        # Add tags to database
        for tag in tags:
            self.db.add(tag)
        
        self.db.flush()
    
    def _create_tag(self, analysis: BillValuesAnalysis, category: str, tag_type: str, 
                   tag_name: str, display_text: str, severity: int, 
                   color: str, icon: str) -> BillValuesTag:
        """Helper to create a values tag with neutral language."""
        return BillValuesTag(
            bill_id=analysis.bill_id,
            analysis_id=analysis.id,
            tag_category=category,
            tag_type=tag_type,
            tag_name=tag_name,
            display_text=display_text,
            severity_level=severity,
            display_priority=severity,
            color_theme=color,
            icon_name=icon,
            is_active=True
        )
    
    def _create_fallback_analysis(self, bill: Bill) -> BillValuesAnalysis:
        """Create minimal analysis record when analysis fails."""
        import json
        analysis = BillValuesAnalysis(
            bill_id=bill.id,
            democracy_threat_score=0,
            democracy_support_score=0,
            human_rights_threat_score=0,
            human_rights_support_score=0,
            environmental_threat_score=0,
            environmental_support_score=0,
            overall_threat_level='none',
            overall_support_level='none',
            analysis_reasoning=json.dumps({'error': 'Analysis failed, manual review required'}),
            confidence_score=0.0,
            analyzed_at=datetime.utcnow(),
            ai_model_version="fallback",
            requires_human_review=True
        )
        self.db.add(analysis)
        self.db.flush()
        return analysis

    def get_bill_analysis(self, bill_id: str) -> Optional[BillValuesAnalysis]:
        """Get existing analysis for a bill."""
        return self.db.query(BillValuesAnalysis).filter(
            BillValuesAnalysis.bill_id == bill_id
        ).first()
    
    def get_bills_requiring_review(self) -> List[BillValuesAnalysis]:
        """Get all bills that require human review."""
        return self.db.query(BillValuesAnalysis).filter(
            BillValuesAnalysis.requires_human_review == True,
            BillValuesAnalysis.reviewed_at.is_(None)
        ).all()
