"""Add hero_summary_citations and prepare for source_index summary

Revision ID: 010_add_hero_summary_citations_and_source_index_summary
Revises: 009_add_review_override_to_bill_details
Create Date: 2025-08-12 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '010_hero_summary_citations'
down_revision: Union[str, Sequence[str], None] = '009_add_review_override'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add hero_summary_citations JSONB column
    op.add_column('bill_details', sa.Column('hero_summary_citations', postgresql.JSONB(astext_type=sa.Text()), nullable=True))

    # Note: source_index already exists and is JSONB. We will start including a "summary"
    # field per entry at the application layer; no DB migration is needed for nested JSON keys.


def downgrade() -> None:
    op.drop_column('bill_details', 'hero_summary_citations')

