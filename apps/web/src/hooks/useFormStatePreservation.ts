'use client';

import { useEffect, useCallback } from 'react';
import { UseFormReturn } from 'react-hook-form';

interface UseFormStatePreservationOptions<T> {
  form: UseFormReturn<T>;
  storageKey: string;
  preserveOnAuth?: boolean;
  excludeFields?: (keyof T)[];
  onRestore?: (restoredData: any) => void;
}

export function useFormStatePreservation<T extends Record<string, any>>({
  form,
  storageKey,
  preserveOnAuth = true,
  excludeFields = [],
  onRestore
}: UseFormStatePreservationOptions<T>) {
  
  // Save form state to localStorage
  const saveFormState = useCallback(() => {
    try {
      const formData = form.getValues();
      
      // Exclude sensitive or unnecessary fields
      const filteredData = { ...formData };
      excludeFields.forEach(field => {
        delete filteredData[field];
      });
      
      localStorage.setItem(storageKey, JSON.stringify(filteredData));
      localStorage.setItem(`${storageKey}-timestamp`, Date.now().toString());
      
      console.log('💾 Form state saved to localStorage:', storageKey);
    } catch (error) {
      console.error('❌ Failed to save form state:', error);
    }
  }, [form, storageKey, excludeFields]);

  // Load form state from localStorage
  const loadFormState = useCallback(() => {
    try {
      const savedData = localStorage.getItem(storageKey);
      const timestamp = localStorage.getItem(`${storageKey}-timestamp`);
      
      if (savedData && timestamp) {
        // Check if the saved data is not too old (expire after 1 hour)
        const savedTime = parseInt(timestamp);
        const currentTime = Date.now();
        const oneHour = 60 * 60 * 1000;
        
        if (currentTime - savedTime < oneHour) {
          const parsedData = JSON.parse(savedData);
          
          // Reset form with saved data
          form.reset(parsedData);
          
          console.log('📥 Form state loaded from localStorage:', storageKey);
          return parsedData;
        } else {
          // Clean up expired data
          localStorage.removeItem(storageKey);
          localStorage.removeItem(`${storageKey}-timestamp`);
          console.log('🧹 Expired form state cleaned up');
        }
      }
    } catch (error) {
      console.error('❌ Failed to load form state:', error);
    }
    return null;
  }, [form, storageKey]);

  // Clear saved form state
  const clearFormState = useCallback(() => {
    try {
      localStorage.removeItem(storageKey);
      localStorage.removeItem(`${storageKey}-timestamp`);
      console.log('🗑️ Form state cleared from localStorage:', storageKey);
    } catch (error) {
      console.error('❌ Failed to clear form state:', error);
    }
  }, [storageKey]);

  // Check if we should restore state after authentication
  useEffect(() => {
    if (preserveOnAuth) {
      const shouldPreserve = localStorage.getItem('preserve-form-state');
      const expectedKey = localStorage.getItem('form-state-key');
      
      if (shouldPreserve === 'true' && expectedKey === storageKey) {
        console.log('🔄 Restoring form state after authentication...');
        
        const restoredData = loadFormState();
        
        if (restoredData) {
          // Call the onRestore callback if provided
          if (onRestore) {
            onRestore(restoredData);
          }
          
          // Clean up the preservation flags
          localStorage.removeItem('preserve-form-state');
          localStorage.removeItem('form-state-key');
          localStorage.removeItem('return-url');
          
          // Clear the saved form data now that it's been restored
          setTimeout(() => {
            clearFormState();
          }, 1000); // Small delay to ensure form has been updated
        }
      }
    }
  }, [preserveOnAuth, storageKey, loadFormState, clearFormState]);

  // Auto-save form data when it changes (debounced)
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      if (type === 'change') {
        // Debounce the save operation
        const timeoutId = setTimeout(() => {
          saveFormState();
        }, 500);

        return () => clearTimeout(timeoutId);
      }
    });

    return () => subscription.unsubscribe();
  }, [form, saveFormState]);

  return {
    saveFormState,
    loadFormState,
    clearFormState
  };
}