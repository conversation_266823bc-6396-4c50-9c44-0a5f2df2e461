#!/usr/bin/env python3
"""
Test the bill processing fix for the bill_id None issue
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

async def test_bill_processing_fix():
    """Test the unified bill processing with the fix"""
    try:
        from app.db.database import get_db
        from app.services.unified_bill_processing_service import UnifiedBillProcessingService
        
        # Get database session
        db = next(get_db())
        
        try:
            service = UnifiedBillProcessingService(db)
            
            print("🧪 Testing bill processing fix for HR7...")
            
            # Test processing HR7 which was failing before
            result = await service.process_bill_by_number(
                bill_number="HR7",
                congress_session=118,
                environment="development",
                use_enhanced_analysis=False  # Use cost-optimized span-grounded analysis
            )
            
            print(f"\n📊 Processing Result:")
            print(f"Success: {result.get('success', False)}")
            
            if result.get('success'):
                print(f"✅ Bill ID: {result.get('bill_id', 'Unknown')}")
                print(f"✅ Bill Number: {result.get('bill_number', 'Unknown')}")
                print(f"✅ Title: {result.get('title', 'Unknown')}")
                print(f"✅ Environment: {result.get('environment', 'Unknown')}")
                
                # Check if bill_details was created
                if 'updated' in result:
                    updated = result['updated']
                    print(f"✅ AI Analysis: {updated.get('ai_analysis', False)}")
                    print(f"✅ Details Persisted: {updated.get('details_persisted', False)}")
                    print(f"✅ Values Analysis: {updated.get('values_analysis', False)}")
                
                print("\n🎉 Bill processing completed successfully!")
                return True
            else:
                error = result.get('error', 'Unknown error')
                print(f"❌ Processing failed: {error}")
                
                # Check if it's the same bill_id None error
                if 'bill_id' in error and 'None' in error:
                    print("❌ Still getting the bill_id None error - fix didn't work")
                elif 'rolled back' in error:
                    print("❌ Transaction rollback error - need to investigate further")
                else:
                    print("❌ Different error - may be progress")
                
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_bill_creation():
    """Test just the bill creation part without AI processing"""
    try:
        from app.db.database import get_db
        from app.services.unified_bill_processing_service import UnifiedBillProcessingService
        from app.services.congress_gov_api import CongressGovAPI
        
        db = next(get_db())
        
        try:
            print("🧪 Testing simple bill creation...")
            
            # Get bill metadata from Congress.gov
            congress_api = CongressGovAPI()
            bill_metadata = congress_api.get_bill_by_number(118, "hr", 7)

            if not bill_metadata:
                print("❌ Could not fetch bill metadata")
                return False

            print(f"✅ Fetched metadata for: {bill_metadata.get('title', 'Unknown')}")

            # Get bill text
            full_text = await congress_api.get_bill_full_text(118, "hr", 7)
            
            if not full_text:
                print("❌ Could not fetch bill text")
                return False
            
            print(f"✅ Fetched bill text: {len(full_text)} characters")
            
            # Test bill creation
            service = UnifiedBillProcessingService(db)
            bill = await service._create_bill_record(bill_metadata, full_text, {})
            
            if bill and bill.id:
                print(f"✅ Created bill successfully!")
                print(f"   ID: {bill.id}")
                print(f"   Number: {bill.bill_number}")
                print(f"   Title: {bill.title[:100]}...")
                return True
            else:
                print(f"❌ Bill creation failed - no ID")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Simple bill creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run tests"""
    print("🧪 Testing Bill Processing Fix")
    print("=" * 50)
    
    # Test 1: Simple bill creation
    simple_success = await test_simple_bill_creation()
    
    print("\n" + "-" * 50)
    
    # Test 2: Full bill processing (only if simple creation works)
    if simple_success:
        full_success = await test_bill_processing_fix()
    else:
        print("⏭️ Skipping full processing test due to simple creation failure")
        full_success = False
    
    print("\n" + "=" * 50)
    print("📋 Test Results:")
    print(f"✅ Simple bill creation: {'PASS' if simple_success else 'FAIL'}")
    print(f"✅ Full bill processing: {'PASS' if full_success else 'FAIL'}")
    
    if simple_success and full_success:
        print("\n🎉 ALL TESTS PASSED! The bill_id None issue is fixed!")
    elif simple_success:
        print("\n⚠️ Bill creation works, but full processing needs attention")
    else:
        print("\n❌ Basic bill creation is failing - need to investigate")

if __name__ == "__main__":
    asyncio.run(main())
