# Officials Data Deployment Guide for Staging

This guide provides multiple methods to populate officials data (including social media and contact information) on `staging.modernaction.io`.

## Prerequisites

1. **Staging Environment**: `staging.modernaction.io` must be deployed and running
2. **OpenStates API Key**: `OPENSTATES_API_KEY` must be configured in staging environment variables
3. **Database Migrations**: All database migrations must be applied (especially the officials table enhancements)

## Method 1: Automated Script (Recommended)

Use the automated deployment script that handles everything:

```bash
# Full deployment (all states, all officials)
./deploy_officials_staging.sh

# Test run without making changes
./deploy_officials_staging.sh --dry-run

# Limited deployment for testing
./deploy_officials_staging.sh --limit 100

# Custom staging URL
./deploy_officials_staging.sh --staging-url your-staging-domain.com
```

## Method 2: AWS ECS Manual Task

If you prefer manual control, run the backfill as an ECS task:

```bash
# Get network configuration from existing service
aws ecs describe-services \
  --cluster modernaction-staging \
  --services modernaction-api-staging \
  --query 'services[0].networkConfiguration.awsvpcConfiguration'

# Run the backfill task
aws ecs run-task \
  --cluster modernaction-staging \
  --task-definition modernaction-api-staging \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-xxx],securityGroups=[sg-xxx],assignPublicIp=ENABLED}" \
  --overrides '{
    "containerOverrides": [{
      "name": "modernaction-api",
      "command": ["bash", "-c", "cd /app && python backfill_officials.py --limit 200"]
    }]
  }'
```

## Method 3: Direct SSH/Container Access

If you have direct access to the staging container:

```bash
# Connect to staging container
# (Method depends on your deployment setup)

# Navigate to API directory
cd /app

# Run backfill script
python backfill_officials.py --dry-run  # Test first
python backfill_officials.py --limit 100  # Limited run
python backfill_officials.py  # Full run (all states)
```

## Method 4: State-by-State Deployment

For incremental deployment or testing specific states:

```bash
# Single state
python backfill_officials.py --state CA

# Multiple states in sequence
for state in CA NY TX FL; do
  python backfill_officials.py --state $state
done
```

## Verification Steps

After deployment, verify the data is populated correctly:

### 1. Check API Endpoints

```bash
# Test ZIP code lookup
curl "https://staging.modernaction.io/api/v1/officials/zip/90210"

# Test specific official by ID
curl "https://staging.modernaction.io/api/v1/officials/{official-id}"

# Check OpenStates health
curl "https://staging.modernaction.io/api/v1/debug/openstates-health"
```

### 2. Test Frontend Integration

1. Visit `https://staging.modernaction.io/officials`
2. Enter a ZIP code (e.g., 90210)
3. Verify officials are displayed with:
   - Photos
   - Contact information
   - Social media links
   - Proper styling

### 3. Test Official Profile Pages

1. Click on an official from the search results
2. Verify the profile page loads with:
   - Complete contact information
   - Social media accounts
   - Committee information
   - Legislative activity (if available)

### 4. Test Dashboard Integration

1. Visit `https://staging.modernaction.io/dashboard`
2. Verify "Your Representatives" section shows officials based on user ZIP

## Expected Data Coverage

After successful deployment, you should have:

- **Federal Officials**: All U.S. Senators and Representatives
- **State Officials**: State senators and representatives from major districts
- **Contact Information**: Email addresses, phone numbers, office addresses
- **Social Media**: Twitter, Facebook, Instagram, YouTube, LinkedIn accounts
- **Photos**: Official headshots from government sources
- **Metadata**: Committee memberships, party affiliations, biographical information

## Troubleshooting

### Issue: OpenStates API Not Configured

```
Error: OpenStates API is not enabled
```

**Solution**: Ensure `OPENSTATES_API_KEY` is set in staging environment variables.

### Issue: Database Connection Errors

```
Error: Could not connect to database
```

**Solution**: Check that database migrations have been applied and database is accessible from ECS tasks.

### Issue: Rate Limiting

```
Error: Too many requests to OpenStates API
```

**Solution**: The backfill script includes built-in rate limiting. If you encounter this, wait a few minutes and retry with `--limit` to process smaller batches.

### Issue: Network Timeouts

```
Error: Request timeout
```

**Solution**: The script includes retry logic. For persistent issues, run with smaller batches using `--limit`.

## Performance Notes

- **Full backfill**: Processes all 50 states + DC (~3,000-5,000 officials)
- **Estimated time**: 10-30 minutes depending on network and API response times
- **Memory usage**: Moderate (each official record is ~1-2KB)
- **API calls**: 1 call per ZIP code (51 calls) + geocoding calls

## Monitoring

During deployment, monitor:

1. **ECS Task Logs**: Check CloudWatch logs for the modernaction-api-staging task
2. **Database Growth**: Monitor officials table row count
3. **API Response Times**: Ensure staging remains responsive
4. **Error Rates**: Watch for API errors in application logs

## Rollback Plan

If issues occur during deployment:

1. **Stop the backfill task** if still running
2. **Database rollback** (if needed):
   ```sql
   -- Remove recently added officials (if needed)
   DELETE FROM officials WHERE created_at >= '2025-08-07';
   ```
3. **Clear cache** if using Redis/caching
4. **Restart services** if needed

## Environment Variables Reference

Required environment variables in staging:

```bash
# OpenStates API (required)
OPENSTATES_API_KEY=your_openstates_api_key_here

# Database (should already be configured)
DATABASE_URL=postgresql://user:pass@host:port/dbname

# Optional: API rate limiting
OPENSTATES_RATE_LIMIT=10  # requests per second
```

## Next Steps

After successful deployment:

1. **Test user flows**: Verify ZIP lookups work correctly
2. **SEO validation**: Check that official profile pages are indexed
3. **Performance testing**: Ensure the additional data doesn't slow down the application
4. **User acceptance testing**: Have stakeholders verify the new functionality

---

For questions or issues, refer to the main project documentation or contact the development team.