{"name": "modern-action-2.0", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "env:dev": "./scripts/load-env.sh development", "env:staging": "./scripts/load-env.sh staging", "env:prod": "./scripts/load-env.sh production", "dev": "npm run env:dev && concurrently \"cd apps/api && poetry run uvicorn app.main:app --reload\" \"cd apps/web && npm run dev\"", "staging": "npm run env:staging && echo 'Staging environment loaded. Deploy with: cd infrastructure && cdk deploy'", "build": "cd apps/web && npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/columj9/modern-action-2.0.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/columj9/modern-action-2.0/issues"}, "homepage": "https://github.com/columj9/modern-action-2.0#readme", "devDependencies": {"@playwright/test": "^1.54.1"}, "dependencies": {"@anthropic-ai/claude-code": "^1.0.67", "axios": "^1.10.0", "playwright": "^1.54.1", "zustand": "^5.0.6"}, "directories": {"doc": "docs", "test": "tests"}}