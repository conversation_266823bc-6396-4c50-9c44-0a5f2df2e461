"""
Section Templates Service - PHASE 3.1
Provides structured templates for legal document analysis following HR5-118 standards
"""

from typing import Dict, List, Any
from dataclasses import dataclass
import re


@dataclass
class SectionTemplate:
    """Template for generating legal document sections"""
    section_type: str
    title_pattern: str
    importance: str
    required_fields: List[str]
    min_subsections: int
    max_subsections: int
    description: str


class SectionTemplatesService:
    """
    PHASE 3.1: Advanced section templates for HR5-118 legal precision
    Maps bill content to structured section templates for comprehensive analysis
    """
    
    def __init__(self):
        self.templates = self._initialize_templates()
    
    def _initialize_templates(self) -> Dict[str, SectionTemplate]:
        """Initialize HR5-118 style legal document templates"""
        return {
            "title": SectionTemplate(
                section_type="title",
                title_pattern="SEC. 1: SHORT TITLE",
                importance="primary",
                required_fields=["title", "detailed_summary", "key_actions"],
                min_subsections=1,
                max_subsections=1,
                description="Bill title and identification"
            ),
            
            "definitions": SectionTemplate(
                section_type="definitions", 
                title_pattern="SEC. {num}: DEFINITIONS",
                importance="technical",
                required_fields=["title", "detailed_summary", "key_actions", "affected_parties"],
                min_subsections=1,
                max_subsections=8,
                description="Legal definitions and terminology"
            ),
            
            "establishment": SectionTemplate(
                section_type="establishment",
                title_pattern="SEC. {num}: ESTABLISHMENT OF {entity}",
                importance="primary",
                required_fields=["title", "detailed_summary", "key_actions", "affected_parties"],
                min_subsections=3,
                max_subsections=10,
                description="Creation of new entities, programs, or frameworks"
            ),
            
            "requirements": SectionTemplate(
                section_type="requirements",
                title_pattern="SEC. {num}: {requirement_type} REQUIREMENTS",
                importance="primary",
                required_fields=["title", "detailed_summary", "key_actions", "affected_parties"],
                min_subsections=2,
                max_subsections=12,
                description="Mandatory requirements and compliance standards"
            ),
            
            "amendments": SectionTemplate(
                section_type="amendments",
                title_pattern="SEC. {num}: AMENDMENTS TO {law}",
                importance="secondary", 
                required_fields=["title", "detailed_summary", "key_actions", "affected_parties"],
                min_subsections=2,
                max_subsections=15,
                description="Modifications to existing laws"
            ),
            
            "enforcement": SectionTemplate(
                section_type="enforcement",
                title_pattern="SEC. {num}: ENFORCEMENT AND PENALTIES",
                importance="primary",
                required_fields=["title", "detailed_summary", "key_actions", "affected_parties"],
                min_subsections=3,
                max_subsections=8,
                description="Enforcement mechanisms and penalty structures"
            ),
            
            "funding": SectionTemplate(
                section_type="funding",
                title_pattern="SEC. {num}: AUTHORIZATION OF APPROPRIATIONS",
                importance="primary",
                required_fields=["title", "detailed_summary", "key_actions", "affected_parties"],
                min_subsections=2,
                max_subsections=6,
                description="Funding provisions and appropriations"
            ),
            
            "implementation": SectionTemplate(
                section_type="implementation",
                title_pattern="SEC. {num}: IMPLEMENTATION TIMELINE",
                importance="secondary",
                required_fields=["title", "detailed_summary", "key_actions", "affected_parties"],
                min_subsections=2,
                max_subsections=8,
                description="Implementation schedules and deadlines"
            ),
            
            "reporting": SectionTemplate(
                section_type="reporting",
                title_pattern="SEC. {num}: REPORTING REQUIREMENTS",
                importance="secondary",
                required_fields=["title", "detailed_summary", "key_actions", "affected_parties"],
                min_subsections=2,
                max_subsections=6,
                description="Reporting and oversight obligations"
            ),
            
            "effective_date": SectionTemplate(
                section_type="effective_date",
                title_pattern="SEC. {num}: EFFECTIVE DATE",
                importance="technical",
                required_fields=["title", "detailed_summary", "key_actions"],
                min_subsections=1,
                max_subsections=3,
                description="When the law takes effect"
            )
        }
    
    def analyze_bill_structure(self, bill_text: str, evidence_spans: List[Dict]) -> Dict[str, Any]:
        """
        PHASE 3.1: Analyze bill structure and map to templates
        Returns comprehensive section strategy for HR5-118 standards
        """
        
        structure_analysis = {
            "detected_sections": [],
            "template_mappings": [],
            "section_strategy": [],
            "estimated_sections": 0,
            "hr5118_coverage": 0.0
        }
        
        # Step 1: Detect existing SEC. patterns in bill text
        sec_patterns = re.findall(r'SEC\.\s*(\d+)\.\s*([A-Z\s]+)', bill_text, re.IGNORECASE)
        structure_analysis["detected_sections"] = [
            {"number": num, "title": title.strip()} 
            for num, title in sec_patterns
        ]
        
        # Step 2: Map detected sections to templates
        for sec_info in structure_analysis["detected_sections"]:
            template = self._match_section_to_template(sec_info["title"], bill_text)
            if template:
                structure_analysis["template_mappings"].append({
                    "section_number": sec_info["number"],
                    "section_title": sec_info["title"],
                    "template_type": template.section_type,
                    "template": template
                })
        
        # Step 3: Generate comprehensive section strategy
        section_strategy = self._generate_section_strategy(bill_text, evidence_spans, structure_analysis["template_mappings"])
        structure_analysis["section_strategy"] = section_strategy
        
        # Step 4: Calculate estimates
        total_sections = sum(item["estimated_subsections"] for item in section_strategy)
        structure_analysis["estimated_sections"] = total_sections
        structure_analysis["hr5118_coverage"] = min(total_sections / 44.0, 1.0)
        
        return structure_analysis
    
    def _match_section_to_template(self, section_title: str, bill_text: str) -> SectionTemplate:
        """Match a section title to the most appropriate template"""
        title_lower = section_title.lower()
        
        # Pattern matching for template assignment
        if "short title" in title_lower or "title" in title_lower:
            return self.templates["title"]
        elif "definition" in title_lower:
            return self.templates["definitions"]
        elif "establishment" in title_lower or "creation" in title_lower:
            return self.templates["establishment"]
        elif "requirement" in title_lower or "standard" in title_lower:
            return self.templates["requirements"]
        elif "amendment" in title_lower or "amend" in title_lower:
            return self.templates["amendments"]
        elif "enforcement" in title_lower or "penalt" in title_lower:
            return self.templates["enforcement"]
        elif "authorization" in title_lower or "appropriation" in title_lower or "funding" in title_lower:
            return self.templates["funding"]
        elif "implementation" in title_lower or "timeline" in title_lower:
            return self.templates["implementation"]
        elif "report" in title_lower or "oversight" in title_lower:
            return self.templates["reporting"]
        elif "effective date" in title_lower:
            return self.templates["effective_date"]
        else:
            # Default to requirements template for unmatched sections
            return self.templates["requirements"]
    
    def _generate_section_strategy(self, bill_text: str, evidence_spans: List[Dict], 
                                 template_mappings: List[Dict]) -> List[Dict]:
        """Generate comprehensive section strategy for HR5-118 coverage"""
        
        strategy = []
        used_templates = {mapping["template_type"] for mapping in template_mappings}
        
        # Step 1: Expand existing mapped sections with subsections
        for mapping in template_mappings:
            template = mapping["template"]
            subsection_count = self._calculate_subsection_count(template, bill_text, evidence_spans)
            
            strategy.append({
                "section_type": template.section_type,
                "main_section": {
                    "title": f"SEC. {mapping['section_number']}: {mapping['section_title']}",
                    "importance": template.importance
                },
                "subsections": self._generate_subsections(template, subsection_count, bill_text),
                "estimated_subsections": 1 + subsection_count,  # Main + subsections
                "template": template
            })
        
        # Step 2: Add missing essential templates for comprehensive coverage
        essential_templates = ["title", "definitions", "establishment", "requirements", 
                             "amendments", "enforcement", "funding", "implementation", 
                             "reporting", "effective_date"]
        
        section_num = len(template_mappings) + 1
        for template_type in essential_templates:
            if template_type not in used_templates:
                template = self.templates[template_type]
                if self._should_include_template(template_type, bill_text):
                    subsection_count = self._calculate_subsection_count(template, bill_text, evidence_spans)
                    
                    strategy.append({
                        "section_type": template.section_type,
                        "main_section": {
                            "title": template.title_pattern.format(num=section_num, 
                                                                 entity="RELEVANT ENTITY",
                                                                 requirement_type="COMPLIANCE",
                                                                 law="EXISTING LAW"),
                            "importance": template.importance
                        },
                        "subsections": self._generate_subsections(template, subsection_count, bill_text),
                        "estimated_subsections": 1 + subsection_count,
                        "template": template
                    })
                    section_num += 1
        
        return strategy
    
    def _calculate_subsection_count(self, template: SectionTemplate, bill_text: str, 
                                  evidence_spans: List[Dict]) -> int:
        """Calculate optimal number of subsections for this template type"""
        
        # Base calculation on template type and bill complexity
        base_count = template.min_subsections
        
        # Adjust based on bill length and evidence
        bill_complexity = len(bill_text) / 1000  # Rough complexity metric
        evidence_count = len(evidence_spans)
        
        if template.section_type == "amendments":
            # Amendments can have many subsections
            additional = min(int(bill_complexity * 2), template.max_subsections - base_count)
        elif template.section_type == "requirements":
            # Requirements scale with evidence
            additional = min(int(evidence_count / 10), template.max_subsections - base_count)
        elif template.section_type == "establishment":
            # Establishment sections need detailed breakdown
            additional = min(int(bill_complexity * 1.5), template.max_subsections - base_count)
        else:
            # Conservative scaling for other types
            additional = min(int(bill_complexity), template.max_subsections - base_count)
        
        return base_count + additional
    
    def _generate_subsections(self, template: SectionTemplate, count: int, bill_text: str) -> List[Dict]:
        """Generate subsection structure for a template"""
        
        subsections = []
        subsection_patterns = {
            "title": [],
            "definitions": ["(a) General Definitions", "(b) Technical Terms", "(c) Entity Classifications"],
            "establishment": ["(a) Framework Creation", "(b) Organizational Structure", "(c) Authority Designation", 
                            "(d) Operational Requirements", "(e) Coordination Mechanisms"],
            "requirements": ["(a) General Requirements", "(b) Compliance Standards", "(c) Implementation Guidelines",
                           "(d) Monitoring Procedures", "(e) Reporting Obligations"],
            "amendments": ["(a) Section Modifications", "(b) Paragraph Insertions", "(c) Conforming Changes",
                         "(d) Technical Corrections", "(e) Effective Date Adjustments"],
            "enforcement": ["(a) Enforcement Authority", "(b) Penalty Structure", "(c) Compliance Monitoring",
                          "(d) Appeals Process", "(e) Remedial Actions"],
            "funding": ["(a) Authorization Amounts", "(b) Distribution Mechanisms", "(c) Funding Timeline",
                       "(d) Cost-sharing Requirements"],
            "implementation": ["(a) Phase 1 Requirements", "(b) Phase 2 Requirements", "(c) Timeline Milestones",
                             "(d) Compliance Deadlines"],
            "reporting": ["(a) Annual Reports", "(b) Data Collection", "(c) Public Disclosure",
                        "(d) Congressional Notification"],
            "effective_date": ["(a) General Effective Date", "(b) Phased Implementation"]
        }
        
        patterns = subsection_patterns.get(template.section_type, [])
        for i in range(min(count, len(patterns))):
            subsections.append({
                "title": patterns[i],
                "importance": "secondary" if template.importance == "primary" else "technical"
            })
        
        # Add numbered subsections if we need more
        for i in range(len(patterns), count):
            subsections.append({
                "title": f"({chr(97 + i)}) Additional {template.section_type.title()} Provision {i + 1}",
                "importance": "technical"
            })
        
        return subsections
    
    def _should_include_template(self, template_type: str, bill_text: str) -> bool:
        """Determine if a template should be included based on bill content"""
        
        bill_lower = bill_text.lower()
        
        inclusion_criteria = {
            "title": True,  # Always include
            "definitions": "define" in bill_lower or "definition" in bill_lower or "means" in bill_lower,
            "establishment": "establish" in bill_lower or "create" in bill_lower or "program" in bill_lower,
            "requirements": "require" in bill_lower or "shall" in bill_lower or "must" in bill_lower,
            "amendments": "amend" in bill_lower or "section" in bill_lower,
            "enforcement": "enforce" in bill_lower or "penalty" in bill_lower or "violation" in bill_lower,
            "funding": "appropriation" in bill_lower or "fund" in bill_lower or "$" in bill_text,
            "implementation": "implement" in bill_lower or "effective" in bill_lower,
            "reporting": "report" in bill_lower or "data" in bill_lower,
            "effective_date": True  # Always include
        }
        
        return inclusion_criteria.get(template_type, True)
    
    def generate_enhanced_prompt_with_templates(self, bill_text: str, bill_metadata: Dict,
                                              evidence_spans: List[Dict], structure_analysis: Dict) -> str:
        """
        PHASE 3.1: Generate enhanced prompt using section templates
        Creates structured analysis guidance for HR5-118 standards
        """
        
        section_strategy = structure_analysis["section_strategy"]
        estimated_sections = structure_analysis["estimated_sections"]
        
        # Build detailed section requirements
        section_requirements = []
        for strategy_item in section_strategy:
            main_section = strategy_item["main_section"]
            subsections = strategy_item["subsections"]
            
            section_requirements.append(f"""
{main_section["title"]} ({main_section["importance"]})
{chr(10).join([f'  {sub["title"]} ({sub["importance"]})' for sub in subsections])}
""")
        
        enhanced_prompt = f"""PHASE 3.1: STRUCTURED LEGAL ANALYSIS WITH SECTION TEMPLATES

BILL: {bill_metadata.get('title', 'Unknown')} ({bill_metadata.get('bill_number', 'Unknown')})

CRITICAL REQUIREMENT: Generate EXACTLY {max(estimated_sections, 30)} detailed sections minimum using the structured approach below.

SECTION TEMPLATE STRATEGY:
You MUST follow this EXACT section structure for comprehensive HR5-118 coverage:

{''.join(section_requirements)}

ENHANCED ANALYSIS REQUIREMENTS:
- Each section must have substantial legal content (150+ words)
- Use precise legal language with specific citations
- Include exact amounts, dates, deadlines, and penalties
- Name specific entities, not generic terms
- Ground every major claim in evidence IDs
- Break down complex provisions into subsections

EVIDENCE CONTEXT (100 spans available):
{chr(10).join([f"ID: {span['id']} | {span.get('heading', 'Unknown')}" for span in evidence_spans[:20]])}
... and {len(evidence_spans) - 20} additional evidence spans

BILL TEXT ANALYSIS:
{bill_text[:6000]}

QUALITY STANDARDS:
- Minimum {max(estimated_sections, 30)} sections with template structure
- Legal precision matching HR5-118 standards
- Comprehensive coverage of all bill provisions
- Evidence-grounded analysis with specific citations
- Professional legal document formatting

Generate the complete_analysis array following the section template structure above."""

        return enhanced_prompt


def get_section_templates_service():
    """Factory function for section templates service"""
    return SectionTemplatesService()