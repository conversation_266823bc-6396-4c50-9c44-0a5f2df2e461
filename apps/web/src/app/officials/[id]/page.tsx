import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { officialApi } from '@/services/apiClient';
import { Official } from '@/types';
import Link from 'next/link';
import { SocialShare } from '@/components/shared/SocialShare';
import { OfficialImage } from '@/components/officials/OfficialImage';
import { SocialMediaContactWidget } from '@/components/officials/SocialMediaContactWidget';
import { TrackOfficialView } from '@/components/officials/TrackOfficialView';

interface OfficialPageProps {
  params: {
    id: string;
  };
}

// Generate metadata for SEO
export async function generateMetadata({ params }: OfficialPageProps): Promise<Metadata> {
  try {
    const official = await officialApi.getOfficialById(params.id);
    
    const displayName = official.full_name || official.name;
    const title = official.title || (official.chamber === 'house' ? 'Representative' : 'Senator');
    
    return {
      title: `${displayName} - ${title} | ModernAction.io`,
      description: `Contact information and legislative profile for ${displayName}, ${title} ${official.state ? `from ${official.state}` : ''}. Find phone numbers, email addresses, social media accounts, and legislative activity.`,
      keywords: [
        displayName,
        title,
        'congress',
        'representative', 
        'senator',
        official.state || '',
        'contact information',
        'legislative activity',
        'bills sponsored',
        'voting record'
      ].filter(Boolean).join(', '),
      openGraph: {
        title: `${displayName} - ${title}`,
        description: `Legislative profile and contact information for ${displayName}`,
        images: official.official_photo_url ? [{ 
          url: official.official_photo_url,
          alt: `Photo of ${displayName}`
        }] : undefined,
        type: 'profile',
        siteName: 'ModernAction.io'
      },
      twitter: {
        card: 'summary_large_image',
        title: `${displayName} - ${title}`,
        description: `Contact ${displayName} and view their legislative activity on ModernAction.io`,
        images: official.official_photo_url ? [official.official_photo_url] : undefined
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      }
    };
  } catch (error) {
    return {
      title: 'Official Profile | ModernAction.io',
      description: 'View detailed information about your elected representatives.',
      robots: {
        index: false,
        follow: false
      }
    };
  }
}

async function getOfficial(id: string): Promise<Official | null> {
  try {
    console.log(`Attempting to fetch official with ID: ${id}`);
    const startTime = Date.now();
    
    const official = await officialApi.getOfficialById(id);
    
    const duration = Date.now() - startTime;
    console.log(`Successfully fetched official ${id} in ${duration}ms`);
    
    return official;
  } catch (error) {
    console.error('Error fetching official:', error);
    
    // Log specific error details for debugging
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack?.slice(0, 500)
      });
    }
    
    return null;
  }
}

export default async function OfficialPage({ params }: OfficialPageProps) {
  const official = await getOfficial(params.id);
  
  if (!official) {
    notFound();
  }

  const displayName = official.full_name || official.name;
  const title = official.title || (official.chamber === 'house' ? 'Representative' : 'Senator');
  const photoUrl = official.official_photo_url || official.photo_url || '/images/default-official.png';

  const getPartyColor = (party?: string) => {
    if (!party) return 'bg-gray-100 text-gray-800';
    
    const partyLower = party.toLowerCase();
    if (partyLower.includes('democrat') || partyLower.includes('dem')) {
      return 'bg-blue-100 text-blue-800 border-blue-200';
    }
    if (partyLower.includes('republican') || partyLower.includes('rep')) {
      return 'bg-red-100 text-red-800 border-red-200';
    }
    return 'bg-gray-100 text-gray-800 border-gray-200';
  };

  const getSocialMediaLinks = () => {
    if (!official.social_media) return [];
    
    return Object.entries(official.social_media).map(([platform, url]) => ({
      platform,
      url,
      icon: getSocialIcon(platform)
    }));
  };

  const getSocialIcon = (platform: string) => {
    const platformLower = platform.toLowerCase();
    
    if (platformLower.includes('twitter')) return '🐦';
    if (platformLower.includes('facebook')) return '📘';
    if (platformLower.includes('instagram')) return '📷';
    if (platformLower.includes('youtube')) return '📺';
    if (platformLower.includes('linkedin')) return '💼';
    if (platformLower.includes('website')) return '🌐';
    
    return '🔗';
  };

  // Generate structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": displayName,
    "jobTitle": title,
    "worksFor": {
      "@type": "GovernmentOrganization",
      "name": official.chamber === 'house' ? 'U.S. House of Representatives' : 'U.S. Senate',
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Washington",
        "addressRegion": "DC",
        "addressCountry": "US"
      }
    },
    "address": official.state ? {
      "@type": "PostalAddress",
      "addressRegion": official.state,
      "addressCountry": "US"
    } : undefined,
    "email": official.email || undefined,
    "telephone": official.phone || undefined,
    "url": (official.website || official.homepage_url) || undefined,
    "image": official.official_photo_url || undefined,
    "sameAs": official.social_media ? Object.values(official.social_media).filter(Boolean) : undefined,
    "memberOf": official.party ? {
      "@type": "PoliticalParty",
      "name": official.party
    } : undefined,
    "description": official.bio || `${title} ${official.state ? `from ${official.state}` : ''}`,
  };

  return (
    <>
      {/* Analytics Tracking */}
      <TrackOfficialView official={official} />
      
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <div className="mb-6">
          <Link
            href="/officials"
            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors duration-200"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Officials Search
          </Link>
        </div>

        {/* Main Profile Card */}
        <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
          {/* Header Section */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-8">
            <div className="flex flex-col sm:flex-row items-center sm:items-start space-y-4 sm:space-y-0 sm:space-x-6">
              <div className="flex-shrink-0">
                <OfficialImage
                  src={photoUrl}
                  alt={`Photo of ${displayName}`}
                  width={128}
                  height={128}
                  className="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg"
                />
              </div>
              
              <div className="text-center sm:text-left text-white flex-1">
                <h1 className="text-3xl font-bold mb-2">{displayName}</h1>
                <div className="text-xl mb-4">
                  {title}
                  {official.state && (
                    <span className="ml-2 opacity-90">({official.state})</span>
                  )}
                  {official.district && (
                    <span className="ml-1 opacity-90">- District {official.district}</span>
                  )}
                </div>

                {official.party && (
                  <div className="inline-flex items-center">
                    <span className={`px-4 py-2 rounded-full text-sm font-medium border ${getPartyColor(official.party)} bg-white`}>
                      {official.party}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div className="p-6 space-y-8">
            {/* Contact Information */}
            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Contact Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Primary Contact */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Primary Contact</h3>
                  
                  {official.email && (
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">📧</span>
                      <div>
                        <div className="text-sm text-gray-600">Email</div>
                        <a 
                          href={`mailto:${official.email}`}
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          {official.email}
                        </a>
                      </div>
                    </div>
                  )}

                  {official.phone && (
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">📞</span>
                      <div>
                        <div className="text-sm text-gray-600">Phone</div>
                        <a 
                          href={`tel:${official.phone}`}
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          {official.phone}
                        </a>
                      </div>
                    </div>
                  )}

                  {(official.website || official.homepage_url) && (
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">🌐</span>
                      <div>
                        <div className="text-sm text-gray-600">Website</div>
                        <a 
                          href={official.website || official.homepage_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          Official Website
                        </a>
                      </div>
                    </div>
                  )}
                </div>

                {/* Office Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Office Information</h3>
                  
                  {official.dc_office_address && (
                    <div className="flex items-start space-x-3">
                      <span className="text-2xl">🏛️</span>
                      <div>
                        <div className="text-sm text-gray-600">Washington D.C. Office</div>
                        <div className="text-gray-900">{official.dc_office_address}</div>
                        {official.dc_office_phone && (
                          <a 
                            href={`tel:${official.dc_office_phone}`}
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            {official.dc_office_phone}
                          </a>
                        )}
                      </div>
                    </div>
                  )}

                  {official.local_office_address && (
                    <div className="flex items-start space-x-3">
                      <span className="text-2xl">🏢</span>
                      <div>
                        <div className="text-sm text-gray-600">Local Office</div>
                        <div className="text-gray-900">{official.local_office_address}</div>
                        {official.local_office_phone && (
                          <a 
                            href={`tel:${official.local_office_phone}`}
                            className="text-blue-600 hover:text-blue-800 text-sm"
                          >
                            {official.local_office_phone}
                          </a>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </section>

            {/* Interactive Contact Widget */}
            <section>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Contact & Engage</h2>
              <p className="text-gray-600 mb-6">
                Reach out to {displayName} about issues that matter to you. Social media messages often get faster responses than traditional contact methods.
              </p>
              
              <SocialMediaContactWidget
                officials={[official]}
                context="official-profile"
                variant="full"
                className="mb-6"
              />
              
              {/* Traditional Social Media Links as Backup */}
              {getSocialMediaLinks().length > 0 && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Direct Social Media Links</h3>
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                    {getSocialMediaLinks().map(({ platform, url, icon }) => (
                      <a
                        key={platform}
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-sm"
                      >
                        <span className="text-lg">{icon}</span>
                        <span className="font-medium text-gray-900">{platform}</span>
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </section>

            {/* Legislative Activity */}
            {(official.bills_sponsored_count !== undefined || official.bills_cosponsored_count !== undefined || official.votes_cast_count !== undefined) && (
              <section>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Legislative Activity</h2>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                  {official.bills_sponsored_count !== undefined && (
                    <div className="bg-blue-50 rounded-lg p-6 text-center">
                      <div className="text-3xl font-bold text-blue-600 mb-2">{official.bills_sponsored_count}</div>
                      <div className="text-sm font-medium text-blue-800">Bills Sponsored</div>
                    </div>
                  )}
                  {official.bills_cosponsored_count !== undefined && (
                    <div className="bg-green-50 rounded-lg p-6 text-center">
                      <div className="text-3xl font-bold text-green-600 mb-2">{official.bills_cosponsored_count}</div>
                      <div className="text-sm font-medium text-green-800">Bills Co-sponsored</div>
                    </div>
                  )}
                  {official.votes_cast_count !== undefined && (
                    <div className="bg-purple-50 rounded-lg p-6 text-center">
                      <div className="text-3xl font-bold text-purple-600 mb-2">{official.votes_cast_count}</div>
                      <div className="text-sm font-medium text-purple-800">Votes Cast</div>
                    </div>
                  )}
                </div>
              </section>
            )}

            {/* Committees */}
            {official.committees && official.committees.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Committee Memberships</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {official.committees.map((committee, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <div className="font-medium text-gray-900">{committee.name}</div>
                      {committee.role && (
                        <div className="text-sm text-gray-600">{committee.role}</div>
                      )}
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Leadership Positions */}
            {official.leadership_positions && official.leadership_positions.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Leadership Positions</h2>
                <div className="space-y-3">
                  {official.leadership_positions.map((position, index) => (
                    <div key={index} className="bg-amber-50 rounded-lg p-4 border border-amber-200">
                      <div className="font-medium text-gray-900">{position.title}</div>
                      {position.organization && (
                        <div className="text-sm text-gray-600">{position.organization}</div>
                      )}
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Biography */}
            {official.bio && (
              <section>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Biography</h2>
                <div className="bg-gray-50 rounded-lg p-6">
                  <p className="text-gray-700 leading-relaxed">{official.bio}</p>
                </div>
              </section>
            )}

            {/* Response Metrics */}
            {(official.response_rate !== undefined || official.avg_response_time !== undefined) && (
              <section>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Engagement Metrics</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  {official.response_rate !== undefined && (
                    <div className="bg-gray-50 rounded-lg p-6 text-center">
                      <div className="text-3xl font-bold text-gray-700 mb-2">{official.response_rate}%</div>
                      <div className="text-sm font-medium text-gray-600">Response Rate</div>
                    </div>
                  )}
                  {official.avg_response_time !== undefined && (
                    <div className="bg-gray-50 rounded-lg p-6 text-center">
                      <div className="text-3xl font-bold text-gray-700 mb-2">{official.avg_response_time}h</div>
                      <div className="text-sm font-medium text-gray-600">Avg Response Time</div>
                    </div>
                  )}
                </div>
              </section>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex justify-center space-x-4">
          <Link
            href="/bills"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            View Bills & Take Action
          </Link>
          
          <Link
            href="/officials"
            className="inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            Find Other Officials
          </Link>

          {/* Social Share */}
          <SocialShare
            official={official}
            className="inline-flex"
          />
        </div>
      </div>
    </div>
    </>
  );
}