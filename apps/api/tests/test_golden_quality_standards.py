"""
Golden test set for quality standards
Tests HR5 and HR7 sections to ensure quality standards are maintained
"""

import pytest
import asyncio
from typing import Dict, Any
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.span_grounded_validator import SpanGroundedValidator
from app.services.enhanced_span_retriever import EnhancedSpanRetriever
from app.services.bill_details_service import BillDetailsService
from app.db.database import get_db

class TestGoldenQualityStandards:
    """Golden test set that must always pass"""
    
    @pytest.fixture
    def validator(self):
        return SpanGroundedValidator()
    
    @pytest.fixture
    def retriever(self):
        return EnhancedSpanRetriever()
    
    @pytest.fixture
    def details_service(self):
        db = next(get_db())
        service = BillDetailsService(db)
        yield service
        db.close()
    
    def test_hr5_curriculum_disclosure_quality(self, validator):
        """Test HR5 curriculum disclosure section meets quality standards"""
        
        # HR5 golden example (good quality)
        hr5_analysis = {
            "tldr": {
                "content": "The Parents Bill of Rights Act requires local educational agencies to post curriculum information online by September 30, 2024.",
                "citations": [
                    {
                        "quote": "not later than September 30, 2024",
                        "heading": "SEC. 1111. STATE PLANS.",
                        "anchor_id": "sec-3",
                        "start_offset": 2100,
                        "end_offset": 2135
                    }
                ]
            },
            "who_affects": [
                {
                    "content": "Local educational agencies must comply with new curriculum disclosure requirements.",
                    "citations": [
                        {
                            "quote": "each local educational agency",
                            "heading": "SEC. 1111. STATE PLANS.",
                            "anchor_id": "sec-3",
                            "start_offset": 1950,
                            "end_offset": 1980
                        }
                    ]
                }
            ],
            "why_matters": {
                "content": "Parents will have access to detailed curriculum information for the first time.",
                "citations": [
                    {
                        "quote": "curriculum or description of such curriculum",
                        "heading": "SEC. 1111. STATE PLANS.",
                        "anchor_id": "sec-3",
                        "start_offset": 2000,
                        "end_offset": 2040
                    }
                ]
            },
            "key_provisions": [
                {
                    "content": "Agencies must post curriculum information on their website by September 30, 2024.",
                    "citations": [
                        {
                            "quote": "post on the website of the local educational agency",
                            "heading": "SEC. 1111. STATE PLANS.",
                            "anchor_id": "sec-3",
                            "start_offset": 1800,
                            "end_offset": 1850
                        }
                    ]
                }
            ]
        }
        
        is_valid, errors, metrics = validator.validate_analysis(hr5_analysis)
        
        # Golden standard assertions
        assert is_valid, f"HR5 golden example failed validation: {errors}"
        assert metrics['evidence_coverage'] == 1.0, "HR5 must have 100% evidence coverage"
        assert metrics['invalid_citation_count'] == 0, "HR5 must have no invalid citations"
        
        # Check all citations have proper headings and anchor_ids
        for field in ['tldr', 'who_affects', 'why_matters', 'key_provisions']:
            if field in hr5_analysis:
                field_data = hr5_analysis[field]
                if isinstance(field_data, dict):
                    citations = field_data.get('citations', [])
                elif isinstance(field_data, list):
                    citations = []
                    for item in field_data:
                        citations.extend(item.get('citations', []))
                
                for citation in citations:
                    assert citation.get('heading'), f"HR5 {field}: Citation missing heading"
                    assert citation.get('anchor_id'), f"HR5 {field}: Citation missing anchor_id"
                    assert len(citation.get('quote', '')) >= 12, f"HR5 {field}: Quote too short"
    
    def test_hr7_bad_example_rejection(self, validator):
        """Test that HR7 bad example is correctly rejected"""
        
        # HR7 bad example (current poor quality)
        hr7_bad_analysis = {
            "tldr": {
                "content": "The bill prohibits federal funding for abortions and requires disclosure.",
                "citations": [
                    {
                        "quote": "Funding for Abortion and Abortion Insurance Full Disclosure",
                        "heading": None,  # ❌ NULL HEADING
                        "anchor_id": None,  # ❌ NULL ANCHOR_ID
                        "start_offset": 2639,
                        "end_offset": 2698
                    }
                ]
            },
            "who_affects": [
                {
                    "content": "All provisions are effective immediately upon enactment.",  # ❌ GENERIC
                    "citations": []  # ❌ NO CITATIONS
                }
            ],
            "why_matters": {
                "content": "This is important legislation.",  # ❌ VAGUE
                "citations": []  # ❌ NO CITATIONS
            },
            "key_provisions": [
                {
                    "content": "Technical provisions and administrative details",  # ❌ GENERIC PHRASE
                    "citations": []  # ❌ NO CITATIONS
                }
            ]
        }
        
        is_valid, errors, metrics = validator.validate_analysis(hr7_bad_analysis)
        
        # Must be rejected
        assert not is_valid, "HR7 bad example must be rejected"
        assert metrics['evidence_coverage'] < 1.0, "HR7 bad example must have poor coverage"
        assert metrics['invalid_citation_count'] > 0, "HR7 bad example must have invalid citations"
        
        # Check specific error types
        error_text = ' '.join(errors)
        assert 'null/empty heading' in error_text, "Must detect null heading"
        assert 'null/empty anchor_id' in error_text, "Must detect null anchor_id"
        assert 'No citations provided' in error_text, "Must detect missing citations"
    
    def test_budget_classification_accuracy(self, retriever):
        """Test budget classifier returns correct categories"""
        
        budget_test_text = """
        SEC. 2. AUTHORIZATION OF APPROPRIATIONS.
        There are authorized to be appropriated $50,000,000 for fiscal year 2024.
        
        SEC. 3. APPROPRIATIONS.
        There are appropriated out of any money in the Treasury $25,000,000.
        
        SEC. 4. PENALTIES.
        Any violation shall be subject to a civil penalty of not more than $10,000.
        
        SEC. 5. USER FEES.
        The agency may collect a surcharge of $100 per application.
        """
        
        metadata = {'bill_id': 'test', 'title': 'Budget Test Bill'}
        result = retriever.extract_enhanced_spans(budget_test_text, metadata)
        
        budget_spans = [s for s in result.get('evidence', []) if s.get('type') == 'budget']
        
        # Check budget categories are correctly classified
        categories_found = set(s.get('budget_category') for s in budget_spans if s.get('budget_category'))
        
        assert 'authorization' in categories_found, "Must detect authorization category"
        assert 'appropriation' in categories_found, "Must detect appropriation category"
        assert 'penalty' in categories_found, "Must detect penalty category"
        assert 'surcharge' in categories_found, "Must detect surcharge category"
    
    def test_mandate_detection_accuracy(self, retriever):
        """Test mandate detection finds key requirements"""
        
        mandate_test_text = """
        SEC. 1. REQUIREMENTS.
        Each agency shall comply with this Act not later than 180 days.
        No funds may be used for administrative costs exceeding 10 percent.
        The Secretary must enforce these provisions through regular monitoring.
        Violations are prohibited and subject to penalties.
        """
        
        metadata = {'bill_id': 'test', 'title': 'Mandate Test Bill'}
        result = retriever.extract_enhanced_spans(mandate_test_text, metadata)
        
        mandate_spans = [s for s in result.get('evidence', []) if s.get('type') == 'mandate']
        
        # Must find mandate language
        assert len(mandate_spans) >= 3, "Must detect multiple mandate spans"
        
        # Check for key mandate words in quotes
        all_quotes = ' '.join(s.get('quote', '') for s in mandate_spans)
        assert 'shall' in all_quotes.lower(), "Must detect 'shall' mandates"
        assert 'must' in all_quotes.lower(), "Must detect 'must' mandates"
        assert 'prohibited' in all_quotes.lower(), "Must detect 'prohibited' language"
    
    def test_quality_gates_fail_closed(self, details_service):
        """Test that quality gates fail closed for bad content"""
        
        bad_payload = {
            "hero_summary": "Generic bill summary with no specifics.",
            "hero_summary_citations": [],  # ❌ NO CITATIONS
            "overview": {
                "what_does": {
                    "content": "Various provisions and technical details",  # ❌ GENERIC
                    "citations": []  # ❌ NO CITATIONS
                }
            }
        }
        
        result = details_service._apply_quality_gates(bad_payload)
        
        # Must fail quality gates
        assert not result['passed'], "Bad content must fail quality gates"
        assert result['quality_score'] < 0.8, "Bad content must have low quality score"
        assert len(result['errors']) > 0, "Bad content must have errors"
    
    def test_routing_logic_accuracy(self, retriever):
        """Test that routing logic correctly identifies high vs medium priority"""
        
        # High priority bill (money + mandates)
        high_priority_text = """
        SEC. 1. There are authorized to be appropriated $1,000,000.
        SEC. 2. Each agency shall comply with these requirements.
        """
        
        # Medium priority bill (no money/mandates)
        medium_priority_text = """
        SEC. 1. FINDINGS.
        Congress finds that education is important.
        SEC. 2. SENSE OF CONGRESS.
        It is the sense of Congress that states should improve education.
        """
        
        metadata = {'bill_id': 'test', 'title': 'Test Bill'}
        
        high_result = retriever.extract_enhanced_spans(high_priority_text, metadata)
        medium_result = retriever.extract_enhanced_spans(medium_priority_text, metadata)
        
        assert high_result.get('routing') == 'high', "Money + mandates must route to high"
        assert high_result.get('has_money'), "Must detect money language"
        assert high_result.get('has_mandates'), "Must detect mandate language"
        
        assert medium_result.get('routing') == 'medium', "Findings/sense must route to medium"
        assert not medium_result.get('has_money'), "Must not detect money in findings"
        assert not medium_result.get('has_mandates'), "Must not detect mandates in findings"

# Pytest configuration
if __name__ == "__main__":
    pytest.main([__file__, "-v"])
