"""
Bill Importance Scoring Service

Analyzes bills to determine their importance and impact level.
Important bills automatically get AI processing, while less important ones can be processed on-demand.
"""

import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum


class ImportanceLevel(str, Enum):
    CRITICAL = "critical"      # 90-100: Major legislation, constitutional amendments
    HIGH = "high"             # 70-89: Significant policy changes, budget bills
    MEDIUM = "medium"         # 40-69: Standard legislation with moderate impact
    LOW = "low"              # 20-39: Minor bills, technical corrections
    MINIMAL = "minimal"       # 0-19: Ceremonial, naming, commemorative


@dataclass
class ImportanceScore:
    score: int  # 0-100
    level: ImportanceLevel
    reason: str
    auto_process: bool  # Whether to automatically send to AI
    key_indicators: List[str]
    budget_impact: Optional[str] = None
    affected_population: Optional[str] = None


class BillImportanceScorer:
    """Scores bills based on content analysis to determine processing priority"""
    
    def __init__(self):
        self.high_impact_keywords = {
            # Constitutional and fundamental rights
            'constitutional': 15,
            'amendment': 15,
            'constitution': 15,
            'civil rights': 15,
            'voting rights': 18,
            'voting': 12,
            'election': 12,
            'voter access': 15,
            'political spending': 12,
            'ethics': 8,
            'human rights': 10,
            
            # Major policy areas
            'healthcare': 15,
            'health insurance': 12,
            'medicare for all': 20,
            'comprehensive': 8,
            'national': 8,
            'replacing': 8,
            'climate change': 12,
            'environment': 8,
            'immigration': 10,
            'tax': 8,
            'taxes': 8,
            'taxation': 8,
            'budget': 10,
            'appropriation': 8,
            'spending': 6,
            
            # Economic impact
            'trillion': 15,
            'billion': 10,
            'million': 5,
            'economic': 8,
            'employment': 8,
            'unemployment': 8,
            'minimum wage': 10,
            'social security': 10,
            'medicare': 10,
            'medicaid': 10,
            
            # Security and defense
            'national security': 12,
            'defense': 8,
            'military': 8,
            'homeland security': 10,
            'terrorism': 10,
            'cybersecurity': 8,
            
            # Education and social
            'education': 8,
            'student loan': 8,
            'housing': 8,
            'infrastructure': 10,
            'transportation': 6,
            
            # Technology and innovation
            'artificial intelligence': 8,
            'technology': 6,
            'innovation': 6,
            'research': 6,
            
            # Justice and law enforcement
            'criminal justice': 10,
            'police': 8,
            'law enforcement': 8,
            'prison': 8,
            'sentencing': 8,
            
            # International relations
            'trade': 8,
            'tariff': 8,
            'international': 6,
            'foreign policy': 8,
            'sanctions': 8,
        }
        
        self.low_impact_keywords = {
            # Ceremonial and naming
            'post office': -10,
            'post offices': -10,
            'designate': -8,
            'designation': -8,
            'naming': -10,
            'commemorate': -8,
            'commemorative': -8,
            'honor': -5,
            'honoring': -5,
            'recognize': -5,
            'recognition': -5,
            'awareness': -5,
            'week': -8,
            'day': -8,
            'month': -5,
            
            # Administrative/technical
            'technical correction': -10,
            'clerical': -8,
            'administrative': -5,
            'procedural': -5,
            'clarification': -5,
            'clarify': -5,
            
            # Minor adjustments
            'minor': -8,
            'small': -5,
            'technical': -5,
        }
        
        self.urgency_indicators = {
            'emergency': 15,
            'urgent': 10,
            'immediate': 10,
            'crisis': 12,
            'disaster': 10,
            'relief': 8,
        }
        
        self.scope_indicators = {
            'nationwide': 10,
            'federal': 8,
            'all states': 10,
            'universal': 12,
            'comprehensive': 8,
            'reform': 10,
            'overhaul': 12,
        }
    
    def score_bill(self, title: str, summary: str = "", bill_number: str = "") -> ImportanceScore:
        """
        Score a bill's importance based on title, summary, and bill characteristics
        
        Returns ImportanceScore with recommendation for AI processing
        """
        score = 0
        indicators = []
        
        # Combine text for analysis
        text = f"{title} {summary}".lower()
        
        # 1. Base score from bill type
        if bill_number:
            type_score, type_indicator = self._score_bill_type(bill_number)
            score += type_score
            if type_indicator:
                indicators.append(type_indicator)
        
        # 2. Content keyword scoring
        keyword_score, keyword_indicators = self._score_keywords(text)
        score += keyword_score
        indicators.extend(keyword_indicators)
        
        # 3. Urgency and scope modifiers
        urgency_score, urgency_indicators = self._score_urgency_and_scope(text)
        score += urgency_score
        indicators.extend(urgency_indicators)
        
        # 4. Length and complexity indicators
        complexity_score, complexity_indicators = self._score_complexity(title, summary)
        score += complexity_score
        indicators.extend(complexity_indicators)
        
        # 5. Budget impact detection
        budget_impact = self._detect_budget_impact(text)
        if budget_impact:
            score += 10
            indicators.append(f"Budget impact: {budget_impact}")
        
        # 6. Population impact estimation
        population_impact = self._estimate_population_impact(text)
        
        # Normalize score to 0-100
        score = max(0, min(100, score))
        
        # Determine importance level and auto-processing
        level, auto_process, reason = self._determine_level_and_processing(score, indicators)
        
        return ImportanceScore(
            score=score,
            level=level,
            reason=reason,
            auto_process=auto_process,
            key_indicators=indicators[:5],  # Top 5 indicators
            budget_impact=budget_impact,
            affected_population=population_impact
        )
    
    def _score_bill_type(self, bill_number: str) -> Tuple[int, Optional[str]]:
        """Score based on bill type (HR, S, HJRes, etc.)"""
        bill_number = bill_number.upper()
        
        if any(x in bill_number for x in ['HJRES', 'SJRES']):
            return 15, "Joint resolution (high impact potential)"
        elif any(x in bill_number for x in ['HRES', 'SRES']):
            return -5, "Simple resolution (typically procedural)"
        elif bill_number.startswith('HR'):
            return 5, "House bill"
        elif bill_number.startswith('S'):
            return 5, "Senate bill"
        
        return 0, None
    
    def _score_keywords(self, text: str) -> Tuple[int, List[str]]:
        """Score based on keyword analysis"""
        score = 0
        indicators = []
        
        # High impact keywords
        for keyword, points in self.high_impact_keywords.items():
            if keyword in text:
                score += points
                indicators.append(f"High impact: {keyword} (+{points})")
        
        # Low impact keywords (reduce score)
        for keyword, points in self.low_impact_keywords.items():
            if keyword in text:
                score += points  # points are negative
                indicators.append(f"Low impact: {keyword} ({points})")
        
        return score, indicators
    
    def _score_urgency_and_scope(self, text: str) -> Tuple[int, List[str]]:
        """Score based on urgency and scope indicators"""
        score = 0
        indicators = []
        
        # Urgency indicators
        for indicator, points in self.urgency_indicators.items():
            if indicator in text:
                score += points
                indicators.append(f"Urgency: {indicator} (+{points})")
        
        # Scope indicators
        for indicator, points in self.scope_indicators.items():
            if indicator in text:
                score += points
                indicators.append(f"Scope: {indicator} (+{points})")
        
        return score, indicators
    
    def _score_complexity(self, title: str, summary: str) -> Tuple[int, List[str]]:
        """Score based on text complexity and length"""
        score = 0
        indicators = []
        
        # Title length indicator
        if len(title) > 100:
            score += 5
            indicators.append("Long title suggests complexity (+5)")
        
        # Summary length indicator
        if len(summary) > 500:
            score += 8
            indicators.append("Detailed summary suggests complexity (+8)")
        elif len(summary) > 200:
            score += 3
            indicators.append("Moderate summary length (+3)")
        
        # Multiple sections/subsections
        if summary.count('section') > 3:
            score += 5
            indicators.append("Multiple sections (+5)")
        
        return score, indicators
    
    def _detect_budget_impact(self, text: str) -> Optional[str]:
        """Detect and estimate budget impact from text"""
        # Dollar amount patterns
        dollar_patterns = [
            r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)\s*(trillion|billion|million)',
            r'(\d+(?:,\d{3})*)\s*(?:trillion|billion|million)\s*dollars?',
        ]
        
        for pattern in dollar_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                if 'trillion' in match.group().lower():
                    return "Trillion-dollar impact"
                elif 'billion' in match.group().lower():
                    return "Billion-dollar impact"
                elif 'million' in match.group().lower():
                    return "Million-dollar impact"
        
        # Funding keywords
        funding_keywords = ['appropriation', 'funding', 'budget', 'spending', 'allocation']
        if any(keyword in text for keyword in funding_keywords):
            return "Budget/funding implications"
        
        return None
    
    def _estimate_population_impact(self, text: str) -> Optional[str]:
        """Estimate affected population size"""
        population_indicators = {
            'all americans': 'National (330M+ people)',
            'nationwide': 'National (330M+ people)',
            'federal': 'National (330M+ people)',
            'universal': 'National (330M+ people)',
            'medicare': 'Senior citizens (65M+ people)',
            'medicaid': 'Low-income families (80M+ people)',
            'social security': 'Retirees and disabled (70M+ people)',
            'students': 'Student population (50M+ people)',
            'veterans': 'Veterans (18M+ people)',
            'seniors': 'Senior citizens (55M+ people)',
            'children': 'Children under 18 (75M+ people)',
            'workers': 'Working population (160M+ people)',
            'families': 'American families (130M+ people)',
        }
        
        for indicator, impact in population_indicators.items():
            if indicator in text:
                return impact
        
        return None
    
    def _determine_level_and_processing(self, score: int, indicators: List[str]) -> Tuple[ImportanceLevel, bool, str]:
        """Determine importance level, auto-processing decision, and reason"""
        
        if score >= 85:
            return ImportanceLevel.CRITICAL, True, "Critical legislation with major national impact"
        elif score >= 60:
            return ImportanceLevel.HIGH, True, "High-impact legislation affecting significant populations"
        elif score >= 35:
            return ImportanceLevel.MEDIUM, True, "Moderate impact legislation worthy of detailed analysis"
        elif score >= 15:
            return ImportanceLevel.LOW, False, "Lower impact legislation - manual analysis available on request"
        else:
            return ImportanceLevel.MINIMAL, False, "Minimal impact legislation (ceremonial, naming, etc.)"


def score_bill_importance(title: str, summary: str = "", bill_number: str = "") -> ImportanceScore:
    """Convenience function to score a single bill"""
    scorer = BillImportanceScorer()
    return scorer.score_bill(title, summary, bill_number)


# Example usage and testing
if __name__ == "__main__":
    scorer = BillImportanceScorer()
    
    # Test cases
    test_bills = [
        {
            "title": "To designate the facility of the United States Postal Service located at 123 Main Street in Anytown as the 'John Doe Post Office Building'.",
            "summary": "This bill designates a post office building.",
            "bill_number": "HR1234"
        },
        {
            "title": "For the People Act of 2021",
            "summary": "This bill addresses voter access, election integrity, election security, political spending, and ethics for the three branches of government. The bill expands voter registration and voting access and limits removing voters from voter rolls.",
            "bill_number": "HR1"
        },
        {
            "title": "Infrastructure Investment and Jobs Act",
            "summary": "This bill provides $1.2 trillion in funding for roads, bridges, broadband, and other infrastructure improvements nationwide.",
            "bill_number": "HR3684"
        }
    ]
    
    for bill in test_bills:
        result = scorer.score_bill(bill["title"], bill["summary"], bill["bill_number"])
        print(f"\nBill: {bill['bill_number']}")
        print(f"Title: {bill['title'][:80]}...")
        print(f"Score: {result.score}/100 ({result.level.value})")
        print(f"Auto-process: {result.auto_process}")
        print(f"Reason: {result.reason}")
        print(f"Key indicators: {result.key_indicators}")