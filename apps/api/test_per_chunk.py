#!/usr/bin/env python3
"""
Test the new per-chunk analysis system
"""
import asyncio
import sys
sys.path.append('.')
from app.db.database import get_db
from app.services.ai_service import AIService
from app.services.balanced_analysis_service import BalancedAnalysisService
from app.services.intelligent_evidence_extractor import get_intelligent_extractor
from app.models.bill import Bill

async def test_per_chunk_analysis():
    print('🔥 TESTING PER-CHUNK ANALYSIS SYSTEM')
    
    # Setup
    ai_service = AIService()
    balanced_service = BalancedAnalysisService(ai_service)
    evidence_extractor = get_intelligent_extractor()
    db = next(get_db())
    
    # Get bill
    bill = db.query(Bill).filter(Bill.bill_number == 'HR4922').first()
    print(f'Testing with: {bill.title}')
    
    # Extract evidence chunks
    bill_metadata = {'title': bill.title, 'bill_number': bill.bill_number, 'session_year': bill.session_year, 'bill_id': str(bill.id)}
    evidence_result = await evidence_extractor.extract_intelligent_evidence(bill.full_text or '', bill_metadata)
    evidence_spans = [{'id': f'span_{i}', 'quote': ev.content, 'heading': ev.heading, 'start_offset': ev.start_offset, 'end_offset': ev.end_offset, 'importance_score': ev.confidence_score, 'quality_metrics': {'quality_level': 'high' if ev.confidence_score > 0.7 else 'medium', 'grounding_value': ev.confidence_score}} for i, ev in enumerate(evidence_result)]
    
    print(f'📋 Evidence chunks extracted: {len(evidence_spans)}')
    for i, span in enumerate(evidence_spans):
        print(f'  {i+1}. {span["heading"][:60]}...')
    
    # Run per-chunk analysis
    print('\n🔥 STARTING PER-CHUNK ANALYSIS')
    print(f'Each of {len(evidence_spans)} chunks gets individual OpenAI call')
    
    analysis_result = await balanced_service.analyze_bill_balanced(bill.full_text or '', bill_metadata, evidence_spans)
    
    if analysis_result['success']:
        sections = analysis_result['analysis'].get('complete_analysis', [])
        print(f'\n✅ SUCCESS: {len(sections)} sections generated')
        
        # Show first few sections
        for i, section in enumerate(sections[:3]):
            print(f'\n--- SECTION {i+1} ---')
            print(f'Title: {section.get("title", "No title")}')
            print(f'Who it affects: {section.get("who_it_affects", "Not specified")}')
            print(f'Why it matters: {section.get("why_it_matters", "Not specified")}')
            print(f'Detailed summary: {section.get("detailed_summary", "Not specified")[:200]}...')
            print(f'Key actions: {section.get("key_actions", [])}')
        
        # Check if we have details_payload
        details_payload = analysis_result.get('details_payload')
        if details_payload:
            print('\n📊 BILL DETAILS PAYLOAD READY')
            print(f'Hero summary: {details_payload.get("hero_summary", "Missing")[:100]}...')
            print(f'Overview sections: {len(details_payload.get("overview", {}).get("complete_analysis", []))}')
        
        cost = analysis_result.get('cost_breakdown', {}).get('total_cost', 0)
        print(f'\n💰 Total cost: ${cost:.4f}')
        
    else:
        print(f'❌ Analysis failed: {analysis_result.get("error")}')
    
    db.close()

if __name__ == "__main__":
    asyncio.run(test_per_chunk_analysis())