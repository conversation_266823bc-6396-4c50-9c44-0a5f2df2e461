import React from 'react';

interface GroupPillProps {
  group: string;
}

export const GroupPill: React.FC<GroupPillProps> = ({ group }) => {
  const getGroupIcon = (group: string) => {
    const lowerGroup = group.toLowerCase();
    if (lowerGroup.includes('business') || lowerGroup.includes('company')) return '🏢';
    if (lowerGroup.includes('consumer') || lowerGroup.includes('family')) return '👥';
    if (lowerGroup.includes('environment') || lowerGroup.includes('climate')) return '🌱';
    if (lowerGroup.includes('worker') || lowerGroup.includes('labor')) return '👷';
    if (lowerGroup.includes('energy') || lowerGroup.includes('oil') || lowerGroup.includes('gas')) return '⚡';
    if (lowerGroup.includes('government') || lowerGroup.includes('agency')) return '🏛️';
    if (lowerGroup.includes('state') || lowerGroup.includes('local')) return '🗺️';
    return '📋';
  };

  return (
    <span className="inline-flex items-center gap-1.5 bg-green-100 text-green-800 h-8 px-3 rounded-full text-xs font-medium">
      <span className="text-sm">{getGroupIcon(group)}</span>
      {group}
    </span>
  );
};
