# app/models/bill_values.py
"""
Models for bill values analysis and content moderation system.

This module contains the database models for analyzing bills against our core values:
- Democracy protection and democratic processes
- Human rights and civil liberties  
- Environmental justice and sustainability

The system provides AI-powered analysis with human oversight capabilities.
"""

from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, ForeignKey, DECIMAL
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.base_class import Base
from app.db.types import get_uuid_type  # UUID string for now; production can switch to postgresql.UUID(as_uuid=True)


class BillValuesAnalysis(Base):
    """
    Model for storing comprehensive values analysis of bills.
    
    This table stores the AI-generated analysis of how bills align with or threaten
    our core values, along with scoring, confidence metrics, and moderation flags.
    """
    __tablename__ = "bill_values_analysis"

    # Foreign key to the bill
    bill_id = Column(String(255), ForeignKey("bills.id"), nullable=False, index=True)

    # Democracy Scores (1-10 scale, 10 = strongest threat/support)
    democracy_threat_score = Column(Integer, default=0, nullable=False)
    democracy_support_score = Column(Integer, default=0, nullable=False)
    
    # Human Rights Scores (1-10 scale)
    human_rights_threat_score = Column(Integer, default=0, nullable=False)
    human_rights_support_score = Column(Integer, default=0, nullable=False)
    
    # Environmental Scores (1-10 scale)
    environmental_threat_score = Column(Integer, default=0, nullable=False)
    environmental_support_score = Column(Integer, default=0, nullable=False)
    
    # Overall Assessment Categories
    overall_threat_level = Column(String(20), nullable=True)  # 'none', 'low', 'medium', 'high', 'critical'
    overall_support_level = Column(String(20), nullable=True)  # 'none', 'low', 'medium', 'high', 'strong'
    
    # AI Analysis Details (JSON for structured reasoning)
    analysis_reasoning = Column(Text, nullable=True)
    confidence_score = Column(DECIMAL(3, 2), nullable=True)  # 0.00 to 1.00

    # Moderation and Review Flags
    requires_human_review = Column(Boolean, default=False, nullable=False)
    is_flagged = Column(Boolean, default=False, nullable=False)
    is_blocked = Column(Boolean, default=False, nullable=False)
    
    # Human Review Tracking
    reviewed_by = Column(get_uuid_type(), ForeignKey("users.id"), nullable=True)
    reviewed_at = Column(DateTime, nullable=True)
    review_notes = Column(Text, nullable=True)
    
    # Processing Metadata
    analyzed_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    ai_model_version = Column(String(50), nullable=True)  # Track which AI model version was used
    
    # Relationships
    bill = relationship("Bill", back_populates="values_analysis")
    reviewer = relationship("User", foreign_keys=[reviewed_by])
    values_tags = relationship("BillValuesTag", back_populates="analysis", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<BillValuesAnalysis(bill_id='{self.bill_id}', threat_level='{self.overall_threat_level}', support_level='{self.overall_support_level}')>"

    @property
    def max_threat_score(self) -> int:
        """Get the highest threat score across all categories"""
        return max(
            self.democracy_threat_score,
            self.human_rights_threat_score,
            self.environmental_threat_score
        )

    @property
    def max_support_score(self) -> int:
        """Get the highest support score across all categories"""
        return max(
            self.democracy_support_score,
            self.human_rights_support_score,
            self.environmental_support_score
        )

    @property
    def needs_attention(self) -> bool:
        """Check if this analysis requires immediate attention"""
        return (
            self.max_threat_score >= 7 or  # High threat scores
            self.requires_human_review or
            self.is_flagged or
            (self.confidence_score and self.confidence_score < 0.7)  # Low confidence
        )


class BillValuesTag(Base):
    """
    Model for storing user-facing tags derived from values analysis.
    
    This table stores the curated, neutral-language tags that will be displayed
    to users on the frontend. These are derived from the raw analysis but use
    carefully crafted neutral language.
    """
    __tablename__ = "bill_values_tags"

    # Foreign keys
    bill_id = Column(String(255), ForeignKey("bills.id"), nullable=False, index=True)
    analysis_id = Column(get_uuid_type(), ForeignKey("bill_values_analysis.id"), nullable=False, index=True)
    
    # Tag Classification
    tag_category = Column(String(50), nullable=False, index=True)  # 'democracy', 'human_rights', 'environment'
    tag_type = Column(String(20), nullable=False, index=True)  # 'threat', 'support', 'neutral', 'impact'
    
    # Tag Content (neutral language for public display)
    tag_name = Column(String(100), nullable=False)  # Internal identifier
    display_text = Column(String(200), nullable=False)  # User-facing neutral text
    description = Column(Text, nullable=True)  # Longer explanation if needed
    
    # Severity and Priority
    severity_level = Column(Integer, nullable=False, default=1)  # 1-10 scale
    display_priority = Column(Integer, nullable=False, default=5)  # For frontend ordering
    
    # Visual Styling Hints
    color_theme = Column(String(20), nullable=True)  # 'blue', 'green', 'orange', 'red' for neutral styling
    icon_name = Column(String(50), nullable=True)  # Icon identifier for frontend
    
    # Metadata
    is_active = Column(Boolean, default=True, nullable=False)  # Can be disabled without deletion
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    bill = relationship("Bill")
    analysis = relationship("BillValuesAnalysis", back_populates="values_tags")

    def __repr__(self):
        return f"<BillValuesTag(bill_id='{self.bill_id}', category='{self.tag_category}', display_text='{self.display_text}')>"

    @property
    def is_high_priority(self) -> bool:
        """Check if this tag should be prominently displayed"""
        return self.severity_level >= 7 or self.display_priority >= 8


# Add indexes for performance
from sqlalchemy import Index

# Indexes for BillValuesAnalysis
Index('idx_bill_values_analysis_bill_id', BillValuesAnalysis.bill_id)
Index('idx_bill_values_analysis_threat_level', BillValuesAnalysis.overall_threat_level)
Index('idx_bill_values_analysis_support_level', BillValuesAnalysis.overall_support_level)
Index('idx_bill_values_analysis_flagged', BillValuesAnalysis.is_flagged)
Index('idx_bill_values_analysis_review_needed', BillValuesAnalysis.requires_human_review)

# Indexes for BillValuesTag
Index('idx_bill_values_tags_bill_id', BillValuesTag.bill_id)
Index('idx_bill_values_tags_category', BillValuesTag.tag_category)
Index('idx_bill_values_tags_type', BillValuesTag.tag_type)
Index('idx_bill_values_tags_active', BillValuesTag.is_active)
Index('idx_bill_values_tags_priority', BillValuesTag.display_priority)
