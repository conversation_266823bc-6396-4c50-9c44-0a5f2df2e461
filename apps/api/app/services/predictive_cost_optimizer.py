"""
Predictive Cost Optimizer - Phase 4 Intelligent Cost Management
Predicts optimal cost allocation and processing strategies based on bill characteristics and learned patterns
"""

import logging
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict
import asyncio

from sqlalchemy.orm import Session
from sqlalchemy import and_, func

logger = logging.getLogger(__name__)

class CostCategory(Enum):
    """Categories of costs in bill processing"""
    AI_TOKENS = "ai_tokens"
    EVIDENCE_EXTRACTION = "evidence_extraction"
    QUALITY_VALIDATION = "quality_validation"
    ANALYSIS_GENERATION = "analysis_generation"
    MAPPING_VERIFICATION = "mapping_verification"
    OVERHEAD = "overhead"

class OptimizationStrategy(Enum):
    """Cost optimization strategies"""
    QUALITY_FIRST = "quality_first"      # Prioritize quality over cost
    BALANCED = "balanced"                # Balance quality and cost
    COST_EFFICIENT = "cost_efficient"   # Minimize cost while meeting quality thresholds
    BUDGET_CONSTRAINED = "budget_constrained"  # Hard budget limits

class PredictionConfidence(Enum):
    """Confidence levels for predictions"""
    HIGH = "high"       # >85% confidence
    MEDIUM = "medium"   # 65-85% confidence
    LOW = "low"         # 45-65% confidence
    UNCERTAIN = "uncertain"  # <45% confidence

@dataclass
class CostPrediction:
    """Cost prediction for bill processing"""
    bill_id: str
    bill_characteristics: Dict[str, Any]
    predicted_costs: Dict[CostCategory, float]
    total_predicted_cost: float
    confidence: PredictionConfidence
    confidence_score: float  # 0.0-1.0
    cost_breakdown: Dict[str, float]
    optimization_opportunities: List[str]
    risk_factors: List[str]

@dataclass
class OptimizationRecommendation:
    """Cost optimization recommendation"""
    recommendation_id: str
    strategy: OptimizationStrategy
    predicted_savings: float
    quality_impact: float  # Expected change in quality (-1.0 to 1.0)
    implementation_details: Dict[str, Any]
    success_probability: float  # 0.0-1.0
    prerequisites: List[str]
    monitoring_metrics: List[str]

@dataclass
class BudgetAllocation:
    """Optimized budget allocation for processing"""
    total_budget: float
    category_allocations: Dict[CostCategory, float]
    processing_strategy: str
    quality_target: float
    expected_quality: float
    buffer_percentage: float
    allocation_rationale: str

@dataclass
class CostOptimizationReport:
    """Comprehensive cost optimization analysis"""
    report_id: str
    timestamp: datetime
    analysis_period: str
    total_cost_analyzed: float
    potential_savings: float
    optimization_recommendations: List[OptimizationRecommendation]
    budget_recommendations: Dict[str, BudgetAllocation]
    cost_efficiency_metrics: Dict[str, float]
    trend_analysis: Dict[str, Any]

class PredictiveCostOptimizer:
    """
    Phase 4 Predictive Cost Optimization System that intelligently predicts
    and optimizes processing costs based on bill characteristics and learned patterns
    """
    
    def __init__(self, db: Session):
        self.db = db
        
        # Cost modeling data
        self.cost_history = defaultdict(list)
        self.pattern_cost_cache = {}
        
        # Prediction models (simplified ML-like approach)
        self.cost_prediction_models = self._initialize_cost_models()
        
        # Optimization configuration
        self.optimization_config = {
            'default_quality_target': 0.80,
            'minimum_quality_threshold': 0.65,
            'maximum_budget_per_bill': 0.30,
            'emergency_budget_multiplier': 1.5,
            'quality_cost_sensitivity': 0.1,  # Cost increase per 0.1 quality improvement
        }
        
        # Cost categories and their typical distributions
        self.cost_distributions = {
            CostCategory.AI_TOKENS: 0.60,           # 60% of total cost
            CostCategory.EVIDENCE_EXTRACTION: 0.15,  # 15% of total cost
            CostCategory.QUALITY_VALIDATION: 0.10,   # 10% of total cost
            CostCategory.ANALYSIS_GENERATION: 0.10,  # 10% of total cost
            CostCategory.MAPPING_VERIFICATION: 0.03, # 3% of total cost
            CostCategory.OVERHEAD: 0.02             # 2% of total cost
        }
        
        # Historical patterns for learning
        self.processing_patterns = defaultdict(lambda: {
            'cost_samples': [],
            'quality_samples': [],
            'time_samples': [],
            'success_rate': 1.0
        })
        
        # Budget optimization strategies
        self.budget_strategies = self._initialize_budget_strategies()
    
    async def predict_processing_cost(self, bill_metadata: Dict[str, Any],
                                    bill_text: str,
                                    processing_strategy: str = "balanced") -> CostPrediction:
        """
        Predict the cost of processing a bill based on its characteristics
        """
        
        logger.info(f"💰 Predicting processing cost for {bill_metadata.get('bill_number', 'Unknown')}")
        
        # Extract bill characteristics
        characteristics = await self._extract_bill_characteristics(bill_metadata, bill_text)
        
        # Find similar bills in history
        similar_patterns = self._find_similar_processing_patterns(characteristics)
        
        # Generate cost prediction
        predicted_costs = await self._generate_cost_prediction(characteristics, similar_patterns)
        
        # Calculate confidence
        confidence_score, confidence_level = self._calculate_prediction_confidence(
            characteristics, similar_patterns, predicted_costs
        )
        
        # Identify optimization opportunities
        optimization_opportunities = await self._identify_optimization_opportunities(
            characteristics, predicted_costs
        )
        
        # Identify risk factors
        risk_factors = self._identify_cost_risk_factors(characteristics, predicted_costs)
        
        # Calculate total predicted cost
        total_cost = sum(predicted_costs.values())
        
        # Create detailed cost breakdown
        cost_breakdown = {
            'base_processing': predicted_costs.get(CostCategory.AI_TOKENS, 0.0),
            'evidence_work': (predicted_costs.get(CostCategory.EVIDENCE_EXTRACTION, 0.0) +
                            predicted_costs.get(CostCategory.MAPPING_VERIFICATION, 0.0)),
            'quality_assurance': predicted_costs.get(CostCategory.QUALITY_VALIDATION, 0.0),
            'analysis_generation': predicted_costs.get(CostCategory.ANALYSIS_GENERATION, 0.0),
            'system_overhead': predicted_costs.get(CostCategory.OVERHEAD, 0.0)
        }
        
        prediction = CostPrediction(
            bill_id=bill_metadata.get('bill_id', 'unknown'),
            bill_characteristics=characteristics,
            predicted_costs=predicted_costs,
            total_predicted_cost=total_cost,
            confidence=confidence_level,
            confidence_score=confidence_score,
            cost_breakdown=cost_breakdown,
            optimization_opportunities=optimization_opportunities,
            risk_factors=risk_factors
        )
        
        logger.info(f"💡 Cost prediction complete: ${total_cost:.3f} ({confidence_level.value} confidence)")
        
        return prediction
    
    def _initialize_cost_models(self) -> Dict[str, Dict[str, Any]]:
        """Initialize cost prediction models for different bill characteristics"""
        
        return {
            'by_complexity': {
                'simple': {'base_cost': 0.05, 'variance': 0.01},
                'moderate': {'base_cost': 0.10, 'variance': 0.02},
                'complex': {'base_cost': 0.18, 'variance': 0.04},
                'omnibus': {'base_cost': 0.25, 'variance': 0.05},
                'mega': {'base_cost': 0.30, 'variance': 0.03}
            },
            
            'by_category': {
                'healthcare': {'multiplier': 1.2, 'quality_premium': 0.15},
                'defense': {'multiplier': 1.3, 'quality_premium': 0.18},
                'economy': {'multiplier': 1.1, 'quality_premium': 0.12},
                'environment': {'multiplier': 1.0, 'quality_premium': 0.10},
                'ceremonial': {'multiplier': 0.3, 'quality_premium': 0.02}
            },
            
            'by_urgency': {
                'routine': {'multiplier': 1.0},
                'priority': {'multiplier': 1.1},
                'urgent': {'multiplier': 1.3},
                'emergency': {'multiplier': 1.5}
            },
            
            'by_funding_level': {
                'none': {'evidence_cost': 0.01},
                'small': {'evidence_cost': 0.02},
                'medium': {'evidence_cost': 0.03},
                'large': {'evidence_cost': 0.05},
                'massive': {'evidence_cost': 0.08}
            }
        }
    
    def _initialize_budget_strategies(self) -> Dict[OptimizationStrategy, Dict[str, Any]]:
        """Initialize budget allocation strategies"""
        
        return {
            OptimizationStrategy.QUALITY_FIRST: {
                'quality_target': 0.90,
                'budget_ceiling': 0.30,
                'evidence_priority': 'high',
                'validation_level': 'comprehensive',
                'acceptable_cost_increase': 0.10
            },
            
            OptimizationStrategy.BALANCED: {
                'quality_target': 0.80,
                'budget_ceiling': 0.20,
                'evidence_priority': 'medium',
                'validation_level': 'standard',
                'acceptable_cost_increase': 0.05
            },
            
            OptimizationStrategy.COST_EFFICIENT: {
                'quality_target': 0.70,
                'budget_ceiling': 0.10,
                'evidence_priority': 'low',
                'validation_level': 'basic',
                'acceptable_cost_increase': 0.00
            },
            
            OptimizationStrategy.BUDGET_CONSTRAINED: {
                'quality_target': 0.65,
                'budget_ceiling': 0.05,
                'evidence_priority': 'minimal',
                'validation_level': 'basic',
                'acceptable_cost_increase': -0.02  # Actually need cost reduction
            }
        }
    
    async def _extract_bill_characteristics(self, bill_metadata: Dict[str, Any],
                                          bill_text: str) -> Dict[str, Any]:
        """Extract relevant characteristics for cost prediction"""
        
        characteristics = {
            # Basic characteristics
            'bill_number': bill_metadata.get('bill_number', ''),
            'bill_type': bill_metadata.get('bill_type', 'hr').lower(),
            'title_length': len(bill_metadata.get('title', '')),
            'summary_length': len(bill_metadata.get('summary', '')),
            
            # Text characteristics
            'text_length': len(bill_text),
            'section_count': len(re.findall(r'SEC\.|SECTION', bill_text, re.IGNORECASE)),
            'word_count': len(bill_text.split()),
            'paragraph_count': len(bill_text.split('\n\n')),
            
            # Complexity indicators
            'has_funding': bool(re.search(r'\$[\d,]+', bill_text)),
            'funding_amount_indicators': len(re.findall(r'\$[\d,]+', bill_text)),
            'enforcement_indicators': len(re.findall(r'penalty|fine|violation', bill_text, re.IGNORECASE)),
            'deadline_indicators': len(re.findall(r'within \d+|not later than|deadline', bill_text, re.IGNORECASE)),
            'cross_references': len(re.findall(r'section \d+|subsection', bill_text, re.IGNORECASE)),
            
            # Derived characteristics
            'estimated_complexity': self._estimate_complexity(bill_text),
            'estimated_category': self._estimate_category(bill_metadata, bill_text),
            'estimated_urgency': self._estimate_urgency(bill_metadata, bill_text),
            'estimated_political_sensitivity': self._estimate_political_sensitivity(bill_metadata, bill_text)
        }
        
        return characteristics
    
    def _estimate_complexity(self, bill_text: str) -> str:
        """Estimate bill complexity for cost modeling"""
        
        text_length = len(bill_text)
        section_count = len(re.findall(r'SEC\.|SECTION', bill_text, re.IGNORECASE))
        
        if text_length > 100000 or section_count > 100:
            return 'mega'
        elif text_length > 50000 or section_count > 50:
            return 'omnibus'
        elif text_length > 20000 or section_count > 15:
            return 'complex'
        elif text_length > 5000 or section_count > 5:
            return 'moderate'
        else:
            return 'simple'
    
    def _estimate_category(self, bill_metadata: Dict[str, Any], bill_text: str) -> str:
        """Estimate bill category for cost modeling"""
        
        full_text = (bill_metadata.get('title', '') + ' ' + 
                    bill_metadata.get('summary', '') + ' ' + 
                    bill_text[:2000]).lower()
        
        category_keywords = {
            'healthcare': ['health', 'medical', 'medicare', 'medicaid'],
            'defense': ['defense', 'military', 'armed forces', 'security'],
            'economy': ['economic', 'business', 'trade', 'financial'],
            'environment': ['environment', 'climate', 'pollution', 'conservation'],
            'ceremonial': ['naming', 'designation', 'commemoration', 'post office']
        }
        
        scores = {}
        for category, keywords in category_keywords.items():
            scores[category] = sum(full_text.count(keyword) for keyword in keywords)
        
        if scores:
            return max(scores.items(), key=lambda x: x[1])[0]
        else:
            return 'other'
    
    def _estimate_urgency(self, bill_metadata: Dict[str, Any], bill_text: str) -> str:
        """Estimate bill urgency for cost modeling"""
        
        full_text = (bill_metadata.get('title', '') + ' ' + bill_text[:1000]).lower()
        
        if 'emergency' in full_text or 'immediately' in full_text:
            return 'emergency'
        elif 'urgent' in full_text or 'expedited' in full_text:
            return 'urgent'
        elif 'priority' in full_text or re.search(r'within \d{1,2} days', full_text):
            return 'priority'
        else:
            return 'routine'
    
    def _estimate_political_sensitivity(self, bill_metadata: Dict[str, Any], bill_text: str) -> str:
        """Estimate political sensitivity for cost modeling"""
        
        sensitive_terms = ['abortion', 'gun', 'immigration', 'climate', 'tax increase']
        full_text = (bill_metadata.get('title', '') + ' ' + bill_text[:2000]).lower()
        
        sensitivity_score = sum(full_text.count(term) for term in sensitive_terms)
        
        if sensitivity_score >= 3:
            return 'high'
        elif sensitivity_score >= 1:
            return 'medium'
        else:
            return 'low'
    
    def _find_similar_processing_patterns(self, characteristics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find similar bill processing patterns from history"""
        
        similar_patterns = []
        target_complexity = characteristics['estimated_complexity']
        target_category = characteristics['estimated_category']
        
        # Look for exact matches first
        exact_pattern_key = f"{target_category}_{target_complexity}"
        if exact_pattern_key in self.processing_patterns:
            pattern_data = self.processing_patterns[exact_pattern_key]
            if len(pattern_data['cost_samples']) > 0:
                similar_patterns.append({
                    'pattern_key': exact_pattern_key,
                    'match_score': 1.0,
                    'data': pattern_data
                })
        
        # Look for complexity matches
        complexity_pattern_key = f"complexity_{target_complexity}"
        if complexity_pattern_key in self.processing_patterns:
            pattern_data = self.processing_patterns[complexity_pattern_key]
            if len(pattern_data['cost_samples']) > 0:
                similar_patterns.append({
                    'pattern_key': complexity_pattern_key,
                    'match_score': 0.8,
                    'data': pattern_data
                })
        
        # Look for category matches
        category_pattern_key = f"category_{target_category}"
        if category_pattern_key in self.processing_patterns:
            pattern_data = self.processing_patterns[category_pattern_key]
            if len(pattern_data['cost_samples']) > 0:
                similar_patterns.append({
                    'pattern_key': category_pattern_key,
                    'match_score': 0.7,
                    'data': pattern_data
                })
        
        return similar_patterns
    
    async def _generate_cost_prediction(self, characteristics: Dict[str, Any],
                                       similar_patterns: List[Dict[str, Any]]) -> Dict[CostCategory, float]:
        """Generate detailed cost prediction by category"""
        
        # Start with base cost model
        complexity = characteristics['estimated_complexity']
        category = characteristics['estimated_category']
        urgency = characteristics['estimated_urgency']
        
        base_cost_info = self.cost_prediction_models['by_complexity'].get(
            complexity, {'base_cost': 0.10, 'variance': 0.02}
        )
        base_cost = base_cost_info['base_cost']
        
        # Apply category multiplier
        category_info = self.cost_prediction_models['by_category'].get(
            category, {'multiplier': 1.0, 'quality_premium': 0.10}
        )
        category_multiplier = category_info['multiplier']
        
        # Apply urgency multiplier
        urgency_info = self.cost_prediction_models['by_urgency'].get(
            urgency, {'multiplier': 1.0}
        )
        urgency_multiplier = urgency_info['multiplier']
        
        # Calculate adjusted base cost
        adjusted_base_cost = base_cost * category_multiplier * urgency_multiplier
        
        # Learn from similar patterns if available
        if similar_patterns:
            pattern_costs = []
            for pattern in similar_patterns:
                cost_samples = pattern['data']['cost_samples']
                if cost_samples:
                    avg_cost = sum(cost_samples) / len(cost_samples)
                    weighted_cost = avg_cost * pattern['match_score']
                    pattern_costs.append(weighted_cost)
            
            if pattern_costs:
                learned_cost = sum(pattern_costs) / len(pattern_costs)
                # Blend learned cost with model prediction
                adjusted_base_cost = (adjusted_base_cost * 0.4) + (learned_cost * 0.6)
        
        # Distribute cost across categories
        predicted_costs = {}
        for cost_category, percentage in self.cost_distributions.items():
            category_cost = adjusted_base_cost * percentage
            
            # Apply specific adjustments
            if cost_category == CostCategory.EVIDENCE_EXTRACTION:
                # Higher evidence cost for funding bills
                if characteristics.get('has_funding', False):
                    category_cost *= 1.5
                # More evidence needed for complex bills
                if complexity in ['complex', 'omnibus', 'mega']:
                    category_cost *= 1.3
            
            elif cost_category == CostCategory.QUALITY_VALIDATION:
                # Higher validation for sensitive topics
                if characteristics.get('estimated_political_sensitivity') == 'high':
                    category_cost *= 1.4
            
            elif cost_category == CostCategory.AI_TOKENS:
                # Scale with text length
                text_length = characteristics.get('text_length', 10000)
                length_multiplier = min(2.0, max(0.5, text_length / 20000))
                category_cost *= length_multiplier
            
            predicted_costs[cost_category] = category_cost
        
        return predicted_costs
    
    def _calculate_prediction_confidence(self, characteristics: Dict[str, Any],
                                       similar_patterns: List[Dict[str, Any]],
                                       predicted_costs: Dict[CostCategory, float]) -> Tuple[float, PredictionConfidence]:
        """Calculate confidence in cost prediction"""
        
        confidence_score = 0.5  # Base confidence
        
        # Boost confidence based on similar patterns
        if similar_patterns:
            total_samples = sum(len(pattern['data']['cost_samples']) for pattern in similar_patterns)
            if total_samples >= 20:
                confidence_score += 0.3
            elif total_samples >= 10:
                confidence_score += 0.2
            elif total_samples >= 5:
                confidence_score += 0.1
        
        # Boost confidence for well-understood categories
        well_understood_categories = ['ceremonial', 'healthcare', 'defense']
        if characteristics['estimated_category'] in well_understood_categories:
            confidence_score += 0.1
        
        # Boost confidence for common complexity levels
        if characteristics['estimated_complexity'] in ['simple', 'moderate', 'complex']:
            confidence_score += 0.1
        
        # Reduce confidence for unusual characteristics
        total_cost = sum(predicted_costs.values())
        if total_cost > 0.25 or total_cost < 0.01:  # Unusual cost range
            confidence_score -= 0.15
        
        # Convert to confidence level
        if confidence_score >= 0.85:
            confidence_level = PredictionConfidence.HIGH
        elif confidence_score >= 0.65:
            confidence_level = PredictionConfidence.MEDIUM
        elif confidence_score >= 0.45:
            confidence_level = PredictionConfidence.LOW
        else:
            confidence_level = PredictionConfidence.UNCERTAIN
        
        return min(1.0, confidence_score), confidence_level
    
    async def _identify_optimization_opportunities(self, characteristics: Dict[str, Any],
                                                 predicted_costs: Dict[CostCategory, float]) -> List[str]:
        """Identify cost optimization opportunities"""
        
        opportunities = []
        total_cost = sum(predicted_costs.values())
        
        # High-cost reduction opportunities
        if total_cost > 0.20:
            opportunities.append("High predicted cost - consider cost-efficient processing strategy")
        
        # Evidence extraction optimization
        evidence_cost = predicted_costs.get(CostCategory.EVIDENCE_EXTRACTION, 0.0)
        if evidence_cost > 0.05:
            opportunities.append("High evidence extraction cost - consider selective evidence processing")
        
        # AI token optimization
        ai_cost = predicted_costs.get(CostCategory.AI_TOKENS, 0.0)
        if ai_cost > 0.15:
            opportunities.append("High AI token cost - consider bill chunking or model optimization")
        
        # Category-specific optimizations
        category = characteristics['estimated_category']
        if category == 'ceremonial':
            opportunities.append("Ceremonial bill detected - use template-based processing")
        
        complexity = characteristics['estimated_complexity']
        if complexity == 'simple' and total_cost > 0.08:
            opportunities.append("Simple bill with high predicted cost - review processing strategy")
        
        # Text length optimization
        text_length = characteristics.get('text_length', 0)
        if text_length > 50000 and total_cost > 0.25:
            opportunities.append("Large bill - consider section-by-section processing")
        
        return opportunities[:5]  # Top 5 opportunities
    
    def _identify_cost_risk_factors(self, characteristics: Dict[str, Any],
                                  predicted_costs: Dict[CostCategory, float]) -> List[str]:
        """Identify factors that could increase costs"""
        
        risk_factors = []
        
        # Complexity risks
        complexity = characteristics['estimated_complexity']
        if complexity in ['omnibus', 'mega']:
            risk_factors.append("High complexity may require additional processing iterations")
        
        # Political sensitivity risks
        if characteristics['estimated_political_sensitivity'] == 'high':
            risk_factors.append("High political sensitivity may require enhanced quality validation")
        
        # Urgency risks
        if characteristics['estimated_urgency'] in ['urgent', 'emergency']:
            risk_factors.append("Urgent timeline may require premium processing resources")
        
        # Technical complexity risks
        if characteristics.get('cross_references', 0) > 20:
            risk_factors.append("High cross-reference count may increase analysis complexity")
        
        # Funding analysis risks
        if characteristics.get('funding_amount_indicators', 0) > 10:
            risk_factors.append("Multiple funding amounts may require detailed financial analysis")
        
        return risk_factors
    
    async def optimize_budget_allocation(self, target_budget: float,
                                       quality_target: float = 0.80,
                                       strategy: OptimizationStrategy = OptimizationStrategy.BALANCED) -> BudgetAllocation:
        """
        Optimize budget allocation for processing based on strategy and targets
        """
        
        strategy_config = self.budget_strategies[strategy]
        
        # Adjust quality target based on strategy
        if quality_target > strategy_config['quality_target']:
            quality_target = strategy_config['quality_target']
        
        # Calculate category allocations based on strategy
        base_allocations = dict(self.cost_distributions)
        
        if strategy == OptimizationStrategy.QUALITY_FIRST:
            # Increase evidence and validation allocations
            base_allocations[CostCategory.EVIDENCE_EXTRACTION] *= 1.5
            base_allocations[CostCategory.QUALITY_VALIDATION] *= 2.0
            
        elif strategy == OptimizationStrategy.COST_EFFICIENT:
            # Reduce evidence and validation allocations
            base_allocations[CostCategory.EVIDENCE_EXTRACTION] *= 0.7
            base_allocations[CostCategory.QUALITY_VALIDATION] *= 0.5
            
        elif strategy == OptimizationStrategy.BUDGET_CONSTRAINED:
            # Minimize all non-essential allocations
            base_allocations[CostCategory.EVIDENCE_EXTRACTION] *= 0.5
            base_allocations[CostCategory.QUALITY_VALIDATION] *= 0.3
            base_allocations[CostCategory.MAPPING_VERIFICATION] *= 0.3
        
        # Normalize allocations to sum to 1.0
        total_allocation = sum(base_allocations.values())
        normalized_allocations = {
            category: (allocation / total_allocation) 
            for category, allocation in base_allocations.items()
        }
        
        # Apply to target budget
        category_budgets = {
            category: target_budget * percentage
            for category, percentage in normalized_allocations.items()
        }
        
        # Calculate expected quality
        expected_quality = self._predict_quality_from_allocation(category_budgets, strategy)
        
        # Calculate buffer
        buffer_percentage = 0.10 if strategy == OptimizationStrategy.QUALITY_FIRST else 0.05
        
        # Generate rationale
        rationale = f"Optimized for {strategy.value} strategy with {quality_target:.0%} quality target"
        
        return BudgetAllocation(
            total_budget=target_budget,
            category_allocations=category_budgets,
            processing_strategy=strategy.value,
            quality_target=quality_target,
            expected_quality=expected_quality,
            buffer_percentage=buffer_percentage,
            allocation_rationale=rationale
        )
    
    def _predict_quality_from_allocation(self, category_budgets: Dict[CostCategory, float],
                                       strategy: OptimizationStrategy) -> float:
        """Predict quality score based on budget allocation"""
        
        # Base quality from strategy
        base_quality = {
            OptimizationStrategy.QUALITY_FIRST: 0.85,
            OptimizationStrategy.BALANCED: 0.80,
            OptimizationStrategy.COST_EFFICIENT: 0.75,
            OptimizationStrategy.BUDGET_CONSTRAINED: 0.70
        }.get(strategy, 0.75)
        
        # Adjustments based on allocation
        evidence_budget = category_budgets.get(CostCategory.EVIDENCE_EXTRACTION, 0.0)
        validation_budget = category_budgets.get(CostCategory.QUALITY_VALIDATION, 0.0)
        
        # Quality boost from evidence work
        if evidence_budget > 0.03:
            base_quality += 0.05
        elif evidence_budget < 0.01:
            base_quality -= 0.08
        
        # Quality boost from validation
        if validation_budget > 0.02:
            base_quality += 0.03
        elif validation_budget < 0.005:
            base_quality -= 0.05
        
        return min(0.95, max(0.60, base_quality))
    
    async def record_actual_costs(self, bill_id: str, actual_costs: Dict[str, float],
                                actual_quality: float, processing_time: int) -> None:
        """Record actual processing costs for learning"""
        
        # Store in cost history
        cost_record = {
            'timestamp': datetime.utcnow(),
            'bill_id': bill_id,
            'total_cost': sum(actual_costs.values()),
            'quality': actual_quality,
            'processing_time': processing_time,
            'cost_breakdown': actual_costs
        }
        
        self.cost_history['all'].append(cost_record)
        
        # Update processing patterns (simplified pattern matching)
        # In a real system, this would use the bill characteristics to update specific patterns
        pattern_key = "general_pattern"  # Simplified for now
        
        pattern_data = self.processing_patterns[pattern_key]
        pattern_data['cost_samples'].append(cost_record['total_cost'])
        pattern_data['quality_samples'].append(actual_quality)
        pattern_data['time_samples'].append(processing_time)
        
        # Keep only recent samples
        for sample_list in [pattern_data['cost_samples'], pattern_data['quality_samples'], pattern_data['time_samples']]:
            if len(sample_list) > 100:
                sample_list.pop(0)
        
        logger.debug(f"Recorded actual costs for {bill_id}: ${cost_record['total_cost']:.3f}")
    
    async def generate_cost_optimization_report(self, analysis_period_days: int = 30) -> CostOptimizationReport:
        """Generate comprehensive cost optimization analysis report"""
        
        cutoff_date = datetime.utcnow() - timedelta(days=analysis_period_days)
        recent_costs = [
            record for record in self.cost_history['all']
            if record['timestamp'] > cutoff_date
        ]
        
        if not recent_costs:
            # Return empty report if no data
            return CostOptimizationReport(
                report_id=f"cost_optimization_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                timestamp=datetime.utcnow(),
                analysis_period=f"{analysis_period_days}d",
                total_cost_analyzed=0.0,
                potential_savings=0.0,
                optimization_recommendations=[],
                budget_recommendations={},
                cost_efficiency_metrics={},
                trend_analysis={}
            )
        
        # Calculate metrics
        total_cost = sum(record['total_cost'] for record in recent_costs)
        avg_cost = total_cost / len(recent_costs)
        avg_quality = sum(record['quality'] for record in recent_costs) / len(recent_costs)
        
        # Generate optimization recommendations
        recommendations = await self._generate_cost_optimization_recommendations(recent_costs)
        
        # Generate budget recommendations
        budget_recommendations = {}
        for strategy in OptimizationStrategy:
            allocation = await self.optimize_budget_allocation(
                target_budget=avg_cost,
                strategy=strategy
            )
            budget_recommendations[strategy.value] = allocation
        
        # Calculate cost efficiency metrics
        cost_efficiency = {
            'cost_per_quality_point': avg_cost / max(avg_quality, 0.1),
            'average_processing_cost': avg_cost,
            'cost_variance': self._calculate_variance([r['total_cost'] for r in recent_costs]),
            'quality_cost_correlation': self._calculate_correlation(
                [r['total_cost'] for r in recent_costs],
                [r['quality'] for r in recent_costs]
            )
        }
        
        # Trend analysis
        if len(recent_costs) >= 10:
            costs_over_time = [r['total_cost'] for r in recent_costs]
            trend_analysis = {
                'cost_trend': 'increasing' if costs_over_time[-5:] > costs_over_time[:5] else 'decreasing',
                'trend_slope': self._calculate_trend_slope(costs_over_time),
                'volatility': self._calculate_variance(costs_over_time)
            }
        else:
            trend_analysis = {'insufficient_data': True}
        
        # Estimate potential savings
        potential_savings = total_cost * 0.15  # Assume 15% savings potential
        
        return CostOptimizationReport(
            report_id=f"cost_optimization_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            timestamp=datetime.utcnow(),
            analysis_period=f"{analysis_period_days}d",
            total_cost_analyzed=total_cost,
            potential_savings=potential_savings,
            optimization_recommendations=recommendations,
            budget_recommendations=budget_recommendations,
            cost_efficiency_metrics=cost_efficiency,
            trend_analysis=trend_analysis
        )
    
    async def _generate_cost_optimization_recommendations(self, cost_records: List[Dict[str, Any]]) -> List[OptimizationRecommendation]:
        """Generate cost optimization recommendations based on historical data"""
        
        recommendations = []
        
        # Analyze cost efficiency
        inefficient_records = [r for r in cost_records if r['total_cost'] > 0.20 and r['quality'] < 0.75]
        
        if len(inefficient_records) > len(cost_records) * 0.2:  # >20% inefficient
            recommendations.append(OptimizationRecommendation(
                recommendation_id=f"efficiency_{datetime.utcnow().strftime('%Y%m%d')}",
                strategy=OptimizationStrategy.COST_EFFICIENT,
                predicted_savings=sum(r['total_cost'] for r in inefficient_records) * 0.3,
                quality_impact=-0.05,
                implementation_details={
                    'switch_to_cost_efficient_strategy': True,
                    'reduce_evidence_extraction': True,
                    'optimize_ai_token_usage': True
                },
                success_probability=0.8,
                prerequisites=["Pattern recognition system", "Quality monitoring"],
                monitoring_metrics=["cost_per_bill", "quality_score", "processing_time"]
            ))
        
        # High-cost optimization
        high_cost_records = [r for r in cost_records if r['total_cost'] > 0.25]
        if high_cost_records:
            recommendations.append(OptimizationRecommendation(
                recommendation_id=f"high_cost_{datetime.utcnow().strftime('%Y%m%d')}",
                strategy=OptimizationStrategy.BALANCED,
                predicted_savings=sum(r['total_cost'] for r in high_cost_records) * 0.2,
                quality_impact=0.0,
                implementation_details={
                    'implement_budget_caps': True,
                    'use_tiered_processing': True,
                    'optimize_evidence_selection': True
                },
                success_probability=0.7,
                prerequisites=["Budget allocation system", "Tiered processing"],
                monitoring_metrics=["budget_adherence", "cost_distribution"]
            ))
        
        return recommendations[:5]  # Top 5 recommendations
    
    def _calculate_variance(self, values: List[float]) -> float:
        """Calculate variance of values"""
        if len(values) < 2:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance
    
    def _calculate_correlation(self, x_values: List[float], y_values: List[float]) -> float:
        """Calculate correlation between two sets of values"""
        if len(x_values) != len(y_values) or len(x_values) < 2:
            return 0.0
        
        n = len(x_values)
        sum_x = sum(x_values)
        sum_y = sum(y_values)
        sum_xy = sum(x_values[i] * y_values[i] for i in range(n))
        sum_x2 = sum(x * x for x in x_values)
        sum_y2 = sum(y * y for y in y_values)
        
        numerator = n * sum_xy - sum_x * sum_y
        denominator_x = n * sum_x2 - sum_x * sum_x
        denominator_y = n * sum_y2 - sum_y * sum_y
        
        if denominator_x <= 0 or denominator_y <= 0:
            return 0.0
        
        correlation = numerator / (denominator_x * denominator_y) ** 0.5
        return correlation
    
    def _calculate_trend_slope(self, values: List[float]) -> float:
        """Calculate trend slope of values over time"""
        if len(values) < 2:
            return 0.0
        
        n = len(values)
        x_values = list(range(n))
        
        sum_x = sum(x_values)
        sum_y = sum(values)
        sum_xy = sum(x_values[i] * values[i] for i in range(n))
        sum_x2 = sum(x * x for x in x_values)
        
        denominator = n * sum_x2 - sum_x * sum_x
        if denominator == 0:
            return 0.0
        
        slope = (n * sum_xy - sum_x * sum_y) / denominator
        return slope

# Global instance
predictive_cost_optimizer = None

def get_predictive_cost_optimizer(db: Session) -> PredictiveCostOptimizer:
    """Get or create the global predictive cost optimizer instance"""
    global predictive_cost_optimizer
    if predictive_cost_optimizer is None:
        predictive_cost_optimizer = PredictiveCostOptimizer(db)
    return predictive_cost_optimizer