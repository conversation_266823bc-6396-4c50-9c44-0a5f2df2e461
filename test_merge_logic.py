#!/usr/bin/env python3
"""
Focused test to verify Complete Analysis + Secondary Analysis merge logic
"""
import sys
import os
sys.path.append('./apps/api')
os.environ['ENV_FILE'] = '.env.local'

import asyncio
from app.db.database import get_db
from app.services.unified_bill_processing_service import UnifiedBillProcessingService

async def test_merge_logic():
    print("🔧 FOCUSED TEST: Complete Analysis + Secondary Analysis Merge Logic")
    
    try:
        db = next(get_db())
        service = UnifiedBillProcessingService(db)
        
        print("Processing HR4922 with enhanced analysis enabled...")
        result = await service.process_bill_by_number(
            bill_number="HR4922",
            congress_session=119,
            environment="development",
            use_comprehensive=False,
            use_enhanced_analysis=True  # This triggers the merge logic
        )
        
        print(f"Processing result: {'✅ Success' if result.get('success') else '❌ Failed'}")
        
        if result.get('success'):
            print(f"Bill ID: {result.get('bill_id')}")
            print(f"Title: {result.get('title', 'Unknown')}")
            
            # Wait for database to update
            await asyncio.sleep(3)
            
            # Check the results directly from database
            from app.models.bill_details import BillDetails
            details = db.query(BillDetails).filter(BillDetails.seo_slug == 'hr4922-119').first()
            
            if details:
                print("✅ Bill details found in database!")
                overview = details.overview or {}
                
                # Check Complete Analysis
                complete_analysis = overview.get('complete_analysis', [])
                print(f"Complete Analysis sections: {len(complete_analysis)}")
                
                # Check Secondary Analysis fields
                secondary_fields = ['what_does', 'who_affects', 'why_matters', 'key_provisions', 'timeline', 'cost_impact']
                secondary_count = sum(1 for field in secondary_fields if field in overview)
                print(f"Secondary Analysis fields: {secondary_count}/6")
                
                for field in secondary_fields:
                    if field in overview:
                        field_data = overview[field]
                        if isinstance(field_data, dict):
                            content_len = len(field_data.get('content', ''))
                            citations_len = len(field_data.get('citations', []))
                            print(f"  ✅ {field}: {content_len} chars, {citations_len} citations")
                        else:
                            print(f"  ⚠️ {field}: {type(field_data)} (unexpected format)")
                
                # Verify merge worked correctly
                if len(complete_analysis) > 0 and secondary_count > 0:
                    print(f"\n🎉 SUCCESS: Both Complete Analysis ({len(complete_analysis)} sections) and Secondary Analysis ({secondary_count} fields) are present!")
                    print("✅ Merge logic is working correctly!")
                elif len(complete_analysis) > 0:
                    print(f"\n⚠️ PARTIAL: Complete Analysis working ({len(complete_analysis)} sections) but Secondary Analysis missing ({secondary_count} fields)")
                elif secondary_count > 0:
                    print(f"\n⚠️ PARTIAL: Secondary Analysis working ({secondary_count} fields) but Complete Analysis missing")
                else:
                    print("\n❌ FAILED: Neither Complete Analysis nor Secondary Analysis present")
            else:
                print("❌ No bill details found in database")
        else:
            print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
            
        db.close()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_merge_logic())