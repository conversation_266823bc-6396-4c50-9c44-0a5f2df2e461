# AI Pipeline Documentation
> Last updated: 2025-08-11


## Overview

The AI Pipeline transforms raw legislative text into structured, citation-based analysis. Every claim is backed by exact quotes from the source bill text, ensuring maximum transparency and verifiability.

## Architecture

```mermaid
graph TD
    A[Raw Bill Text] --> B[Text Ingestion Service]
    B --> C[Text Citation Service]
    C --> D[AI Analysis Service]
    D --> E[Citation Validation]
    E --> F[Bill Details Assembly]
    F --> G[Metrics Computation]
    G --> H[Database Storage]
```

## Core Services

### 1. Text Citation Service (`TextCitationService`)

**Purpose**: Segments bill text and validates citations

**Key Methods:**
- `build_source_index(full_text)` - Creates section index with offsets
- `bind_quote(full_text, index, quote)` - Validates and binds quotes
- `bind_citations_bulk(full_text, index, quotes)` - Bulk quote validation

**Implementation:**

```python
class TextCitationService:
    def __init__(self, heading_pattern: str = r"^SEC\.\s*[^\n]+|^Section\s+\d+|^TITLE\s+[IVXLC]+"):
        self.heading_regex = re.compile(heading_pattern, re.MULTILINE)

    def build_source_index(self, full_text: str) -> List[SourceIndexItem]:
        """Build index of sections with character offsets and anchor IDs."""
        items = []
        matches = list(self.heading_regex.finditer(full_text))

        for i, m in enumerate(matches):
            start = m.start()
            end = matches[i + 1].start() if i + 1 < len(matches) else len(full_text)
            items.append(SourceIndexItem(
                heading=m.group(0).strip(),
                start_offset=start,
                end_offset=end,
                anchor_id=f"sec-{i+1}"
            ))
        return items

    def bind_quote(self, full_text: str, source_index: List[SourceIndexItem], quote: str) -> Optional[CitationBinding]:
        """Find exact quote in text and return binding with metadata."""
        if not quote or not full_text:
            return None

        # Normalize whitespace for robust matching
        q = re.sub(r"\s+", " ", quote.strip())
        pos = full_text.find(q)

        if pos == -1:
            return None

        # Find containing section
        section = next((item for item in source_index if item.start_offset <= pos < item.end_offset), None)

        return CitationBinding(
            quote=quote,
            start_offset=pos,
            end_offset=pos + len(q),
            heading=section.heading if section else None,
            anchor_id=section.anchor_id if section else None,
        )
```

### 2. AI Analysis Service (`AIService`)

**Purpose**: Generates structured content with OpenAI GPT-4

**Key Method**: `generate_detailed_bill_analysis(bill_text, metadata)`

**Implementation:**

```python
async def generate_detailed_bill_analysis(self, bill_text: str, bill_metadata: dict) -> dict:
    """Generate structured sections with citations for BillDetails."""

    if not self.enabled:
        return self._get_fallback_data(bill_metadata)

    # Generate each section sequentially to avoid rate limits
    structured_summary = await self._generate_summary(bill_text, bill_metadata)
    support_reasons = await self._generate_support_reasons(bill_text, bill_metadata)
    oppose_reasons = await self._generate_oppose_reasons(bill_text, bill_metadata)
    amend_reasons = await self._generate_amend_reasons(bill_text, bill_metadata)
    message_templates = await self._generate_message_templates(bill_text, bill_metadata)
    tags = await self._generate_tags(bill_text, bill_metadata)

    # Return structured payload for BillDetails
    return {
        "hero_summary": structured_summary.get("tldr"),
        "overview": {
            "what_does": {"content": structured_summary.get("what_does", {}).get("content", ""), "citations": []},
            "who_affects": {"content": structured_summary.get("who_affects", {}).get("content", ""), "citations": []},
            "why_matters": {"content": structured_summary.get("why_matters", {}).get("content", ""), "citations": []},
            "key_provisions": [{"content": item, "citations": []} for item in structured_summary.get("key_provisions", {}).get("items", [])],
            "cost_impact": {"content": structured_summary.get("cost_impact", {}).get("content", ""), "citations": []},
            "timeline": [{"content": item, "citations": []} for item in structured_summary.get("timeline", {}).get("items", [])]
        },
        "positions": {
            "support_reasons": [{"claim": r, "justification": "", "citations": []} for r in support_reasons],
            "oppose_reasons": [{"claim": r, "justification": "", "citations": []} for r in oppose_reasons],
            "amend_reasons": [{"claim": r, "justification": "", "citations": []} for r in amend_reasons]
        },
        "message_templates": message_templates,
        "tags": tags,
        "other_details": []
    }
```

### 3. Bill Details Service (`BillDetailsService`)

**Purpose**: Orchestrates the complete pipeline with citation enrichment

**Key Method**: `create_or_update_details(bill, full_text, details_payload)`

**Pipeline Steps:**

1. **Source Index Creation**: Build section map with offsets
2. **Citation Enrichment**: Add missing citations to content
3. **Metrics Computation**: Calculate coverage and unverified counts
4. **Review Flag Setting**: Mark low-coverage content for human review
5. **Database Storage**: Save structured data

**Implementation:**

```python
def create_or_update_details(self, bill: Bill, full_text: str, details_payload: Dict[str, Any]) -> BillDetails:
    # Build source index
    source_index_items = self.text_service.build_source_index(full_text)

    # Enrich missing citations
    details_payload = self._enrich_citations(details_payload, full_text, source_index_items)

    # Compute metrics
    metrics = self._compute_metrics(details_payload)

    # Set review flag (require review if coverage < 60%)
    needs_review = metrics.get("coverage_ratio", 0.0) < 0.6

    # Create or update database record
    return self._save_to_database(bill, details_payload, metrics, needs_review)
```

## Citation Enrichment Algorithm

### Candidate Quote Generation

The system uses heuristic extraction to find potential quotes:

```python
def _generate_candidate_quotes(self, content: str, full_text: str, max_quotes: int = 2) -> List[str]:
    """Extract potential quotes using sliding window approach."""
    quotes = []
    text_lc = full_text.lower()

    # Split content into sentences
    sentences = re.split(r"(?<=[\.!?])\s+", content)

    for sent in sentences:
        words = sent.strip().split()

        # Try windows from 8 words down to 4
        for win_size in range(8, 3, -1):
            for i in range(len(words) - win_size + 1):
                phrase = " ".join(words[i:i+win_size])
                phrase_norm = re.sub(r"\s+", " ", phrase).strip().lower()

                if phrase_norm in text_lc:
                    # Find exact match in original text
                    idx = text_lc.find(phrase_norm)
                    if idx != -1:
                        original_quote = full_text[idx:idx + len(phrase_norm)]
                        if original_quote not in quotes:
                            quotes.append(original_quote)
                            break

            if len(quotes) >= max_quotes:
                break

    return quotes
```

### Citation Binding

Each candidate quote is validated and bound to source metadata:

```python
def _enrich_citations(self, details_payload: Dict[str, Any], full_text: str, source_index_items):
    """Add citations to sections lacking them."""

    def enrich_section(section: Dict[str, Any]):
        if not section or section.get("citations"):
            return  # Skip if already has citations

        content = section.get("content", "")
        quotes = self._generate_candidate_quotes(content, full_text)

        # Bind each quote to source text
        citations = []
        for quote in quotes:
            binding = self.text_service.bind_quote(full_text, source_index_items, quote)
            if binding:
                citations.append({
                    "quote": binding.quote,
                    "start_offset": binding.start_offset,
                    "end_offset": binding.end_offset,
                    "heading": binding.heading,
                    "anchor_id": binding.anchor_id,
                })

        section["citations"] = citations

    # Enrich all sections
    for section_name in ["what_does", "who_affects", "why_matters", "cost_impact"]:
        enrich_section(details_payload.get("overview", {}).get(section_name))

    # Enrich provisions and timeline items
    for item in details_payload.get("overview", {}).get("key_provisions", []):
        enrich_section(item)

    # Enrich position reasons
    for position_type in ["support_reasons", "oppose_reasons", "amend_reasons"]:
        for reason in details_payload.get("positions", {}).get(position_type, []):
            if not reason.get("citations"):
                content = reason.get("claim", "")
                quotes = self._generate_candidate_quotes(content, full_text)
                reason["citations"] = [self._bind_quote_to_citation(q, full_text, source_index_items) for q in quotes if self.text_service.bind_quote(full_text, source_index_items, q)]

    return details_payload
```

## Quality Assurance

### Metrics Computation

The system tracks coverage and quality metrics:

```python
def _compute_metrics(self, details_payload: Dict[str, Any]) -> Dict[str, Any]:
    total_sections = 0
    verified_sections = 0

    def count_sections(obj):
        nonlocal total_sections, verified_sections
        if isinstance(obj, dict) and "content" in obj:
            total_sections += 1
            if obj.get("citations") and len(obj["citations"]) > 0:
                verified_sections += 1
        elif isinstance(obj, (list, dict)):
            for item in (obj.values() if isinstance(obj, dict) else obj):
                count_sections(item)

    count_sections(details_payload)

    coverage_ratio = (verified_sections / total_sections) if total_sections > 0 else 0.0
    unverified_count = total_sections - verified_sections

    return {
        "coverage_ratio": round(coverage_ratio, 3),
        "unverified_count": unverified_count
    }
```

### Review Flags

## Unified Pipeline Integration

- Service: UnifiedBillProcessingService
- Endpoint: POST /api/v1/admin/process-bill-details
- Steps executed:
  1) Fetch metadata from Congress.gov
  2) Fetch full bill text (with robust fallbacks)
  3) Run AI detailed analysis for BillDetails
  4) Persist BillDetails with source_index and metrics
  5) Run values analysis and attach scores
- Idempotency: Existing bills updated; details re-enriched; metrics refreshed.

## Resilience & Fallbacks

- Partial failures in AI calls do not abort pipeline
- TLDR seeds overview.what_does when sections are empty
- Normalization for arrays returned under alternative keys (provisions/milestones)
- Long text truncation for model context window

Content is flagged for human review based on coverage:

```python
# Require human review if coverage < 60%
needs_review = metrics.get("coverage_ratio", 0.0) < 0.6
```

## Integration with Unified Processing

The AI pipeline is integrated into the unified bill processing service:

```python
class UnifiedBillProcessingService:
    async def process_bill_by_number(self, bill_number: str, congress_session: int = 118):
        # 1. Fetch bill metadata
        bill_metadata = await self._fetch_bill_metadata(bill_number, congress_session)

        # 2. Get full bill text
        full_text = await self._fetch_bill_text(bill_metadata, congress_session)

        # 3. Run AI analysis for bill_details
        details_payload = await self._run_ai_detailed_analysis(full_text, bill_metadata)

        # 4. Create bill record (with legacy fields)
        ai_results = await self._run_ai_analysis(full_text, bill_metadata)
        bill = await self._create_bill_record(bill_metadata, full_text, ai_results)

        # 5. Create bill_details from detailed payload
        if details_payload:
            self.details_service.create_or_update_details(bill, full_text, details_payload)

        return bill
```

## Error Handling

### Fail-Safe Design

The pipeline is designed to fail gracefully:

1. **Missing Quotes**: Sections without valid quotes are marked as unverified
2. **AI Failures**: Fallback to basic structured data
3. **Rate Limits**: Sequential processing with delays
4. **Validation Errors**: Drop invalid citations, flag for review

### Retry Logic

Critical operations include retry mechanisms:

```python
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
async def _generate_summary(self, bill_text: str, metadata: dict) -> dict:
    # AI generation with automatic retries
    pass
```

## Configuration

### Environment Variables

```bash
# AI Service Configuration
OPENAI_API_KEY=your_openai_api_key
AI_MODEL=gpt-4
AI_TEMPERATURE=0.3
AI_MAX_TOKENS=4000

# Processing Limits
MAX_BILL_TEXT_LENGTH=50000
MAX_CITATIONS_PER_SECTION=3
COVERAGE_THRESHOLD=0.6
```

### Heading Patterns

Configurable regex patterns for section detection:

```python
DEFAULT_HEADING_PATTERN = r"^SEC\.\s*[^\n]+|^Section\s+\d+|^TITLE\s+[IVXLC]+|^Subtitle\s+[A-Z]"

# Custom pattern for different document types
CUSTOM_PATTERN = r"^Article\s+[IVX]+:|^Chapter\s+\d+"
```

## Monitoring

### Key Metrics

- Citation coverage ratio per bill
- AI processing success rate
- Quote validation failure rate
- Human review queue length
- Processing time per bill

### Logging

Comprehensive logging at each pipeline stage:

```python
logger.info(f"Processing bill {bill_number}: text length {len(full_text)} chars")
logger.info(f"Generated {len(source_index)} sections")
logger.info(f"Coverage ratio: {metrics['coverage_ratio']:.1%}")
logger.warning(f"Bill {bill_id} needs human review: low coverage")
```

## Testing

### Unit Tests

- Citation validation with synthetic bill text
- Quote binding accuracy
- Metrics computation correctness
- Error handling scenarios

### Integration Tests

- End-to-end pipeline execution
- API contract validation
- Database persistence verification

### Example Test

```python
def test_citation_validation():
    service = TextCitationService()
    bill_text = "SEC. 101. This bill expands healthcare access."

    index = service.build_source_index(bill_text)
    quote = "expands healthcare access"

    binding = service.bind_quote(bill_text, index, quote)

    assert binding is not None
    assert binding.quote == quote
    assert "SEC. 101" in binding.heading
    assert binding.start_offset > 0
```

## Performance Optimization

### Async Processing

All AI operations are asynchronous to avoid blocking:

```python
async def process_bill_complete(self, bill_text: str, metadata: dict):
    # Sequential processing to avoid rate limits
    summary = await self._generate_summary(bill_text, metadata)
    await asyncio.sleep(1)  # Rate limit protection

    support = await self._generate_support_reasons(bill_text, metadata)
    await asyncio.sleep(1)

    return self._assemble_results(summary, support, ...)
```

### Caching Strategy

- Bill text cached after initial fetch
- Source indices cached per bill
- AI responses cached for retry scenarios

## See Also

- [API Documentation](./api-documentation.md)
- [Testing Guide](./testing-guide.md)
- [Frontend Integration](./frontend-guide.md)