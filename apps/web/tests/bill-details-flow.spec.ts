/**
 * Comprehensive Playwright E2E Tests for Bill Details Flow
 * 
 * Tests the complete user journey:
 * 1. Homepage -> Bill card
 * 2. Bill card -> Action modal  
 * 3. Action modal "Learn more" -> Bill details page
 * 4. Bill details page anchor navigation
 * 5. Citation display and source navigation
 * 6. Moderation banner display
 * 7. Mobile responsiveness
 * 8. SEO metadata validation
 */

import { test, expect, Page } from '@playwright/test';

// Test fixtures and utilities
const SAMPLE_BILLS = [
  {
    id: 'bill_123',
    title: 'Healthcare Access Act',
    bill_number: 'H.R.5',
    session_year: 118,
    status: 'introduced',
    chamber: 'house',
    simple_summary: 'This bill expands healthcare access to underserved communities.',
    seo_slug: 'hr5-118',
    sponsor_name: '<PERSON><PERSON> <PERSON>',
    sponsor_party: 'D',
    is_featured: true,
    priority_score: 85
  }
];

const SAMPLE_BILL_DETAILS = {
  id: 'bill_123',
  bill_id: 'bill_123',
  seo_slug: 'hr5-118',
  seo_title: 'Healthcare Access Act - Bill Details',
  seo_meta_description: 'Comprehensive analysis of the Healthcare Access Act including provisions for Medicaid expansion and premium subsidies.',
  canonical_url: 'https://modernaction.io/bills/hr5-118',
  hero_summary: 'This bill expands healthcare access to underserved communities by providing Medicaid expansion and premium subsidies for health insurance coverage.',
  needs_human_review: true,
  metrics: {
    coverage_ratio: 0.75,
    unverified_count: 2
  },
  overview: {
    what_does: {
      content: 'This bill expands Medicaid eligibility to all individuals with incomes up to 138% of the federal poverty level and provides premium subsidies for health insurance purchases.',
      citations: [
        {
          quote: 'expands Medicaid eligibility to all individuals with incomes up to 138% of the federal poverty level',
          start_offset: 45,
          end_offset: 120,
          heading: 'SEC. 101. MEDICAID EXPANSION',
          anchor_id: 'sec-1'
        }
      ]
    },
    who_affects: {
      content: 'This legislation affects approximately 12 million previously uninsured Americans, low-income families, and individuals purchasing health insurance through state exchanges.',
      citations: [
        {
          quote: 'healthcare coverage to approximately 12 million previously uninsured Americans',
          start_offset: 180,
          end_offset: 258,
          heading: 'SEC. 101. MEDICAID EXPANSION',
          anchor_id: 'sec-1'
        }
      ]
    },
    key_provisions: [
      {
        content: 'Expands Medicaid eligibility to 138% of federal poverty level',
        citations: [
          {
            quote: 'expands Medicaid eligibility to all individuals with incomes up to 138% of the federal poverty level',
            start_offset: 45,
            end_offset: 145,
            heading: 'SEC. 101. MEDICAID EXPANSION',
            anchor_id: 'sec-1'
          }
        ]
      }
    ]
  },
  positions: {
    support_reasons: [
      {
        claim: 'Expands healthcare coverage to millions of uninsured Americans',
        justification: 'The Medicaid expansion provision will provide essential healthcare access to low-income individuals who currently cannot afford coverage.',
        citations: [
          {
            quote: 'healthcare coverage to approximately 12 million previously uninsured Americans',
            start_offset: 180,
            end_offset: 258,
            heading: 'SEC. 101. MEDICAID EXPANSION',
            anchor_id: 'sec-1'
          }
        ]
      }
    ],
    oppose_reasons: [
      {
        claim: 'Significant cost to taxpayers at $5 billion annually',
        justification: 'The bill\'s high price tag may strain federal budgets and require increased deficit spending or tax increases.',
        citations: [
          {
            quote: '$5 billion annually for the next five years',
            start_offset: 450,
            end_offset: 488,
            heading: 'SEC. 102. SUBSIDIES FOR HEALTH INSURANCE',
            anchor_id: 'sec-2'
          }
        ]
      }
    ]
  },
  source_index: [
    {
      heading: 'SEC. 101. MEDICAID EXPANSION',
      start_offset: 0,
      end_offset: 250,
      anchor_id: 'sec-1'
    },
    {
      heading: 'SEC. 102. SUBSIDIES FOR HEALTH INSURANCE',
      start_offset: 251,
      end_offset: 500,
      anchor_id: 'sec-2'
    }
  ]
};

// Helper function to setup API mocks
async function setupAPIMocks(page: Page) {
  // Mock bills list API
  await page.route('**/api/v1/bills?*', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(SAMPLE_BILLS),
    });
  });

  // Mock bill action data API
  await page.route('**/api/v1/bills/*/action-data', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        bill_id: 'bill_123',
        support_reasons: ['Expands healthcare coverage', 'Makes insurance affordable'],
        oppose_reasons: ['High cost to taxpayers', 'Creates federal bureaucracy'],
        amend_reasons: ['Income thresholds need adjustment']
      }),
    });
  });

  // Mock bill details API
  await page.route('**/api/v1/bills/details/by-slug/*', async route => {
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(SAMPLE_BILL_DETAILS),
    });
  });
}

test.describe('Bill Details Flow - Complete User Journey', () => {
  test.beforeEach(async ({ page }) => {
    await setupAPIMocks(page);
  });

  test('complete flow from homepage to bill details with anchor navigation', async ({ page }) => {
    // Step 1: Start at homepage
    await page.goto('/');
    
    // Wait for bills to load
    await page.waitForSelector('[data-testid="bill-card"]');
    
    // Verify bills are displayed
    const billCards = page.locator('[data-testid="bill-card"]');
    await expect(billCards).toHaveCount(1);
    
    // Step 2: Click on bill card to open action modal
    await billCards.first().click();
    
    // Wait for modal to appear
    await page.waitForSelector('[data-testid="bill-action-modal"]');
    
    // Verify modal opened correctly
    await expect(page.locator('[data-testid="bill-action-modal"]')).toBeVisible();
    await expect(page.locator('[data-testid="modal-title"]')).toContainText('Take Action on');
    
    // Step 3: Verify bill details section in modal
    await expect(page.locator('[data-testid="bill-details-section"]')).toBeVisible();
    
    // Step 4: Find and interact with "Learn more" link
    const learnMoreLink = page.locator('[data-testid="learn-more-link"]').first();
    await expect(learnMoreLink).toBeVisible();
    
    // Get the href to verify it contains expected slug and anchor
    const href = await learnMoreLink.getAttribute('href');
    expect(href).toMatch(/\/bills\/hr5-118#/);
    
    // Click the learn more link (remove target="_blank" to stay in same tab for testing)
    await learnMoreLink.evaluate((el: HTMLAnchorElement) => {
      el.removeAttribute('target');
    });
    await learnMoreLink.click();
    
    // Step 5: Verify we're on the bill details page
    await expect(page).toHaveURL(/\/bills\/hr5-118/);
    await page.waitForSelector('h1');
    
    // Verify page structure
    await expect(page.locator('h1')).toContainText('Healthcare Access Act');
    
    // Step 6: Test navigation menu
    const navLinks = [
      { text: 'What it does', anchor: '#overview-what' },
      { text: 'Who it affects', anchor: '#overview-who' },
      { text: 'Provisions', anchor: '#provisions' }
    ];
    
    for (const link of navLinks) {
      await expect(page.locator(`nav a[href="${link.anchor}"]`)).toBeVisible();
    }
    
    // Step 7: Test anchor navigation
    await page.click('nav a[href="#overview-what"]');
    await expect(page.locator('#overview-what')).toBeVisible();
    
    await page.click('nav a[href="#overview-who"]');  
    await expect(page.locator('#overview-who')).toBeVisible();
    
    // Step 8: Verify section content and citations
    const whatDoesSection = page.locator('#overview-what');
    await expect(whatDoesSection.locator('h2')).toContainText('What this bill does');
    await expect(whatDoesSection.locator('p')).toContainText('expands Medicaid eligibility');
    
    // Verify citations are present
    await expect(page.locator('[data-testid="citations"]')).toHaveCount.greaterThan(0);
    
    const firstCitation = page.locator('[data-testid="citations"]').first();
    await expect(firstCitation.locator('[data-testid="citation-quote"]')).toBeVisible();
    await expect(firstCitation.locator('[data-testid="citation-link"]')).toBeVisible();
    
    // Step 9: Test citation link navigation  
    const citationLink = firstCitation.locator('[data-testid="citation-link"]').first();
    const citationHref = await citationLink.getAttribute('href');
    if (citationHref?.includes('#sec-')) {
      await citationLink.click();
      // Verify we scrolled to the referenced section (check URL hash)
      await expect(page).toHaveURL(new RegExp(citationHref.split('#')[1]));
    }
    
    // Step 10: Verify positions section
    await expect(page.locator('#positions')).toBeVisible();
    await expect(page.locator('#positions h2')).toContainText('Positions');
    
    // Check that support/oppose columns are present
    const positionColumns = page.locator('#positions .grid > div');
    await expect(positionColumns).toHaveCount(3); // Support, Oppose, Amend
  });

  test('displays moderation banner with coverage metrics correctly', async ({ page }) => {
    await page.goto('/bills/hr5-118');
    
    // Wait for page to load
    await page.waitForSelector('h1');
    
    // Verify moderation banner is displayed
    await expect(page.locator('[data-testid="moderation-banner"]')).toBeVisible();
    
    // Check banner content
    const banner = page.locator('[data-testid="moderation-banner"]');
    await expect(banner.locator('h3')).toContainText('AI-Generated Analysis');
    await expect(banner).toContainText('This content was generated by our AI');
    await expect(banner).toContainText('awaiting review by a human expert');
    await expect(banner).toContainText('Use it as a starting point');
    
    // Verify metrics display
    await expect(page.locator('[data-testid="coverage-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="coverage-metric"]')).toContainText('Coverage: 75%');
    
    await expect(page.locator('[data-testid="unverified-metric"]')).toBeVisible();
    await expect(page.locator('[data-testid="unverified-metric"]')).toContainText('2 sections awaiting citations');
  });

  test('handles citation links and source navigation', async ({ page }) => {
    await page.goto('/bills/hr5-118');
    await page.waitForSelector('[data-testid="citations"]');
    
    // Find first citation with a valid link
    const citations = page.locator('[data-testid="citations"]');
    const citationLink = citations.locator('[data-testid="citation-link"]').first();
    
    // Verify citation structure
    await expect(citationLink).toBeVisible();
    await expect(citationLink).toHaveAttribute('href', /#sec-\d+/);
    
    // Click citation link and verify navigation
    const href = await citationLink.getAttribute('href');
    const anchor = href?.split('#')[1];
    
    if (anchor) {
      await citationLink.click();
      
      // Verify URL contains the anchor
      await expect(page).toHaveURL(new RegExp(`#${anchor}`));
      
      // Verify the target section exists (even if not visible due to mocked data)
      // In a real scenario, this would scroll to the actual section
    }
  });

  test('works correctly on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('/bills/hr5-118');
    await page.waitForSelector('h1');
    
    // Verify mobile responsiveness
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('nav')).toBeVisible();
    
    // Test mobile navigation
    const navLink = page.locator('nav a[href="#overview-what"]');
    await expect(navLink).toBeVisible();
    
    await navLink.click();
    await expect(page.locator('#overview-what')).toBeVisible();
    
    // Verify content stacks properly on mobile
    const sections = page.locator('section');
    await expect(sections).toHaveCount.greaterThan(3);
    
    // Test moderation banner on mobile
    if (await page.locator('[data-testid="moderation-banner"]').isVisible()) {
      const banner = page.locator('[data-testid="moderation-banner"]');
      await expect(banner).toBeVisible();
      
      // Check metrics are readable on mobile
      await expect(page.locator('[data-testid="coverage-metric"]')).toBeVisible();
    }
  });

  test('handles missing bill details gracefully', async ({ page }) => {
    // Mock 404 response for bill details
    await page.route('**/api/v1/bills/details/by-slug/nonexistent-bill', async route => {
      await route.fulfill({
        status: 404,
        contentType: 'application/json',
        body: JSON.stringify({ detail: 'Bill details not found' }),
      });
    });
    
    await page.goto('/bills/nonexistent-bill');
    
    // Verify fallback content is shown
    await expect(page.locator('h1')).toContainText('Bill details not available yet');
    await expect(page.locator('p')).toContainText("We couldn't load the detailed analysis");
    await expect(page.locator('p')).toContainText('Try again shortly');
  });

  test('validates SEO metadata and structured data', async ({ page }) => {
    await page.goto('/bills/hr5-118');
    await page.waitForSelector('h1');
    
    // Check page title
    const title = await page.title();
    expect(title).toContain('Healthcare Access Act');
    
    // Check meta description
    const metaDescription = page.locator('meta[name="description"]');
    await expect(metaDescription).toHaveAttribute('content');
    
    // Check canonical URL
    const canonicalLink = page.locator('link[rel="canonical"]');
    await expect(canonicalLink).toHaveAttribute('href');
    const canonicalHref = await canonicalLink.getAttribute('href');
    expect(canonicalHref).toContain('/bills/hr5-118');
    
    // Check Open Graph tags
    await expect(page.locator('meta[property="og:title"]')).toHaveAttribute('content');
    await expect(page.locator('meta[property="og:description"]')).toHaveAttribute('content');
    await expect(page.locator('meta[property="og:url"]')).toHaveAttribute('content');
    await expect(page.locator('meta[property="og:type"]')).toHaveAttribute('content', 'article');
    
    // Check JSON-LD structured data
    const jsonLdScript = page.locator('script[type="application/ld+json"]');
    await expect(jsonLdScript).toHaveCount.greaterThanOrEqual(1);
    
    // Validate JSON-LD content
    const jsonLdContent = await jsonLdScript.first().textContent();
    if (jsonLdContent) {
      const structuredData = JSON.parse(jsonLdContent);
      expect(structuredData).toHaveProperty('@context', 'https://schema.org');
      expect(structuredData).toHaveProperty('@type', 'Legislation');
      expect(structuredData).toHaveProperty('name');
      expect(structuredData).toHaveProperty('description');
      expect(structuredData).toHaveProperty('url');
    }
  });

  test('validates additional bill content section', async ({ page }) => {
    // Mock bill details with additional content
    await page.route('**/api/v1/bills/details/by-slug/hr5-118', async route => {
      const billDetailsWithAdditional = {
        ...SAMPLE_BILL_DETAILS,
        other_details: [
          {
            content: 'Additional provisions include administrative requirements for state implementation and reporting standards.',
            citations: [
              {
                quote: 'administrative requirements for state implementation',
                start_offset: 600,
                end_offset: 645,
                heading: 'SEC. 103. IMPLEMENTATION',
                anchor_id: 'sec-3'
              }
            ]
          }
        ]
      };
      
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(billDetailsWithAdditional),
      });
    });
    
    await page.goto('/bills/hr5-118');
    await page.waitForSelector('h1');
    
    // Verify Additional Bill Content section
    await expect(page.locator('#additional-content')).toBeVisible();
    await expect(page.locator('#additional-content h2')).toContainText('Additional Bill Content');
    
    // Verify content and citations in additional section
    const additionalSection = page.locator('#additional-content');
    await expect(additionalSection).toContainText('administrative requirements');
    
    // Check that citations are present in additional content
    const additionalCitations = additionalSection.locator('[data-testid="citations"]');
    await expect(additionalCitations).toBeVisible();
  });

  test('validates source index section', async ({ page }) => {
    await page.goto('/bills/hr5-118');
    await page.waitForSelector('h1');
    
    // Verify source index section exists
    await expect(page.locator('#source')).toBeVisible();
    await expect(page.locator('#source h2')).toContainText('Source Index');
    
    // Verify source index items are listed
    const sourceItems = page.locator('#source li');
    await expect(sourceItems).toHaveCount.greaterThanOrEqual(2);
    
    // Verify source items contain expected content
    await expect(sourceItems.first()).toContainText('SEC. 101');
    await expect(sourceItems.nth(1)).toContainText('SEC. 102');
  });
});

test.describe('Bill Details Flow - Error Scenarios', () => {
  test('handles API errors gracefully', async ({ page }) => {
    // Mock API error for bill details
    await page.route('**/api/v1/bills/details/by-slug/hr5-118', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ detail: 'Internal server error' }),
      });
    });
    
    await page.goto('/bills/hr5-118');
    
    // Should show error fallback
    await expect(page.locator('h1')).toContainText('Bill details not available yet');
  });

  test('handles slow API responses', async ({ page }) => {
    // Mock slow API response
    await page.route('**/api/v1/bills/details/by-slug/hr5-118', async route => {
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(SAMPLE_BILL_DETAILS),
      });
    });
    
    await page.goto('/bills/hr5-118');
    
    // Should eventually load the content
    await expect(page.locator('h1')).toContainText('Healthcare Access Act', { timeout: 10000 });
  });

  test('validates accessibility features', async ({ page }) => {
    await setupAPIMocks(page);
    await page.goto('/bills/hr5-118');
    await page.waitForSelector('h1');
    
    // Check heading hierarchy
    const h1 = page.locator('h1');
    await expect(h1).toHaveCount(1);
    
    const h2s = page.locator('h2');
    await expect(h2s).toHaveCount.greaterThanOrEqual(3);
    
    // Check link accessibility
    const citationLinks = page.locator('[data-testid="citation-link"]');
    const linkCount = await citationLinks.count();
    
    for (let i = 0; i < linkCount; i++) {
      const link = citationLinks.nth(i);
      await expect(link).toHaveAttribute('href');
      
      // Links should have descriptive text (section headings)
      const linkText = await link.textContent();
      expect(linkText).toBeTruthy();
      expect(linkText).toMatch(/SEC\.|Section/);
    }
    
    // Check that important content has proper ARIA labels or semantic markup
    const navElement = page.locator('nav');
    await expect(navElement).toBeVisible();
    
    const mainElement = page.locator('main');
    await expect(mainElement).toBeVisible();
  });
});

test.describe('Bill Details Flow - Performance', () => {
  test('loads within acceptable time limits', async ({ page }) => {
    await setupAPIMocks(page);
    
    const startTime = Date.now();
    await page.goto('/bills/hr5-118');
    await page.waitForSelector('h1');
    const loadTime = Date.now() - startTime;
    
    // Should load within 5 seconds (generous for E2E testing)
    expect(loadTime).toBeLessThan(5000);
    
    // Check that key content is visible quickly
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('[data-testid="moderation-banner"]')).toBeVisible();
    await expect(page.locator('#overview-what')).toBeVisible();
  });

  test('handles multiple concurrent navigation actions', async ({ page }) => {
    await setupAPIMocks(page);
    await page.goto('/bills/hr5-118');
    await page.waitForSelector('nav');
    
    // Rapidly click multiple navigation links
    const navPromises = [
      page.click('nav a[href="#overview-what"]'),
      page.waitForTimeout(100),
      page.click('nav a[href="#overview-who"]'),
      page.waitForTimeout(100),  
      page.click('nav a[href="#provisions"]')
    ];
    
    await Promise.all(navPromises);
    
    // Should end up at the last clicked section
    await expect(page.locator('#provisions')).toBeVisible();
  });
});