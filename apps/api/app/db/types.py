# app/db/types.py
from sqlalchemy import Text, String

# For production/test parity with PostgreSQL, JSONB is the canonical type.
# We keep helpers only for UUID string fallback used by Base.id and some FKs.

def get_json_type():
    """Deprecated: prefer explicit postgresql.JSONB in models."""
    return Text  # Only used in legacy code paths; should be removed.

def get_uuid_type():
    """UUID string type for IDs and foreign keys. In future, migrate to postgresql.UUID(as_uuid=True)."""
    return String(36)
