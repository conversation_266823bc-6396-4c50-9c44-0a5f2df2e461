# app/services/ai_values_analyzer.py
"""
AI-powered values analysis service using OpenAI GPT-4.

This service analyzes bills against three core values:
- Democracy Protection: voting rights, transparency, governmental accountability
- Human Rights: civil liberties, equality, social justice
- Environmental Justice: climate action, environmental protection, sustainability
"""

import logging
import json
import openai
from typing import Dict, Optional, List
from datetime import datetime
from app.core.config import Settings

logger = logging.getLogger(__name__)


class AIValuesAnalyzer:
    """AI-powered bill values analysis using OpenAI GPT-4."""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client = openai.OpenAI(api_key=settings.OPENAI_API_KEY) if settings.OPENAI_API_KEY else None
        
    def analyze_bill_values(self, bill_text: str, bill_title: str, bill_summary: str = None) -> Dict:
        """
        Analyze a bill's impact on core democratic values using AI.
        
        Args:
            bill_text: Full text of the bill
            bill_title: Title of the bill
            bill_summary: Optional summary of the bill
            
        Returns:
            Dictionary containing values analysis results
        """
        if not self.client:
            logger.warning("OpenAI API key not configured, falling back to mock analysis")
            return self._generate_fallback_analysis(bill_title)
        
        try:
            # Prepare the analysis prompt
            prompt = self._create_analysis_prompt(bill_text, bill_title, bill_summary)
            
            # Get AI analysis
            response = self.client.chat.completions.create(
                model="gpt-4-turbo-preview",
                messages=[
                    {
                        "role": "system",
                        "content": "You are a neutral political analyst specializing in evaluating legislation's impact on democratic values. Provide objective, factual analysis without partisan bias."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=0.3,  # Lower temperature for more consistent analysis
                max_tokens=1500,
                response_format={"type": "json_object"}
            )
            
            # Parse the response
            analysis_result = json.loads(response.choices[0].message.content)
            
            # Validate and normalize the response
            return self._validate_and_normalize_response(analysis_result)
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI analysis response: {e}")
            return self._generate_fallback_analysis(bill_title)
        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            return self._generate_fallback_analysis(bill_title)
    
    def _create_analysis_prompt(self, bill_text: str, bill_title: str, bill_summary: str = None) -> str:
        """Create the analysis prompt for AI evaluation."""
        
        # Truncate bill text if too long (GPT-4 context limit)
        max_bill_text_length = 8000
        if len(bill_text) > max_bill_text_length:
            bill_text = bill_text[:max_bill_text_length] + "... [TRUNCATED]"
        
        prompt = f"""
Analyze the following bill for its impact on three core democratic values. Provide scores from 1-10 for both potential threats and support in each category.

BILL TITLE: {bill_title}

BILL SUMMARY: {bill_summary or 'No summary provided'}

BILL TEXT:
{bill_text}

Please analyze this bill's impact on:

1. DEMOCRACY PROTECTION
   - Voting rights and election integrity
   - Government transparency and accountability  
   - Public participation in governance
   - Separation of powers and checks/balances

2. HUMAN RIGHTS
   - Civil liberties and individual freedoms
   - Equality and non-discrimination
   - Social and economic justice
   - Access to essential services

3. ENVIRONMENTAL JUSTICE
   - Climate change mitigation and adaptation
   - Environmental protection and conservation
   - Sustainable development
   - Environmental health and safety

For each category, provide:
- threat_score (1-10): How much this bill might negatively impact this value
- support_score (1-10): How much this bill might positively impact this value
- key_provisions: List of specific bill provisions relevant to this value

Also provide:
- overall_assessment: Brief neutral summary of the bill's overall impact
- confidence_level: Your confidence in this analysis (0.0-1.0)
- requires_human_review: Boolean indicating if human expert review is recommended
- key_concerns: Any significant concerns that warrant attention
- neutral_tags: 3-5 neutral descriptive tags for this bill's impact areas

Respond in JSON format:
{{
  "democracy": {{
    "threat_score": 0,
    "support_score": 0,
    "key_provisions": []
  }},
  "human_rights": {{
    "threat_score": 0,
    "support_score": 0,
    "key_provisions": []
  }},
  "environmental": {{
    "threat_score": 0,
    "support_score": 0,
    "key_provisions": []
  }},
  "overall_assessment": "",
  "confidence_level": 0.0,
  "requires_human_review": false,
  "key_concerns": [],
  "neutral_tags": []
}}
"""
        return prompt
    
    def _validate_and_normalize_response(self, ai_response: Dict) -> Dict:
        """Validate and normalize the AI response to expected format."""
        
        # Extract scores with validation
        democracy_threat = self._validate_score(ai_response.get('democracy', {}).get('threat_score', 0))
        democracy_support = self._validate_score(ai_response.get('democracy', {}).get('support_score', 0))
        
        human_rights_threat = self._validate_score(ai_response.get('human_rights', {}).get('threat_score', 0))
        human_rights_support = self._validate_score(ai_response.get('human_rights', {}).get('support_score', 0))
        
        environmental_threat = self._validate_score(ai_response.get('environmental', {}).get('threat_score', 0))
        environmental_support = self._validate_score(ai_response.get('environmental', {}).get('support_score', 0))
        
        # Calculate overall levels
        max_threat = max(democracy_threat, human_rights_threat, environmental_threat)
        max_support = max(democracy_support, human_rights_support, environmental_support)
        
        overall_threat_level = self._score_to_level(max_threat)
        overall_support_level = self._score_to_level(max_support)
        
        # Validate confidence
        confidence = ai_response.get('confidence_level', 0.5)
        confidence = max(0.0, min(1.0, float(confidence)))
        
        return {
            'democracy_threat_score': democracy_threat,
            'democracy_support_score': democracy_support,
            'human_rights_threat_score': human_rights_threat,
            'human_rights_support_score': human_rights_support,
            'environmental_threat_score': environmental_threat,
            'environmental_support_score': environmental_support,
            'overall_threat_level': overall_threat_level,
            'overall_support_level': overall_support_level,
            'confidence_score': confidence,
            'needs_human_review': ai_response.get('requires_human_review', confidence < 0.7),
            'analysis_summary': ai_response.get('overall_assessment', 'AI analysis completed'),
            'key_concerns': ai_response.get('key_concerns', []),
            'neutral_tags': ai_response.get('neutral_tags', []),
            'ai_reasoning': {
                'democracy': ai_response.get('democracy', {}),
                'human_rights': ai_response.get('human_rights', {}),
                'environmental': ai_response.get('environmental', {}),
                'methodology': 'OpenAI GPT-4 analysis with neutral criteria framework v2.0'
            }
        }
    
    def _validate_score(self, score) -> int:
        """Validate and normalize a score to 0-10 range."""
        try:
            score = int(float(score))
            return max(0, min(10, score))
        except (ValueError, TypeError):
            return 0
    
    def _score_to_level(self, score: int) -> str:
        """Convert numeric score to categorical level."""
        if score == 0:
            return 'NONE'
        elif score <= 3:
            return 'LOW'
        elif score <= 6:
            return 'MEDIUM'
        elif score <= 8:
            return 'HIGH'
        else:
            return 'CRITICAL'
    
    def _generate_fallback_analysis(self, bill_title: str) -> Dict:
        """Generate fallback analysis when AI is unavailable."""
        logger.info(f"Generating fallback analysis for: {bill_title}")
        
        # Use improved keyword-based analysis
        title_lower = bill_title.lower() if bill_title else ""
        
        # Enhanced keyword detection
        democracy_keywords = ['voting', 'election', 'ballot', 'democracy', 'transparency', 
                            'accountability', 'ethics', 'campaign', 'lobbying', 'oversight']
        rights_keywords = ['rights', 'equality', 'discrimination', 'healthcare', 'education',
                         'housing', 'welfare', 'social', 'civil', 'justice', 'freedom']
        environment_keywords = ['climate', 'environment', 'energy', 'carbon', 'renewable', 
                              'pollution', 'conservation', 'wildlife', 'water', 'sustainability']
        
        # Calculate scores based on relevance
        democracy_relevance = sum(1 for word in democracy_keywords if word in title_lower)
        rights_relevance = sum(1 for word in rights_keywords if word in title_lower)
        env_relevance = sum(1 for word in environment_keywords if word in title_lower)
        
        # Assign scores based on relevance (more sophisticated than pure random)
        democracy_support = min(8, 3 + democracy_relevance * 2) if democracy_relevance > 0 else 2
        rights_support = min(8, 3 + rights_relevance * 2) if rights_relevance > 0 else 2
        env_support = min(8, 3 + env_relevance * 2) if env_relevance > 0 else 2
        
        # Lower threat scores for most bills (realistic assumption)
        democracy_threat = max(1, democracy_relevance) if 'restrict' in title_lower else 1
        rights_threat = max(1, rights_relevance) if any(word in title_lower for word in ['ban', 'restrict', 'limit']) else 1
        env_threat = max(1, env_relevance) if any(word in title_lower for word in ['deregulat', 'drill', 'mining']) else 1
        
        max_support = max(democracy_support, rights_support, env_support)
        max_threat = max(democracy_threat, rights_threat, env_threat)
        
        return {
            'democracy_threat_score': democracy_threat,
            'democracy_support_score': democracy_support,
            'human_rights_threat_score': rights_threat,
            'human_rights_support_score': rights_support,
            'environmental_threat_score': env_threat,
            'environmental_support_score': env_support,
            'overall_threat_level': self._score_to_level(max_threat),
            'overall_support_level': self._score_to_level(max_support),
            'confidence_score': 0.6,  # Lower confidence for fallback
            'needs_human_review': True,  # Always flag fallback for review
            'analysis_summary': f"Automated analysis based on bill title keywords. Human review recommended.",
            'key_concerns': ["Analysis based on limited information - full review needed"],
            'neutral_tags': self._generate_tags_from_keywords(title_lower),
            'ai_reasoning': {
                'methodology': 'Keyword-based fallback analysis v2.0 - AI unavailable'
            }
        }
    
    def _generate_tags_from_keywords(self, title_lower: str) -> List[str]:
        """Generate neutral tags based on bill title keywords."""
        tags = []
        
        if any(word in title_lower for word in ['voting', 'election', 'democracy']):
            tags.append("Electoral Process Impact")
        
        if any(word in title_lower for word in ['healthcare', 'health']):
            tags.append("Healthcare Policy")
            
        if any(word in title_lower for word in ['education', 'school']):
            tags.append("Education Policy")
            
        if any(word in title_lower for word in ['environment', 'climate', 'energy']):
            tags.append("Environmental Policy")
            
        if any(word in title_lower for word in ['tax', 'budget', 'spending']):
            tags.append("Fiscal Policy")
            
        if any(word in title_lower for word in ['security', 'defense', 'military']):
            tags.append("Security Policy")
            
        # Ensure we have at least some tags
        if not tags:
            tags = ["General Legislation", "Policy Impact"]
        
        return tags[:5]  # Limit to 5 tags