#!/usr/bin/env python3
"""
Test script for the bill importance scoring system
"""

import sys
import os
sys.path.append('apps/api')

from app.services.bill_importance_scorer import BillImportanceScorer, ImportanceLevel

def test_importance_scoring():
    """Test the bill importance scoring system with various bill types"""
    
    scorer = BillImportanceScorer()
    
    test_cases = [
        {
            "name": "Post Office Naming (Should be MINIMAL)",
            "title": "To designate the facility of the United States Postal Service located at 123 Main Street as the 'John Doe Post Office'",
            "summary": "This bill designates a post office building.",
            "bill_number": "HR1234",
            "expected_level": ImportanceLevel.MINIMAL,
            "expected_auto_process": False
        },
        {
            "name": "For the People Act (Should be HIGH)",
            "title": "For the People Act of 2021",
            "summary": "This bill addresses voter access, election integrity, election security, political spending, and ethics for the three branches of government. The bill expands voter registration and voting access.",
            "bill_number": "HR1",
            "expected_level": ImportanceLevel.HIGH,
            "expected_auto_process": True
        },
        {
            "name": "Infrastructure Bill (Should be MEDIUM/HIGH)",
            "title": "Infrastructure Investment and Jobs Act",
            "summary": "This bill provides $1.2 trillion in funding for roads, bridges, broadband, and other infrastructure improvements nationwide.",
            "bill_number": "HR3684",
            "expected_level": ImportanceLevel.MEDIUM,
            "expected_auto_process": True
        },
        {
            "name": "Healthcare Reform (Should be HIGH/CRITICAL)",
            "title": "Medicare for All Act",
            "summary": "This bill establishes a national health insurance program to provide comprehensive healthcare coverage for all Americans, replacing private health insurance.",
            "bill_number": "HR1976",
            "expected_level": [ImportanceLevel.HIGH, ImportanceLevel.CRITICAL],  # Accept either
            "expected_auto_process": True
        },
        {
            "name": "Technical Correction (Should be LOW/MINIMAL)",
            "title": "Technical Corrections to the Tax Code",
            "summary": "This bill makes minor technical corrections and clarifications to various sections of the Internal Revenue Code.",
            "bill_number": "HR9999",
            "expected_level": [ImportanceLevel.LOW, ImportanceLevel.MINIMAL],  # Accept either
            "expected_auto_process": False
        }
    ]
    
    print("🎯 Bill Importance Scoring Test Results")
    print("=" * 60)
    
    all_passed = True
    
    for test_case in test_cases:
        result = scorer.score_bill(
            test_case["title"],
            test_case["summary"],
            test_case["bill_number"]
        )
        
        # Check if results match expectations
        expected_levels = test_case["expected_level"] if isinstance(test_case["expected_level"], list) else [test_case["expected_level"]]
        level_correct = result.level in expected_levels
        auto_process_correct = result.auto_process == test_case["expected_auto_process"]
        
        status = "✅ PASS" if (level_correct and auto_process_correct) else "❌ FAIL"
        if not (level_correct and auto_process_correct):
            all_passed = False
        
        print(f"\n{status} {test_case['name']}")
        print(f"   Bill: {test_case['bill_number']}")
        print(f"   Score: {result.score}/100")
        expected_str = "/".join([level.value for level in expected_levels])
        print(f"   Level: {result.level.value} (expected: {expected_str})")
        print(f"   Auto-process: {result.auto_process} (expected: {test_case['expected_auto_process']})")
        print(f"   Reason: {result.reason}")
        print(f"   Key indicators: {result.key_indicators[:3]}")
        
        if not level_correct:
            print(f"   ⚠️  Level mismatch: got {result.level.value}, expected {expected_str}")
        if not auto_process_correct:
            print(f"   ⚠️  Auto-process mismatch: got {result.auto_process}, expected {test_case['expected_auto_process']}")
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 All tests PASSED! The importance scoring system is working correctly.")
        print("\n💡 Summary:")
        print("   • Post office naming: MINIMAL importance, no auto-processing")
        print("   • Voting rights: HIGH importance, auto-processing enabled")
        print("   • Infrastructure: MEDIUM/HIGH importance, auto-processing enabled")
        print("   • Healthcare: HIGH importance, auto-processing enabled")
        print("   • Technical corrections: LOW importance, no auto-processing")
    else:
        print("❌ Some tests FAILED. Check the scoring logic.")
        return False
    
    return True

def test_cost_implications():
    """Test the cost implications of the importance scoring system"""
    
    print("\n💰 Cost Optimization Analysis")
    print("=" * 60)
    
    scorer = BillImportanceScorer()
    
    # Simulate a batch of 100 bills
    bill_types = [
        ("Post office naming", 40),  # 40% of bills are ceremonial
        ("Technical corrections", 20),  # 20% are technical
        ("Minor legislation", 25),  # 25% are minor
        ("Important legislation", 15)  # 15% are truly important
    ]
    
    total_bills = 100
    total_auto_processed = 0
    total_manual_available = 0
    
    for bill_type, percentage in bill_types:
        count = int(total_bills * percentage / 100)
        
        if bill_type == "Post office naming":
            # Minimal importance
            auto_processed = 0
            manual_available = count
        elif bill_type == "Technical corrections":
            # Low importance
            auto_processed = 0
            manual_available = count
        elif bill_type == "Minor legislation":
            # Medium importance
            auto_processed = int(count * 0.6)  # Some medium bills auto-process
            manual_available = count - auto_processed
        else:
            # Important legislation
            auto_processed = count
            manual_available = 0
        
        total_auto_processed += auto_processed
        total_manual_available += manual_available
        
        print(f"{bill_type:20} {count:3} bills -> {auto_processed:2} auto-processed, {manual_available:2} manual available")
    
    cost_per_ai_analysis = 0.25  # $0.25 per AI analysis
    
    old_cost = total_bills * cost_per_ai_analysis
    new_cost = total_auto_processed * cost_per_ai_analysis
    savings = old_cost - new_cost
    savings_percentage = (savings / old_cost) * 100
    
    print(f"\n📊 Cost Analysis:")
    print(f"   Old system (process all): ${old_cost:.2f}")
    print(f"   New system (smart processing): ${new_cost:.2f}")
    print(f"   💰 Savings: ${savings:.2f} ({savings_percentage:.1f}%)")
    print(f"   📈 Bills auto-processed: {total_auto_processed}/{total_bills} ({(total_auto_processed/total_bills)*100:.1f}%)")
    print(f"   🔍 Bills available for manual processing: {total_manual_available}")

if __name__ == "__main__":
    print("🚀 Testing Bill Importance Scoring System")
    
    success = test_importance_scoring()
    test_cost_implications()
    
    if success:
        print("\n✅ System ready for deployment!")
        exit(0)
    else:
        print("\n❌ System needs adjustments before deployment.")
        exit(1)