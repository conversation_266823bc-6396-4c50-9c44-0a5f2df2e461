'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Official } from '@/types';
import { officialApi } from '@/services/apiClient';

interface BillSponsorsProps {
  sponsorName?: string;
  sponsorParty?: string;
  sponsorState?: string;
  sponsorBioguideId?: string;
  cosponsors?: Array<{
    name: string;
    party?: string;
    state?: string;
    bioguide_id?: string;
  }>;
  className?: string;
  maxCosponsorsShown?: number;
}

interface OfficialProfile {
  id: string;
  name: string;
  title?: string;
  party?: string;
  state?: string;
  photo_url?: string;
  official_photo_url?: string;
  bioguide_id?: string;
}

export const BillSponsors: React.FC<BillSponsorsProps> = ({
  sponsorName,
  sponsorParty,
  sponsorState,
  sponsorBioguideId,
  cosponsors = [],
  className = '',
  maxCosponsorsShown = 5
}) => {
  const [sponsorProfile, setSponsorProfile] = useState<OfficialProfile | null>(null);
  const [cosponsorProfiles, setCosponsorProfiles] = useState<Map<string, OfficialProfile>>(new Map());
  const [showAllCosponsors, setShowAllCosponsors] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadOfficialProfiles();
  }, [sponsorName, sponsorBioguideId, cosponsors]);

  const loadOfficialProfiles = async () => {
    if (!sponsorName && cosponsors.length === 0) return;

    try {
      setLoading(true);
      const profileMap = new Map<string, OfficialProfile>();

      // Load sponsor profile
      if (sponsorName) {
        try {
          let sponsor = null;
          
          // Try to find by bioguide ID first (most accurate)
          if (sponsorBioguideId) {
            try {
              sponsor = await officialApi.getOfficialByExternalId('bioguide', sponsorBioguideId);
            } catch (e) {
              console.log('Sponsor not found by bioguide ID:', sponsorBioguideId);
            }
          }
          
          // Fallback: search by name and state
          if (!sponsor && sponsorState) {
            try {
              const searchResults = await officialApi.searchOfficials({
                query: sponsorName,
                state: sponsorState,
                limit: 1
              });
              if (searchResults.length > 0) {
                sponsor = searchResults[0];
              }
            } catch (e) {
              console.log('Sponsor search failed:', e);
            }
          }
          
          if (sponsor) {
            setSponsorProfile(sponsor);
          }
        } catch (error) {
          console.log('Could not load sponsor profile:', error);
        }
      }

      // Load cosponsor profiles
      for (const cosponsor of cosponsors) {
        try {
          let profile = null;
          
          // Try bioguide ID first
          if (cosponsor.bioguide_id) {
            try {
              profile = await officialApi.getOfficialByExternalId('bioguide', cosponsor.bioguide_id);
            } catch (e) {
              console.log('Cosponsor not found by bioguide ID:', cosponsor.bioguide_id);
            }
          }
          
          // Fallback: search by name and state
          if (!profile && cosponsor.state) {
            try {
              const searchResults = await officialApi.searchOfficials({
                query: cosponsor.name,
                state: cosponsor.state,
                limit: 1
              });
              if (searchResults.length > 0) {
                profile = searchResults[0];
              }
            } catch (e) {
              console.log('Cosponsor search failed:', e);
            }
          }
          
          if (profile) {
            profileMap.set(cosponsor.name, profile);
          }
        } catch (error) {
          console.log(`Could not load cosponsor profile for ${cosponsor.name}:`, error);
        }
      }

      setCosponsorProfiles(profileMap);
    } catch (error) {
      console.error('Error loading official profiles:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPartyColor = (party?: string) => {
    if (!party) return 'text-gray-600';
    
    const partyLower = party.toLowerCase();
    if (partyLower.includes('democrat') || partyLower.includes('dem')) {
      return 'text-blue-600';
    }
    if (partyLower.includes('republican') || partyLower.includes('rep')) {
      return 'text-red-600';
    }
    return 'text-gray-600';
  };

  const renderOfficialLink = (
    official: OfficialProfile | null, 
    fallbackName: string, 
    fallbackParty?: string, 
    fallbackState?: string,
    isMain: boolean = false
  ) => {
    const displayName = official?.name || fallbackName;
    const displayParty = official?.party || fallbackParty;
    const displayState = official?.state || fallbackState;
    
    if (official) {
      return (
        <Link
          href={`/officials/${official.id}`}
          className={`inline-flex items-center space-x-2 hover:bg-gray-50 rounded-lg p-2 transition-colors duration-200 ${
            isMain ? 'font-semibold' : 'text-sm'
          }`}
        >
          {(official.official_photo_url || official.photo_url) && (
            <img
              src={official.official_photo_url || official.photo_url}
              alt={displayName}
              className="w-6 h-6 rounded-full object-cover border border-gray-200"
              onError={(e) => {
                (e.target as HTMLImageElement).style.display = 'none';
              }}
            />
          )}
          <span className="hover:text-blue-600 transition-colors duration-200">
            {displayName}
          </span>
          {displayParty && (
            <span className={`text-xs ${getPartyColor(displayParty)}`}>
              ({displayParty})
            </span>
          )}
          {displayState && (
            <span className="text-xs text-gray-500">
              {displayState}
            </span>
          )}
        </Link>
      );
    }

    // Fallback: show name without link
    return (
      <div className={`inline-flex items-center space-x-1 ${isMain ? 'font-semibold' : 'text-sm text-gray-600'}`}>
        <span>{displayName}</span>
        {displayParty && (
          <span className={`text-xs ${getPartyColor(displayParty)}`}>
            ({displayParty})
          </span>
        )}
        {displayState && (
          <span className="text-xs text-gray-500">
            {displayState}
          </span>
        )}
      </div>
    );
  };

  if (!sponsorName && cosponsors.length === 0) {
    return null;
  }

  const displayedCosponsors = showAllCosponsors ? cosponsors : cosponsors.slice(0, maxCosponsorsShown);
  const hasMoreCosponsors = cosponsors.length > maxCosponsorsShown;

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      {/* Sponsor Section */}
      {sponsorName && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Sponsor</h4>
          <div className="flex items-center">
            {loading ? (
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
              </div>
            ) : (
              renderOfficialLink(sponsorProfile, sponsorName, sponsorParty, sponsorState, true)
            )}
          </div>
        </div>
      )}

      {/* Cosponsors Section */}
      {cosponsors.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">
              Cosponsors ({cosponsors.length})
            </h4>
            {hasMoreCosponsors && (
              <button
                onClick={() => setShowAllCosponsors(!showAllCosponsors)}
                className="text-xs text-blue-600 hover:text-blue-800 font-medium"
              >
                {showAllCosponsors ? 'Show Less' : `Show All ${cosponsors.length}`}
              </button>
            )}
          </div>

          {loading ? (
            <div className="space-y-2">
              {Array.from({ length: Math.min(3, cosponsors.length) }).map((_, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse"></div>
                  <div className="h-3 bg-gray-200 rounded w-24 animate-pulse"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-1">
              {displayedCosponsors.map((cosponsor, index) => (
                <div key={index} className="flex items-center">
                  {renderOfficialLink(
                    cosponsorProfiles.get(cosponsor.name) || null,
                    cosponsor.name,
                    cosponsor.party,
                    cosponsor.state
                  )}
                </div>
              ))}
              
              {!showAllCosponsors && hasMoreCosponsors && (
                <div className="text-xs text-gray-500 mt-2">
                  And {cosponsors.length - maxCosponsorsShown} more...
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Empty State */}
      {!sponsorName && cosponsors.length === 0 && (
        <div className="text-sm text-gray-500 text-center py-2">
          Sponsor information not available
        </div>
      )}
    </div>
  );
};

export default BillSponsors;