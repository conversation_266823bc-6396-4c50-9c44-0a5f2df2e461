"""enhance_officials_table_for_public_profiles

Revision ID: 18fff2d51f56
Revises: de0f65151d5f
Create Date: 2025-08-06 18:28:20.898107

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '18fff2d51f56'
down_revision: Union[str, Sequence[str], None] = 'de0f65151d5f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add enhanced fields for public profiles and OpenStates integration
    
    # Name parsing fields
    op.add_column('officials', sa.Column('first_name', sa.String(100), nullable=True))
    op.add_column('officials', sa.Column('last_name', sa.String(100), nullable=True))
    op.add_column('officials', sa.Column('full_name', sa.String(255), nullable=True))
    
    # Additional external IDs
    op.add_column('officials', sa.Column('govtrack_id', sa.String(50), nullable=True))
    
    # Enhanced contact information
    op.add_column('officials', sa.Column('dc_office_phone', sa.String(50), nullable=True))
    op.add_column('officials', sa.Column('dc_office_address', sa.Text, nullable=True))
    op.add_column('officials', sa.Column('local_office_phone', sa.String(50), nullable=True))
    op.add_column('officials', sa.Column('local_office_address', sa.Text, nullable=True))
    
    # Committee and legislative information
    op.add_column('officials', sa.Column('committees', sa.Text, nullable=True))  # JSON array of committees
    op.add_column('officials', sa.Column('leadership_positions', sa.Text, nullable=True))  # JSON array of leadership roles
    
    # Enhanced term information
    op.add_column('officials', sa.Column('current_term_start', sa.String(50), nullable=True))
    op.add_column('officials', sa.Column('current_term_end', sa.String(50), nullable=True))
    op.add_column('officials', sa.Column('next_election_date', sa.String(50), nullable=True))
    
    # Social media enhancement
    op.add_column('officials', sa.Column('youtube_channel', sa.String(255), nullable=True))
    op.add_column('officials', sa.Column('linkedin_url', sa.String(255), nullable=True))
    
    # Public profile enhancement
    op.add_column('officials', sa.Column('official_photo_url', sa.String(255), nullable=True))  # Official government photo
    op.add_column('officials', sa.Column('homepage_url', sa.String(255), nullable=True))  # Official homepage
    
    # Voting statistics for public profiles
    op.add_column('officials', sa.Column('bills_sponsored_count', sa.Integer, nullable=True))
    op.add_column('officials', sa.Column('bills_cosponsored_count', sa.Integer, nullable=True))
    op.add_column('officials', sa.Column('votes_cast_count', sa.Integer, nullable=True))
    
    # Create indexes for performance
    op.create_index('idx_officials_first_last_name', 'officials', ['first_name', 'last_name'])
    op.create_index('idx_officials_full_name', 'officials', ['full_name'])
    op.create_index('idx_officials_govtrack_id', 'officials', ['govtrack_id'])
    op.create_index('idx_officials_current_term', 'officials', ['current_term_start', 'current_term_end'])


def downgrade() -> None:
    """Downgrade schema."""
    # Drop indexes
    op.drop_index('idx_officials_current_term', table_name='officials')
    op.drop_index('idx_officials_govtrack_id', table_name='officials')
    op.drop_index('idx_officials_full_name', table_name='officials')
    op.drop_index('idx_officials_first_last_name', table_name='officials')
    
    # Drop columns
    op.drop_column('officials', 'votes_cast_count')
    op.drop_column('officials', 'bills_cosponsored_count')
    op.drop_column('officials', 'bills_sponsored_count')
    op.drop_column('officials', 'homepage_url')
    op.drop_column('officials', 'official_photo_url')
    op.drop_column('officials', 'linkedin_url')
    op.drop_column('officials', 'youtube_channel')
    op.drop_column('officials', 'next_election_date')
    op.drop_column('officials', 'current_term_end')
    op.drop_column('officials', 'current_term_start')
    op.drop_column('officials', 'leadership_positions')
    op.drop_column('officials', 'committees')
    op.drop_column('officials', 'local_office_address')
    op.drop_column('officials', 'local_office_phone')
    op.drop_column('officials', 'dc_office_address')
    op.drop_column('officials', 'dc_office_phone')
    op.drop_column('officials', 'govtrack_id')
    op.drop_column('officials', 'full_name')
    op.drop_column('officials', 'last_name')
    op.drop_column('officials', 'first_name')
