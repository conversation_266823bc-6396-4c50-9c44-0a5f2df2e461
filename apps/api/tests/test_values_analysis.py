# tests/test_values_analysis.py
"""
Comprehensive tests for the Values Analysis System.

Tests both AI-powered analysis and fallback mechanisms.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.orm import Session
from datetime import datetime

from app.services.bill_values_analysis_service import BillValuesAnalysisService
from app.services.ai_values_analyzer import AIValuesAnalyzer
from app.models.bill import Bill
from app.models.bill_values import BillValuesAnalysis
from app.core.config import Settings


class TestAIValuesAnalyzer:
    """Test the AI-powered values analyzer."""
    
    def test_ai_analyzer_initialization_with_api_key(self):
        """Test AI analyzer initializes correctly with API key."""
        settings = Settings()
        settings.OPENAI_API_KEY = "test-key"
        
        analyzer = AIValuesAnalyzer(settings)
        
        assert analyzer.settings == settings
        assert analyzer.client is not None
    
    def test_ai_analyzer_initialization_without_api_key(self):
        """Test AI analyzer handles missing API key gracefully."""
        settings = Settings()
        settings.OPENAI_API_KEY = None
        
        analyzer = AIValuesAnalyzer(settings)
        
        assert analyzer.settings == settings
        assert analyzer.client is None
    
    @patch('openai.OpenAI')
    def test_analyze_bill_values_success(self, mock_openai_class):
        """Test successful AI analysis of bill values."""
        # Setup
        settings = Settings()
        settings.OPENAI_API_KEY = "test-key"
        
        # Mock OpenAI response
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = '''
        {
            "democracy": {
                "threat_score": 2,
                "support_score": 7,
                "key_provisions": ["Voting access expansion"]
            },
            "human_rights": {
                "threat_score": 1,
                "support_score": 8,
                "key_provisions": ["Civil rights protection"]
            },
            "environmental": {
                "threat_score": 3,
                "support_score": 9,
                "key_provisions": ["Climate action measures"]
            },
            "overall_assessment": "This bill strongly supports democratic values",
            "confidence_level": 0.85,
            "requires_human_review": false,
            "key_concerns": [],
            "neutral_tags": ["Environmental Protection", "Voting Rights"]
        }
        '''
        
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test
        analyzer = AIValuesAnalyzer(settings)
        result = analyzer.analyze_bill_values(
            bill_text="Sample bill text about climate action and voting",
            bill_title="Climate Action and Voting Rights Act",
            bill_summary="A bill to address climate change and expand voting access"
        )
        
        # Assertions
        assert result['democracy_threat_score'] == 2
        assert result['democracy_support_score'] == 7
        assert result['human_rights_threat_score'] == 1
        assert result['human_rights_support_score'] == 8
        assert result['environmental_threat_score'] == 3
        assert result['environmental_support_score'] == 9
        assert result['overall_threat_level'] == 'LOW'  # max threat = 3
        assert result['overall_support_level'] == 'CRITICAL'  # max support = 9
        assert result['confidence_score'] == 0.85
        assert result['needs_human_review'] == False
        assert len(result['neutral_tags']) == 2
    
    def test_analyze_bill_values_fallback_no_api_key(self):
        """Test fallback analysis when no API key is provided."""
        settings = Settings()
        settings.OPENAI_API_KEY = None
        
        analyzer = AIValuesAnalyzer(settings)
        result = analyzer.analyze_bill_values(
            bill_text="Sample bill text",
            bill_title="Climate Action Now Act"
        )
        
        # Should fall back to keyword analysis
        assert isinstance(result, dict)
        assert 'democracy_threat_score' in result
        assert 'environmental_support_score' in result
        assert result['confidence_score'] == 0.6  # Lower confidence for fallback
        assert result['needs_human_review'] == True
        assert "keyword-based fallback" in result['ai_reasoning']['methodology'].lower()
    
    def test_validate_score_function(self):
        """Test score validation function."""
        settings = Settings()
        analyzer = AIValuesAnalyzer(settings)
        
        # Valid scores
        assert analyzer._validate_score(5) == 5
        assert analyzer._validate_score("7") == 7
        assert analyzer._validate_score(0) == 0
        assert analyzer._validate_score(10) == 10
        
        # Out of range scores
        assert analyzer._validate_score(-5) == 0
        assert analyzer._validate_score(15) == 10
        
        # Invalid inputs
        assert analyzer._validate_score("invalid") == 0
        assert analyzer._validate_score(None) == 0
    
    def test_score_to_level_conversion(self):
        """Test score to level conversion."""
        settings = Settings()
        analyzer = AIValuesAnalyzer(settings)
        
        assert analyzer._score_to_level(0) == 'NONE'
        assert analyzer._score_to_level(2) == 'LOW'
        assert analyzer._score_to_level(5) == 'MEDIUM'
        assert analyzer._score_to_level(7) == 'HIGH'
        assert analyzer._score_to_level(9) == 'CRITICAL'


class TestBillValuesAnalysisService:
    """Test the bill values analysis service."""
    
    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=Session)
    
    @pytest.fixture
    def mock_settings(self):
        """Create mock settings."""
        settings = Settings()
        settings.OPENAI_API_KEY = "test-key"
        return settings
    
    @pytest.fixture
    def sample_bill(self):
        """Create a sample bill for testing."""
        bill = Mock(spec=Bill)
        bill.id = "test-bill-id"
        bill.title = "Climate Action Now Act"
        bill.summary = "A comprehensive bill to address climate change"
        bill.full_text = "Section 1: This bill establishes new climate policies..."
        return bill
    
    def test_service_initialization(self, mock_db, mock_settings):
        """Test service initializes correctly."""
        service = BillValuesAnalysisService(mock_db, mock_settings)
        
        assert service.db == mock_db
        assert service.settings == mock_settings
        assert isinstance(service.ai_analyzer, AIValuesAnalyzer)
    
    def test_analyze_bill_values_sync_new_analysis(self, mock_db, mock_settings, sample_bill):
        """Test synchronous analysis for a new bill."""
        # Setup
        mock_db.query.return_value.filter.return_value.first.return_value = None  # No existing analysis
        
        service = BillValuesAnalysisService(mock_db, mock_settings)
        
        # Mock the AI analyzer
        with patch.object(service.ai_analyzer, 'analyze_bill_values') as mock_analyze:
            mock_analyze.return_value = {
                'democracy_threat_score': 2,
                'democracy_support_score': 7,
                'human_rights_threat_score': 1,
                'human_rights_support_score': 6,
                'environmental_threat_score': 1,
                'environmental_support_score': 9,
                'overall_threat_level': 'LOW',
                'overall_support_level': 'CRITICAL',
                'confidence_score': 0.85,
                'needs_human_review': False,
                'analysis_summary': 'Strong environmental support'
            }
            
            # Test
            result = service.analyze_bill_values_sync(sample_bill)
            
            # Assertions
            assert isinstance(result, BillValuesAnalysis)
            assert result.bill_id == sample_bill.id
            assert result.democracy_threat_score == 2
            assert result.environmental_support_score == 9
            assert result.overall_support_level == 'CRITICAL'
            assert result.confidence_score == 0.85
            mock_db.add.assert_called_once()
            mock_db.flush.assert_called_once()
    
    def test_analyze_bill_values_sync_existing_analysis(self, mock_db, mock_settings, sample_bill):
        """Test that existing analysis is returned without reprocessing."""
        # Setup existing analysis
        existing_analysis = Mock(spec=BillValuesAnalysis)
        existing_analysis.bill_id = sample_bill.id
        existing_analysis.democracy_support_score = 5
        
        mock_db.query.return_value.filter.return_value.first.return_value = existing_analysis
        
        service = BillValuesAnalysisService(mock_db, mock_settings)
        
        # Test
        result = service.analyze_bill_values_sync(sample_bill)
        
        # Assertions
        assert result == existing_analysis
        mock_db.add.assert_not_called()  # Should not add new analysis
    
    def test_prepare_bill_text(self, mock_db, mock_settings):
        """Test bill text preparation for AI analysis."""
        service = BillValuesAnalysisService(mock_db, mock_settings)
        
        # Test with full_text
        bill = Mock()
        bill.full_text = "Full bill text"
        bill.text = "Partial text"
        bill.summary = "Summary"
        bill.title = "Title"
        
        result = service._prepare_bill_text(bill)
        assert result == "Full bill text"
        
        # Test fallback hierarchy
        bill.full_text = None
        result = service._prepare_bill_text(bill)
        assert result == "Partial text"
        
        bill.text = None
        result = service._prepare_bill_text(bill)
        assert result == "Summary"  # Uses summary, not bill.summary
        
        bill.summary = None
        bill.ai_summary = "AI Summary"  # This is checked before title
        result = service._prepare_bill_text(bill)
        assert result == "AI Summary"
        
        bill.ai_summary = None
        result = service._prepare_bill_text(bill)
        assert result == "Title"
        
        # Test with no content
        bill.title = None
        result = service._prepare_bill_text(bill)
        assert result == "No bill text available"
    
    def test_get_bill_analysis(self, mock_db, mock_settings):
        """Test retrieving existing bill analysis."""
        mock_analysis = Mock(spec=BillValuesAnalysis)
        mock_db.query.return_value.filter.return_value.first.return_value = mock_analysis
        
        service = BillValuesAnalysisService(mock_db, mock_settings)
        result = service.get_bill_analysis("test-bill-id")
        
        assert result == mock_analysis
    
    def test_get_bills_requiring_review(self, mock_db, mock_settings):
        """Test retrieving bills that require human review."""
        mock_analyses = [Mock(spec=BillValuesAnalysis), Mock(spec=BillValuesAnalysis)]
        
        # Setup the mock chain properly - the service uses filter() with multiple conditions
        mock_db.query.return_value.filter.return_value.all.return_value = mock_analyses
        
        service = BillValuesAnalysisService(mock_db, mock_settings)
        result = service.get_bills_requiring_review()
        
        assert result == mock_analyses
        assert len(result) == 2


class TestValuesAnalysisEndpoints:
    """Integration tests for values analysis API endpoints."""
    
    @pytest.fixture
    def mock_bill_data(self):
        """Sample bill data for testing."""
        return {
            "id": "test-bill-id",
            "title": "Sample Climate Bill",
            "bill_number": "HR-123",
            "summary": "A bill about climate action"
        }
    
    def test_keyword_based_fallback_analysis(self):
        """Test that keyword-based analysis works correctly."""
        settings = Settings()
        settings.OPENAI_API_KEY = None  # Force fallback
        
        analyzer = AIValuesAnalyzer(settings)
        
        # Test environmental bill
        result = analyzer._generate_fallback_analysis("Clean Energy and Climate Action Act")
        assert result['environmental_support_score'] > result['democracy_support_score']
        assert 'Environmental Policy' in result['neutral_tags']
        
        # Test voting rights bill
        result = analyzer._generate_fallback_analysis("Voting Rights Protection Act")
        assert result['democracy_support_score'] > result['environmental_support_score']
        assert 'Electoral Process Impact' in result['neutral_tags']
        
        # Test healthcare bill
        result = analyzer._generate_fallback_analysis("Medicare for All Healthcare Act")
        assert result['human_rights_support_score'] >= 5
        assert 'Healthcare Policy' in result['neutral_tags']
    
    @patch('openai.OpenAI')
    def test_ai_analysis_error_handling(self, mock_openai_class):
        """Test that AI analysis errors are handled gracefully."""
        settings = Settings()
        settings.OPENAI_API_KEY = "test-key"
        
        # Mock OpenAI to raise an exception
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        mock_client.chat.completions.create.side_effect = Exception("API Error")
        
        analyzer = AIValuesAnalyzer(settings)
        result = analyzer.analyze_bill_values(
            bill_text="Test bill",
            bill_title="Test Climate Bill"
        )
        
        # Should fallback gracefully
        assert isinstance(result, dict)
        assert result['needs_human_review'] == True
        assert result['confidence_score'] == 0.6
        assert 'fallback' in result['ai_reasoning']['methodology'].lower()


def test_comprehensive_analysis_workflow():
    """Test the complete analysis workflow from bill to results."""
    # This would be an integration test that exercises the full pipeline
    # when the system is fully deployed
    pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])