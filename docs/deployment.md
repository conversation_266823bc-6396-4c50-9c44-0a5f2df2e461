# Deployment Guide
> Last updated: 2025-08-11


## Overview

This guide covers deploying the Bill Details system to production, including database migrations, environment configuration, and monitoring setup.

## Prerequisites

- AWS account with appropriate permissions
- Docker installed locally
- PostgreSQL database (AWS RDS recommended)
- Redis instance for caching
- OpenAI API key for AI processing
- CDK CLI installed and configured

## Environment Setup

### Environment Variables

Create environment files for different stages:

#### `.env.production`
```bash
# Database
DATABASE_URL=***************************************/modernaction_prod
REDIS_URL=redis://redis-host:6379/0

# AI Service
OPENAI_API_KEY=your_production_openai_key
AI_MODEL=gpt-4
AI_TEMPERATURE=0.3

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_BASE_URL=https://api.modernaction.io

# Security
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=modernaction.io,api.modernaction.io
CORS_ORIGINS=https://modernaction.io

# Bill Details Configuration
BILL_DETAILS_COVERAGE_THRESHOLD=0.6
BILL_DETAILS_MAX_CITATIONS=3
BILL_DETAILS_REQUIRE_REVIEW=true
```

#### `.env.staging`
```bash
# Similar to production but with staging values
DATABASE_URL=******************************************/modernaction_staging
API_BASE_URL=https://staging.modernaction.io
CORS_ORIGINS=https://staging.modernaction.io
```

### Web Application Environment

#### `apps/web/.env.production`
```bash
NEXT_PUBLIC_API_URL=https://api.modernaction.io/api/v1
NEXT_PUBLIC_APP_URL=https://modernaction.io
NEXT_PUBLIC_AUTH0_DOMAIN=your-auth0-domain
NEXT_PUBLIC_AUTH0_CLIENT_ID=your-auth0-client-id
AUTH0_SECRET=your-auth0-secret
AUTH0_BASE_URL=https://modernaction.io
```

## Database Migration

### 1. Backup Current Database

```bash
# Create backup before deployment
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. Run Migrations

```bash
# Navigate to API directory
cd apps/api

# Install dependencies
poetry install

# Run database migrations
poetry run alembic upgrade head

# Verify migration
poetry run alembic current
```

### 3. Verify Bill Details Schema

```sql
-- Connect to database and verify tables
\dt bill_details

-- Check table structure
\d bill_details

-- Verify data integrity
SELECT COUNT(*) FROM bill_details;
SELECT needs_human_review, COUNT(*) FROM bill_details GROUP BY needs_human_review;
```

## Docker Deployment

### 1. Build Images

#### API Docker Image

```dockerfile
# apps/api/Dockerfile.prod
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY pyproject.toml poetry.lock ./
RUN pip install poetry && poetry config virtualenvs.create false
RUN poetry install --no-dev

# Copy application code
COPY . .

# Run database migrations and start server
CMD ["sh", "-c", "poetry run alembic upgrade head && poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000"]
```

#### Web Docker Image

```dockerfile
# apps/web/Dockerfile.prod
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM node:18-alpine AS runner

WORKDIR /app
COPY --from=builder /app/next.config.ts ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

EXPOSE 3000
CMD ["npm", "start"]
```

### 2. Build and Push Images

```bash
# Build API image
cd apps/api
docker build -f Dockerfile.prod -t modernaction-api:latest .

# Build Web image
cd ../web
docker build -f Dockerfile.prod -t modernaction-web:latest .

# Tag for ECR (replace with your registry)
docker tag modernaction-api:latest 308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-api:latest
docker tag modernaction-web:latest 308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-web:latest

# Push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 308755113449.dkr.ecr.us-east-1.amazonaws.com
docker push 308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-api:latest
docker push 308755113449.dkr.ecr.us-east-1.amazonaws.com/modernaction-web:latest
```

## AWS Infrastructure Deployment

### 1. CDK Deployment

```bash
# Navigate to infrastructure directory
cd infrastructure

# Install CDK dependencies
pip install -r requirements.txt

# Bootstrap CDK (first time only)
ENVIRONMENT=production cdk bootstrap

# Deploy production stack
ENVIRONMENT=production cdk deploy ModernAction-production --require-approval never
```

### 2. ECS Service Configuration

Update the ECS task definition to include bill details environment variables:

```python
# infrastructure/modernaction/ecs_service.py
task_definition = ecs.FargateTaskDefinition(
    self,
    "TaskDefinition",
    memory_limit_mib=2048,
    cpu=1024,
)

container = task_definition.add_container(
    "api-container",
    image=ecs.ContainerImage.from_registry(f"{ecr_repository.repository_uri}:latest"),
    environment={
        "DATABASE_URL": database_url,
        "OPENAI_API_KEY": openai_api_key,
        "BILL_DETAILS_COVERAGE_THRESHOLD": "0.6",
        "BILL_DETAILS_MAX_CITATIONS": "3",
        # ... other environment variables
    },
    logging=ecs.LogDrivers.aws_logs(
        stream_prefix="modernaction-api",
        log_retention=logs.RetentionDays.ONE_WEEK
    ),
)
```

## SSL/TLS Configuration

### 1. Certificate Setup

```bash
# Request SSL certificate through AWS Certificate Manager
aws acm request-certificate \
  --domain-name modernaction.io \
  --subject-alternative-names "*.modernaction.io" \
  --validation-method DNS \
  --region us-east-1
```

### 2. Load Balancer Configuration

Update ALB to use HTTPS and redirect HTTP:

```python
# infrastructure/modernaction/load_balancer.py
listener_https = alb.add_listener(
    "HTTPSListener",
    port=443,
    protocol=elbv2.ApplicationProtocol.HTTPS,
    certificates=[certificate],
    default_action=elbv2.ListenerAction.forward([target_group])
)

# Redirect HTTP to HTTPS
alb.add_listener(
    "HTTPListener",
    port=80,
    protocol=elbv2.ApplicationProtocol.HTTP,
    default_action=elbv2.ListenerAction.redirect(
        protocol="HTTPS",
        port="443",
        permanent=True
    )
)
```

## Monitoring & Logging

### 1. CloudWatch Setup

```python
# infrastructure/modernaction/monitoring.py
# API metrics
api_error_alarm = cloudwatch.Alarm(
    self, "APIErrorAlarm",
    metric=cloudwatch.Metric(
        namespace="ModernAction/API",
        metric_name="BillDetailsErrors",
        statistic="Sum"
    ),
    threshold=10,
    evaluation_periods=2
)

# Bill processing metrics
bill_processing_alarm = cloudwatch.Alarm(
    self, "BillProcessingAlarm",
    metric=cloudwatch.Metric(
        namespace="ModernAction/BillDetails",
        metric_name="ProcessingFailures",
        statistic="Sum"
    ),
    threshold=5,
    evaluation_periods=1
)
```

### 2. Application Logging

Update logging configuration:

```python
# apps/api/app/core/logging.py
import logging
import json
from datetime import datetime

class StructuredFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }

        # Add bill details specific context
        if hasattr(record, 'bill_id'):
            log_entry['bill_id'] = record.bill_id
        if hasattr(record, 'coverage_ratio'):
            log_entry['coverage_ratio'] = record.coverage_ratio

        return json.dumps(log_entry)

# Configure root logger
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    handlers=[logging.StreamHandler()]
)

# Use structured formatter for production
if os.getenv("ENVIRONMENT") == "production":
    handler = logging.StreamHandler()
    handler.setFormatter(StructuredFormatter())
    logging.getLogger().addHandler(handler)
```

## Health Checks

### 1. API Health Endpoint

Add comprehensive health check:

```python
# apps/api/app/api/v1/endpoints/health.py
@router.get("/health/detailed")
async def detailed_health_check(db: Session = Depends(get_db)):
    """Detailed health check including bill details system."""
    checks = {}
    overall_status = "healthy"

    # Database connectivity
    try:
        db.execute("SELECT 1")
        checks["database"] = "healthy"
    except Exception as e:
        checks["database"] = f"unhealthy: {str(e)}"
        overall_status = "unhealthy"

    # Bill details table check
    try:
        count = db.query(BillDetails).count()
        checks["bill_details_table"] = f"healthy ({count} records)"
    except Exception as e:
        checks["bill_details_table"] = f"unhealthy: {str(e)}"
        overall_status = "unhealthy"

    # AI service check
    ai_service = AIService()
    if ai_service.enabled:
        checks["ai_service"] = "healthy"
    else:
        checks["ai_service"] = "disabled"
        overall_status = "degraded"

    # Citation service check
    try:
        citation_service = TextCitationService()
        test_text = "SEC. 1. TEST SECTION\nThis is a test."
        index = citation_service.build_source_index(test_text)
        if len(index) > 0:
            checks["citation_service"] = "healthy"
        else:
            checks["citation_service"] = "unhealthy: no sections found"
            overall_status = "unhealthy"
    except Exception as e:
        checks["citation_service"] = f"unhealthy: {str(e)}"
        overall_status = "unhealthy"

    return {
        "status": overall_status,
        "timestamp": datetime.utcnow().isoformat(),
        "checks": checks,
        "version": "1.0.0"
    }
```

### 2. Load Balancer Health Check

Configure ALB health check:

```python
# infrastructure/modernaction/load_balancer.py
target_group = elbv2.ApplicationTargetGroup(
    self, "TargetGroup",
    port=8000,
    protocol=elbv2.ApplicationProtocol.HTTP,
    target_type=elbv2.TargetType.IP,
    vpc=vpc,
    health_check=elbv2.HealthCheck(
        enabled=True,
        healthy_http_codes="200",
        path="/api/v1/health/detailed",
        protocol=elbv2.Protocol.HTTP,
        timeout=Duration.seconds(10),
        interval=Duration.seconds(30),
        healthy_threshold_count=2,
        unhealthy_threshold_count=3
    )
)
```

## Performance Optimization

### 1. Database Optimization

```sql
-- Create indexes for bill details queries
CREATE INDEX CONCURRENTLY idx_bill_details_slug ON bill_details(seo_slug);
CREATE INDEX CONCURRENTLY idx_bill_details_review ON bill_details(needs_human_review);
CREATE INDEX CONCURRENTLY idx_bill_details_metrics ON bill_details USING GIN(metrics);

-- Analyze tables
ANALYZE bill_details;
```

### 2. CDN Configuration

```python
# infrastructure/modernaction/cdn.py
distribution = cloudfront.Distribution(
    self, "Distribution",
    default_behavior=cloudfront.BehaviorOptions(
        origin=origins.HttpOrigin(alb.load_balancer_dns_name),
        viewer_protocol_policy=cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        cache_policy=cloudfront.CachePolicy.CACHING_OPTIMIZED,
        allowed_methods=cloudfront.AllowedMethods.ALLOW_ALL,
        compress=True
    ),
    additional_behaviors={
        "/api/*": cloudfront.BehaviorOptions(
            origin=origins.HttpOrigin(alb.load_balancer_dns_name),
            cache_policy=cloudfront.CachePolicy.CACHING_DISABLED,
            viewer_protocol_policy=cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS
        ),
        "/bills/*": cloudfront.BehaviorOptions(
            origin=origins.HttpOrigin(web_alb.load_balancer_dns_name),
            cache_policy=cloudfront.CachePolicy.CACHING_OPTIMIZED_FOR_UNCOMPRESSED_OBJECTS,
            viewer_protocol_policy=cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS
        )
    }
)
```

## Post-Deployment Verification

### 1. Smoke Tests

```bash
# Test API endpoints
curl -f https://api.modernaction.io/api/v1/health/detailed
curl -f https://api.modernaction.io/api/v1/bills/details/by-slug/hr5-118

# Test web application
curl -f https://modernaction.io/bills/hr5-118
curl -f https://modernaction.io/api/auth/login

# Test SSL certificate
openssl s_client -connect modernaction.io:443 -servername modernaction.io < /dev/null
```

### 2. Database Verification

```sql
-- Verify bill details are being created
SELECT
    COUNT(*) as total_details,
    COUNT(*) FILTER (WHERE needs_human_review = true) as needs_review,
    AVG((metrics->>'coverage_ratio')::float) as avg_coverage
FROM bill_details;

-- Check recent activity
SELECT seo_slug, created_at, needs_human_review
FROM bill_details
ORDER BY created_at DESC
LIMIT 10;
```

### 3. Performance Testing

```bash
# Load test bill details endpoints
ab -n 100 -c 10 https://api.modernaction.io/api/v1/bills/details/by-slug/hr5-118

# Test web page performance
lighthouse https://modernaction.io/bills/hr5-118 --output html --output-path lighthouse-report.html
```

## Rollback Procedures

### 1. Database Rollback

```bash
# Rollback database migration if needed
poetry run alembic downgrade -1

# Restore from backup if necessary
psql $DATABASE_URL < backup_20240115_120000.sql
```

### 2. Application Rollback

```bash
# Rollback to previous ECS task definition
aws ecs update-service \
    --cluster modernaction-production \
    --service modernaction-api \
    --task-definition modernaction-api:previous-revision

# Rollback CDK stack
ENVIRONMENT=production cdk deploy ModernAction-production --no-rollback
```

## Maintenance

### 1. Database Maintenance

```sql
-- Regular maintenance tasks
VACUUM ANALYZE bill_details;
REINDEX INDEX idx_bill_details_slug;

-- Clean up old data (if needed)
DELETE FROM bill_details
WHERE created_at < NOW() - INTERVAL '1 year'
AND needs_human_review = false;
```

### 2. Log Rotation

```bash
# Configure log rotation for long-term storage
aws logs put-retention-policy \
    --log-group-name /aws/ecs/modernaction-api \
    --retention-in-days 30
```

### 3. Monitoring Dashboard

Create CloudWatch dashboard for bill details metrics:

```python
# infrastructure/modernaction/dashboard.py
dashboard = cloudwatch.Dashboard(
    self, "BillDetailsDashboard",
    dashboard_name="ModernAction-BillDetails"
)

dashboard.add_widgets(
    cloudwatch.GraphWidget(
        title="Bill Details Processing",
        left=[
            cloudwatch.Metric(namespace="ModernAction", metric_name="BillDetailsCreated"),
            cloudwatch.Metric(namespace="ModernAction", metric_name="CitationValidationErrors")
        ]
    ),
    cloudwatch.SingleValueWidget(
        title="Coverage Metrics",
        metrics=[
            cloudwatch.Metric(namespace="ModernAction", metric_name="AverageCoverage"),
            cloudwatch.Metric(namespace="ModernAction", metric_name="ReviewQueueLength")
        ]
    )
)
```

## Security Considerations

### 1. API Security

- Rate limiting implemented
- CORS properly configured
- JWT tokens validated
- Input sanitization for all endpoints
- SQL injection protection via ORM

### 2. Data Security

- Database encryption at rest
- API communication over HTTPS only
- Environment variables properly secured
- No sensitive data in logs

### 3. Bill Text Security

- Bill text stored securely
- Citations validated to prevent injection
- User input sanitized in all AI interactions

## Troubleshooting

### Common Issues

1. **Bill Details Not Loading**: Check database connection and migration status
2. **Citations Not Appearing**: Verify AI service API key and text citation service
3. **High Memory Usage**: Monitor ECS task memory and adjust limits
4. **Slow API Responses**: Check database indexes and query performance

### Debug Commands

```bash
# Check ECS service status
aws ecs describe-services --cluster modernaction-production --services modernaction-api

# View application logs
aws logs tail /aws/ecs/modernaction-api --follow

# Check database connections
psql $DATABASE_URL -c "SELECT count(*) FROM pg_stat_activity;"
```

## See Also

- [System Architecture](./bill-details-system.md)
- [API Documentation](./api-documentation.md)
- [Testing Guide](./testing-guide.md)
- [AI Pipeline Documentation](./ai-pipeline.md)