import React, { useState, useEffect, useRef } from 'react';
import { BillStatus } from '../../types';

interface FilterBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  statusFilter: BillStatus | 'all';
  onStatusFilterChange: (status: BillStatus | 'all') => void;
  urgencyFilter: 'all' | 'high' | 'medium' | 'low';
  onUrgencyFilterChange: (urgency: 'all' | 'high' | 'medium' | 'low') => void;
  sortBy: 'priority' | 'date' | 'title' | 'action_count';
  onSortChange: (sort: 'priority' | 'date' | 'title' | 'action_count') => void;
  totalCount: number;
  filteredCount: number;
}

const SearchIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const FilterIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
  </svg>
);

const SortIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
  </svg>
);

const XIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
);

export const FilterBar: React.FC<FilterBarProps> = ({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  urgencyFilter,
  onUrgencyFilterChange,
  sortBy,
  onSortChange,
  totalCount,
  filteredCount
}) => {
  const [isMobileDrawerOpen, setIsMobileDrawerOpen] = useState(false);
  const filterButtonRef = useRef<HTMLButtonElement>(null);
  
  const hasActiveFilters = searchQuery || statusFilter !== 'all' || urgencyFilter !== 'all' || sortBy !== 'priority';
  const activeFilterCount = [
    searchQuery,
    statusFilter !== 'all',
    urgencyFilter !== 'all',
    sortBy !== 'priority'
  ].filter(Boolean).length;

  const resetFilters = () => {
    onSearchChange('');
    onStatusFilterChange('all');
    onUrgencyFilterChange('all');
    onSortChange('priority');
  };
  
  const openMobileDrawer = () => {
    setIsMobileDrawerOpen(true);
  };

  const closeMobileDrawer = () => {
    setIsMobileDrawerOpen(false);
    // Return focus to the trigger button
    setTimeout(() => {
      filterButtonRef.current?.focus();
    }, 100);
  };

  const applyFiltersAndCloseDrawer = () => {
    closeMobileDrawer();
  };

  const resetFiltersAndCloseDrawer = () => {
    resetFilters();
    closeMobileDrawer();
  };

  // Handle escape key to close drawer
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isMobileDrawerOpen) {
        closeMobileDrawer();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isMobileDrawerOpen]);

  // Lock body scroll when drawer is open
  useEffect(() => {
    if (isMobileDrawerOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isMobileDrawerOpen]);

  const getActiveFilterPills = () => {
    const pills = [];
    
    if (searchQuery) {
      pills.push({
        key: 'search',
        label: `Search: "${searchQuery}"`.substring(0, 25) + (searchQuery.length > 25 ? '...' : ''),
        onRemove: () => onSearchChange('')
      });
    }
    
    if (statusFilter !== 'all') {
      const statusLabels: Record<string, string> = {
        committee: 'In Committee',
        floor: 'Floor Vote',
        passed: 'Passed'
      };
      pills.push({
        key: 'status',
        label: `Status: ${statusLabels[statusFilter] || statusFilter}`,
        onRemove: () => onStatusFilterChange('all')
      });
    }
    
    if (urgencyFilter !== 'all') {
      const urgencyLabels: Record<string, string> = {
        high: '🔥 High Urgency',
        medium: '⚡ Medium Urgency', 
        low: '📋 Low Urgency'
      };
      pills.push({
        key: 'urgency',
        label: urgencyLabels[urgencyFilter],
        onRemove: () => onUrgencyFilterChange('all')
      });
    }
    
    if (sortBy !== 'priority') {
      const sortLabels: Record<string, string> = {
        date: 'Sort: Date',
        title: 'Sort: Title',
        action_count: 'Sort: Actions'
      };
      pills.push({
        key: 'sort',
        label: sortLabels[sortBy],
        onRemove: () => onSortChange('priority')
      });
    }
    
    return pills;
  };

  const activeFilterPills = getActiveFilterPills();

  return (
    <>
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 sticky top-0 z-40">
      {/* Main Toolbar */}
      <div className="p-4">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 min-w-0">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
                <SearchIcon />
              </div>
              <input
                type="text"
                placeholder="Search bills... (⌘K for quick search)"
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg text-sm bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              />
            </div>
          </div>

          {/* Mobile Filter Button - Show only on mobile */}
          <div className="lg:hidden">
            <button
              ref={filterButtonRef}
              onClick={openMobileDrawer}
              className="inline-flex h-10 items-center gap-2 rounded-lg px-4 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              aria-label="Open filters"
            >
              <FilterIcon />
              Filters
              {hasActiveFilters && (
                <span className="bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-semibold">
                  {activeFilterCount}
                </span>
              )}
            </button>
          </div>

          {/* Desktop Filters and Sort - Hide on mobile */}
          <div className="hidden lg:flex flex-wrap items-center gap-3">
            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => onStatusFilterChange(e.target.value as BillStatus | 'all')}
              className="h-10 border border-gray-300 rounded-lg px-3 text-sm text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option value="all">All Statuses</option>
              <option value="committee">In Committee</option>
              <option value="floor">Floor Vote</option>
              <option value="passed">Passed</option>
            </select>

            {/* Urgency Filter */}
            <select
              value={urgencyFilter}
              onChange={(e) => onUrgencyFilterChange(e.target.value as 'all' | 'high' | 'medium' | 'low')}
              className="h-10 border border-gray-300 rounded-lg px-3 text-sm text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option value="all">All Urgency</option>
              <option value="high">🔥 High</option>
              <option value="medium">⚡ Medium</option>
              <option value="low">📋 Low</option>
            </select>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => onSortChange(e.target.value as 'priority' | 'date' | 'title' | 'action_count')}
              className="h-10 border border-gray-300 rounded-lg px-3 text-sm text-gray-900 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option value="priority">Priority</option>
              <option value="date">Date</option>
              <option value="title">Title (A-Z)</option>
              <option value="action_count">Most Actions</option>
            </select>

            {/* Quick Filter Chips */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => {
                  onStatusFilterChange('floor' as BillStatus);
                  onUrgencyFilterChange('high');
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    onStatusFilterChange('floor' as BillStatus);
                    onUrgencyFilterChange('high');
                  }
                }}
                aria-pressed={statusFilter === 'floor' && urgencyFilter === 'high'}
                className="inline-flex h-8 items-center gap-1.5 rounded-full px-3 text-xs font-medium bg-red-100 text-red-800 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-300 focus:ring-offset-2 transition-colors"
              >
                <span aria-hidden="true">🔥</span> Urgent
              </button>
              <button
                onClick={() => {
                  onStatusFilterChange('committee' as BillStatus);
                  onUrgencyFilterChange('all');
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    onStatusFilterChange('committee' as BillStatus);
                    onUrgencyFilterChange('all');
                  }
                }}
                aria-pressed={statusFilter === 'committee'}
                className="inline-flex h-8 items-center gap-1.5 rounded-full px-3 text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-green-300 focus:ring-offset-2 transition-colors"
              >
                <span aria-hidden="true">📈</span> Active
              </button>
            </div>

            {/* Reset Button */}
            {hasActiveFilters && (
              <button
                onClick={resetFilters}
                className="inline-flex h-8 items-center gap-1.5 rounded-lg px-3 text-xs font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-offset-2 transition-colors"
              >
                Reset
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Active Filters Row - Direct under toolbar */}
      {activeFilterPills.length > 0 && (
        <div className="px-4 pb-4 pt-2 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium text-gray-600">
                Active filters ({activeFilterPills.length}):
              </span>
              <div className="flex flex-wrap items-center gap-1.5">
                {activeFilterPills.map((pill) => (
                  <button
                    key={pill.key}
                    onClick={pill.onRemove}
                    className="inline-flex items-center gap-1.5 h-7 px-2.5 bg-blue-50 text-blue-800 text-xs font-medium rounded-full hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-offset-2 transition-colors group"
                    aria-label={`Remove filter: ${pill.label}`}
                  >
                    <span className="truncate max-w-32">{pill.label}</span>
                    <svg className="w-3 h-3 text-blue-600 group-hover:text-blue-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                ))}
              </div>
            </div>
            {/* Right-aligned Reset as quiet link */}
            <button
              onClick={resetFilters}
              className="text-xs text-gray-600 hover:text-gray-900 underline hover:no-underline transition-colors focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-offset-2 rounded px-1 py-1"
            >
              Reset
            </button>
          </div>
        </div>
      )}
    </div>

    {/* Mobile Filter Drawer */}
    {isMobileDrawerOpen && (
      <div className="fixed inset-0 z-50 lg:hidden" role="dialog" aria-modal="true" aria-labelledby="mobile-filter-title">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={closeMobileDrawer}
          aria-hidden="true"
        />
        
        {/* Drawer Panel */}
        <div className="fixed inset-0 flex">
          <div className="relative ml-auto flex h-full w-full max-w-sm flex-col overflow-y-auto bg-white shadow-xl">
            {/* Header */}
            <div className="flex items-center justify-between px-4 py-4 border-b border-gray-200">
              <h2 id="mobile-filter-title" className="text-lg font-semibold text-gray-900">Filters</h2>
              <button
                onClick={closeMobileDrawer}
                className="p-2 -mr-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg"
                aria-label="Close filters"
              >
                <XIcon />
              </button>
            </div>

            {/* Filter Content */}
            <div className="flex-1 px-4 py-6 space-y-6">
              {/* Status Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-3">Status</label>
                <div className="space-y-2">
                  {[
                    { value: 'all', label: 'All Statuses' },
                    { value: 'committee', label: 'In Committee' },
                    { value: 'floor', label: 'Floor Vote' },
                    { value: 'passed', label: 'Passed' }
                  ].map((option) => (
                    <label key={option.value} className="flex items-center">
                      <input
                        type="radio"
                        name="status"
                        value={option.value}
                        checked={statusFilter === option.value}
                        onChange={(e) => onStatusFilterChange(e.target.value as BillStatus | 'all')}
                        className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-sm text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Urgency Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-3">Urgency</label>
                <div className="space-y-2">
                  {[
                    { value: 'all', label: 'All Urgency' },
                    { value: 'high', label: '🔥 High', emoji: true },
                    { value: 'medium', label: '⚡ Medium', emoji: true },
                    { value: 'low', label: '📋 Low', emoji: true }
                  ].map((option) => (
                    <label key={option.value} className="flex items-center">
                      <input
                        type="radio"
                        name="urgency"
                        value={option.value}
                        checked={urgencyFilter === option.value}
                        onChange={(e) => onUrgencyFilterChange(e.target.value as 'all' | 'high' | 'medium' | 'low')}
                        className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-sm text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Sort Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-3">Sort by</label>
                <div className="space-y-2">
                  {[
                    { value: 'priority', label: 'Priority' },
                    { value: 'date', label: 'Date' },
                    { value: 'title', label: 'Title (A-Z)' },
                    { value: 'action_count', label: 'Most Actions' }
                  ].map((option) => (
                    <label key={option.value} className="flex items-center">
                      <input
                        type="radio"
                        name="sort"
                        value={option.value}
                        checked={sortBy === option.value}
                        onChange={(e) => onSortChange(e.target.value as 'priority' | 'date' | 'title' | 'action_count')}
                        className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-3 text-sm text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Quick Filter Chips */}
              <div>
                <label className="block text-sm font-medium text-gray-900 mb-3">Quick Filters</label>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => {
                      onStatusFilterChange('floor' as BillStatus);
                      onUrgencyFilterChange('high');
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        onStatusFilterChange('floor' as BillStatus);
                        onUrgencyFilterChange('high');
                      }
                    }}
                    aria-pressed={statusFilter === 'floor' && urgencyFilter === 'high'}
                    className="inline-flex h-8 items-center gap-1.5 rounded-full px-3 text-xs font-medium bg-red-100 text-red-800 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-300 focus:ring-offset-2 transition-colors"
                  >
                    <span aria-hidden="true">🔥</span> Urgent
                  </button>
                  <button
                    onClick={() => {
                      onStatusFilterChange('committee' as BillStatus);
                      onUrgencyFilterChange('all');
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        onStatusFilterChange('committee' as BillStatus);
                        onUrgencyFilterChange('all');
                      }
                    }}
                    aria-pressed={statusFilter === 'committee'}
                    className="inline-flex h-8 items-center gap-1.5 rounded-full px-3 text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-green-300 focus:ring-offset-2 transition-colors"
                  >
                    <span aria-hidden="true">📈</span> Active
                  </button>
                </div>
              </div>
            </div>

            {/* Sticky Bottom Action Bar */}
            <div className="border-t border-gray-200 bg-white px-4 py-4">
              <div className="flex gap-3">
                <button
                  onClick={resetFiltersAndCloseDrawer}
                  className="flex-1 bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  Reset
                </button>
                <button
                  onClick={applyFiltersAndCloseDrawer}
                  className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Apply Filters
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )}
    </>
  );
};
