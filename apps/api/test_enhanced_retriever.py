#!/usr/bin/env python3
"""
Test the enhanced span retriever
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

async def test_enhanced_retriever():
    """Test the enhanced span retriever with sample bill text"""
    try:
        from app.services.enhanced_span_retriever import EnhancedSpanRetriever
        
        retriever = EnhancedSpanRetriever()
        
        print("🧪 Testing Enhanced Span Retriever")
        print("=" * 60)
        
        # Sample bill text with mandates, budget, and scope
        sample_bill_text = """
        SEC. 1. SHORT TITLE.
        This Act may be cited as the "Test Bill Act of 2024".
        
        SEC. 2. FUNDING AUTHORIZATION.
        (a) In General.--There are authorized to be appropriated $50,000,000 for fiscal year 2024 to carry out this section.
        (b) Limitations.--No funds authorized under this section shall be used for administrative costs exceeding 10 percent of the total appropriation.
        
        SEC. 3. REQUIREMENTS AND PENALTIES.
        (a) Mandatory Compliance.--Each agency shall comply with the requirements of this Act not later than 180 days after the date of enactment.
        (b) Civil Penalties.--Any person who violates this section shall be subject to a civil penalty of not more than $10,000 per violation.
        (c) Enforcement.--The Secretary must enforce the provisions of this Act through regular monitoring and inspection.
        
        SEC. 4. DEFINITIONS.
        (a) In General.--For purposes of this Act, the following definitions apply:
        (1) AGENCY.--The term "agency" means any Federal department or independent establishment.
        (2) VIOLATION.--The term "violation" means any failure to comply with the requirements of this Act.
        """
        
        bill_metadata = {
            'bill_id': 'test-bill-123',
            'title': 'Test Bill Act of 2024',
            'bill_number': 'HR123'
        }
        
        # Test span extraction
        result = retriever.extract_enhanced_spans(sample_bill_text, bill_metadata)
        
        print(f"📊 Extraction Results:")
        print(f"Total spans: {result.get('total_spans', 0)}")
        print(f"Routing: {result.get('routing', 'unknown')}")
        print(f"Has money: {result.get('has_money', False)}")
        print(f"Has mandates: {result.get('has_mandates', False)}")
        
        evidence_spans = result.get('evidence', [])
        print(f"\n📋 Evidence Spans ({len(evidence_spans)}):")
        
        # Group by type
        by_type = {}
        for span in evidence_spans:
            span_type = span.get('type', 'unknown')
            if span_type not in by_type:
                by_type[span_type] = []
            by_type[span_type].append(span)
        
        for span_type, spans in by_type.items():
            print(f"\n🔹 {span_type.upper()} spans ({len(spans)}):")
            for i, span in enumerate(spans[:3]):  # Show first 3
                quote = span.get('quote', '')[:80] + '...' if len(span.get('quote', '')) > 80 else span.get('quote', '')
                priority = span.get('priority', 'unknown')
                heading = span.get('heading', 'Unknown')
                budget_cat = span.get('budget_category', '')
                
                print(f"  {i+1}. [{priority}] {quote}")
                print(f"     Section: {heading}")
                if budget_cat:
                    print(f"     Budget Type: {budget_cat}")
        
        # Test specific features
        print(f"\n🧪 Feature Tests:")
        
        # Test mandate detection
        mandate_spans = [s for s in evidence_spans if s.get('type') == 'mandate']
        print(f"✅ Mandate detection: {len(mandate_spans)} spans found")
        
        # Test budget classification
        budget_spans = [s for s in evidence_spans if s.get('type') == 'budget']
        budget_categories = set(s.get('budget_category') for s in budget_spans if s.get('budget_category'))
        print(f"✅ Budget classification: {len(budget_spans)} spans, categories: {budget_categories}")
        
        # Test clause completion
        long_spans = [s for s in evidence_spans if len(s.get('quote', '')) > 50]
        print(f"✅ Clause completion: {len(long_spans)} spans with >50 chars")
        
        # Test priority assignment
        high_priority = [s for s in evidence_spans if s.get('priority') == 'high']
        print(f"✅ Priority assignment: {len(high_priority)} high-priority spans")
        
        return len(evidence_spans) > 0 and result.get('routing') in ['high', 'medium']
        
    except Exception as e:
        print(f"❌ Enhanced retriever test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ai_service_integration():
    """Test that AI service uses the enhanced retriever"""
    try:
        from app.services.ai_service import AIService
        
        print("\n🔗 Testing AI Service Integration")
        print("=" * 60)
        
        ai_service = AIService()
        if not ai_service.enabled:
            print("⚠️ AI service not enabled - skipping integration test")
            return True
        
        print("✅ AI service has enhanced retriever:", hasattr(ai_service, 'span_retriever'))
        print("✅ Retriever type:", type(ai_service.span_retriever).__name__)
        
        # Test span extraction through AI service
        sample_text = "SEC. 1. There are authorized to be appropriated $1,000,000 for this Act. Each agency shall comply within 30 days."
        metadata = {'bill_id': 'test', 'title': 'Test'}
        
        spans = await ai_service._extract_evidence_spans(sample_text, metadata)
        
        print(f"✅ Span extraction works: {len(spans.get('tldr', []))} tldr, {len(spans.get('budget', []))} budget, {len(spans.get('who', []))} who spans")
        
        return True
        
    except Exception as e:
        print(f"❌ AI service integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all enhanced retriever tests"""
    print("🧪 Testing Enhanced Span Retriever System")
    print("=" * 60)
    
    # Test enhanced retriever
    retriever_success = await test_enhanced_retriever()
    
    # Test AI service integration
    integration_success = await test_ai_service_integration()
    
    print("\n" + "=" * 60)
    print("📋 Overall Test Results:")
    print(f"✅ Enhanced retriever: {'PASS' if retriever_success else 'FAIL'}")
    print(f"✅ AI service integration: {'PASS' if integration_success else 'FAIL'}")
    
    if retriever_success and integration_success:
        print("\n🎉 ALL ENHANCED RETRIEVER TESTS PASSED!")
        print("🚀 Better evidence spans with clause completion")
        print("💰 Budget classification working")
        print("⚖️ Mandate detection enhanced")
        print("🎯 Priority routing implemented")
    else:
        print("\n❌ ENHANCED RETRIEVER TESTS FAILED")
        print("🔧 Need to fix retriever system")

if __name__ == "__main__":
    asyncio.run(main())
