# Frontend Integration Guide
> Last updated: 2025-08-11


## Overview

The Bill Details frontend provides a comprehensive, SEO-optimized interface for displaying legislative analysis with citation-based transparency. Built with Next.js 15 App Router and TypeScript.

## Architecture

### Route Structure

```
/bills/[slug]/page.tsx - Main bill details page
```

### Key Components

1. **BillDetailsPage** - Main SSR page component
2. **Citations** - Citation display with source links
3. **PositionColumn** - Support/oppose/amend reasons
4. **BillActionModal** - Integration point with "Learn more" links

## Implementation

### Page Component (`/bills/[slug]/page.tsx`)

```typescript
// Server-side rendered with dynamic metadata
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const details = await fetchDetailsBySlug(params.id);
  return {
    title: details?.seo_title || 'Bill Details',
    description: details?.seo_meta_description || '',
    alternates: { canonical: details?.canonical_url },
    // JSON-LD structured data
    other: {
      'script:ld+json': JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'Legislation',
        name: title,
        description,
        url: canonical,
      }),
    },
  };
}
```

### Data Fetching

```typescript
async function fetchDetailsBySlug(slug: string): Promise<BillDetailsResponse | null> {
  try {
    const data = await billApi.getBillDetailsBySlug(slug);
    return data as BillDetailsResponse;
  } catch (err) {
    // Graceful error handling - returns null for 404s
    return null;
  }
}
```

## Key Features

### 1. Moderation Banner

Displays when `needs_human_review: true`:

```tsx
{details.needs_human_review && (
  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6" data-testid="moderation-banner">
    <h3 className="font-semibold text-blue-900 mb-2">AI-Generated Analysis</h3>
    <p className="text-blue-800 text-sm">
      This content was generated by our AI and is awaiting review by a human expert.
      Use it as a starting point for your own research, not as a definitive source.
    </p>
    {details.metrics && (
      <div className="mt-2 text-xs text-blue-700 flex gap-4">
        <span data-testid="coverage-metric">
          Coverage: {Math.round(details.metrics.coverage_ratio * 100)}%
        </span>
        {details.metrics.unverified_count > 0 && (
          <span data-testid="unverified-metric">
            {details.metrics.unverified_count} sections awaiting citations
          </span>
        )}
      </div>
    )}
  </div>
)}
```

### 2. Citation Display

Each section includes citations with source links:

```tsx
function Citations({ citations }: { citations: Citation[] }) {
  if (!citations?.length) return null;

  return (
    <div className="mt-2 text-sm text-gray-500" data-testid="citations">
      {citations.map((citation, i) => (
        <div key={i}>
          "<span data-testid="citation-quote">{citation.quote}</span>"
          {citation.heading && (
            <span> —
              <a
                href={`#${citation.anchor_id}`}
                className="text-blue-700 underline"
                data-testid="citation-link"
              >
                {citation.heading}
              </a>
            </span>
          )}
        </div>
      ))}
    </div>
  );
}
```

### 3. Anchor Navigation

Structured sections with navigable anchors:

```tsx
<nav className="text-sm mb-6 text-blue-700 flex gap-4">
  <a href="#overview-what">What it does</a>
  <a href="#overview-who">Who it affects</a>
  <a href="#overview-why">Why it matters</a>
  <a href="#provisions">Provisions</a>
  <a href="#cost">Cost</a>
  <a href="#timeline">Timeline</a>
</nav>

<section id="overview-what" className="mb-8">
  <h2 className="text-xl font-semibold mb-2">What this bill does</h2>
  {details.overview?.what_does?.content && (
    <div className="space-y-2">
      <p>{details.overview.what_does.content}</p>
      <Citations citations={details.overview.what_does.citations || []} />
    </div>
  )}
</section>
```

### 4. Additional Bill Content

Transparency section for non-key content:

```tsx
{details.other_details?.length && (
  <section id="additional-content" className="mb-12">
    <h2 className="text-xl font-semibold mb-4">Additional Bill Content</h2>
    <div className="space-y-4">
      {details.other_details.map((section, i) => (
        <div key={i} className="bg-white p-4 rounded-lg border">
          <p className="text-gray-700">{section.content}</p>
          <Citations citations={section.citations || []} />
        </div>
      ))}
    </div>
  </section>
)}
```

### 5. Error Handling

Graceful fallback for missing data:

```tsx
if (!details) {
  return (
    <div className="min-h-screen bg-gray-50">
      <main className="max-w-3xl mx-auto px-4 py-10">
        <h1 className="text-2xl font-bold mb-4">Bill details not available yet</h1>
        <p className="text-gray-700 mb-6">
          We couldn't load the detailed analysis for this bill right now. Try again shortly.
        </p>
      </main>
    </div>
  );
}
```

## Action Modal Integration

The `BillActionModal` component includes "Learn more" links that deep-link to bill details:

```tsx
// In BillActionModal.tsx
const getAnchorId = (title: string): string => {
  if (title.toLowerCase().includes('what')) return 'overview-what';
  if (title.toLowerCase().includes('who')) return 'overview-who';
  if (title.toLowerCase().includes('why')) return 'overview-why';
  if (title.toLowerCase().includes('provision')) return 'provisions';
  return title.toLowerCase().replace(/[^a-z0-9]/g, '-');
};

// Learn more link
<a
  href={`/bills/${bill.seo_slug}#${getAnchorId(title)}`}
  target="_blank"
  rel="noopener noreferrer"
  className="text-xs text-blue-600 hover:text-blue-800 underline"
  data-testid="learn-more-link"
>
  Learn more →
</a>
```

## SEO Optimization

### Metadata Generation

```typescript
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const details = await fetchDetailsBySlug(params.id);
  const title = details?.seo_title || 'Bill Details';
  const description = details?.seo_meta_description || details?.hero_summary || '';
  const canonical = details?.canonical_url || `https://modernaction.io/bills/${params.id}`;

  return {
    title,
    description,
    alternates: { canonical },
    openGraph: {
      title,
      description,
      url: canonical,
      type: 'article'
    },
    other: {
      'script:ld+json': JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'Legislation',
        name: title,
        description,
        url: canonical,
      }),
    },
  };
## Slug Utilities

Use shared helpers to build canonical links:

```ts
// apps/web/src/utils/slug.ts
export function normalizeBillNumber(input: string): string {
  return (input || '').toLowerCase().replace(/[^a-z0-9]/g, '');
}
export function buildBillSlug(billNumber: string, session: number | string): string {
  const num = normalizeBillNumber(billNumber);
  return `${encodeURIComponent(num)}-${session}`;
}
```

Example usage in EnhancedBillCard:

```tsx
<Link href={`/bills/${buildBillSlug(bill.bill_number || '', bill.session_year)}`}>
  Learn More
</Link>
```

}
```

### URL Structure

- Canonical format: `/bills/hr5-118`
- Supports legacy formats: `/bills/5-118`
- Clean, readable slugs
- Proper canonical tags

## Responsive Design

The layout is fully responsive with mobile-first design:

```tsx
<div className="max-w-5xl mx-auto px-4 py-10">
  {/* Mobile-optimized navigation */}
  <nav className="text-sm mb-6 text-blue-700 flex flex-wrap gap-4">
    {/* Navigation links */}
  </nav>

  {/* Grid layouts collapse on mobile */}
  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
    {/* Position columns */}
  </div>
</div>
```

## TypeScript Integration

### Type Definitions

```typescript
// Extended Bill type with seo_slug
interface Bill extends BaseModel {
  // ... existing fields
  seo_slug?: string;
}

// Bill details response type
interface BillDetailsResponse {
  id: string;
  bill_id: string;
  seo_slug?: string;
  seo_title?: string;
  seo_meta_description?: string;
  canonical_url?: string;
  hero_summary?: string;
  overview?: Overview;
  positions?: Positions;
  other_details?: SectionWithCitations[];
  source_index?: SourceIndexItem[];
  needs_human_review: boolean;
  metrics?: Metrics;
  created_at: string;
  updated_at: string;
}
```

### API Client

```typescript
// In apiClient.ts
export const billApi = {
  getBillDetailsBySlug: async (slug: string): Promise<BillDetailsResponse> => {
    const response = await fetch(`/api/v1/bills/details/by-slug/${slug}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch bill details: ${response.statusText}`);
    }
    return response.json();
  }
};
```

## Testing

### Test IDs

All interactive elements include `data-testid` attributes:

- `moderation-banner` - AI content warning
- `coverage-metric` - Citation coverage percentage
- `unverified-metric` - Unverified sections count
- `citations` - Citation container
- `citation-quote` - Individual quote text
- `citation-link` - Source section link

### E2E Testing

```typescript
// Cypress test example
it('displays citations with working source links', () => {
  cy.visit('/bills/hr5-118');

  cy.get('[data-testid="citations"]').first().within(() => {
    cy.get('[data-testid="citation-quote"]').should('be.visible');
    cy.get('[data-testid="citation-link"]').should('have.attr', 'href').and('include', '#sec-');

    // Click citation link and verify navigation
    cy.get('[data-testid="citation-link"]').first().click();
    cy.url().should('include', '#sec-');
  });
});
```

## Performance Considerations

### Server-Side Rendering

- Full SSR with Next.js App Router
- Metadata generation at build time
- Efficient data fetching with error boundaries

### Caching Strategy

- API responses cached at CDN level
- Static generation for popular bills
- Incremental Static Regeneration (ISR) support

### Bundle Optimization

- Tree-shaking for unused components
- Code splitting at route level
- Optimized CSS with Tailwind purging

## See Also

- [API Documentation](./api-documentation.md)
- [Testing Guide](./testing-guide.md)
- [AI Pipeline Documentation](./ai-pipeline.md)