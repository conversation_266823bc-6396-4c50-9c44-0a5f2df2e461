#!/usr/bin/env python3
"""
Test Phase 2 Quality Improvements
Validates that Phase 2 enhancements maintain cost controls while improving quality
"""

import sys
import os
import asyncio
import logging
import time
from typing import Dict, List, Any

# Add the API directory to the path
sys.path.append('/Users/<USER>/modern-action-2.0/apps/api')

from app.services.balanced_analysis_service import BalancedAnalysisService
from app.services.ai_service import AIService
from app.services.quality_validation_service import get_quality_validator, QualityLevel
from app.services.evidence_quality_service import get_evidence_quality_service
from app.services.quality_metrics_tracking_service import get_quality_metrics_tracker
from app.services.content_quality_scoring_service import get_content_quality_scorer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Phase2QualityTester:
    """Test Phase 2 quality improvements"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.balanced_service = BalancedAnalysisService(self.ai_service)
        self.quality_validator = get_quality_validator()
        self.evidence_quality_service = get_evidence_quality_service()
        self.quality_tracker = get_quality_metrics_tracker()
        self.quality_scorer = get_content_quality_scorer()
    
    async def test_phase2_improvements(self):
        """Test Phase 2 quality improvements with sample bill data"""
        
        print("🧪 Testing Phase 2 Quality Improvements")
        print("=" * 60)
        
        # Test bill data (simulating a complex bill with quality requirements)
        test_bill = {
            'bill_id': 'test-phase2-001',
            'title': 'Test Enhanced Quality Analysis Act of 2024',
            'bill_text': """
            This Act appropriates $500 million to the Department of Environmental Protection
            for implementation of enhanced water quality monitoring systems. The Secretary
            shall establish monitoring stations within 180 days of enactment, with civil
            penalties up to $100,000 for non-compliance with reporting requirements.
            
            Section 2. Funding Authorization
            There is authorized to be appropriated $500,000,000 for fiscal year 2025
            to carry out the provisions of this Act.
            
            Section 3. Implementation Requirements
            Not later than 180 days after the date of enactment, the Secretary shall
            establish at least 50 monitoring stations in high-priority watersheds.
            
            Section 4. Enforcement
            Any person who fails to comply with monitoring requirements shall be
            subject to a civil penalty of not more than $100,000 per violation.
            
            Section 5. Reporting
            The Secretary shall submit annual reports to Congress detailing monitoring
            results and compliance actions taken under this Act.
            """
        }
        
        # Enhanced evidence spans (simulating high-quality extraction)
        test_evidence = [
            {
                'id': 'ev_001',
                'heading': 'Funding Authorization',
                'quote': 'There is authorized to be appropriated $500,000,000 for fiscal year 2025 to carry out the provisions of this Act.',
                'start_offset': 200,
                'end_offset': 300
            },
            {
                'id': 'ev_002', 
                'heading': 'Implementation Timeline',
                'quote': 'Not later than 180 days after the date of enactment, the Secretary shall establish at least 50 monitoring stations in high-priority watersheds.',
                'start_offset': 400,
                'end_offset': 520
            },
            {
                'id': 'ev_003',
                'heading': 'Civil Penalties',
                'quote': 'Any person who fails to comply with monitoring requirements shall be subject to a civil penalty of not more than $100,000 per violation.',
                'start_offset': 600,
                'end_offset': 720
            },
            {
                'id': 'ev_004',
                'heading': 'Agency Responsibility',
                'quote': 'The Secretary shall establish monitoring stations within 180 days of enactment, with civil penalties up to $100,000 for non-compliance.',
                'start_offset': 100,
                'end_offset': 200
            },
            {
                'id': 'ev_005',
                'heading': 'Congressional Reporting',
                'quote': 'The Secretary shall submit annual reports to Congress detailing monitoring results and compliance actions taken under this Act.',
                'start_offset': 800,
                'end_offset': 900
            }
        ]
        
        # Test Phase 2 analysis
        start_time = time.time()
        
        try:
            # Run balanced analysis with Phase 2 improvements
            result = await self.balanced_service.analyze_bill_balanced(
                bill_text=test_bill['bill_text'],
                bill_metadata=test_bill,
                evidence_spans=test_evidence
            )
            
            analysis_time = time.time() - start_time
            
            if result['success']:
                await self._evaluate_phase2_results(result, analysis_time, test_bill)
            else:
                print(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")
                return False
            
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            return False
        
        return True
    
    async def _evaluate_phase2_results(self, result: Dict[str, Any], 
                                     analysis_time: float, test_bill: Dict[str, Any]):
        """Evaluate Phase 2 results for quality and cost efficiency"""
        
        print("\n📊 Phase 2 Results Analysis")
        print("-" * 40)
        
        # Extract key metrics
        quality_metrics = result.get('quality_metrics', {})
        evidence_quality = result.get('evidence_quality', {})
        cost_breakdown = result.get('cost_breakdown', {})
        analysis = result.get('analysis', {})
        
        # 1. QUALITY ASSESSMENT
        print(f"\n🎯 QUALITY METRICS:")
        print(f"   Overall Score: {quality_metrics.get('overall_score', 0):.2f}")
        print(f"   Quality Level: {quality_metrics.get('quality_level', 'unknown')}")
        print(f"   Specificity: {quality_metrics.get('specificity_score', 0):.2f}")
        print(f"   Evidence Grounding: {quality_metrics.get('evidence_grounding_score', 0):.2f}")
        print(f"   Comprehensiveness: {quality_metrics.get('comprehensiveness_score', 0):.2f}")
        print(f"   Clarity: {quality_metrics.get('clarity_score', 0):.2f}")
        print(f"   Actionability: {quality_metrics.get('actionability_score', 0):.2f}")
        
        # 2. EVIDENCE QUALITY ASSESSMENT
        print(f"\n📚 EVIDENCE QUALITY:")
        print(f"   Input Spans: {evidence_quality.get('total_spans_input', 0)}")
        print(f"   Validated Spans: {evidence_quality.get('total_spans_validated', 0)}")
        print(f"   Average Quality: {evidence_quality.get('average_quality_score', 0):.2f}")
        print(f"   Quality Filter Applied: {evidence_quality.get('quality_improvement_applied', False)}")
        
        # 3. COST CONTROL ASSESSMENT
        print(f"\n💰 COST CONTROL:")
        print(f"   Total Cost: ${cost_breakdown.get('total_cost', 0):.4f}")
        print(f"   Budget Remaining: ${cost_breakdown.get('budget_remaining', 0):.4f}")
        print(f"   Budget Exhausted: {cost_breakdown.get('budget_exhausted', False)}")
        print(f"   Analysis Time: {analysis_time:.2f}s")
        
        # 4. CONTENT QUALITY ASSESSMENT
        print(f"\n📝 CONTENT QUALITY:")
        complete_analysis = analysis.get('complete_analysis', [])
        print(f"   Sections Generated: {len(complete_analysis)}")
        
        # Check for specific quality indicators
        has_specific_amounts = self._check_specific_amounts(analysis)
        has_specific_deadlines = self._check_specific_deadlines(analysis)
        has_specific_enforcement = self._check_specific_enforcement(analysis)
        has_evidence_grounding = self._check_evidence_grounding(analysis)
        
        print(f"   Specific Amounts: {'✅' if has_specific_amounts else '❌'}")
        print(f"   Specific Deadlines: {'✅' if has_specific_deadlines else '❌'}")
        print(f"   Specific Enforcement: {'✅' if has_specific_enforcement else '❌'}")
        print(f"   Evidence Grounded: {'✅' if has_evidence_grounding else '❌'}")
        
        # 5. HR5-118 COMPLIANCE ASSESSMENT
        print(f"\n🏆 HR5-118 COMPLIANCE:")
        hr5118_compliant = quality_metrics.get('overall_score', 0) >= 0.8
        print(f"   HR5-118 Compliant: {'✅' if hr5118_compliant else '❌'}")
        
        # 6. PHASE 2 IMPROVEMENTS VALIDATION
        print(f"\n🚀 PHASE 2 IMPROVEMENTS:")
        
        # Quality validation was applied
        quality_validation_applied = 'quality_metrics' in result
        print(f"   Quality Validation: {'✅' if quality_validation_applied else '❌'}")
        
        # Evidence quality filtering was applied
        evidence_filtering_applied = evidence_quality.get('quality_improvement_applied', False)
        print(f"   Evidence Filtering: {'✅' if evidence_filtering_applied else '❌'}")
        
        # Sophisticated analysis patterns used
        sophisticated_patterns = self._check_sophisticated_patterns(analysis)
        print(f"   Sophisticated Patterns: {'✅' if sophisticated_patterns else '❌'}")
        
        # Quality metrics tracking
        quality_tracking_applied = len(self.quality_tracker.quality_snapshots) > 0
        print(f"   Quality Tracking: {'✅' if quality_tracking_applied else '❌'}")
        
        # 7. OVERALL ASSESSMENT
        print(f"\n🎉 OVERALL PHASE 2 ASSESSMENT:")
        
        total_checks = 8
        passed_checks = sum([
            hr5118_compliant,
            cost_breakdown.get('total_cost', 1) < 0.25,  # Within budget
            has_specific_amounts,
            has_evidence_grounding,
            quality_validation_applied,
            evidence_filtering_applied,
            sophisticated_patterns,
            quality_tracking_applied
        ])
        
        success_rate = passed_checks / total_checks
        print(f"   Success Rate: {success_rate:.1%} ({passed_checks}/{total_checks})")
        
        if success_rate >= 0.8:
            print(f"   Result: ✅ PHASE 2 SUCCESSFUL - Quality improvements working effectively")
        elif success_rate >= 0.6:
            print(f"   Result: ⚠️ PHASE 2 PARTIAL - Some improvements working, needs refinement")
        else:
            print(f"   Result: ❌ PHASE 2 NEEDS WORK - Quality improvements not meeting targets")
        
        # 8. SAMPLE OUTPUT INSPECTION
        print(f"\n🔍 SAMPLE OUTPUT INSPECTION:")
        if complete_analysis:
            first_section = complete_analysis[0]
            print(f"   First Section Title: {first_section.get('title', 'N/A')}")
            print(f"   Summary: {first_section.get('detailed_summary', 'N/A')[:100]}...")
            print(f"   Evidence IDs: {first_section.get('ev_ids', [])}")
    
    def _check_specific_amounts(self, analysis: Dict[str, Any]) -> bool:
        """Check if analysis contains specific monetary amounts"""
        import re
        content = str(analysis)
        amounts = re.findall(r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion))?', content)
        return len(amounts) > 0
    
    def _check_specific_deadlines(self, analysis: Dict[str, Any]) -> bool:
        """Check if analysis contains specific deadlines"""
        import re
        content = str(analysis)
        deadlines = re.findall(r'(?:180\s*days?|within\s+\d+|not later than)', content, re.IGNORECASE)
        return len(deadlines) > 0
    
    def _check_specific_enforcement(self, analysis: Dict[str, Any]) -> bool:
        """Check if analysis contains specific enforcement mechanisms"""
        import re
        content = str(analysis)
        enforcement = re.findall(r'(?:penalty|fine)[^.]*\$[\d,]+', content, re.IGNORECASE)
        return len(enforcement) > 0
    
    def _check_evidence_grounding(self, analysis: Dict[str, Any]) -> bool:
        """Check if analysis sections are grounded in evidence"""
        complete_analysis = analysis.get('complete_analysis', [])
        if not complete_analysis:
            return False
        
        grounded_sections = sum(1 for section in complete_analysis if section.get('ev_ids'))
        return grounded_sections >= len(complete_analysis) * 0.8  # 80% grounded
    
    def _check_sophisticated_patterns(self, analysis: Dict[str, Any]) -> bool:
        """Check if sophisticated analysis patterns are used"""
        complete_analysis = analysis.get('complete_analysis', [])
        if not complete_analysis:
            return False
        
        # Check for sophisticated section structure
        has_importance_levels = any(s.get('importance') in ['primary', 'secondary'] for s in complete_analysis)
        has_detailed_summaries = any(len(s.get('detailed_summary', '')) > 50 for s in complete_analysis)
        has_key_actions = any(len(s.get('key_actions', [])) > 0 for s in complete_analysis)
        has_affected_parties = any(len(s.get('affected_parties', [])) > 0 for s in complete_analysis)
        
        return all([has_importance_levels, has_detailed_summaries, has_key_actions, has_affected_parties])

async def main():
    """Run Phase 2 quality improvement tests"""
    
    print("🚀 Starting Phase 2 Quality Improvement Tests")
    print("=" * 60)
    
    tester = Phase2QualityTester()
    
    try:
        success = await tester.test_phase2_improvements()
        
        if success:
            print("\n✅ Phase 2 testing completed successfully!")
            print("🎯 Quality improvements are working as expected")
            print("💰 Cost controls are maintained")
            print("🏆 HR5-118 standards are being met")
        else:
            print("\n❌ Phase 2 testing failed!")
            print("🔧 Quality improvements need refinement")
        
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())