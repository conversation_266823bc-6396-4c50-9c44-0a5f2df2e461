describe('Bill <PERSON>ails Page', () => {
  it('loads homepage and navigates to HR5 details without 404', () => {
    cy.visit('/')
    cy.contains('Bills Moving Through Congress', { timeout: 20000 }).should('be.visible')

    // Find HR5 card by bill number text and click Learn More
    cy.contains(/HR5/i, { timeout: 20000 }).closest('a,div').within(() => {
      cy.contains(/Learn More|Details/i).click({ force: true })
    })

    cy.url().should('include', '/bills/hr5-118')
    cy.contains(/What this bill does|AI-Generated Analysis|Quick Summary/i, { timeout: 20000 }).should('exist')
  })
})

