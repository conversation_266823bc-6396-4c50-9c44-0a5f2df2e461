"""Add bill_details table

Revision ID: 007_add_bill_details
Revises: f791c0c650e3
Create Date: 2025-08-10 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '007_add_bill_details'
down_revision: Union[str, Sequence[str], None] = 'f791c0c650e3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'bill_details',
        sa.Column('bill_id', sa.String(length=255), nullable=False),
        sa.Column('seo_slug', sa.String(length=255), nullable=True),
        sa.Column('seo_title', sa.String(length=512), nullable=True),
        sa.Column('seo_meta_description', sa.Text(), nullable=True),
        sa.Column('canonical_url', sa.String(length=512), nullable=True),
        sa.Column('hero_summary', sa.Text(), nullable=True),
        sa.Column('overview', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('positions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('message_templates', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('other_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('source_index', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('needs_human_review', sa.Boolean(), nullable=False, server_default=sa.text('false')),
        sa.Column('reviewed_at', sa.DateTime(), nullable=True),
        sa.Column('reviewed_by', sa.String(length=255), nullable=True),
        sa.Column('moderation_notes', sa.Text(), nullable=True),
        sa.Column('metrics', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('id', sa.String(length=36), nullable=False),
        sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
        sa.ForeignKeyConstraint(['bill_id'], ['bills.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('bill_id', name='uq_bill_details_bill_id')
    )
    op.create_index(op.f('ix_bill_details_bill_id'), 'bill_details', ['bill_id'], unique=True)
    op.create_index(op.f('ix_bill_details_seo_slug'), 'bill_details', ['seo_slug'], unique=True)


def downgrade() -> None:
    op.drop_index(op.f('ix_bill_details_seo_slug'), table_name='bill_details')
    op.drop_index(op.f('ix_bill_details_bill_id'), table_name='bill_details')
    op.drop_table('bill_details')

