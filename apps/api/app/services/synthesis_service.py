# app/services/synthesis_service.py
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from openai import AsyncOpenAI
from tenacity import retry, stop_after_attempt, wait_exponential
import logging

from .deep_analysis_service import ChunkAnalysis
from .bill_chunking_service import BillChunk

logger = logging.getLogger(__name__)


@dataclass
class ComprehensiveBillAnalysis:
    """Complete analysis of a bill synthesized from all chunks"""
    hero_summary: str
    hero_summary_citations: List[Dict[str, Any]]
    
    # Enhanced overview with specific mechanisms
    primary_mechanisms: List[Dict[str, Any]]
    secondary_provisions: List[Dict[str, Any]]
    enforcement_framework: Dict[str, Any]
    funding_impacts: Dict[str, Any]
    implementation_timeline: List[Dict[str, Any]]
    
    # Comprehensive transparency section
    complete_analysis: List[Dict[str, Any]]

    # Additional details section (fluff)
    additional_details: List[Dict[str, Any]]

    # Traditional sections (enhanced)
    overview: Dict[str, Any]
    positions: Dict[str, Any]


class SynthesisService:
    """Service for synthesizing chunk analyses into comprehensive bill analysis"""
    
    def __init__(self, openai_api_key: str):
        self.client = AsyncOpenAI(api_key=openai_api_key)

    async def synthesize_bill_analysis(
        self, 
        chunks: List[BillChunk], 
        chunk_analyses: List[ChunkAnalysis],
        bill_metadata: Dict[str, Any]
    ) -> ComprehensiveBillAnalysis:
        """Synthesize all chunk analyses into comprehensive bill analysis"""
        
        # Generate hero summary
        hero_summary, hero_citations = await self._generate_hero_summary(chunk_analyses, bill_metadata)
        
        # Extract and organize mechanisms
        primary_mechanisms = await self._extract_primary_mechanisms(chunk_analyses)
        secondary_provisions = await self._extract_secondary_provisions(chunk_analyses)
        
        # Analyze enforcement and funding
        enforcement_framework = await self._analyze_enforcement_framework(chunk_analyses)
        funding_impacts = await self._analyze_funding_impacts(chunk_analyses)
        
        # Create implementation timeline
        implementation_timeline = await self._create_implementation_timeline(chunk_analyses)
        
        # Generate comprehensive transparency section
        complete_analysis = await self._generate_complete_analysis(chunks, chunk_analyses)

        # Generate additional details section (fluff)
        additional_details = await self._generate_additional_details(chunks, chunk_analyses)

        # Generate traditional overview sections
        overview = await self._generate_enhanced_overview(chunk_analyses, bill_metadata)

        # Generate positions
        positions = await self._generate_positions(chunk_analyses, bill_metadata)

        return ComprehensiveBillAnalysis(
            hero_summary=hero_summary,
            hero_summary_citations=hero_citations,
            primary_mechanisms=primary_mechanisms,
            secondary_provisions=secondary_provisions,
            enforcement_framework=enforcement_framework,
            funding_impacts=funding_impacts,
            implementation_timeline=implementation_timeline,
            complete_analysis=complete_analysis,
            additional_details=additional_details,
            overview=overview,
            positions=positions
        )

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_hero_summary(
        self, 
        analyses: List[ChunkAnalysis], 
        metadata: Dict[str, Any]
    ) -> tuple[str, List[Dict[str, Any]]]:
        """Generate a specific, actionable hero summary"""
        
        # Extract top mechanisms for context
        key_mechanisms = []
        for analysis in analyses:
            for action in analysis.specific_actions[:2]:  # Top 2 per chunk
                key_mechanisms.append(action)
        
        prompt = f"""
You are a world-class legislative analyst. Create a hero summary that SPECIFICALLY explains what this bill does.

BILL: {metadata.get('title', 'Unknown Bill')}
KEY MECHANISMS: {json.dumps(key_mechanisms[:10], indent=2)}

CRITICAL REQUIREMENTS:
1. Be SPECIFIC about mechanisms (not "increases transparency" but "requires schools to post curriculum online within 180 days")
2. Name CONCRETE requirements and deadlines
3. Identify PRIMARY affected parties
4. Use 8th grade language but be precise
5. 2-3 sentences maximum
6. Include 2-3 EXACT QUOTES from the mechanisms to support claims

Format as JSON:
{{
  "summary": "Specific 2-3 sentence explanation of what this bill actually does",
  "citations": [
    {{"quote": "exact text from bill supporting the summary"}},
    {{"quote": "another exact quote"}}
  ]
}}

Focus on the MOST IMPORTANT mechanisms that affect the most people.

JSON Response:
"""

        try:
            response = await self.client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500,
                temperature=0.1
            )
            
            content = response.choices[0].message.content.strip()
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            json_str = content[start_idx:end_idx]
            data = json.loads(json_str)
            
            return data.get("summary", ""), data.get("citations", [])
            
        except Exception as e:
            logger.error(f"Failed to generate hero summary: {e}")
            return f"This bill, {metadata.get('title', 'Unknown')}, modifies federal education policy.", []

    async def _extract_primary_mechanisms(self, analyses: List[ChunkAnalysis]) -> List[Dict[str, Any]]:
        """Extract the most important mechanisms from all analyses"""
        mechanisms = []
        
        for analysis in analyses:
            for action in analysis.specific_actions:
                # Score importance
                score = self._score_mechanism_importance(action)
                
                if score >= 4:  # High importance threshold
                    mechanisms.append({
                        "mechanism": action["action"],
                        "affected_parties": action["who_must_act"],
                        "requirements": action["what_they_must_do"],
                        "enforcement": self._find_related_enforcement(action, analysis),
                        "timeline": self._find_related_timeline(action, analysis),
                        "citations": action["citations"],
                        "importance_score": score
                    })
        
        # Sort by importance and return top 8
        mechanisms.sort(key=lambda x: x["importance_score"], reverse=True)
        return mechanisms[:8]

    async def _extract_secondary_provisions(self, analyses: List[ChunkAnalysis]) -> List[Dict[str, Any]]:
        """Extract secondary but still important provisions"""
        provisions = []
        
        for analysis in analyses:
            for action in analysis.specific_actions:
                score = self._score_mechanism_importance(action)
                
                if 2 <= score < 4:  # Medium importance
                    provisions.append({
                        "provision": action["action"],
                        "affected_parties": action["who_must_act"],
                        "citations": action["citations"]
                    })
        
        return provisions[:12]  # Top 12 secondary provisions

    async def _analyze_enforcement_framework(self, analyses: List[ChunkAnalysis]) -> Dict[str, Any]:
        """Analyze how the bill is enforced"""
        enforcement_mechanisms = []
        penalties = []
        enforcing_agencies = set()
        
        for analysis in analyses:
            for enforcement in analysis.enforcement_mechanisms:
                enforcement_mechanisms.append(enforcement["mechanism"])
                if enforcement["penalty"]:
                    penalties.append(enforcement["penalty"])
                if enforcement["enforcing_agency"]:
                    enforcing_agencies.add(enforcement["enforcing_agency"])
        
        return {
            "mechanisms": enforcement_mechanisms[:5],
            "penalties": penalties[:5],
            "enforcing_agencies": list(enforcing_agencies),
            "summary": f"Enforcement through {len(enforcement_mechanisms)} mechanisms by {len(enforcing_agencies)} agencies"
        }

    async def _analyze_funding_impacts(self, analyses: List[ChunkAnalysis]) -> Dict[str, Any]:
        """Analyze funding impacts across the bill"""
        funding_changes = []
        total_amounts = []
        affected_entities = set()
        
        for analysis in analyses:
            for funding in analysis.funding_impacts:
                funding_changes.append({
                    "type": funding["impact_type"],
                    "entity": funding["affected_entity"],
                    "amount": funding["amount"],
                    "conditions": funding["conditions"]
                })
                
                if funding["affected_entity"]:
                    affected_entities.add(funding["affected_entity"])
        
        return {
            "changes": funding_changes,
            "affected_entities": list(affected_entities),
            "summary": f"{len(funding_changes)} funding impacts affecting {len(affected_entities)} entity types"
        }

    async def _create_implementation_timeline(self, analyses: List[ChunkAnalysis]) -> List[Dict[str, Any]]:
        """Create a comprehensive implementation timeline"""
        timeline_items = []
        
        for analysis in analyses:
            for timeline in analysis.timelines:
                timeline_items.append({
                    "deadline": timeline["deadline"],
                    "action": timeline["action_required"],
                    "responsible_party": timeline["responsible_party"],
                    "citations": timeline["citations"]
                })
        
        # Sort by deadline (basic text sort for now)
        timeline_items.sort(key=lambda x: x["deadline"])
        
        return timeline_items[:10]  # Top 10 most important deadlines

    async def _generate_complete_analysis(
        self, 
        chunks: List[BillChunk], 
        analyses: List[ChunkAnalysis]
    ) -> List[Dict[str, Any]]:
        """Generate comprehensive transparency section covering ALL provisions"""
        complete_analysis = []
        
        for chunk, analysis in zip(chunks, analyses):
            # Summarize what this chunk does
            chunk_summary = {
                "title": chunk.title,
                "importance": chunk.importance,
                "summary": self._summarize_chunk_analysis(analysis),
                "affected_parties": chunk.affected_parties,
                "key_actions": [action["action"] for action in analysis.specific_actions[:3]],
                "citations": self._get_chunk_citations(analysis)
            }
            
            complete_analysis.append(chunk_summary)
        
        return complete_analysis

    async def _generate_additional_details(
        self,
        chunks: List[BillChunk],
        analyses: List[ChunkAnalysis]
    ) -> List[Dict[str, Any]]:
        """Generate comprehensive 'fluff' section covering all other bill provisions"""

        # Collect all provisions that aren't primary mechanisms
        additional_provisions = []

        for chunk, analysis in zip(chunks, analyses):
            # Skip chunks that are already covered in primary mechanisms
            if chunk.importance == "primary":
                continue

            # Extract secondary and technical provisions
            chunk_provisions = []

            # Add all specific actions from this chunk
            for action in analysis.specific_actions:
                importance_score = self._score_mechanism_importance(action)

                # Include secondary and technical provisions (not primary)
                if importance_score < 4:
                    chunk_provisions.append({
                        "provision": action["action"],
                        "type": "requirement" if any(word in action["action"].lower() for word in ["requires", "mandates", "must"]) else "provision",
                        "affected_parties": action.get("who_must_act", ""),
                        "details": action.get("what_they_must_do", ""),
                        "citations": action.get("citations", [])
                    })

            # Add enforcement mechanisms
            for enforcement in analysis.enforcement_mechanisms:
                chunk_provisions.append({
                    "provision": enforcement["mechanism"],
                    "type": "enforcement",
                    "affected_parties": enforcement.get("enforcing_agency", ""),
                    "details": enforcement.get("penalty", ""),
                    "citations": enforcement.get("citations", [])
                })

            # Add funding impacts
            for funding in analysis.funding_impacts:
                chunk_provisions.append({
                    "provision": f"{funding['impact_type']} for {funding['affected_entity']}",
                    "type": "funding",
                    "affected_parties": funding.get("affected_entity", ""),
                    "details": funding.get("conditions", ""),
                    "citations": funding.get("citations", [])
                })

            # Add legal changes
            for legal in analysis.legal_changes:
                chunk_provisions.append({
                    "provision": f"Modifies {legal['law_being_changed']}",
                    "type": "legal_change",
                    "affected_parties": "",
                    "details": legal.get("specific_change", ""),
                    "citations": legal.get("citations", [])
                })

            # Add implementation details
            for impl in analysis.implementation_details:
                chunk_provisions.append({
                    "provision": impl["requirement"],
                    "type": "implementation",
                    "affected_parties": "",
                    "details": impl.get("how_to_comply", ""),
                    "citations": impl.get("citations", [])
                })

            if chunk_provisions:
                additional_provisions.append({
                    "section_title": chunk.title,
                    "section_type": chunk.chunk_type,
                    "importance": chunk.importance,
                    "provisions": chunk_provisions
                })

        # Also add any definitions, technical sections, etc.
        definition_sections = []
        technical_sections = []

        for chunk, analysis in zip(chunks, analyses):
            if chunk.chunk_type == "definitions":
                definition_sections.append({
                    "section_title": chunk.title,
                    "provisions": [{"provision": "Contains definitions and terminology used throughout the bill", "type": "definition", "affected_parties": "", "details": "", "citations": []}]
                })
            elif chunk.chunk_type == "technical":
                technical_sections.append({
                    "section_title": chunk.title,
                    "provisions": [{"provision": "Technical provisions and administrative details", "type": "technical", "affected_parties": "", "details": "", "citations": []}]
                })

        # Combine all additional details
        all_additional = additional_provisions + definition_sections + technical_sections

        return all_additional[:20]  # Limit to top 20 most relevant additional details

    def _score_mechanism_importance(self, action: Dict[str, Any]) -> int:
        """Score the importance of a mechanism"""
        score = 0
        action_text = action["action"].lower()
        
        # High importance keywords
        high_keywords = ["requires", "prohibits", "mandates", "establishes", "funding", "penalty"]
        score += sum(3 for keyword in high_keywords if keyword in action_text)
        
        # Medium importance keywords
        medium_keywords = ["must", "shall", "compliance", "violation", "enforcement"]
        score += sum(2 for keyword in medium_keywords if keyword in action_text)
        
        # Low importance keywords
        low_keywords = ["may", "should", "encourage", "sense of congress"]
        score += sum(1 for keyword in low_keywords if keyword in action_text)
        
        # Bonus for affecting multiple parties
        who_acts = action.get("who_must_act", "").lower()
        if "school" in who_acts and ("parent" in who_acts or "student" in who_acts):
            score += 2
        
        return score

    def _find_related_enforcement(self, action: Dict[str, Any], analysis: ChunkAnalysis) -> Optional[str]:
        """Find enforcement mechanism related to this action"""
        for enforcement in analysis.enforcement_mechanisms:
            if any(word in enforcement["mechanism"].lower() for word in action["action"].lower().split()[:3]):
                return enforcement["mechanism"]
        return None

    def _find_related_timeline(self, action: Dict[str, Any], analysis: ChunkAnalysis) -> Optional[str]:
        """Find timeline related to this action"""
        for timeline in analysis.timelines:
            if any(word in timeline["action_required"].lower() for word in action["action"].lower().split()[:3]):
                return timeline["deadline"]
        return None

    def _summarize_chunk_analysis(self, analysis: ChunkAnalysis) -> str:
        """Create a brief summary of what this chunk does"""
        if analysis.specific_actions:
            primary_action = analysis.specific_actions[0]["action"]
            return f"This section {primary_action.lower()}"
        return "This section contains technical provisions"

    def _get_chunk_citations(self, analysis: ChunkAnalysis) -> List[Dict[str, Any]]:
        """Get key citations from chunk analysis"""
        citations = []
        for action in analysis.specific_actions[:2]:
            citations.extend(action.get("citations", []))
        return citations[:3]  # Top 3 citations per chunk

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_enhanced_overview(
        self,
        analyses: List[ChunkAnalysis],
        metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate enhanced overview sections with specific details"""

        # Collect all mechanisms for context
        all_mechanisms = []
        all_parties = set()
        all_enforcement = []
        all_funding = []

        for analysis in analyses:
            all_mechanisms.extend(analysis.specific_actions)
            for party_info in analysis.affected_parties:
                all_parties.add(party_info.get("party", ""))
            all_enforcement.extend(analysis.enforcement_mechanisms)
            all_funding.extend(analysis.funding_impacts)

        prompt = f"""
You are a world-class legislative analyst. Create enhanced overview sections that SPECIFICALLY explain what this bill does.

BILL: {metadata.get('title', 'Unknown Bill')}
MECHANISMS: {json.dumps(all_mechanisms[:15], indent=2)}
AFFECTED PARTIES: {list(all_parties)}
ENFORCEMENT: {json.dumps(all_enforcement[:5], indent=2)}
FUNDING: {json.dumps(all_funding[:5], indent=2)}

Create SPECIFIC, ACTIONABLE overview sections. No vague generalizations.

Format as JSON:
{{
  "what_does": {{
    "content": "SPECIFIC explanation of the 3-4 most important things this bill requires/prohibits",
    "citations": [
      {{"quote": "exact text supporting this explanation"}}
    ]
  }},
  "who_affects": {{
    "content": "SPECIFIC groups affected and HOW they are affected",
    "citations": [
      {{"quote": "exact text mentioning affected groups"}}
    ]
  }},
  "why_matters": {{
    "content": "SPECIFIC real-world impacts and consequences for regular people",
    "citations": [
      {{"quote": "exact text showing why this matters"}}
    ]
  }},
  "cost_impact": {{
    "content": "SPECIFIC financial impacts, amounts, and funding changes",
    "citations": [
      {{"quote": "exact text about costs or funding"}}
    ]
  }},
  "key_provisions": [
    {{
      "content": "First major provision with specific requirements",
      "citations": [{{"quote": "exact text of this provision"}}]
    }},
    {{
      "content": "Second major provision with specific requirements",
      "citations": [{{"quote": "exact text of this provision"}}]
    }}
  ],
  "timeline": [
    {{
      "content": "Specific deadline and what must be done",
      "citations": [{{"quote": "exact text about timing"}}]
    }}
  ]
}}

Be ruthlessly specific. Focus on CONCRETE requirements, deadlines, and impacts.

JSON Response:
"""

        try:
            response = await self.client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=1500,
                temperature=0.1
            )

            content = response.choices[0].message.content.strip()
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)

        except Exception as e:
            logger.error(f"Failed to generate enhanced overview: {e}")
            return self._create_fallback_overview(metadata)

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _generate_positions(
        self,
        analyses: List[ChunkAnalysis],
        metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate position reasons based on specific mechanisms"""

        # Extract key mechanisms for position generation
        key_mechanisms = []
        enforcement_mechanisms = []

        for analysis in analyses:
            key_mechanisms.extend(analysis.specific_actions[:3])
            enforcement_mechanisms.extend(analysis.enforcement_mechanisms[:2])

        prompt = f"""
Generate specific position reasons based on the actual mechanisms in this bill.

BILL: {metadata.get('title', 'Unknown Bill')}
KEY MECHANISMS: {json.dumps(key_mechanisms[:10], indent=2)}
ENFORCEMENT: {json.dumps(enforcement_mechanisms[:5], indent=2)}

Create SPECIFIC reasons based on ACTUAL bill provisions, not generic talking points.

Format as JSON:
{{
  "support_reasons": [
    {{
      "claim": "Specific reason to support based on actual provision",
      "justification": "Why this provision is beneficial with concrete examples",
      "citations": [{{"quote": "exact text supporting this reason"}}]
    }}
  ],
  "oppose_reasons": [
    {{
      "claim": "Specific concern based on actual provision",
      "justification": "Why this provision is problematic with concrete examples",
      "citations": [{{"quote": "exact text supporting this concern"}}]
    }}
  ],
  "amend_reasons": [
    {{
      "claim": "Specific improvement needed based on actual provision",
      "justification": "Why this change would improve the bill",
      "citations": [{{"quote": "exact text that needs improvement"}}]
    }}
  ]
}}

Generate 6-8 reasons per category. Be specific to THIS bill's actual provisions.

JSON Response:
"""

        try:
            response = await self.client.chat.completions.create(
                model="gpt-4-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=2000,
                temperature=0.2
            )

            content = response.choices[0].message.content.strip()
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)

        except Exception as e:
            logger.error(f"Failed to generate positions: {e}")
            return self._create_fallback_positions()

    def _create_fallback_overview(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback overview when AI fails"""
        return {
            "what_does": {
                "content": f"This bill, {metadata.get('title', 'Unknown')}, modifies federal education policy.",
                "citations": []
            },
            "who_affects": {
                "content": "This bill affects educational institutions and stakeholders.",
                "citations": []
            },
            "why_matters": {
                "content": "This legislation impacts educational policy and practices.",
                "citations": []
            },
            "cost_impact": {
                "content": "Financial impacts are being analyzed.",
                "citations": []
            },
            "key_provisions": [],
            "timeline": []
        }

    def _create_fallback_positions(self) -> Dict[str, Any]:
        """Create fallback positions when AI fails"""
        return {
            "support_reasons": [
                {
                    "claim": "This bill addresses important educational issues",
                    "justification": "Analysis in progress",
                    "citations": []
                }
            ],
            "oppose_reasons": [
                {
                    "claim": "This bill may have unintended consequences",
                    "justification": "Analysis in progress",
                    "citations": []
                }
            ],
            "amend_reasons": [
                {
                    "claim": "This bill could benefit from clarifications",
                    "justification": "Analysis in progress",
                    "citations": []
                }
            ]
        }
