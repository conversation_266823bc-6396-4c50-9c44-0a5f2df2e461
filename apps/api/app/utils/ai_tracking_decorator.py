"""
AI Tracking Decorator

Automatically tracks token usage and costs for AI operations.
"""

import time
import logging
from functools import wraps
from typing import Any, Callable, Optional
from app.services.ai_usage_tracking_service import AIUsageTrackingService
from app.db.database import get_db

logger = logging.getLogger(__name__)


def track_ai_usage(
    operation_type: str,
    operation_subtype: str = None,
    model_name: str = "gpt-4-turbo",
    bill_id_param: str = None,
    user_id_param: str = None
):
    """
    Decorator to automatically track AI usage for OpenAI API calls
    
    Args:
        operation_type: Type of operation ('bill_analysis', 'reason_generation', etc.)
        operation_subtype: Subtype of operation ('support_reasons', 'comprehensive_analysis', etc.)
        model_name: AI model being used (default: 'gpt-4-turbo')
        bill_id_param: Parameter name that contains bill_id (e.g., 'bill_metadata')
        user_id_param: Parameter name that contains user_id
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            start_time = time.time()
            success = True
            error_message = None
            prompt_tokens = 0
            completion_tokens = 0
            prompt_length = 0
            response_length = 0
            bill_id = None
            user_id = None
            
            try:
                # Extract bill_id and user_id from parameters if specified
                if bill_id_param:
                    if bill_id_param in kwargs:
                        bill_data = kwargs[bill_id_param]
                        if isinstance(bill_data, dict):
                            bill_id = bill_data.get('bill_id') or bill_data.get('id')
                        elif hasattr(bill_data, 'id'):
                            bill_id = bill_data.id
                
                if user_id_param and user_id_param in kwargs:
                    user_id = kwargs[user_id_param]
                
                # Call the original function
                result = await func(*args, **kwargs)
                
                # Extract token usage from result if it's an OpenAI response
                if hasattr(result, 'usage'):
                    prompt_tokens = result.usage.prompt_tokens
                    completion_tokens = result.usage.completion_tokens
                elif hasattr(result, 'choices') and len(result.choices) > 0:
                    # For some OpenAI responses, usage might be in a different location
                    if hasattr(result, 'usage'):
                        prompt_tokens = result.usage.prompt_tokens
                        completion_tokens = result.usage.completion_tokens
                
                # Try to extract prompt and response lengths
                if len(args) > 0 and isinstance(args[0], str):
                    prompt_length = len(args[0])
                elif 'prompt' in kwargs:
                    prompt_length = len(kwargs['prompt'])
                
                if hasattr(result, 'choices') and len(result.choices) > 0:
                    if hasattr(result.choices[0], 'message'):
                        response_length = len(result.choices[0].message.content or '')
                    elif hasattr(result.choices[0], 'text'):
                        response_length = len(result.choices[0].text or '')
                
                return result
                
            except Exception as e:
                success = False
                error_message = str(e)
                logger.error(f"AI operation failed: {operation_type} - {e}")
                raise
                
            finally:
                # Track the usage
                try:
                    response_time_ms = (time.time() - start_time) * 1000
                    
                    # Get database session
                    db = next(get_db())
                    tracking_service = AIUsageTrackingService(db)
                    
                    tracking_service.track_ai_usage(
                        operation_type=operation_type,
                        operation_subtype=operation_subtype,
                        model_name=model_name,
                        prompt_tokens=prompt_tokens,
                        completion_tokens=completion_tokens,
                        response_time_ms=response_time_ms,
                        success=success,
                        bill_id=bill_id,
                        user_id=user_id,
                        error_message=error_message,
                        prompt_length=prompt_length,
                        response_length=response_length
                    )
                    
                    db.close()
                    
                except Exception as tracking_error:
                    logger.error(f"Failed to track AI usage: {tracking_error}")
                    # Don't fail the original operation due to tracking errors
        
        return wrapper
    return decorator


def track_openai_response(
    response: Any,
    operation_type: str,
    operation_subtype: str = None,
    model_name: str = "gpt-4-turbo",
    bill_id: str = None,
    user_id: str = None,
    prompt_length: int = None,
    response_time_ms: float = None,
    success: bool = True,
    error_message: str = None
):
    """
    Manually track an OpenAI API response
    
    Use this when you can't use the decorator (e.g., in existing code)
    """
    try:
        prompt_tokens = 0
        completion_tokens = 0
        response_length = 0
        
        # Extract token usage from OpenAI response
        if hasattr(response, 'usage'):
            prompt_tokens = response.usage.prompt_tokens
            completion_tokens = response.usage.completion_tokens
        
        # Extract response length
        if hasattr(response, 'choices') and len(response.choices) > 0:
            if hasattr(response.choices[0], 'message'):
                response_length = len(response.choices[0].message.content or '')
            elif hasattr(response.choices[0], 'text'):
                response_length = len(response.choices[0].text or '')
        
        # Track the usage
        db = next(get_db())
        tracking_service = AIUsageTrackingService(db)
        
        tracking_service.track_ai_usage(
            operation_type=operation_type,
            operation_subtype=operation_subtype,
            model_name=model_name,
            prompt_tokens=prompt_tokens,
            completion_tokens=completion_tokens,
            response_time_ms=response_time_ms or 0,
            success=success,
            bill_id=bill_id,
            user_id=user_id,
            error_message=error_message,
            prompt_length=prompt_length,
            response_length=response_length
        )
        
        db.close()
        
        logger.info(f"Tracked AI usage: {operation_type} - {prompt_tokens + completion_tokens} tokens")
        
    except Exception as e:
        logger.error(f"Failed to track OpenAI response: {e}")


class AIUsageContext:
    """Context manager for tracking AI usage in complex operations"""
    
    def __init__(
        self,
        operation_type: str,
        operation_subtype: str = None,
        model_name: str = "gpt-4-turbo",
        bill_id: str = None,
        user_id: str = None
    ):
        self.operation_type = operation_type
        self.operation_subtype = operation_subtype
        self.model_name = model_name
        self.bill_id = bill_id
        self.user_id = user_id
        self.start_time = None
        self.total_prompt_tokens = 0
        self.total_completion_tokens = 0
        self.total_prompt_length = 0
        self.total_response_length = 0
        self.operations = []
        
    def __enter__(self):
        self.start_time = time.time()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            response_time_ms = (time.time() - self.start_time) * 1000
            success = exc_type is None
            error_message = str(exc_val) if exc_val else None
            
            # Track the aggregated usage
            db = next(get_db())
            tracking_service = AIUsageTrackingService(db)
            
            tracking_service.track_ai_usage(
                operation_type=self.operation_type,
                operation_subtype=self.operation_subtype,
                model_name=self.model_name,
                prompt_tokens=self.total_prompt_tokens,
                completion_tokens=self.total_completion_tokens,
                response_time_ms=response_time_ms,
                success=success,
                bill_id=self.bill_id,
                user_id=self.user_id,
                error_message=error_message,
                prompt_length=self.total_prompt_length,
                response_length=self.total_response_length
            )
            
            db.close()
            
        except Exception as e:
            logger.error(f"Failed to track AI usage in context: {e}")
    
    def add_response(self, response: Any, prompt_length: int = 0):
        """Add an OpenAI response to the tracking context"""
        if hasattr(response, 'usage'):
            self.total_prompt_tokens += response.usage.prompt_tokens
            self.total_completion_tokens += response.usage.completion_tokens
        
        self.total_prompt_length += prompt_length
        
        if hasattr(response, 'choices') and len(response.choices) > 0:
            if hasattr(response.choices[0], 'message'):
                self.total_response_length += len(response.choices[0].message.content or '')
            elif hasattr(response.choices[0], 'text'):
                self.total_response_length += len(response.choices[0].text or '')
