import { Button } from '../../../components/ui/button'
import { Badge } from '../../../components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card'
import { MegaphoneIcon, Heart, Mail, Phone } from 'lucide-react'

export default function ButtonsDemo() {
  return (
    <div className="min-h-screen bg-slate-50 py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-slate-900 mb-4">Button Modernization Showcase</h1>
          <p className="text-lg text-slate-600">See how shadcn/ui transforms our button components</p>
        </div>

        <div className="grid gap-8">
          {/* Primary Action Buttons */}
          <Card>
            <CardHeader>
              <CardTitle>Primary Action Buttons</CardTitle>
              <CardDescription>Main call-to-action buttons for civic engagement</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-slate-700">Before (Custom CSS)</h4>
                <div className="flex flex-wrap gap-4">
                  <button className="bg-blue-700 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-blue-800 transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center gap-2">
                    <MegaphoneIcon className="w-5 h-5" />
                    Take Action
                  </button>
                  <button className="border-2 border-blue-700 text-blue-700 px-8 py-4 rounded-xl text-lg font-semibold hover:bg-blue-50 transition-all duration-200 flex items-center justify-center gap-2">
                    Learn More
                  </button>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-slate-700">After (shadcn/ui)</h4>
                <div className="flex flex-wrap gap-4">
                  <Button variant="action" size="xl">
                    <MegaphoneIcon className="w-5 h-5" />
                    Take Action
                  </Button>
                  <Button variant="civic" size="xl">
                    <MegaphoneIcon className="w-5 h-5" />
                    Civic Action
                  </Button>
                  <Button variant="outline" size="xl">
                    Learn More
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Button Variants */}
          <Card>
            <CardHeader>
              <CardTitle>All Button Variants</CardTitle>
              <CardDescription>Complete range of button styles for different use cases</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-600">Default</h5>
                  <Button>Default</Button>
                </div>
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-600">Secondary</h5>
                  <Button variant="secondary">Secondary</Button>
                </div>
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-600">Outline</h5>
                  <Button variant="outline">Outline</Button>
                </div>
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-600">Ghost</h5>
                  <Button variant="ghost">Ghost</Button>
                </div>
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-600">Destructive</h5>
                  <Button variant="destructive">Destructive</Button>
                </div>
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-600">Civic</h5>
                  <Button variant="civic">Civic</Button>
                </div>
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-600">Action</h5>
                  <Button variant="action">Action</Button>
                </div>
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-600">Link</h5>
                  <Button variant="link">Link</Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Button Sizes */}
          <Card>
            <CardHeader>
              <CardTitle>Button Sizes</CardTitle>
              <CardDescription>Consistent sizing across all interface levels</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap items-end gap-4">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-slate-600">Small</p>
                  <Button size="sm">Small</Button>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-slate-600">Default</p>
                  <Button>Default</Button>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-slate-600">Large</p>
                  <Button size="lg">Large</Button>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-slate-600">Extra Large</p>
                  <Button size="xl">Extra Large</Button>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-medium text-slate-600">Icon</p>
                  <Button size="icon">
                    <Heart className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Representative Buttons */}
          <Card>
            <CardHeader>
              <CardTitle>Representative Contact Actions</CardTitle>
              <CardDescription>Specialized buttons for contacting representatives</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-4">
                <Button variant="action" className="w-full">
                  <Mail className="w-4 h-4" />
                  Send Email
                </Button>
                <Button variant="civic" className="w-full">
                  <Phone className="w-4 h-4" />
                  Make Call
                </Button>
                <Button variant="outline" className="w-full">
                  <MegaphoneIcon className="w-4 h-4" />
                  Social Media
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Status Badges */}
          <Card>
            <CardHeader>
              <CardTitle>Status Badges</CardTitle>
              <CardDescription>Semantic indicators for bill status and positions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-600">Bill Status</h5>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="status">Introduced</Badge>
                    <Badge variant="committee">In Committee</Badge>
                    <Badge variant="floor">Floor Vote</Badge>
                    <Badge variant="passed">Passed</Badge>
                    <Badge variant="urgent">Urgent</Badge>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-600">Position Indicators</h5>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="support">Support</Badge>
                    <Badge variant="oppose">Oppose</Badge>
                    <Badge variant="amend">Amend</Badge>
                  </div>
                </div>

                <div className="space-y-3">
                  <h5 className="text-sm font-medium text-slate-600">General Variants</h5>
                  <div className="flex flex-wrap gap-2">
                    <Badge>Default</Badge>
                    <Badge variant="secondary">Secondary</Badge>
                    <Badge variant="destructive">Destructive</Badge>
                    <Badge variant="outline">Outline</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Key Benefits */}
          <Card>
            <CardHeader>
              <CardTitle>shadcn/ui Benefits</CardTitle>
              <CardDescription>Why this modernization matters for ModernAction</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h5 className="font-semibold text-green-600">✅ Improvements</h5>
                  <ul className="space-y-2 text-sm text-slate-600">
                    <li>• Consistent design system across all components</li>
                    <li>• Better accessibility with Radix UI primitives</li>
                    <li>• Smooth animations and micro-interactions</li>
                    <li>• TypeScript support with proper variants</li>
                    <li>• Easier maintenance and customization</li>
                    <li>• Modern 2025 visual language</li>
                    <li>• Copy-paste components (you own the code)</li>
                  </ul>
                </div>
                
                <div className="space-y-3">
                  <h5 className="font-semibold text-blue-600">🎯 Civic Platform Benefits</h5>
                  <ul className="space-y-2 text-sm text-slate-600">
                    <li>• Semantic status indicators for bills</li>
                    <li>• Clear action hierarchy for user engagement</li>
                    <li>• Professional appearance builds trust</li>
                    <li>• Responsive design across all devices</li>
                    <li>• Consistent state management (hover, active, disabled)</li>
                    <li>• Better conversion rates with polished UX</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}