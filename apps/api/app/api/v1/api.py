# app/api/v1/api.py
from fastapi import APIRouter
from app.api.v1.endpoints import health, officials, bills, campaigns, actions, ai, admin, action_analytics, values_simple, users, ai_usage
from app.api.v1 import debug
# Temporarily disable values_analysis due to import issues
# from app.api.v1.endpoints import values_analysis

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(health.router, tags=["health"])
api_router.include_router(officials.router, prefix="/officials", tags=["officials"])
api_router.include_router(bills.router, prefix="/bills", tags=["bills"])
api_router.include_router(campaigns.router, prefix="/campaigns", tags=["campaigns"])
api_router.include_router(actions.router, prefix="/actions", tags=["actions"])
api_router.include_router(users.router, prefix="/users", tags=["users"])

# Advanced analytics (complex features for future)
api_router.include_router(action_analytics.router, prefix="/analytics", tags=["analytics"])

api_router.include_router(ai.router, prefix="/ai", tags=["ai"])
api_router.include_router(ai_usage.router, prefix="/ai-usage", tags=["ai-usage"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
# Use simplified values analysis instead of the complex one
api_router.include_router(values_simple.router, prefix="/values", tags=["values-analysis"])
# Debug endpoints for development and staging
api_router.include_router(debug.router, prefix="/debug", tags=["debug"])
# Temporarily disable values_analysis router due to import issues
# api_router.include_router(values_analysis.router, prefix="/values", tags=["values-analysis"])
