# Data Accuracy Procedures for Officials Database

## Overview

This document outlines the comprehensive procedures for ensuring 100% data accuracy when seeding our staging and production databases with officials information.

## 🎯 Data Accuracy Goals

1. **100% Verified Contact Information** - All email, phone, and website data verified
2. **Current Social Media Accounts** - All social media links actively verified
3. **Accurate Titles and Positions** - Reflecting current roles (e.g., recent House-to-Senate transitions)
4. **Government Domain Verification** - Official emails/websites use .gov domains when expected
5. **Multi-Source Cross-Validation** - Data validated against multiple authoritative sources

## 📊 Data Sources (Authority Hierarchy)

### Tier 1: Primary Authoritative Sources
1. **Congress.gov API** - Most authoritative for federal officials
2. **Senate.gov** - Official Senate information
3. **House.gov** - Official House information
4. **Bioguide** - Biographical Directory of the United States Congress

### Tier 2: Secondary Sources
1. **OpenStates API** - State and federal officials
2. **Google Civic Information API** - General civic data
3. **Official government websites** - Individual official sites

### Tier 3: Verification Sources
1. **Social media platforms** - Direct verification of accounts
2. **Government press releases** - Recent changes/updates
3. **News sources** - For recent transitions (House to Senate, etc.)

## 🔍 Validation Framework

### Multi-Layer Validation Process

#### Layer 1: Format Validation
- **Email format**: Valid email regex + government domain verification
- **Phone format**: Valid US phone number format
- **URL format**: Proper URL structure + accessibility check
- **Social media**: Platform-specific URL pattern validation

#### Layer 2: Content Validation
- **Title accuracy**: Cross-reference with current government directories
- **Party affiliation**: Verify against official records
- **District/State**: Geographic consistency checks
- **Bioguide ID**: Format validation and existence verification

#### Layer 3: Accessibility Validation
- **Website accessibility**: HTTP status code verification
- **Social media accessibility**: Account existence verification
- **Email deliverability**: Domain verification (not actual email sending)

#### Layer 4: Cross-Source Validation
- **Multiple source agreement**: Data consistency across sources
- **Recent changes detection**: Flagging when sources disagree
- **Manual review triggers**: Complex cases requiring human verification

## 🚀 Implementation Process

### Step 1: Data Collection
```bash
# Gather data from multiple sources
python seed_staging_with_validation.py --dry-run
```

### Step 2: Comprehensive Validation
```bash
# Run validation only to review issues
python seed_staging_with_validation.py --validate-only
```

### Step 3: Issue Resolution
- Review validation report
- Fix flagged issues manually
- Update data sources if needed
- Re-run validation

### Step 4: Verified Seeding
```bash
# Seed only verified data (requires 80%+ verification rate)
python seed_staging_with_validation.py --seed
```

## 📋 Validation Metrics

### Confidence Scoring System
- **95-100%**: Automatically approved for production
- **80-94%**: Approved for staging with monitoring
- **70-79%**: Manual review required
- **Below 70%**: Not approved for deployment

### Quality Gates
1. **Minimum 80% verification rate** for staging deployment
2. **Minimum 95% verification rate** for production deployment
3. **Zero critical issues** (invalid government emails, broken official websites)
4. **Manual review completed** for all flagged items

## 🛠️ Tools and Scripts

### Primary Validation Tool
```python
from app.services.officials_data_validator import OfficialsDataValidator

# Validate individual official
async with OfficialsDataValidator() as validator:
    report = await validator.validate_official(official_data)
```

### Batch Processing
```bash
# Process and validate large datasets
python seed_staging_with_validation.py --validate-only --report-file custom_report.json
```

### Manual Verification Helpers
- **Social Media Checker**: Verifies account accessibility
- **Government Domain Validator**: Ensures .gov domains are used appropriately
- **Cross-Reference Tool**: Compares data across multiple sources

## 🔄 Ongoing Maintenance

### Regular Updates
- **Monthly validation runs** on all officials data
- **Immediate validation** after major political events (elections, transitions)
- **Quarterly manual review** of flagged items

### Data Source Monitoring
- **API availability monitoring** for all data sources
- **Change detection** in primary sources
- **New source integration** as they become available

### Error Tracking
- **Validation failure analysis** to improve accuracy
- **Common issue patterns** identification
- **Source reliability scoring** based on accuracy history

## 📊 Specific Data Validation Rules

### Federal Officials
- **Must have**: Bioguide ID, government email/website
- **Social media**: Verified current accounts (noting transitions like @RepName to @SenName)
- **Title verification**: Against current Congress roster
- **District/State**: Must match current representation

### State Officials
- **Government domains**: State-specific (.state.xx.us, etc.)
- **OpenStates verification**: Cross-reference with OpenStates API
- **Local verification**: Against state government websites

### Contact Information
- **Email domains**: .gov, .senate.gov, .house.gov, state domains
- **Phone numbers**: Government office numbers, not personal
- **Websites**: Official government sites, not campaign sites

## 🚨 Manual Review Triggers

### Automatic Flagging Conditions
1. **Recent role transitions** (Representative → Senator)
2. **Non-government email domains**
3. **Inaccessible official websites**
4. **Social media account mismatches**
5. **Data source disagreements**

### Manual Review Process
1. **Research official sources** for accurate information
2. **Verify recent changes** through news/press releases
3. **Update data** with verified information
4. **Document changes** in validation notes
5. **Re-run validation** to confirm fixes

## 📝 Documentation Requirements

### For Each Official Record
- **Data sources used**
- **Verification date**
- **Manual review notes** (if applicable)
- **Confidence score** and reasoning
- **Last updated timestamp**

### For Each Validation Run
- **Overall statistics** and confidence metrics
- **Common issues identified**
- **Manual reviews completed**
- **Recommendations** for next steps

## 🎯 Best Practices

### Data Collection
1. **Always use multiple sources** for verification
2. **Prioritize government sources** over third-party
3. **Check for recent updates** before validation
4. **Document data lineage** for transparency

### Validation Process
1. **Run validation frequently** during development
2. **Address high-confidence issues** first
3. **Batch similar issues** for efficient resolution
4. **Never deploy** with critical validation failures

### Maintenance
1. **Schedule regular updates** for all data
2. **Monitor for political changes** (elections, appointments)
3. **Keep validation rules updated** as new patterns emerge
4. **Maintain data source relationships** for continued access

## 🔍 Quality Assurance Checklist

### Pre-Deployment Verification
- [ ] Validation report shows ≥95% confidence for production (≥80% for staging)
- [ ] All critical issues resolved
- [ ] Government domains verified for official contacts
- [ ] Social media accounts verified as current and accessible
- [ ] Recent political transitions reflected accurately
- [ ] Cross-source validation completed
- [ ] Manual review completed for flagged items
- [ ] Documentation updated with verification details

### Post-Deployment Monitoring
- [ ] Spot-check random officials for accuracy
- [ ] Monitor for broken links or inaccessible accounts
- [ ] User feedback collection for data corrections
- [ ] Regular re-validation scheduled

## 📞 Escalation Procedures

### When Manual Review is Required
1. **Research Phase**: Use official government sources
2. **Verification Phase**: Cross-reference multiple sources
3. **Documentation Phase**: Record findings and sources
4. **Update Phase**: Apply corrections and re-validate
5. **Sign-off Phase**: Final approval for deployment

### Unresolvable Issues
- **Flag for exclusion** from initial deployment
- **Create tracking issue** for future resolution
- **Document reasons** for exclusion
- **Set review schedule** for re-evaluation

This comprehensive validation framework ensures that our platform maintains the highest standards of data accuracy and user trust.