// Number formatting utilities for consistent display across the app

/**
 * Format numbers with compact notation following design rules:
 * <1,000 → plain (973)
 * 1,000–9,999 → 1 decimal (2.4k) 
 * ≥10,000 → no decimals (12k)
 */
export const formatCompactNumber = (num: number): string => {
  if (num < 1000) {
    return num.toString(); // Plain numbers under 1k
  }
  
  if (num < 10000) {
    // 1k-9.9k: show 1 decimal place
    return new Intl.NumberFormat('en', { 
      notation: 'compact',
      maximumFractionDigits: 1,
      minimumFractionDigits: 1
    }).format(num);
  }
  
  // 10k+: no decimals
  return new Intl.NumberFormat('en', { 
    notation: 'compact',
    maximumFractionDigits: 0
  }).format(num);
};

/**
 * Format numbers with thousands separators (2431 → 2,431)
 */
export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('en').format(num);
};

/**
 * Format percentage with sign and appropriate precision
 */
export const formatPercentage = (percent: number, showSign: boolean = true): string => {
  const formatted = new Intl.NumberFormat('en', {
    style: 'percent',
    maximumFractionDigits: 1,
    signDisplay: showSign ? 'always' : 'auto'
  }).format(percent / 100);
  
  return formatted;
};

/**
 * Generate a trend indicator with proper formatting
 */
export const formatTrend = (current: number, previous: number): {
  direction: 'up' | 'down' | 'neutral';
  percentage: string;
  icon: string;
} => {
  if (previous === 0) {
    return {
      direction: 'neutral',
      percentage: '—',
      icon: '•'
    };
  }
  
  const change = ((current - previous) / previous) * 100;
  
  if (Math.abs(change) < 1) {
    return {
      direction: 'neutral',
      percentage: '0%',
      icon: '•'
    };
  }
  
  return {
    direction: change > 0 ? 'up' : 'down',
    percentage: formatPercentage(Math.abs(change), false),
    icon: change > 0 ? '↗' : '↘'
  };
};