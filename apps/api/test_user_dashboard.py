#!/usr/bin/env python3
"""
Test script for user dashboard functionality
Creates sample action data to test the dashboard
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.database import SessionLocal
from app.models.action import Action, ActionStatus, ActionType
from app.models.bill import Bill, BillStatus
from app.models.user import User
from app.models.action_tracking import ReasoningOption, ActionReasoning, CustomReasonsPool
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
import uuid
import json

def create_test_user_actions():
    """Create test actions for dashboard testing"""
    print("Creating test user actions for dashboard...")
    
    db = SessionLocal()
    
    try:
        # Get or create test user
        test_user_auth0_id = "auth0|test-user-123"
        test_email = "<EMAIL>"
        
        # Check by auth0_user_id first
        user = db.query(User).filter(User.auth0_user_id == test_user_auth0_id).first()
        
        # If not found, check by email (in case there's an existing user)
        if not user:
            user = db.query(User).filter(User.email == test_email).first()
        
        if not user:
            print("Creating test user...")
            user = User(
                auth0_user_id=test_user_auth0_id,
                email=test_email, 
                name="Test User",
                is_active=True,
                email_verified=True
            )
            db.add(user)
            db.commit()
            print(f"Created test user: {user.name}")
        else:
            print(f"Using existing user: {user.name} ({user.email})")
        
        test_user_id = user.id  # Use the database-generated UUID
        
        # Get existing bill
        bill = db.query(Bill).filter(Bill.title.ilike("%Affordable Housing%")).first()
        if not bill:
            print("No Affordable Housing bill found, using any bill...")
            bill = db.query(Bill).first()
        
        if not bill:
            print("No bills found in database. Please run bill seeding first.")
            return
            
        print(f"Using bill: {bill.title}")
        
        # Create reasoning options for this bill
        reasoning_options = [
            "This bill will help address the housing crisis",
            "Housing is a fundamental human right",
            "This will create jobs in construction",
            "We need more affordable options for families",
            "This bill supports sustainable development"
        ]
        
        reason_ids = []
        for reason_text in reasoning_options:
            existing_reason = db.query(ReasoningOption).filter(
                ReasoningOption.bill_id == bill.id,
                ReasoningOption.reason_text == reason_text
            ).first()
            
            if not existing_reason:
                reason = ReasoningOption(
                    id=str(uuid.uuid4()),
                    bill_id=bill.id,
                    stance="support",
                    reason_text=reason_text,
                    is_active=True
                )
                db.add(reason)
                reason_ids.append(reason.id)
            else:
                reason_ids.append(existing_reason.id)
        
        # Create test actions
        actions_data = [
            {
                "stance": "support",
                "subject": "Support the Affordable Housing Development Act",
                "message": "Dear Representative,\n\nI am writing to express my strong support for the Affordable Housing Development Act. This legislation is crucial for addressing our housing crisis and ensuring that all Americans have access to safe, affordable housing.\n\nThis bill will help create thousands of new affordable housing units while generating construction jobs in our community. As housing costs continue to rise, we need bold action to ensure housing remains accessible to working families.\n\nI urge you to vote YES on this important legislation.\n\nThank you for your consideration.\n\nSincerely,\nTest User",
                "status": ActionStatus.SENT,
                "days_ago": 5,
                "selected_reasons": reason_ids[:2],
                "custom_reason": "My family struggled to find affordable housing when I was growing up, and I don't want other families to face the same challenges.",
                "representatives": [
                    {"name": "Rep. Jane Smith", "title": "Representative", "party": "D"},
                    {"name": "Sen. John Doe", "title": "Senator", "party": "R"}
                ]
            },
            {
                "stance": "support", 
                "subject": "Re: Affordable Housing Act - Follow up",
                "message": "Dear Senator,\n\nI wanted to follow up on my previous message regarding the Affordable Housing Development Act. I noticed that the bill has advanced to committee review, and I wanted to reiterate my strong support.\n\nThe housing shortage in our district is becoming more severe each month. This legislation provides the tools and funding needed to address this crisis effectively.\n\nI hope you will continue to champion this important cause.\n\nBest regards,\nTest User",
                "status": ActionStatus.DELIVERED,
                "days_ago": 2,
                "selected_reasons": reason_ids[2:4],
                "custom_reason": None,
                "representatives": [
                    {"name": "Sen. John Doe", "title": "Senator", "party": "R"}
                ]
            },
            {
                "stance": "support",
                "subject": "Affordable Housing - Constituent Input", 
                "message": "To Whom It May Concern,\n\nAs a constituent, I am writing to provide input on the Affordable Housing Development Act currently under consideration.\n\nOur community desperately needs this legislation. The lack of affordable housing options is forcing families to choose between paying rent and other necessities. This is unacceptable in a prosperous nation.\n\nPlease support this bill and help make housing affordable for all Americans.\n\nThank you,\nTest User",
                "status": ActionStatus.PENDING,
                "days_ago": 0,
                "selected_reasons": [reason_ids[0], reason_ids[4]],
                "custom_reason": "I work with a local homeless shelter and see firsthand how the housing crisis affects real people every day.",
                "representatives": [
                    {"name": "Rep. Jane Smith", "title": "Representative", "party": "D"},
                    {"name": "Sen. Sarah Johnson", "title": "Senator", "party": "D"}
                ]
            }
        ]
        
        db.commit()  # Commit reasoning options first
        
        # Create actions
        for i, action_data in enumerate(actions_data):
            action_id = str(uuid.uuid4())
            created_at = datetime.now() - timedelta(days=action_data["days_ago"])
            
            action = Action(
                id=action_id,
                user_id=test_user_id,
                bill_id=bill.id,
                subject=action_data["subject"],
                message=action_data["message"],
                action_type=ActionType.EMAIL,
                status=action_data["status"],
                position=action_data["stance"],
                user_name=user.name,
                user_email=user.email,
                created_at=created_at,
                sent_at=created_at + timedelta(minutes=5) if action_data["status"] != ActionStatus.PENDING else None,
                delivered_at=created_at + timedelta(minutes=10) if action_data["status"] == ActionStatus.DELIVERED else None,
                representative_info=json.dumps(action_data["representatives"]),
                user_location=json.dumps({
                    "city": "Test City",
                    "state": "TS", 
                    "zip_code": "12345"
                }),
                delivery_method="email"
            )
            
            db.add(action)
            db.flush()  # Get the action ID
            
            # Add selected reasons
            for reason_id in action_data["selected_reasons"]:
                action_reasoning = ActionReasoning(
                    id=str(uuid.uuid4()),
                    action_id=action.id,
                    reasoning_option_id=reason_id
                )
                db.add(action_reasoning)
            
            # Add custom reason if provided
            if action_data["custom_reason"]:
                custom_reason = CustomReasonsPool(
                    id=str(uuid.uuid4()),
                    action_id=action.id,
                    user_id=test_user_id,
                    bill_id=bill.id,
                    stance=action_data["stance"],
                    custom_reason=action_data["custom_reason"]
                )
                db.add(custom_reason)
            
            print(f"Created action {i+1}: {action_data['subject']} ({action_data['status']})")
        
        db.commit()
        print(f"\n✅ Successfully created {len(actions_data)} test actions for user {user.email}")
        print(f"User ID for testing: {test_user_id}")
        print(f"Test URL: http://localhost:8000/api/v1/simple/user/{test_user_id}/actions")
        
    except Exception as e:
        print(f"❌ Error creating test data: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def test_user_actions_endpoint():
    """Test the user actions API endpoint"""
    print("\n" + "="*50)
    print("Testing User Actions API Endpoint")
    print("="*50)
    
    import requests
    
    # Get the user ID from database
    db = SessionLocal()
    try:
        user = db.query(User).filter(User.auth0_user_id == "auth0|test-user-123").first()
        if not user:
            print("❌ Test user not found. Run the create_test_user_actions function first.")
            return
        test_user_id = user.id
    finally:
        db.close()
    
    url = f"http://localhost:8000/api/v1/simple/user/{test_user_id}/actions"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response Success!")
            print(f"   Total actions: {data['total_count']}")
            print(f"   Actions returned: {len(data['actions'])}")
            
            for i, action in enumerate(data['actions'][:2], 1):
                print(f"\n   Action {i}:")
                print(f"     Title: {action['bill_title']}")
                print(f"     Stance: {action['stance']}")
                print(f"     Status: {action['action_status']}")
                print(f"     Subject: {action['subject']}")
                print(f"     Representatives: {len(action['representatives_contacted'])}")
                print(f"     Selected reasons: {len(action['selected_reasons'])}")
                if action['custom_reason']:
                    print(f"     Custom reason: {action['custom_reason'][:50]}...")
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    print("User Dashboard Test Script")
    print("="*50)
    
    # Create test data
    create_test_user_actions()
    
    # Test API endpoint
    test_user_actions_endpoint()
    
    print(f"\n{'='*50}")
    print("Test completed!")
    print("You can now test the dashboard at: http://localhost:3000/dashboard")
    print("Use the test user ID in Auth0 or modify the dashboard to use the test user ID")
    print("="*50)