#!/bin/sh
# entrypoint.sh

# Exit immediately if a command exits with a non-zero status.
set -e

# Check if we're in development mode (skip secret waiting)
if [ "$NODE_ENV" = "development" ]; then
  echo "🔧 Development mode detected, skipping secret injection wait..."

  # Set default values if not provided
  if [ -z "$AUTH0_SECRET" ]; then
    export AUTH0_SECRET="FClGARHv7jAop8IPRCgGFVxdxnplWigb"
    echo "✅ Using default AUTH0_SECRET for development"
  fi

  if [ -z "$AUTH0_CLIENT_SECRET" ]; then
    export AUTH0_CLIENT_SECRET="8-AbAlGvFrK31NYx3b2akbzp6UOvBdgF6ki112FDb3Naz2RNWxfWsPk-ExpAS1Pb"
    echo "✅ Using default AUTH0_CLIENT_SECRET for development"
  fi
else
  # Production mode - wait for secrets from AWS Secrets Manager
  echo "Waiting for Auth0 secret to be injected..."
  while [ -z "$AUTH0_SECRET" ]; do
    echo "Secret not found, sleeping for 1 second..."
    sleep 1
  done
  echo "✅ Auth0 secret found!"

  # Also wait for AUTH0_CLIENT_SECRET to be available
  echo "Waiting for Auth0 client secret to be injected..."
  while [ -z "$AUTH0_CLIENT_SECRET" ]; do
    echo "Client secret not found, sleeping for 1 second..."
    sleep 1
  done
  echo "✅ Auth0 client secret found!"
fi

# Verify all required Auth0 environment variables are present
echo "Verifying all Auth0 configuration..."
if [ -z "$AUTH0_BASE_URL" ]; then
  echo "❌ AUTH0_BASE_URL is missing"
  exit 1
fi

if [ -z "$AUTH0_ISSUER_BASE_URL" ]; then
  echo "❌ AUTH0_ISSUER_BASE_URL is missing"
  exit 1
fi

if [ -z "$AUTH0_CLIENT_ID" ]; then
  echo "❌ AUTH0_CLIENT_ID is missing"
  exit 1
fi

if [ -z "$AUTH0_AUDIENCE" ]; then
  echo "❌ AUTH0_AUDIENCE is missing"
  exit 1
fi

# Ensure dependencies are present in dev mode when bind-mount hides image node_modules
if [ "$NODE_ENV" = "development" ]; then
  echo "📦 Installing dependencies with npm ci (dev mode) ..."
  npm ci --no-audit --no-fund
fi

echo "✅ All Auth0 configuration verified!"

# In development, ensure deps and run Next.js in dev mode for SSR and hot reload
if [ "$NODE_ENV" = "development" ]; then
  echo "📦 Installing dependencies with npm ci (dev mode) ..."
  npm ci --no-audit --no-fund || npm install
  echo "▶️ Starting Next.js in dev mode (npm run dev) ..."
  exec npm run dev
fi

echo "🚀 Starting ModernAction web application..."
# In production, now execute the main container command (e.g., npm start)
exec "$@"
