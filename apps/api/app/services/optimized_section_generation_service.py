"""
Optimized Section Generation Service - PHASE 3.4
Advanced section generation strategy targeting 30-44+ sections for HR5-118 standards
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SectionGenerationStrategy:
    """Strategy for generating specific sections"""
    strategy_name: str
    target_sections: int
    section_types: List[str]
    importance_weights: Dict[str, float]
    expansion_rules: Dict[str, int]
    quality_thresholds: Dict[str, float]

@dataclass 
class SectionCandidate:
    """Candidate section for generation"""
    title: str
    importance: str
    source_evidence_ids: List[str]
    content_hints: List[str]
    expansion_potential: int
    quality_score: float
    template_type: str

class OptimizedSectionGenerationService:
    """
    PHASE 3.4: Optimized section generation targeting 30-44+ sections
    Uses advanced strategies to maximize section count while maintaining HR5-118 quality
    """
    
    def __init__(self):
        self.generation_strategies = self._initialize_strategies()
        self.expansion_patterns = self._initialize_expansion_patterns()
        self.quality_criteria = self._initialize_quality_criteria()
    
    def _initialize_strategies(self) -> Dict[str, SectionGenerationStrategy]:
        """Initialize section generation strategies"""
        return {
            "hr5118_comprehensive": SectionGenerationStrategy(
                strategy_name="HR5-118 Comprehensive Coverage",
                target_sections=44,
                section_types=[
                    "title_analysis", "definitions_expanded", "establishment_detailed",
                    "requirements_granular", "amendments_specific", "enforcement_comprehensive", 
                    "funding_detailed", "implementation_phased", "reporting_structured", 
                    "compliance_detailed", "affected_parties_analysis", "timeline_breakdown",
                    "legal_implications", "procedural_requirements", "oversight_mechanisms"
                ],
                importance_weights={
                    "primary": 0.4,
                    "secondary": 0.35, 
                    "technical": 0.25
                },
                expansion_rules={
                    "amendments": 8,     # Up to 8 amendment sections
                    "requirements": 6,   # Up to 6 requirement sections
                    "establishment": 5,  # Up to 5 establishment sections
                    "enforcement": 4,    # Up to 4 enforcement sections
                    "funding": 3,        # Up to 3 funding sections
                    "implementation": 4, # Up to 4 implementation sections
                    "reporting": 3,      # Up to 3 reporting sections
                    "definitions": 4     # Up to 4 definition sections
                },
                quality_thresholds={
                    "minimum_content_length": 100,
                    "minimum_evidence_citations": 2,
                    "importance_score_threshold": 0.3
                }
            ),
            
            "balanced_quality": SectionGenerationStrategy(
                strategy_name="Balanced Quality (30+ sections)",
                target_sections=32,
                section_types=[
                    "core_provisions", "key_amendments", "primary_requirements",
                    "enforcement_mechanisms", "funding_provisions", "implementation_plan",
                    "affected_parties", "compliance_standards", "oversight_structure"
                ],
                importance_weights={
                    "primary": 0.5,
                    "secondary": 0.3,
                    "technical": 0.2
                },
                expansion_rules={
                    "amendments": 6,
                    "requirements": 5,
                    "establishment": 4,
                    "enforcement": 3,
                    "funding": 3,
                    "implementation": 3,
                    "reporting": 2,
                    "definitions": 3
                },
                quality_thresholds={
                    "minimum_content_length": 120,
                    "minimum_evidence_citations": 2,
                    "importance_score_threshold": 0.4
                }
            ),
            
            "maximum_expansion": SectionGenerationStrategy(
                strategy_name="Maximum Expansion (50+ sections)",
                target_sections=52,
                section_types=[
                    "title_detailed", "definitions_comprehensive", "establishment_complete",
                    "requirements_exhaustive", "amendments_granular", "enforcement_detailed",
                    "funding_complete", "implementation_comprehensive", "reporting_detailed",
                    "compliance_exhaustive", "procedural_complete", "legal_analysis_detailed",
                    "impact_assessment", "stakeholder_analysis", "technical_specifications"
                ],
                importance_weights={
                    "primary": 0.35,
                    "secondary": 0.4,
                    "technical": 0.25
                },
                expansion_rules={
                    "amendments": 12,
                    "requirements": 10,
                    "establishment": 8,
                    "enforcement": 6,
                    "funding": 5,
                    "implementation": 6,
                    "reporting": 4,
                    "definitions": 6
                },
                quality_thresholds={
                    "minimum_content_length": 80,
                    "minimum_evidence_citations": 1,
                    "importance_score_threshold": 0.25
                }
            )
        }
    
    def _initialize_expansion_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for section expansion"""
        return {
            "sec_breakdown": [
                "SEC. {num}: {title} - Overview and Purpose",
                "SEC. {num}: {title} - Subsection (a) Analysis", 
                "SEC. {num}: {title} - Subsection (b) Requirements",
                "SEC. {num}: {title} - Subsection (c) Implementation",
                "SEC. {num}: {title} - Paragraph (1) Provisions",
                "SEC. {num}: {title} - Paragraph (2) Specifications",
                "SEC. {num}: {title} - Paragraph (3) Compliance"
            ],
            
            "amendment_breakdown": [
                "Amendment to Section {ref} - Overview",
                "Amendment to Section {ref} - Specific Changes",
                "Amendment to Section {ref} - Legal Implications",
                "Amendment to Section {ref} - Implementation Impact",
                "Amendment to Section {ref} - Compliance Requirements"
            ],
            
            "requirement_breakdown": [
                "{type} Requirements - General Provisions",
                "{type} Requirements - Specific Standards",
                "{type} Requirements - Compliance Procedures",
                "{type} Requirements - Monitoring and Oversight",
                "{type} Requirements - Enforcement Mechanisms"
            ],
            
            "funding_breakdown": [
                "Authorization of Appropriations - Amount and Purpose",
                "Authorization of Appropriations - Distribution Mechanism",
                "Authorization of Appropriations - Timeline and Conditions",
                "Funding Sources and Allocation Strategy",
                "Cost-Sharing and Matching Requirements"
            ],
            
            "enforcement_breakdown": [
                "Enforcement Authority and Jurisdiction",
                "Civil Penalties and Sanctions",
                "Administrative Enforcement Procedures",
                "Appeals and Due Process Requirements",
                "Remedial Actions and Compliance Orders"
            ]
        }
    
    def _initialize_quality_criteria(self) -> Dict[str, float]:
        """Initialize quality criteria for section validation"""
        return {
            "has_specific_dollar_amount": 0.8,
            "has_specific_deadline": 0.7,
            "has_enforcement_mechanism": 0.9,
            "has_legal_citation": 0.6,
            "has_named_entity": 0.5,
            "has_quantified_requirement": 0.6,
            "content_length_adequate": 0.4,
            "evidence_citations_present": 0.7
        }
    
    def optimize_section_generation(self, bill_text: str, bill_metadata: Dict, 
                                  structure_analysis: Dict, citation_analysis: Dict,
                                  target_sections: int = 35) -> Dict[str, Any]:
        """
        PHASE 3.4: Optimize section generation for maximum coverage and quality
        """
        logger.info(f"🎯 Optimizing section generation targeting {target_sections}+ sections")
        
        # Select optimal strategy based on target
        strategy = self._select_optimal_strategy(target_sections, structure_analysis, citation_analysis)
        logger.info(f"📋 Selected strategy: {strategy.strategy_name} (target: {strategy.target_sections})")
        
        # Generate section candidates using multiple approaches
        section_candidates = self._generate_section_candidates(
            bill_text, bill_metadata, structure_analysis, citation_analysis, strategy
        )
        
        # Apply advanced expansion techniques
        expanded_candidates = self._apply_expansion_techniques(section_candidates, strategy, bill_text)
        
        # Optimize section selection for quality and coverage
        optimized_sections = self._optimize_section_selection(expanded_candidates, strategy)
        
        # Generate enhanced prompts with optimized structure
        generation_prompts = self._generate_optimized_prompts(
            optimized_sections, strategy, bill_text, bill_metadata, citation_analysis
        )
        
        return {
            'selected_strategy': strategy,
            'section_candidates': len(section_candidates),
            'expanded_candidates': len(expanded_candidates), 
            'optimized_sections': optimized_sections,
            'generation_prompts': generation_prompts,
            'target_achievement': {
                'target_sections': strategy.target_sections,
                'optimized_sections': len(optimized_sections),
                'achievement_rate': len(optimized_sections) / strategy.target_sections,
                'quality_score': self._calculate_overall_quality_score(optimized_sections)
            }
        }
    
    def _select_optimal_strategy(self, target_sections: int, structure_analysis: Dict, 
                               citation_analysis: Dict) -> SectionGenerationStrategy:
        """Select the optimal strategy based on requirements and content analysis"""
        
        # Analyze bill complexity
        bill_complexity = self._analyze_bill_complexity(structure_analysis, citation_analysis)
        
        if target_sections >= 44:
            return self.generation_strategies["hr5118_comprehensive"]
        elif target_sections >= 40:
            return self.generation_strategies["maximum_expansion"]
        elif target_sections >= 30:
            return self.generation_strategies["balanced_quality"]
        else:
            # Adjust balanced strategy for lower targets
            strategy = self.generation_strategies["balanced_quality"].copy()
            strategy.target_sections = target_sections
            return strategy
    
    def _analyze_bill_complexity(self, structure_analysis: Dict, citation_analysis: Dict) -> Dict:
        """Analyze bill complexity to inform strategy selection"""
        return {
            'estimated_sections': structure_analysis.get('estimated_sections', 0),
            'template_coverage': len(structure_analysis.get('section_strategy', [])),
            'evidence_clusters': len(citation_analysis.get('evidence_clusters', [])),
            'statutory_refs': citation_analysis['citation_stats']['statutory_refs'],
            'amendment_count': citation_analysis['citation_stats']['amendment_count'],
            'complexity_score': (
                structure_analysis.get('estimated_sections', 0) * 0.3 +
                len(citation_analysis.get('evidence_clusters', [])) * 0.4 +
                citation_analysis['citation_stats']['statutory_refs'] * 0.3
            ) / 3
        }
    
    def _generate_section_candidates(self, bill_text: str, bill_metadata: Dict,
                                   structure_analysis: Dict, citation_analysis: Dict,
                                   strategy: SectionGenerationStrategy) -> List[SectionCandidate]:
        """Generate comprehensive section candidates using multiple techniques"""
        
        candidates = []
        
        # 1. Template-based candidates
        template_candidates = self._generate_template_based_candidates(
            structure_analysis, strategy
        )
        candidates.extend(template_candidates)
        
        # 2. Citation-cluster candidates  
        cluster_candidates = self._generate_cluster_based_candidates(
            citation_analysis, strategy
        )
        candidates.extend(cluster_candidates)
        
        # 3. SEC-pattern candidates
        sec_candidates = self._generate_sec_pattern_candidates(bill_text, strategy)
        candidates.extend(sec_candidates)
        
        # 4. Amendment-specific candidates
        amendment_candidates = self._generate_amendment_candidates(
            bill_text, citation_analysis, strategy
        )
        candidates.extend(amendment_candidates)
        
        # 5. Requirement-granular candidates
        requirement_candidates = self._generate_requirement_candidates(
            bill_text, citation_analysis, strategy
        )
        candidates.extend(requirement_candidates)
        
        logger.info(f"📊 Generated {len(candidates)} section candidates across 5 techniques")
        return candidates
    
    def _generate_template_based_candidates(self, structure_analysis: Dict,
                                          strategy: SectionGenerationStrategy) -> List[SectionCandidate]:
        """Generate candidates based on section templates"""
        candidates = []
        
        section_strategy = structure_analysis.get('section_strategy', [])
        for strategy_item in section_strategy:
            template_type = strategy_item['section_type']
            main_section = strategy_item['main_section']
            subsections = strategy_item.get('subsections', [])
            
            # Create main section candidate
            candidates.append(SectionCandidate(
                title=main_section['title'],
                importance=main_section['importance'],
                source_evidence_ids=[],
                content_hints=[f"Main {template_type} section"],
                expansion_potential=len(subsections),
                quality_score=0.7,
                template_type=template_type
            ))
            
            # Create subsection candidates
            for subsection in subsections[:strategy.expansion_rules.get(template_type, 3)]:
                candidates.append(SectionCandidate(
                    title=subsection['title'],
                    importance=subsection['importance'],
                    source_evidence_ids=[],
                    content_hints=[f"{template_type} subsection details"],
                    expansion_potential=1,
                    quality_score=0.5,
                    template_type=template_type
                ))
        
        return candidates
    
    def _generate_cluster_based_candidates(self, citation_analysis: Dict,
                                         strategy: SectionGenerationStrategy) -> List[SectionCandidate]:
        """Generate candidates based on evidence clusters"""
        candidates = []
        
        evidence_clusters = citation_analysis.get('evidence_clusters', [])
        for cluster in evidence_clusters:
            theme = cluster.theme
            evidence_ids = cluster.evidence_ids
            
            # Main cluster section
            candidates.append(SectionCandidate(
                title=f"{theme.title()} Provisions - Overview",
                importance="primary" if cluster.cluster_importance > 0.7 else "secondary",
                source_evidence_ids=evidence_ids[:3],
                content_hints=[f"Primary {theme} provisions"],
                expansion_potential=len(evidence_ids),
                quality_score=cluster.cluster_importance,
                template_type=theme
            ))
            
            # Detailed subsections for high-importance clusters
            if cluster.cluster_importance > 0.6:
                expansion_limit = strategy.expansion_rules.get(theme, 2)
                for i in range(min(expansion_limit, len(evidence_ids))):
                    candidates.append(SectionCandidate(
                        title=f"{theme.title()} - Specific Requirement {i+1}",
                        importance="secondary",
                        source_evidence_ids=[evidence_ids[i]],
                        content_hints=[f"Detailed {theme} requirement"],
                        expansion_potential=1,
                        quality_score=cluster.cluster_importance * 0.8,
                        template_type=theme
                    ))
        
        return candidates
    
    def _generate_sec_pattern_candidates(self, bill_text: str,
                                       strategy: SectionGenerationStrategy) -> List[SectionCandidate]:
        """Generate candidates based on SEC. patterns in bill text"""
        candidates = []
        
        # Extract SEC. patterns
        sec_patterns = re.findall(r'SEC\.?\s*(\d+)\.?\s*([A-Z\s]+)', bill_text, re.IGNORECASE)
        
        for sec_num, sec_title in sec_patterns:
            # Main SEC section
            candidates.append(SectionCandidate(
                title=f"SEC. {sec_num}: {sec_title.strip()}",
                importance="primary",
                source_evidence_ids=[],
                content_hints=[f"SEC {sec_num} comprehensive analysis"],
                expansion_potential=3,
                quality_score=0.8,
                template_type="legislation_section"
            ))
            
            # Find subsections within this SEC
            sec_pattern = rf'SEC\.?\s*{sec_num}\.?[^S]*?(?=SEC\.?\s*\d+|$)'
            sec_match = re.search(sec_pattern, bill_text, re.IGNORECASE | re.DOTALL)
            
            if sec_match:
                sec_content = sec_match.group(0)
                
                # Look for subsection patterns (a), (b), (c)
                subsection_patterns = re.findall(r'\(([a-z])\)', sec_content)
                for subsection in subsection_patterns[:4]:  # Limit to 4 subsections
                    candidates.append(SectionCandidate(
                        title=f"SEC. {sec_num}({subsection}): {sec_title.strip()} - Subsection Analysis",
                        importance="secondary",
                        source_evidence_ids=[],
                        content_hints=[f"SEC {sec_num} subsection {subsection} details"],
                        expansion_potential=2,
                        quality_score=0.6,
                        template_type="legislation_subsection"
                    ))
                
                # Look for paragraph patterns (1), (2), (3)
                paragraph_patterns = re.findall(r'\((\d+)\)', sec_content)
                for paragraph in paragraph_patterns[:3]:  # Limit to 3 paragraphs per SEC
                    candidates.append(SectionCandidate(
                        title=f"SEC. {sec_num}({paragraph}): {sec_title.strip()} - Paragraph Analysis", 
                        importance="technical",
                        source_evidence_ids=[],
                        content_hints=[f"SEC {sec_num} paragraph {paragraph} analysis"],
                        expansion_potential=1,
                        quality_score=0.5,
                        template_type="legislation_paragraph"
                    ))
        
        return candidates
    
    def _generate_amendment_candidates(self, bill_text: str, citation_analysis: Dict,
                                     strategy: SectionGenerationStrategy) -> List[SectionCandidate]:
        """Generate candidates specifically for amendments"""
        candidates = []
        
        amendments = citation_analysis['statutory_analysis']['amendments']
        amendment_limit = strategy.expansion_rules.get('amendments', 5)
        
        for amendment in amendments[:amendment_limit]:
            section = amendment['section']
            evidence_id = amendment['evidence_id']
            
            # Main amendment section
            candidates.append(SectionCandidate(
                title=f"Amendment to Section {section} - Overview and Impact",
                importance="primary",
                source_evidence_ids=[evidence_id],
                content_hints=[f"Amendment {section} comprehensive analysis"],
                expansion_potential=3,
                quality_score=0.8,
                template_type="amendment"
            ))
            
            # Specific change analysis
            candidates.append(SectionCandidate(
                title=f"Amendment to Section {section} - Specific Textual Changes",
                importance="secondary", 
                source_evidence_ids=[evidence_id],
                content_hints=[f"Amendment {section} specific changes"],
                expansion_potential=1,
                quality_score=0.6,
                template_type="amendment"
            ))
            
            # Legal implications
            candidates.append(SectionCandidate(
                title=f"Amendment to Section {section} - Legal and Procedural Implications",
                importance="secondary",
                source_evidence_ids=[evidence_id],
                content_hints=[f"Amendment {section} legal implications"],
                expansion_potential=1,
                quality_score=0.6,
                template_type="amendment"
            ))
        
        return candidates
    
    def _generate_requirement_candidates(self, bill_text: str, citation_analysis: Dict,
                                       strategy: SectionGenerationStrategy) -> List[SectionCandidate]:
        """Generate granular requirement-based candidates"""
        candidates = []
        
        # Find requirement patterns
        requirement_patterns = [
            (r'shall\s+([^.]{20,100})', 'Mandatory Requirement'),
            (r'must\s+([^.]{20,100})', 'Mandatory Compliance'),
            (r'required\s+to\s+([^.]{20,100})', 'Required Action'),
            (r'deadline[^.]{0,50}([^.]{20,80})', 'Deadline Requirement'),
            (r'not\s+later\s+than[^.]{0,50}([^.]{20,80})', 'Timeline Requirement')
        ]
        
        requirement_limit = strategy.expansion_rules.get('requirements', 6)
        requirement_count = 0
        
        for pattern, req_type in requirement_patterns:
            matches = re.findall(pattern, bill_text, re.IGNORECASE)
            
            for match in matches[:requirement_limit//len(requirement_patterns)]:
                if requirement_count >= requirement_limit:
                    break
                
                candidates.append(SectionCandidate(
                    title=f"{req_type}: {match[:50].strip()}...",
                    importance="primary" if "deadline" in req_type.lower() or "mandatory" in req_type.lower() else "secondary",
                    source_evidence_ids=[],
                    content_hints=[f"{req_type} analysis and implications"],
                    expansion_potential=2,
                    quality_score=0.7,
                    template_type="requirements"
                ))
                
                requirement_count += 1
        
        return candidates
    
    def _apply_expansion_techniques(self, candidates: List[SectionCandidate],
                                  strategy: SectionGenerationStrategy, bill_text: str) -> List[SectionCandidate]:
        """Apply advanced expansion techniques to increase section count"""
        
        expanded_candidates = candidates.copy()
        
        # 1. Legal precision expansion
        legal_expansions = self._apply_legal_precision_expansion(candidates, bill_text)
        expanded_candidates.extend(legal_expansions)
        
        # 2. Affected parties expansion
        parties_expansions = self._apply_affected_parties_expansion(candidates, bill_text)
        expanded_candidates.extend(parties_expansions)
        
        # 3. Implementation phase expansion
        implementation_expansions = self._apply_implementation_phase_expansion(candidates)
        expanded_candidates.extend(implementation_expansions)
        
        # 4. Compliance detail expansion
        compliance_expansions = self._apply_compliance_detail_expansion(candidates, bill_text)
        expanded_candidates.extend(compliance_expansions)
        
        logger.info(f"📈 Expanded from {len(candidates)} to {len(expanded_candidates)} candidates")
        return expanded_candidates
    
    def _apply_legal_precision_expansion(self, candidates: List[SectionCandidate], 
                                       bill_text: str) -> List[SectionCandidate]:
        """Expand candidates with legal precision focus"""
        expansions = []
        
        for candidate in candidates:
            if candidate.template_type in ['amendment', 'requirements', 'enforcement']:
                # Add legal implications analysis
                expansions.append(SectionCandidate(
                    title=f"{candidate.title} - Legal Precedent and Statutory Context",
                    importance="technical",
                    source_evidence_ids=candidate.source_evidence_ids,
                    content_hints=[f"Legal precedent analysis for {candidate.template_type}"],
                    expansion_potential=1,
                    quality_score=candidate.quality_score * 0.8,
                    template_type=f"{candidate.template_type}_legal"
                ))
        
        return expansions
    
    def _apply_affected_parties_expansion(self, candidates: List[SectionCandidate],
                                        bill_text: str) -> List[SectionCandidate]:
        """Expand candidates with affected parties analysis"""
        expansions = []
        
        # Extract potential affected parties
        party_patterns = [
            r'(?:department|agency|bureau|office|administration)\s+of\s+[a-z\s]+',
            r'(?:secretary|administrator|director|commissioner)\s+of\s+[a-z\s]+',
            r'state\s+and\s+local\s+governments?',
            r'private\s+sector\s+entities?',
            r'individuals?\s+and\s+families?'
        ]
        
        affected_parties = set()
        for pattern in party_patterns:
            matches = re.findall(pattern, bill_text, re.IGNORECASE)
            affected_parties.update(match.strip().title() for match in matches[:3])
        
        # Create affected parties analysis sections
        if affected_parties and len(candidates) > 10:
            for party in list(affected_parties)[:4]:  # Limit to 4 parties
                expansions.append(SectionCandidate(
                    title=f"Impact Analysis: {party}",
                    importance="secondary",
                    source_evidence_ids=[],
                    content_hints=[f"Specific impact analysis for {party}"],
                    expansion_potential=1,
                    quality_score=0.5,
                    template_type="impact_analysis"
                ))
        
        return expansions
    
    def _apply_implementation_phase_expansion(self, candidates: List[SectionCandidate]) -> List[SectionCandidate]:
        """Expand with implementation phase analysis"""
        expansions = []
        
        implementation_candidates = [c for c in candidates if c.template_type == 'implementation']
        
        for candidate in implementation_candidates[:2]:  # Limit to 2 main implementation sections
            phases = ["Phase 1: Planning and Preparation", "Phase 2: Initial Implementation", 
                     "Phase 3: Full Deployment", "Phase 4: Evaluation and Adjustment"]
            
            for phase in phases:
                expansions.append(SectionCandidate(
                    title=f"{candidate.title} - {phase}",
                    importance="secondary",
                    source_evidence_ids=candidate.source_evidence_ids,
                    content_hints=[f"Implementation {phase.lower()} details"],
                    expansion_potential=1,
                    quality_score=candidate.quality_score * 0.7,
                    template_type="implementation_phase"
                ))
        
        return expansions
    
    def _apply_compliance_detail_expansion(self, candidates: List[SectionCandidate], 
                                         bill_text: str) -> List[SectionCandidate]:
        """Expand with detailed compliance analysis"""
        expansions = []
        
        # Look for compliance-related candidates
        compliance_candidates = [c for c in candidates if 'compliance' in c.title.lower() or 
                               c.template_type in ['requirements', 'enforcement']]
        
        for candidate in compliance_candidates[:3]:  # Limit to 3 compliance sections
            compliance_aspects = [
                "Compliance Monitoring Procedures",
                "Documentation and Recordkeeping Requirements", 
                "Audit and Review Mechanisms",
                "Non-Compliance Consequences and Remedies"
            ]
            
            for aspect in compliance_aspects:
                expansions.append(SectionCandidate(
                    title=f"{aspect} - {candidate.template_type.title()} Context",
                    importance="technical",
                    source_evidence_ids=candidate.source_evidence_ids,
                    content_hints=[f"{aspect} detailed analysis"],
                    expansion_potential=1,
                    quality_score=candidate.quality_score * 0.6,
                    template_type="compliance_detail"
                ))
        
        return expansions
    
    def _optimize_section_selection(self, expanded_candidates: List[SectionCandidate],
                                  strategy: SectionGenerationStrategy) -> List[SectionCandidate]:
        """Optimize section selection for best quality and coverage"""
        
        # Score all candidates
        scored_candidates = []
        for candidate in expanded_candidates:
            overall_score = self._calculate_candidate_score(candidate, strategy)
            scored_candidates.append((candidate, overall_score))
        
        # Sort by score
        scored_candidates.sort(key=lambda x: x[1], reverse=True)
        
        # Apply selection strategy
        selected_candidates = []
        template_counts = {}
        
        for candidate, score in scored_candidates:
            # Check template balance
            template_type = candidate.template_type
            current_count = template_counts.get(template_type, 0)
            max_allowed = strategy.expansion_rules.get(template_type, 4)
            
            # Select if under limit and meets quality threshold
            if (current_count < max_allowed and 
                score >= strategy.quality_thresholds['importance_score_threshold'] and
                len(selected_candidates) < strategy.target_sections):
                
                selected_candidates.append(candidate)
                template_counts[template_type] = current_count + 1
        
        # Ensure we meet minimum sections by relaxing criteria if needed
        if len(selected_candidates) < strategy.target_sections * 0.8:
            remaining_needed = int(strategy.target_sections * 0.8) - len(selected_candidates)
            remaining_candidates = [c for c, s in scored_candidates if c not in selected_candidates]
            selected_candidates.extend(remaining_candidates[:remaining_needed])
        
        logger.info(f"🎯 Optimized selection: {len(selected_candidates)} sections from {len(expanded_candidates)} candidates")
        return selected_candidates
    
    def _calculate_candidate_score(self, candidate: SectionCandidate, 
                                 strategy: SectionGenerationStrategy) -> float:
        """Calculate overall score for candidate selection"""
        
        # Base quality score
        score = candidate.quality_score
        
        # Importance weight
        importance_weight = strategy.importance_weights.get(candidate.importance, 0.3)
        score *= importance_weight
        
        # Expansion potential bonus
        if candidate.expansion_potential > 2:
            score += 0.1
        
        # Evidence citation bonus
        if candidate.source_evidence_ids:
            score += len(candidate.source_evidence_ids) * 0.1
        
        # Template type priority
        priority_templates = ['amendments', 'enforcement', 'funding', 'requirements']
        if candidate.template_type in priority_templates:
            score += 0.15
        
        return min(score, 1.0)
    
    def _calculate_overall_quality_score(self, optimized_sections: List[SectionCandidate]) -> float:
        """Calculate overall quality score for the optimized section set"""
        if not optimized_sections:
            return 0.0
        
        return sum(section.quality_score for section in optimized_sections) / len(optimized_sections)
    
    def _generate_optimized_prompts(self, optimized_sections: List[SectionCandidate],
                                  strategy: SectionGenerationStrategy, bill_text: str,
                                  bill_metadata: Dict, citation_analysis: Dict) -> Dict[str, str]:
        """Generate optimized prompts for the selected sections"""
        
        # Create section list for prompt
        section_list = []
        for i, section in enumerate(optimized_sections, 1):
            section_list.append(f"{i:2d}. {section.title} ({section.importance})")
        
        optimized_prompt = f"""PHASE 3.4: OPTIMIZED SECTION GENERATION - {strategy.strategy_name}

BILL: {bill_metadata.get('title', 'Unknown')} ({bill_metadata.get('bill_number', 'Unknown')})

OPTIMIZATION STRATEGY: {strategy.strategy_name}
TARGET SECTIONS: {strategy.target_sections} (MINIMUM REQUIRED)
SELECTED SECTIONS: {len(optimized_sections)}

CRITICAL REQUIREMENTS:
- Generate EXACTLY {len(optimized_sections)} detailed sections following the list below
- Each section must be substantial (100+ words) with legal precision
- Use specific citations from evidence spans
- Include exact amounts, deadlines, penalties, and affected parties
- Follow HR5-118 gold standard requirements

OPTIMIZED SECTION STRUCTURE:
{chr(10).join(section_list)}

ENHANCED ANALYSIS REQUIREMENTS:
- Surgical precision: Break down every provision paragraph by paragraph
- Legal citations: Include specific statutory references and USC sections
- Factual specificity: Use exact dollar amounts, deadlines, penalties
- Evidence grounding: Every major claim must cite evidence IDs
- Actionable language: What specifically happens, not generic descriptions

QUALITY STANDARDS (HR5-118 LEVEL):
- Eliminate generic phrases: "comprehensive provisions", "various stakeholders"  
- Use specific language: "$50 million to EPA", "90-day deadline", "$100,000 penalty"
- Named entities: "Department of Justice", not "relevant agency"
- Precise legal language: "Section 16-2333, District of Columbia Official Code"

EVIDENCE CONTEXT:
{len(citation_analysis.get('enhanced_evidence_spans', []))} high-quality evidence spans available for citation

Generate the complete_analysis array with EXACTLY {len(optimized_sections)} sections as specified above."""

        return {
            'optimized_prompt': optimized_prompt,
            'section_count': len(optimized_sections),
            'strategy_name': strategy.strategy_name,
            'target_achievement': len(optimized_sections) / strategy.target_sections
        }


def get_optimized_section_generation_service():
    """Factory function for optimized section generation service"""
    return OptimizedSectionGenerationService()