"""
Enriched Analysis Service - Two-pass span-grounded analysis with budget controls
Implements comprehensive analysis while staying under $0.30 per bill
"""

import json
import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class EnrichedAnalysisService:
    """Two-pass enriched analysis with strict budget controls"""
    
    def __init__(self, ai_service):
        self.ai_service = ai_service
        
        # Budget controls (hard limits)
        self.max_bill_budget = 0.28  # Leave $0.02 slop under $0.30
        self.pass_a_input_limit = 1800  # tokens
        self.pass_a_output_limit = 1000  # tokens
        self.pass_b_input_limit = 900   # tokens per section
        self.pass_b_output_limit = 400  # tokens per section
        self.max_enriched_sections = 6
        
        # Cost estimates (conservative)
        self.gpt4o_mini_input_cost = 0.00015  # per 1k tokens
        self.gpt4o_mini_output_cost = 0.0006  # per 1k tokens
        self.gpt4o_input_cost = 0.005         # per 1k tokens
        self.gpt4o_output_cost = 0.015        # per 1k tokens
    
    async def analyze_bill_enriched(self, bill_text: str, bill_metadata: Dict, 
                                  evidence_spans: List[Dict]) -> Dict[str, Any]:
        """
        Two-pass enriched analysis with budget controls
        
        Pass A: Skeleton extraction (cheap, comprehensive)
        Pass B: Selective enrichment (targeted, high-quality)
        """
        
        try:
            # Step 1: Free deterministic enrichment
            free_enrichments = self._extract_free_enrichments(bill_text, evidence_spans)
            
            # Step 2: Pass A - Skeleton extraction (gpt-4o-mini)
            skeleton_result = await self._pass_a_skeleton_extraction(evidence_spans, bill_metadata)
            
            if not skeleton_result.get('success'):
                return skeleton_result
            
            skeleton_analysis = skeleton_result['analysis']
            pass_a_cost = skeleton_result['cost']
            
            # Step 3: Section routing and budget check
            sections_to_enrich = self._route_sections_for_enrichment(
                skeleton_analysis, evidence_spans, self.max_bill_budget - pass_a_cost
            )
            
            # Step 4: Pass B - Selective enrichment (gpt-4o for high-impact sections)
            enriched_sections = []
            pass_b_cost = 0.0
            
            for section_data in sections_to_enrich:
                if pass_a_cost + pass_b_cost >= self.max_bill_budget:
                    logger.warning(f"Budget exhausted at ${pass_a_cost + pass_b_cost:.4f}, skipping remaining sections")
                    break
                
                enriched_result = await self._pass_b_selective_enrichment(
                    section_data, skeleton_analysis
                )
                
                if enriched_result.get('success'):
                    enriched_sections.append(enriched_result['section'])
                    pass_b_cost += enriched_result['cost']
            
            # Step 5: Combine results
            final_analysis = self._combine_analysis_passes(
                skeleton_analysis, enriched_sections, free_enrichments
            )
            
            total_cost = pass_a_cost + pass_b_cost
            
            return {
                'success': True,
                'analysis': final_analysis,
                'cost_breakdown': {
                    'pass_a_cost': pass_a_cost,
                    'pass_b_cost': pass_b_cost,
                    'total_cost': total_cost,
                    'budget_remaining': self.max_bill_budget - total_cost,
                    'sections_enriched': len(enriched_sections),
                    'budget_exhausted': total_cost >= self.max_bill_budget
                },
                'processing_level': 'enriched_two_pass'
            }
            
        except Exception as e:
            logger.error(f"Enriched analysis failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_level': 'enriched_failed'
            }
    
    def _extract_free_enrichments(self, bill_text: str, evidence_spans: List[Dict]) -> Dict[str, Any]:
        """Extract enrichments without LLM calls (free)"""
        
        enrichments = {
            'budgets_table': [],
            'mandates_table': [],
            'prohibitions_table': [],
            'deadlines': [],
            'cross_references': [],
            'definitions_changes': []
        }
        
        # Budget classifier (regex-based)
        budget_patterns = {
            'authorization': [
                r'authorized to be appropriated',
                r'authorization of appropriations',
                r'such sums as may be necessary'
            ],
            'appropriation': [
                r'there are appropriated',
                r'appropriated to',
                r'out of any money in the Treasury'
            ],
            'limit': [
                r'not to exceed',
                r'maximum amount',
                r'limited to'
            ],
            'surcharge': [
                r'surcharge',
                r'fee',
                r'assessment'
            ]
        }
        
        for span in evidence_spans:
            quote = span.get('quote', '')
            
            # Classify budget items
            for category, patterns in budget_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, quote, re.IGNORECASE):
                        # Extract amount if present
                        amount_match = re.search(r'\$[\d,]+(?:\.\d{2})?', quote)
                        amount = amount_match.group(0) if amount_match else None
                        
                        enrichments['budgets_table'].append({
                            'kind': category,
                            'amount': amount,
                            'source': span.get('heading', 'Unknown'),
                            'ev': [span]
                        })
                        break
            
            # Extract mandates
            mandate_patterns = [r'shall', r'must', r'required', r'mandatory']
            for pattern in mandate_patterns:
                if re.search(pattern, quote, re.IGNORECASE):
                    # Simple mandate extraction
                    enrichments['mandates_table'].append({
                        'actor': 'Entity',  # Could be enhanced with NER
                        'must_may_not': 'must',
                        'verb': 'comply',
                        'object': quote[:50] + '...' if len(quote) > 50 else quote,
                        'deadline': None,
                        'ev': [span]
                    })
                    break
            
            # Extract prohibitions
            prohibition_patterns = [r'shall not', r'may not', r'prohibited', r'forbidden']
            for pattern in prohibition_patterns:
                if re.search(pattern, quote, re.IGNORECASE):
                    enrichments['prohibitions_table'].append({
                        'actor': 'Entity',
                        'prohibited_action': quote[:50] + '...' if len(quote) > 50 else quote,
                        'scope': span.get('heading', 'Unknown'),
                        'ev': [span]
                    })
                    break
            
            # Extract deadlines
            deadline_patterns = [
                r'not later than (\d+) days?',
                r'within (\d+) days?',
                r'(\d{1,2}/\d{1,2}/\d{4})',
                r'(January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},\s+\d{4}'
            ]
            for pattern in deadline_patterns:
                match = re.search(pattern, quote, re.IGNORECASE)
                if match:
                    enrichments['deadlines'].append({
                        'raw': match.group(0),
                        'normalized': None,  # Could add date parsing
                        'ev': [span]
                    })
                    break
            
            # Extract cross-references
            cross_ref_pattern = r'(\d+)\s+U\.S\.C\.\s+(\d+)'
            match = re.search(cross_ref_pattern, quote)
            if match:
                enrichments['cross_references'].append({
                    'cite': match.group(0),
                    'title': match.group(1),
                    'section': match.group(2),
                    'ev': [span]
                })
        
        # Deduplicate and limit
        for key in enrichments:
            enrichments[key] = enrichments[key][:5]  # Limit to 5 items each
        
        logger.info(f"Free enrichments: {sum(len(v) for v in enrichments.values())} items extracted")
        
        return enrichments
    
    async def _pass_a_skeleton_extraction(self, evidence_spans: List[Dict], 
                                        bill_metadata: Dict) -> Dict[str, Any]:
        """Pass A: Skeleton extraction using gpt-4o-mini"""
        
        # Create evidence pack (limited for Pass A)
        evidence_pack = self._create_evidence_pack(evidence_spans, max_spans=12)
        
        # Skeleton schema (minimal but complete structure)
        skeleton_schema = {
            "type": "object",
            "properties": {
                "complete_analysis": {
                    "type": "array",
                    "maxItems": 8,
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {"type": "string", "maxLength": 100},
                            "importance": {"type": "string", "enum": ["primary", "secondary", "technical"]},
                            "key_actions": {"type": "array", "maxItems": 3, "items": {"type": "string", "maxLength": 80}},
                            "affected_parties": {"type": "array", "maxItems": 3, "items": {"type": "string", "maxLength": 60}},
                            "citations": {
                                "type": "array",
                                "maxItems": 2,
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "q": {"type": "string"},
                                        "h": {"type": "string"},
                                        "a": {"type": "string"},
                                        "st": {"type": "integer"},
                                        "en": {"type": "integer"}
                                    },
                                    "required": ["q", "h", "a", "st", "en"]
                                }
                            }
                        },
                        "required": ["title", "importance", "key_actions", "affected_parties", "citations"]
                    }
                }
            },
            "required": ["complete_analysis"]
        }
        
        system_message = """You are a bill analysis expert. Extract skeleton analysis using ONLY the provided evidence spans.

CRITICAL RULES:
1. Every citation MUST have non-null "h" (heading) and "a" (anchor_id) from the evidence pack
2. Every citation MUST have "q" (quote), "st" (start_offset), "en" (end_offset)
3. Use ONLY evidence spans provided - do not invent content
4. Return valid JSON only

JSON structure:
{
  "complete_analysis": [
    {
      "title": "section title",
      "importance": "primary",
      "key_actions": ["specific action from evidence"],
      "affected_parties": ["specific party from evidence"],
      "citations": [{"q": "exact quote", "h": "exact heading", "a": "exact anchor", "st": 123, "en": 456}]
    }
  ]
}"""
        
        user_content = {
            "bill_metadata": bill_metadata,
            "evidence_pack": evidence_pack,
            "instruction": "Create skeleton analysis with key_actions and affected_parties for each major section. Limit to 2-3 items per array."
        }
        
        try:
            response = await self.ai_service.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": json.dumps(user_content)}
                ],
                max_tokens=self.pass_a_output_limit,
                temperature=0.1,
                response_format={"type": "json_object"}
            )
            
            # Calculate cost
            cost = (
                response.usage.prompt_tokens * self.gpt4o_mini_input_cost / 1000 +
                response.usage.completion_tokens * self.gpt4o_mini_output_cost / 1000
            )
            
            # Parse and validate JSON
            try:
                analysis = json.loads(response.choices[0].message.content)
            except json.JSONDecodeError as e:
                logger.error(f"Pass A JSON decode error: {e}")
                return {
                    'success': False,
                    'error': f"Invalid JSON from AI: {e}",
                    'cost': 0.0
                }

            # Validate structure
            if 'complete_analysis' not in analysis:
                logger.error("Pass A missing complete_analysis field")
                return {
                    'success': False,
                    'error': "Missing complete_analysis field",
                    'cost': 0.0
                }
            
            logger.info(f"Pass A completed: ${cost:.4f}, {len(analysis.get('complete_analysis', []))} sections")
            
            return {
                'success': True,
                'analysis': analysis,
                'cost': cost,
                'tokens_used': response.usage.prompt_tokens + response.usage.completion_tokens
            }
            
        except Exception as e:
            logger.error(f"Pass A skeleton extraction failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'cost': 0.0
            }
    
    def _route_sections_for_enrichment(self, skeleton_analysis: Dict, 
                                     evidence_spans: List[Dict], 
                                     remaining_budget: float) -> List[Dict]:
        """Route sections for Pass B enrichment based on importance and budget"""
        
        sections = skeleton_analysis.get('complete_analysis', [])
        
        # Score sections for enrichment priority
        scored_sections = []
        for section in sections:
            score = 0
            title = section.get('title', '').lower()
            
            # Money terms boost
            if any(term in title for term in ['appropriat', 'fund', 'budget', 'cost']):
                score += 3
            
            # Mandate terms boost  
            if any(term in title for term in ['requirement', 'shall', 'must', 'mandatory']):
                score += 2
            
            # Enforcement terms boost
            if any(term in title for term in ['enforce', 'penalty', 'violation', 'compliance']):
                score += 2
            
            # Importance boost
            importance = section.get('importance', 'technical')
            if importance == 'primary':
                score += 2
            elif importance == 'secondary':
                score += 1
            
            scored_sections.append({
                'section': section,
                'score': score,
                'evidence_spans': evidence_spans  # Will be filtered per section
            })
        
        # Sort by score and limit by budget
        scored_sections.sort(key=lambda x: x['score'], reverse=True)
        
        # Estimate Pass B cost and select sections within budget
        estimated_cost_per_section = (
            self.pass_b_input_limit * self.gpt4o_input_cost / 1000 +
            self.pass_b_output_limit * self.gpt4o_output_cost / 1000
        )
        
        max_sections = min(
            self.max_enriched_sections,
            int(remaining_budget / estimated_cost_per_section)
        )
        
        selected_sections = scored_sections[:max_sections]
        
        logger.info(f"Selected {len(selected_sections)} sections for enrichment (budget: ${remaining_budget:.4f})")
        
        return selected_sections
    
    def _create_evidence_pack(self, evidence_spans: List[Dict], max_spans: int = 12) -> List[Dict]:
        """Create evidence pack with deduplication and clause completion"""
        
        # Sort by priority and length
        priority_order = {'high': 3, 'medium': 2, 'low': 1}
        sorted_spans = sorted(
            evidence_spans,
            key=lambda x: (
                priority_order.get(x.get('priority', 'low'), 1),
                len(x.get('quote', ''))
            ),
            reverse=True
        )
        
        # Deduplicate and limit
        unique_spans = []
        seen_quotes = set()
        
        for span in sorted_spans[:max_spans]:
            quote = span.get('quote', '')[:50]  # First 50 chars for dedup
            if quote not in seen_quotes:
                seen_quotes.add(quote)
                # Ensure all required fields are non-null
                heading = span.get('heading', '') or 'Unknown Section'
                anchor_id = span.get('anchor_id', '') or f"span-{len(unique_spans)}"
                quote = span.get('quote', '').strip()

                if quote and len(quote) >= 10:  # Only include meaningful quotes
                    unique_spans.append({
                        'q': quote,
                        'h': heading,
                        'a': anchor_id,
                        'st': span.get('start_offset', 0),
                        'en': span.get('end_offset', 0)
                    })
        
        return unique_spans

    async def _pass_b_selective_enrichment(self, section_data: Dict,
                                         skeleton_analysis: Dict) -> Dict[str, Any]:
        """Pass B: Selective enrichment for high-priority sections"""

        section = section_data['section']

        # Enhanced schema for Pass B
        enriched_schema = {
            "type": "object",
            "properties": {
                "title": {"type": "string"},
                "importance": {"type": "string"},
                "detailed_summary": {"type": "string", "minLength": 120, "maxLength": 220},
                "key_actions": {"type": "array", "maxItems": 4},
                "affected_parties": {"type": "array", "maxItems": 4},
                "potential_impact": {
                    "type": "array",
                    "maxItems": 3,
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "string", "enum": ["economic", "legal", "operational", "access"]},
                            "s": {"type": "string", "maxLength": 120},
                            "ev": {"type": "array", "maxItems": 2}
                        },
                        "required": ["type", "s", "ev"]
                    }
                },
                "compliance_requirements": {
                    "type": "array",
                    "maxItems": 3,
                    "items": {
                        "type": "object",
                        "properties": {
                            "who": {"type": "string"},
                            "action": {"type": "string"},
                            "deadline": {"type": "string"},
                            "trigger": {"type": "string"},
                            "ev": {"type": "array", "maxItems": 2}
                        },
                        "required": ["who", "action", "ev"]
                    }
                },
                "enforcement": {
                    "type": "array",
                    "maxItems": 2,
                    "items": {
                        "type": "object",
                        "properties": {
                            "agency": {"type": "string"},
                            "mechanism": {"type": "string"},
                            "penalty": {"type": "string"},
                            "ev": {"type": "array", "maxItems": 2}
                        },
                        "required": ["agency", "mechanism", "ev"]
                    }
                },
                "timeline": {
                    "type": "array",
                    "maxItems": 3,
                    "items": {
                        "type": "object",
                        "properties": {
                            "action": {"type": "string"},
                            "deadline": {"type": "string"},
                            "ev": {"type": "array", "maxItems": 1}
                        },
                        "required": ["action", "deadline", "ev"]
                    }
                }
            },
            "required": ["title", "detailed_summary", "key_actions", "affected_parties"]
        }

        system_message = """You are a legislative analysis expert. Enrich the section with detailed analysis using ONLY provided evidence spans.

CRITICAL RULES:
1. Use ONLY evidence spans provided - do not invent content
2. Every "ev" array must contain valid citations with non-null "h", "a", "q", "st", "en"
3. detailed_summary must be 120-220 words and reference evidence
4. Return valid JSON only

JSON structure:
{
  "title": "exact section title",
  "importance": "primary",
  "detailed_summary": "120-220 word summary based on evidence",
  "key_actions": ["action from evidence"],
  "affected_parties": ["party from evidence"],
  "potential_impact": [{"type": "economic", "s": "impact from evidence", "ev": [{"q": "quote", "h": "heading", "a": "anchor", "st": 123, "en": 456}]}],
  "compliance_requirements": [{"who": "entity", "action": "requirement", "deadline": "when", "ev": [{"q": "quote", "h": "heading", "a": "anchor", "st": 123, "en": 456}]}],
  "enforcement": [{"agency": "enforcer", "mechanism": "how", "penalty": "consequence", "ev": [{"q": "quote", "h": "heading", "a": "anchor", "st": 123, "en": 456}]}],
  "timeline": [{"action": "what", "deadline": "when", "ev": [{"q": "quote", "h": "heading", "a": "anchor", "st": 123, "en": 456}]}]
}"""

        # Create targeted evidence pack for this section
        section_evidence = self._filter_evidence_for_section(
            section_data['evidence_spans'],
            section.get('title', '')
        )

        user_content = {
            "skeleton_section": section,
            "evidence_pack": section_evidence[:8],  # Limit for budget control
            "instruction": "Expand this section with detailed_summary (120-220 words), potential_impact, compliance_requirements, enforcement, and timeline. Each field must have evidence spans."
        }

        try:
            response = await self.ai_service.client.chat.completions.create(
                model="gpt-4o",  # Use higher quality model for enrichment
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": json.dumps(user_content)}
                ],
                max_tokens=self.pass_b_output_limit,
                temperature=0.1,
                response_format={"type": "json_object"}
            )

            # Calculate cost
            cost = (
                response.usage.prompt_tokens * self.gpt4o_input_cost / 1000 +
                response.usage.completion_tokens * self.gpt4o_output_cost / 1000
            )

            # Parse and validate JSON
            try:
                enriched_section = json.loads(response.choices[0].message.content)
            except json.JSONDecodeError as e:
                logger.error(f"Pass B JSON decode error: {e}")
                return {
                    'success': False,
                    'error': f"Invalid JSON from AI: {e}",
                    'cost': 0.0
                }

            # Validate required fields
            required_fields = ['title', 'detailed_summary', 'key_actions', 'affected_parties']
            for field in required_fields:
                if field not in enriched_section:
                    logger.error(f"Pass B missing required field: {field}")
                    return {
                        'success': False,
                        'error': f"Missing required field: {field}",
                        'cost': 0.0
                    }

            logger.info(f"Pass B section enriched: ${cost:.4f}, {enriched_section.get('title', 'Unknown')}")

            return {
                'success': True,
                'section': enriched_section,
                'cost': cost,
                'tokens_used': response.usage.prompt_tokens + response.usage.completion_tokens
            }

        except Exception as e:
            logger.error(f"Pass B enrichment failed for section {section.get('title', 'Unknown')}: {e}")
            return {
                'success': False,
                'error': str(e),
                'cost': 0.0
            }

    def _filter_evidence_for_section(self, evidence_spans: List[Dict], section_title: str) -> List[Dict]:
        """Filter evidence spans relevant to a specific section"""

        # Simple relevance scoring based on heading match
        relevant_spans = []
        section_keywords = set(section_title.lower().split())

        for span in evidence_spans:
            heading = span.get('heading', '').lower()
            quote = span.get('quote', '').lower()

            # Score relevance
            relevance_score = 0

            # Exact section match
            if section_title.lower() in heading:
                relevance_score += 10

            # Keyword overlap
            heading_keywords = set(heading.split())
            quote_keywords = set(quote.split()[:10])  # First 10 words

            keyword_overlap = len(section_keywords & (heading_keywords | quote_keywords))
            relevance_score += keyword_overlap

            if relevance_score > 0:
                relevant_spans.append({
                    **span,
                    'relevance_score': relevance_score
                })

        # Sort by relevance and return top spans
        relevant_spans.sort(key=lambda x: x['relevance_score'], reverse=True)
        return relevant_spans[:8]

    def _combine_analysis_passes(self, skeleton_analysis: Dict,
                               enriched_sections: List[Dict],
                               free_enrichments: Dict) -> Dict[str, Any]:
        """Combine Pass A skeleton with Pass B enrichments and free data"""

        # Start with skeleton
        combined_analysis = skeleton_analysis.copy()

        # Replace enriched sections
        complete_analysis = combined_analysis.get('complete_analysis', [])
        enriched_titles = {section.get('title', '') for section in enriched_sections}

        # Keep non-enriched sections from skeleton, replace enriched ones
        final_sections = []
        for section in complete_analysis:
            if section.get('title', '') in enriched_titles:
                # Find and use enriched version
                enriched_section = next(
                    (s for s in enriched_sections if s.get('title', '') == section.get('title', '')),
                    section
                )
                final_sections.append(enriched_section)
            else:
                final_sections.append(section)

        combined_analysis['complete_analysis'] = final_sections

        # Add free enrichments as "additional_details"
        combined_analysis['additional_details'] = free_enrichments

        # Add metadata
        combined_analysis['processing_metadata'] = {
            'total_sections': len(final_sections),
            'enriched_sections': len(enriched_sections),
            'free_enrichments': sum(len(v) for v in free_enrichments.values()),
            'processing_method': 'two_pass_enriched'
        }

        return combined_analysis
