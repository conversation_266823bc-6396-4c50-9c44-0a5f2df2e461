'use client';

import React, { useState, useEffect } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import LoginButton from '../../components/auth/LoginButton';

export default function TestAuthPage() {
  const { user, isLoading } = useUser();
  const [formData, setFormData] = useState({
    name: '',
    message: '',
    stance: 'support'
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const newData = {
      ...formData,
      [e.target.name]: e.target.value
    };
    setFormData(newData);
    
    // Save to localStorage as user types
    localStorage.setItem('test-auth-form', JSON.stringify(newData));
  };

  // Restore form data on page load (after auth redirect)
  useEffect(() => {
    const shouldPreserve = localStorage.getItem('preserve-form-state');
    const expectedKey = localStorage.getItem('form-state-key');
    
    if (shouldPreserve === 'true' && expectedKey === 'test-auth-form') {
      const savedData = localStorage.getItem('test-auth-form');
      if (savedData) {
        try {
          const parsedData = JSON.parse(savedData);
          setFormData(parsedData);
          console.log('✅ Form data restored after authentication:', parsedData);
          
          // Clean up
          localStorage.removeItem('preserve-form-state');
          localStorage.removeItem('form-state-key');
          localStorage.removeItem('return-url');
        } catch (error) {
          console.error('❌ Failed to restore form data:', error);
        }
      }
    }
  }, []);

  const saveFormState = () => {
    localStorage.setItem('test-auth-form', JSON.stringify(formData));
    console.log('💾 Form state saved before authentication');
  };

  if (isLoading) {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">Auth0 Sign-In Flow Test</h1>
        
        {user ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-green-800 mb-2">Signed In Successfully!</h2>
            <p className="text-green-700">Welcome back, {user.name}!</p>
            <p className="text-sm text-green-600">Email: {user.email}</p>
            <a 
              href="/api/auth/logout" 
              className="inline-block mt-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              Sign Out
            </a>
          </div>
        ) : (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-blue-800 mb-2">Not Signed In</h2>
            <p className="text-blue-700 mb-4">Fill out the form below and then sign in to test the flow.</p>
            <LoginButton
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              preserveState={true}
              stateKey="test-auth-form"
              onSaveState={saveFormState}
            >
              Sign In with Auth0
            </LoginButton>
          </div>
        )}

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Test Form</h3>
          <p className="text-sm text-gray-600 mb-4">
            Fill out this form, then click "Sign In" above. After authentication, you should return to this page with your form data preserved.
          </p>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter your name"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Stance</label>
              <select
                name="stance"
                value={formData.stance}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="support">Support</option>
                <option value="oppose">Oppose</option>
                <option value="amend">Amend</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Message</label>
              <textarea
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Write your message..."
              />
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-gray-50 rounded-md">
            <h4 className="text-sm font-semibold text-gray-700 mb-2">Current Form Data:</h4>
            <pre className="text-xs text-gray-600">
              {JSON.stringify(formData, null, 2)}
            </pre>
          </div>
        </div>
        
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h4 className="text-sm font-semibold text-yellow-800 mb-2">Test Instructions:</h4>
          <ol className="text-sm text-yellow-700 space-y-1">
            <li>1. Fill out the form above with some test data</li>
            <li>2. Click "Sign In with Auth0" button</li>
            <li>3. Complete the Auth0 authentication flow</li>
            <li>4. You should be redirected back to this page</li>
            <li>5. Your form data should still be preserved</li>
            <li>6. You should see the "Signed In Successfully!" message</li>
          </ol>
        </div>
      </div>
    </div>
  );
}