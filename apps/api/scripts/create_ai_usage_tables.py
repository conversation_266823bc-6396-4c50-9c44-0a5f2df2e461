#!/usr/bin/env python3
"""
Create AI Usage Tracking Tables

This script creates the AI usage tracking tables in the database.
Run this script to ensure the tables exist before using AI usage tracking.

Usage:
    python scripts/create_ai_usage_tables.py
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
app_dir = Path(__file__).parent.parent
sys.path.insert(0, str(app_dir))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.db.base_class import Base
from app.models.ai_usage import AIUsageLog, AIUsageSummary, AIBudgetAlert
from app.models.bill import Bill  # Import to ensure relationship is established


def create_ai_usage_tables():
    """Create AI usage tracking tables"""
    
    # Get database URL from environment
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        print("ERROR: DATABASE_URL environment variable not set")
        sys.exit(1)
    
    print(f"Connecting to database: {database_url.split('@')[1] if '@' in database_url else database_url}")
    
    try:
        # Create engine and session
        engine = create_engine(database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        # Create all tables
        print("Creating AI usage tracking tables...")
        Base.metadata.create_all(bind=engine, tables=[
            AIUsageLog.__table__,
            AIUsageSummary.__table__,
            AIBudgetAlert.__table__
        ])
        
        # Create indexes for performance
        with engine.connect() as conn:
            print("Creating performance indexes...")
            
            # AI Usage Logs indexes
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_created_at ON ai_usage_logs(created_at);",
                "CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_operation_type ON ai_usage_logs(operation_type);",
                "CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_bill_id ON ai_usage_logs(bill_id);",
                "CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_model_name ON ai_usage_logs(model_name);",
                "CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_success ON ai_usage_logs(success);",
                "CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_user_id ON ai_usage_logs(user_id);",
                
                # AI Usage Summaries indexes
                "CREATE INDEX IF NOT EXISTS idx_ai_usage_summaries_date_bucket ON ai_usage_summaries(date_bucket);",
                "CREATE INDEX IF NOT EXISTS idx_ai_usage_summaries_period_type ON ai_usage_summaries(period_type);",
                "CREATE INDEX IF NOT EXISTS idx_ai_usage_summaries_operation_type ON ai_usage_summaries(operation_type);",
                
                # AI Budget Alerts indexes
                "CREATE INDEX IF NOT EXISTS idx_ai_budget_alerts_alert_type ON ai_budget_alerts(alert_type);",
                "CREATE INDEX IF NOT EXISTS idx_ai_budget_alerts_is_active ON ai_budget_alerts(is_active);",
                "CREATE INDEX IF NOT EXISTS idx_ai_budget_alerts_operation_type ON ai_budget_alerts(operation_type);"
            ]
            
            for index_sql in indexes:
                try:
                    conn.execute(text(index_sql))
                    print(f"  ✓ Created index: {index_sql.split('idx_')[1].split(' ')[0] if 'idx_' in index_sql else 'index'}")
                except Exception as e:
                    print(f"  ⚠ Index creation warning: {e}")
            
            conn.commit()
        
        # Verify tables were created
        with engine.connect() as conn:
            print("\nVerifying tables...")
            
            tables_to_check = ['ai_usage_logs', 'ai_usage_summaries', 'ai_budget_alerts']
            for table_name in tables_to_check:
                result = conn.execute(text(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = '{table_name}'
                    );
                """))
                exists = result.scalar()
                
                if exists:
                    # Count columns
                    col_result = conn.execute(text(f"""
                        SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_name = '{table_name}';
                    """))
                    col_count = col_result.scalar()
                    print(f"  ✓ Table '{table_name}' exists with {col_count} columns")
                else:
                    print(f"  ✗ Table '{table_name}' does not exist")
        
        print("\n✅ AI usage tracking tables created successfully!")
        print("\nNext steps:")
        print("1. Update your application to use the AI usage tracking models")
        print("2. Set up budget alerts using the AIBudgetAlert model")
        print("3. Monitor usage with the AIUsageLog and AIUsageSummary models")
        
    except Exception as e:
        print(f"❌ Error creating AI usage tables: {e}")
        sys.exit(1)


def verify_models():
    """Verify that the models are properly defined"""
    print("Verifying AI usage models...")
    
    # Check AIUsageLog
    assert hasattr(AIUsageLog, '__tablename__')
    assert AIUsageLog.__tablename__ == 'ai_usage_logs'
    print("  ✓ AIUsageLog model verified")
    
    # Check AIUsageSummary
    assert hasattr(AIUsageSummary, '__tablename__')
    assert AIUsageSummary.__tablename__ == 'ai_usage_summaries'
    print("  ✓ AIUsageSummary model verified")
    
    # Check AIBudgetAlert
    assert hasattr(AIBudgetAlert, '__tablename__')
    assert AIBudgetAlert.__tablename__ == 'ai_budget_alerts'
    print("  ✓ AIBudgetAlert model verified")
    
    print("✅ All models verified successfully!")


def create_sample_budget_alerts():
    """Create sample budget alerts for monitoring"""
    database_url = os.getenv("DATABASE_URL")
    if not database_url:
        return
    
    try:
        engine = create_engine(database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as session:
            print("\nCreating sample budget alerts...")
            
            # Daily spending limit
            daily_alert = AIBudgetAlert(
                id="daily-spending-limit",
                alert_name="Daily AI Spending Limit",
                alert_type="daily",
                threshold_amount=50.0,
                notification_emails=["<EMAIL>"]
            )
            
            # Per-bill cost limit
            per_bill_alert = AIBudgetAlert(
                id="per-bill-cost-limit",
                alert_name="Per Bill Cost Limit",
                alert_type="per_bill",
                threshold_amount=0.30,
                operation_type="bill_analysis",
                notification_emails=["<EMAIL>"]
            )
            
            # Monthly budget alert
            monthly_alert = AIBudgetAlert(
                id="monthly-budget-limit",
                alert_name="Monthly AI Budget",
                alert_type="monthly",
                threshold_amount=1000.0,
                notification_emails=["<EMAIL>", "<EMAIL>"]
            )
            
            # Check if alerts already exist
            existing_alerts = session.query(AIBudgetAlert).filter(
                AIBudgetAlert.id.in_([daily_alert.id, per_bill_alert.id, monthly_alert.id])
            ).all()
            
            existing_ids = {alert.id for alert in existing_alerts}
            
            alerts_to_add = []
            if daily_alert.id not in existing_ids:
                alerts_to_add.append(daily_alert)
            if per_bill_alert.id not in existing_ids:
                alerts_to_add.append(per_bill_alert)
            if monthly_alert.id not in existing_ids:
                alerts_to_add.append(monthly_alert)
            
            if alerts_to_add:
                for alert in alerts_to_add:
                    session.add(alert)
                    print(f"  ✓ Created alert: {alert.alert_name}")
                
                session.commit()
                print(f"✅ Created {len(alerts_to_add)} sample budget alerts")
            else:
                print("  ℹ Sample budget alerts already exist")
                
    except Exception as e:
        print(f"⚠ Warning: Could not create sample budget alerts: {e}")


if __name__ == "__main__":
    print("🚀 AI Usage Tracking Table Creation Script")
    print("=" * 50)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Verify models first
    verify_models()
    
    # Create tables
    create_ai_usage_tables()
    
    # Create sample alerts
    create_sample_budget_alerts()
    
    print("\n🎉 Setup complete! AI usage tracking is ready to use.")
