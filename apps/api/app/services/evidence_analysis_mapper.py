"""
Evidence-Analysis Mapper - Phase 3 Grounding Verification
Maps every analysis claim to specific evidence with full traceability and verification
"""

import logging
import re
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import asyncio
from collections import defaultdict

from app.services.evidence_first_processor import EvidenceSpan, EvidenceType

logger = logging.getLogger(__name__)

class ClaimType(Enum):
    """Types of claims in analysis"""
    FACTUAL = "factual"           # Statement of fact from bill
    INTERPRETIVE = "interpretive" # Interpretation of bill provision
    CONSEQUENCE = "consequence"   # Predicted outcome or impact
    PROCEDURAL = "procedural"     # How something works/process
    COMPARATIVE = "comparative"   # Comparison to other laws/bills

class VerificationLevel(Enum):
    """Levels of evidence verification"""
    DIRECT_QUOTE = "direct_quote"         # Claim directly quotes evidence
    DIRECT_SUPPORT = "direct_support"     # Evidence directly supports claim
    INFERENTIAL = "inferential"           # Claim inferred from evidence
    CONTEXTUAL = "contextual"             # Claim supported by context
    UNSUPPORTED = "unsupported"           # No evidence found

@dataclass
class AnalysisClaim:
    """A specific claim made in the analysis"""
    claim_id: str
    claim_text: str
    claim_type: ClaimType
    section_context: str          # Which analysis section this is from
    importance_level: str         # primary, secondary, technical
    specificity_level: float      # How specific the claim is (0-1)
    confidence_level: float       # Confidence in the claim (0-1)

@dataclass
class EvidenceMapping:
    """Mapping between a claim and supporting evidence"""
    claim_id: str
    evidence_span_id: str
    verification_level: VerificationLevel
    support_strength: float       # How strongly evidence supports claim (0-1)
    quote_overlap: float         # Text overlap between claim and evidence (0-1)
    semantic_alignment: float    # Semantic similarity (0-1)
    verification_notes: str      # Explanation of mapping

@dataclass
class MappingReport:
    """Comprehensive report of evidence-analysis mapping"""
    total_claims: int
    mapped_claims: int
    unmapped_claims: List[str]
    verification_distribution: Dict[VerificationLevel, int]
    quality_score: float         # Overall grounding quality (0-1)
    coverage_gaps: List[str]     # Areas lacking evidence support
    strength_distribution: Dict[str, int]  # Distribution of support strengths
    recommendations: List[str]   # Recommendations for improvement

class EvidenceAnalysisMapper:
    """
    Advanced evidence-to-analysis mapping system that ensures every claim
    is properly grounded with full traceability and verification
    """
    
    def __init__(self):
        # Patterns for extracting claims from analysis
        self.claim_patterns = self._initialize_claim_patterns()
        
        # Verification rules for different claim types
        self.verification_rules = self._initialize_verification_rules()
        
        # Semantic similarity patterns
        self.semantic_patterns = self._initialize_semantic_patterns()
        
        # Quality thresholds
        self.quality_thresholds = {
            'min_support_strength': 0.6,
            'min_quote_overlap': 0.3,
            'min_semantic_alignment': 0.4,
            'min_coverage_ratio': 0.8
        }
    
    async def map_evidence_to_analysis(self, analysis: Dict[str, Any], 
                                     evidence_spans: List[EvidenceSpan],
                                     bill_metadata: Dict[str, Any]) -> Tuple[List[EvidenceMapping], MappingReport]:
        """
        Create comprehensive mapping between evidence and analysis claims
        """
        
        logger.info(f"🗺️ Starting evidence-to-analysis mapping for {bill_metadata.get('title', 'Unknown')}")
        
        # Step 1: Extract claims from analysis
        claims = await self._extract_analysis_claims(analysis)
        
        # Step 2: Create evidence index for efficient lookup
        evidence_index = self._create_evidence_index(evidence_spans)
        
        # Step 3: Map each claim to supporting evidence
        mappings = await self._create_evidence_mappings(claims, evidence_index)
        
        # Step 4: Verify and score mappings
        verified_mappings = await self._verify_mappings(mappings, claims, evidence_spans)
        
        # Step 5: Generate comprehensive mapping report
        mapping_report = await self._generate_mapping_report(claims, verified_mappings, evidence_spans)
        
        # Step 6: Identify and flag quality issues
        quality_issues = self._identify_quality_issues(verified_mappings, mapping_report)
        
        logger.info(f"✅ Evidence mapping complete: {len(verified_mappings)} mappings, {mapping_report.quality_score:.2f} quality score")
        
        return verified_mappings, mapping_report
    
    def _initialize_claim_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for extracting different types of claims"""
        
        return {
            'factual_claims': [
                r'(?:the bill|this act|the legislation)\s+(?:establishes|creates|provides|requires|mandates)[^.]*',
                r'(?:appropriates|authorizes|allocates)\s+\$[\d,]+[^.]*',
                r'(?:penalty|fine|sanction)\s+[^.]*\$[\d,]+[^.]*',
                r'(?:deadline|timeline|effective date)[^.]*\d+\s*(?:days?|months?|years?)[^.]*'
            ],
            
            'interpretive_claims': [
                r'this\s+(?:means|indicates|suggests|implies)[^.]*',
                r'the\s+(?:purpose|intent|goal)\s+(?:is|appears to be)[^.]*',
                r'(?:likely|probably|appears to|seems to)[^.]*',
                r'the\s+effect\s+(?:would be|will be)[^.]*'
            ],
            
            'consequence_claims': [
                r'(?:will|would|could)\s+(?:result in|lead to|cause|affect)[^.]*',
                r'(?:citizens|taxpayers|individuals)\s+(?:will|would|could|may)[^.]*',
                r'the\s+impact\s+(?:will be|would be|could be)[^.]*',
                r'this\s+(?:benefits|harms|helps|hurts)[^.]*'
            ],
            
            'procedural_claims': [
                r'(?:process|procedure|method)\s+(?:involves|requires|includes)[^.]*',
                r'(?:secretary|administrator|agency)\s+(?:shall|must|will)[^.]*',
                r'(?:application|petition|request)\s+(?:process|procedure)[^.]*',
                r'(?:steps|requirements|procedures)\s+(?:include|involve)[^.]*'
            ]
        }
    
    def _initialize_verification_rules(self) -> Dict[ClaimType, Dict[str, Any]]:
        """Initialize verification rules for different claim types"""
        
        return {
            ClaimType.FACTUAL: {
                'min_quote_overlap': 0.5,
                'require_direct_support': True,
                'allow_inference': False,
                'evidence_types': [EvidenceType.FUNDING, EvidenceType.ENFORCEMENT, EvidenceType.MANDATE, EvidenceType.TIMELINE]
            },
            
            ClaimType.INTERPRETIVE: {
                'min_quote_overlap': 0.3,
                'require_direct_support': False,
                'allow_inference': True,
                'evidence_types': [EvidenceType.MANDATE, EvidenceType.AUTHORITY, EvidenceType.PROCESS]
            },
            
            ClaimType.CONSEQUENCE: {
                'min_quote_overlap': 0.2,
                'require_direct_support': False,
                'allow_inference': True,
                'evidence_types': [EvidenceType.IMPACT, EvidenceType.SCOPE, EvidenceType.FUNDING]
            },
            
            ClaimType.PROCEDURAL: {
                'min_quote_overlap': 0.4,
                'require_direct_support': True,
                'allow_inference': False,
                'evidence_types': [EvidenceType.PROCESS, EvidenceType.AUTHORITY, EvidenceType.MANDATE]
            }
        }
    
    def _initialize_semantic_patterns(self) -> Dict[str, List[str]]:
        """Initialize semantic similarity patterns"""
        
        return {
            'funding_terms': ['money', 'dollar', 'cost', 'expense', 'budget', 'appropriation', 'fund'],
            'enforcement_terms': ['penalty', 'fine', 'violation', 'compliance', 'enforcement', 'sanction'],
            'timeline_terms': ['deadline', 'time', 'date', 'period', 'duration', 'schedule'],
            'authority_terms': ['secretary', 'agency', 'department', 'authority', 'power', 'jurisdiction'],
            'mandate_terms': ['must', 'shall', 'required', 'mandatory', 'obligation', 'duty'],
            'impact_terms': ['affect', 'impact', 'consequence', 'result', 'outcome', 'effect']
        }
    
    async def _extract_analysis_claims(self, analysis: Dict[str, Any]) -> List[AnalysisClaim]:
        """Extract specific claims from the analysis"""
        
        claims = []
        claim_counter = 0
        
        # Extract claims from complete_analysis sections
        complete_analysis = analysis.get('complete_analysis', [])
        
        for section in complete_analysis:
            section_title = section.get('title', '')
            importance = section.get('importance', 'technical')
            
            # Extract claims from detailed_summary
            summary = section.get('detailed_summary', '')
            if summary:
                section_claims = self._extract_claims_from_text(
                    summary, f"complete_analysis.{section_title}", importance, claim_counter
                )
                claims.extend(section_claims)
                claim_counter += len(section_claims)
            
            # Extract claims from key_actions
            key_actions = section.get('key_actions', [])
            for action in key_actions:
                if action and len(action.strip()) > 10:
                    claim = AnalysisClaim(
                        claim_id=f"claim_{claim_counter}",
                        claim_text=action,
                        claim_type=ClaimType.PROCEDURAL,
                        section_context=f"complete_analysis.{section_title}.key_actions",
                        importance_level=importance,
                        specificity_level=self._calculate_specificity(action),
                        confidence_level=0.8
                    )
                    claims.append(claim)
                    claim_counter += 1
        
        # Extract claims from overview sections
        overview = analysis.get('overview', {})
        for overview_type, overview_data in overview.items():
            if isinstance(overview_data, dict) and 'content' in overview_data:
                content = overview_data['content']
                if content and len(content.strip()) > 10:
                    section_claims = self._extract_claims_from_text(
                        content, f"overview.{overview_type}", 'secondary', claim_counter
                    )
                    claims.extend(section_claims)
                    claim_counter += len(section_claims)
        
        # Extract claims from positions
        positions = analysis.get('positions', {})
        for position_type, position_data in positions.items():
            if isinstance(position_data, list):
                for item in position_data:
                    if isinstance(item, dict):
                        reason = item.get('reason', '')
                        explanation = item.get('explanation', '')
                        
                        if reason:
                            claim = AnalysisClaim(
                                claim_id=f"claim_{claim_counter}",
                                claim_text=reason,
                                claim_type=ClaimType.INTERPRETIVE,
                                section_context=f"positions.{position_type}",
                                importance_level='primary',
                                specificity_level=self._calculate_specificity(reason),
                                confidence_level=0.7
                            )
                            claims.append(claim)
                            claim_counter += 1
        
        logger.debug(f"Extracted {len(claims)} claims from analysis")
        return claims
    
    def _extract_claims_from_text(self, text: str, context: str, 
                                importance: str, start_counter: int) -> List[AnalysisClaim]:
        """Extract individual claims from a text block"""
        
        claims = []
        sentences = re.split(r'[.!?]+', text)
        
        for i, sentence in enumerate(sentences):
            sentence = sentence.strip()
            if len(sentence) < 15:  # Skip very short sentences
                continue
            
            # Determine claim type based on patterns
            claim_type = self._determine_claim_type(sentence)
            
            # Only include substantive claims
            if self._is_substantive_claim(sentence):
                claim = AnalysisClaim(
                    claim_id=f"claim_{start_counter + i}",
                    claim_text=sentence,
                    claim_type=claim_type,
                    section_context=context,
                    importance_level=importance,
                    specificity_level=self._calculate_specificity(sentence),
                    confidence_level=0.8
                )
                claims.append(claim)
        
        return claims
    
    def _create_evidence_index(self, evidence_spans: List[EvidenceSpan]) -> Dict[str, Any]:
        """Create searchable index of evidence spans"""
        
        index = {
            'by_type': defaultdict(list),
            'by_content': {},
            'by_keywords': defaultdict(list),
            'all_spans': evidence_spans
        }
        
        for span in evidence_spans:
            # Index by evidence type
            index['by_type'][span.evidence_type].append(span)
            
            # Index by content
            index['by_content'][span.id] = span
            
            # Index by keywords
            content_words = re.findall(r'\b\w{4,}\b', span.content.lower())
            for word in content_words:
                index['by_keywords'][word].append(span)
        
        return index
    
    async def _create_evidence_mappings(self, claims: List[AnalysisClaim],
                                      evidence_index: Dict[str, Any]) -> List[EvidenceMapping]:
        """Create mappings between claims and evidence"""
        
        mappings = []
        
        for claim in claims:
            # Find potential supporting evidence
            candidate_evidence = self._find_candidate_evidence(claim, evidence_index)
            
            # Score and rank candidates
            scored_candidates = []
            for evidence in candidate_evidence:
                mapping = self._create_initial_mapping(claim, evidence)
                scored_candidates.append((evidence, mapping))
            
            # Sort by support strength
            scored_candidates.sort(key=lambda x: x[1].support_strength, reverse=True)
            
            # Create mappings for best matches
            for evidence, mapping in scored_candidates[:3]:  # Top 3 matches
                if mapping.support_strength > 0.3:  # Minimum threshold
                    mappings.append(mapping)
        
        return mappings
    
    async def _verify_mappings(self, mappings: List[EvidenceMapping],
                             claims: List[AnalysisClaim],
                             evidence_spans: List[EvidenceSpan]) -> List[EvidenceMapping]:
        """Verify and enhance evidence mappings"""
        
        verified_mappings = []
        
        # Create lookup dictionaries
        claims_dict = {claim.claim_id: claim for claim in claims}
        evidence_dict = {span.id: span for span in evidence_spans}
        
        for mapping in mappings:
            claim = claims_dict.get(mapping.claim_id)
            evidence = evidence_dict.get(mapping.evidence_span_id)
            
            if claim and evidence:
                # Enhance mapping with verification
                enhanced_mapping = self._enhance_mapping_verification(mapping, claim, evidence)
                verified_mappings.append(enhanced_mapping)
        
        return verified_mappings
    
    async def _generate_mapping_report(self, claims: List[AnalysisClaim],
                                     mappings: List[EvidenceMapping],
                                     evidence_spans: List[EvidenceSpan]) -> MappingReport:
        """Generate comprehensive mapping quality report"""
        
        # Calculate basic statistics
        total_claims = len(claims)
        mapped_claim_ids = set(mapping.claim_id for mapping in mappings)
        mapped_claims = len(mapped_claim_ids)
        unmapped_claims = [claim.claim_id for claim in claims if claim.claim_id not in mapped_claim_ids]
        
        # Verification level distribution
        verification_distribution = defaultdict(int)
        for mapping in mappings:
            verification_distribution[mapping.verification_level] += 1
        
        # Support strength distribution
        strength_distribution = {'strong': 0, 'moderate': 0, 'weak': 0}
        for mapping in mappings:
            if mapping.support_strength >= 0.7:
                strength_distribution['strong'] += 1
            elif mapping.support_strength >= 0.4:
                strength_distribution['moderate'] += 1
            else:
                strength_distribution['weak'] += 1
        
        # Calculate overall quality score
        quality_score = self._calculate_overall_quality_score(claims, mappings)
        
        # Identify coverage gaps
        coverage_gaps = self._identify_coverage_gaps(claims, mappings)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(claims, mappings, quality_score)
        
        return MappingReport(
            total_claims=total_claims,
            mapped_claims=mapped_claims,
            unmapped_claims=unmapped_claims,
            verification_distribution=dict(verification_distribution),
            quality_score=quality_score,
            coverage_gaps=coverage_gaps,
            strength_distribution=strength_distribution,
            recommendations=recommendations
        )
    
    def _determine_claim_type(self, sentence: str) -> ClaimType:
        """Determine the type of claim based on linguistic patterns"""
        
        sentence_lower = sentence.lower()
        
        # Check for factual claim patterns
        factual_indicators = ['the bill', 'this act', 'appropriates', 'establishes', 'requires', 'mandates']
        if any(indicator in sentence_lower for indicator in factual_indicators):
            return ClaimType.FACTUAL
        
        # Check for interpretive claim patterns
        interpretive_indicators = ['means', 'indicates', 'suggests', 'implies', 'purpose', 'intent']
        if any(indicator in sentence_lower for indicator in interpretive_indicators):
            return ClaimType.INTERPRETIVE
        
        # Check for consequence claim patterns
        consequence_indicators = ['will result', 'would cause', 'impact', 'affect', 'benefits', 'harms']
        if any(indicator in sentence_lower for indicator in consequence_indicators):
            return ClaimType.CONSEQUENCE
        
        # Check for procedural claim patterns
        procedural_indicators = ['process', 'procedure', 'steps', 'application', 'secretary shall', 'agency must']
        if any(indicator in sentence_lower for indicator in procedural_indicators):
            return ClaimType.PROCEDURAL
        
        # Default to factual
        return ClaimType.FACTUAL
    
    def _is_substantive_claim(self, sentence: str) -> bool:
        """Check if sentence contains a substantive claim worth mapping"""
        
        # Filter out generic or non-substantive content
        generic_patterns = [
            r'^(?:the|this|such|any|all)\s+\w+\s+(?:may|shall|will)\s*$',
            r'comprehensive\s+(?:review|analysis|approach)$',
            r'appropriate\s+(?:measures|actions|steps)$',
            r'various\s+(?:stakeholders|parties|entities)$',
        ]
        
        for pattern in generic_patterns:
            if re.search(pattern, sentence, re.IGNORECASE):
                return False
        
        # Must contain specific indicators
        specific_indicators = ['$', 'days', 'months', 'years', 'shall', 'must', 'penalty', 'fund']
        has_specific = any(indicator in sentence.lower() for indicator in specific_indicators)
        
        return has_specific and len(sentence.split()) >= 5
    
    def _calculate_specificity(self, text: str) -> float:
        """Calculate specificity level of text"""
        
        specificity_score = 0.0
        text_lower = text.lower()
        
        # Check for specific indicators
        if re.search(r'\$[\d,]+', text):
            specificity_score += 0.3
        
        if re.search(r'\d+\s*(?:days?|months?|years?)', text):
            specificity_score += 0.2
        
        if re.search(r'(?:shall|must|required)', text):
            specificity_score += 0.2
        
        if re.search(r'(?:section|subsection)\s+\d+', text):
            specificity_score += 0.1
        
        # Check for specific vs generic language
        generic_terms = ['various', 'multiple', 'comprehensive', 'appropriate', 'necessary']
        generic_count = sum(1 for term in generic_terms if term in text_lower)
        specificity_score -= generic_count * 0.05
        
        return max(0.0, min(1.0, specificity_score + 0.2))  # Base score of 0.2
    
    def _find_candidate_evidence(self, claim: AnalysisClaim, 
                               evidence_index: Dict[str, Any]) -> List[EvidenceSpan]:
        """Find evidence spans that could support the claim"""
        
        candidates = []
        seen_ids = set()  # Track by ID to avoid duplicates
        
        # Get verification rules for claim type
        rules = self.verification_rules.get(claim.claim_type, {})
        relevant_types = rules.get('evidence_types', list(EvidenceType))
        
        # Search by evidence type
        for evidence_type in relevant_types:
            if evidence_type in evidence_index['by_type']:
                for span in evidence_index['by_type'][evidence_type]:
                    if span.id not in seen_ids:
                        candidates.append(span)
                        seen_ids.add(span.id)
        
        # Search by keywords
        claim_words = re.findall(r'\b\w{4,}\b', claim.claim_text.lower())
        for word in claim_words:
            if word in evidence_index['by_keywords']:
                for span in evidence_index['by_keywords'][word]:
                    if span.id not in seen_ids:
                        candidates.append(span)
                        seen_ids.add(span.id)
        
        return candidates
    
    def _create_initial_mapping(self, claim: AnalysisClaim, 
                              evidence: EvidenceSpan) -> EvidenceMapping:
        """Create initial mapping between claim and evidence"""
        
        # Calculate quote overlap
        quote_overlap = self._calculate_quote_overlap(claim.claim_text, evidence.content)
        
        # Calculate semantic alignment
        semantic_alignment = self._calculate_semantic_alignment(claim, evidence)
        
        # Calculate support strength
        support_strength = self._calculate_support_strength(claim, evidence, quote_overlap, semantic_alignment)
        
        # Determine verification level
        verification_level = self._determine_verification_level(support_strength, quote_overlap)
        
        return EvidenceMapping(
            claim_id=claim.claim_id,
            evidence_span_id=evidence.id,
            verification_level=verification_level,
            support_strength=support_strength,
            quote_overlap=quote_overlap,
            semantic_alignment=semantic_alignment,
            verification_notes=""
        )
    
    def _calculate_quote_overlap(self, claim_text: str, evidence_content: str) -> float:
        """Calculate text overlap between claim and evidence"""
        
        claim_words = set(re.findall(r'\b\w+\b', claim_text.lower()))
        evidence_words = set(re.findall(r'\b\w+\b', evidence_content.lower()))
        
        if not claim_words or not evidence_words:
            return 0.0
        
        intersection = claim_words & evidence_words
        union = claim_words | evidence_words
        
        return len(intersection) / len(union)
    
    def _calculate_semantic_alignment(self, claim: AnalysisClaim, evidence: EvidenceSpan) -> float:
        """Calculate semantic alignment between claim and evidence"""
        
        alignment_score = 0.0
        
        # Check for semantic category alignment
        claim_text = claim.claim_text.lower()
        evidence_text = evidence.content.lower()
        
        for category, terms in self.semantic_patterns.items():
            claim_has_category = any(term in claim_text for term in terms)
            evidence_has_category = any(term in evidence_text for term in terms)
            
            if claim_has_category and evidence_has_category:
                alignment_score += 0.15
        
        # Boost for claim type alignment with evidence type
        type_alignments = {
            ClaimType.FACTUAL: [EvidenceType.FUNDING, EvidenceType.ENFORCEMENT, EvidenceType.MANDATE],
            ClaimType.PROCEDURAL: [EvidenceType.PROCESS, EvidenceType.AUTHORITY],
            ClaimType.CONSEQUENCE: [EvidenceType.IMPACT, EvidenceType.SCOPE]
        }
        
        if claim.claim_type in type_alignments:
            if evidence.evidence_type in type_alignments[claim.claim_type]:
                alignment_score += 0.2
        
        return min(1.0, alignment_score)
    
    def _calculate_support_strength(self, claim: AnalysisClaim, evidence: EvidenceSpan,
                                  quote_overlap: float, semantic_alignment: float) -> float:
        """Calculate overall support strength"""
        
        # Base calculation
        support_strength = (quote_overlap * 0.4 + semantic_alignment * 0.3 + 
                          evidence.priority_score * 0.2 + evidence.specificity_score * 0.1)
        
        # Adjust based on claim importance
        if claim.importance_level == 'primary':
            support_strength *= 1.1
        elif claim.importance_level == 'technical':
            support_strength *= 0.9
        
        return min(1.0, support_strength)
    
    def _determine_verification_level(self, support_strength: float, 
                                    quote_overlap: float) -> VerificationLevel:
        """Determine verification level based on support metrics"""
        
        if quote_overlap > 0.7 and support_strength > 0.8:
            return VerificationLevel.DIRECT_QUOTE
        elif support_strength > 0.7:
            return VerificationLevel.DIRECT_SUPPORT
        elif support_strength > 0.5:
            return VerificationLevel.INFERENTIAL
        elif support_strength > 0.3:
            return VerificationLevel.CONTEXTUAL
        else:
            return VerificationLevel.UNSUPPORTED
    
    def _enhance_mapping_verification(self, mapping: EvidenceMapping, 
                                    claim: AnalysisClaim, evidence: EvidenceSpan) -> EvidenceMapping:
        """Enhance mapping with detailed verification"""
        
        # Generate verification notes
        notes = []
        
        if mapping.quote_overlap > 0.5:
            notes.append(f"High text overlap ({mapping.quote_overlap:.2f})")
        
        if mapping.semantic_alignment > 0.6:
            notes.append(f"Strong semantic alignment ({mapping.semantic_alignment:.2f})")
        
        if evidence.evidence_type.value in claim.claim_text.lower():
            notes.append("Evidence type mentioned in claim")
        
        # Check for specific verification criteria
        rules = self.verification_rules.get(claim.claim_type, {})
        
        if rules.get('require_direct_support', False) and mapping.support_strength < 0.6:
            notes.append("WARNING: Claim requires direct support but strength is low")
        
        if mapping.quote_overlap < rules.get('min_quote_overlap', 0.3):
            notes.append("WARNING: Quote overlap below threshold for claim type")
        
        mapping.verification_notes = "; ".join(notes)
        
        return mapping
    
    def _calculate_overall_quality_score(self, claims: List[AnalysisClaim],
                                       mappings: List[EvidenceMapping]) -> float:
        """Calculate overall mapping quality score"""
        
        if not claims:
            return 0.0
        
        # Coverage score
        mapped_claims = set(mapping.claim_id for mapping in mappings)
        coverage_score = len(mapped_claims) / len(claims)
        
        # Support strength score
        if mappings:
            avg_support_strength = sum(mapping.support_strength for mapping in mappings) / len(mappings)
        else:
            avg_support_strength = 0.0
        
        # Verification level score
        verification_scores = {
            VerificationLevel.DIRECT_QUOTE: 1.0,
            VerificationLevel.DIRECT_SUPPORT: 0.8,
            VerificationLevel.INFERENTIAL: 0.6,
            VerificationLevel.CONTEXTUAL: 0.4,
            VerificationLevel.UNSUPPORTED: 0.0
        }
        
        if mappings:
            avg_verification_score = sum(verification_scores[mapping.verification_level] 
                                       for mapping in mappings) / len(mappings)
        else:
            avg_verification_score = 0.0
        
        # Weighted overall score
        overall_score = (coverage_score * 0.4 + avg_support_strength * 0.35 + avg_verification_score * 0.25)
        
        return overall_score
    
    def _identify_coverage_gaps(self, claims: List[AnalysisClaim],
                              mappings: List[EvidenceMapping]) -> List[str]:
        """Identify areas where claims lack evidence support"""
        
        gaps = []
        mapped_claims = set(mapping.claim_id for mapping in mappings)
        
        # Check for unmapped important claims
        for claim in claims:
            if claim.claim_id not in mapped_claims:
                if claim.importance_level == 'primary':
                    gaps.append(f"Primary claim unmapped: {claim.claim_text[:50]}...")
                elif claim.specificity_level > 0.7:
                    gaps.append(f"Specific claim unmapped: {claim.claim_text[:50]}...")
        
        # Check for weak mappings of important claims
        for mapping in mappings:
            claim = next((c for c in claims if c.claim_id == mapping.claim_id), None)
            if claim and claim.importance_level == 'primary' and mapping.support_strength < 0.6:
                gaps.append(f"Primary claim weakly supported: {claim.claim_text[:50]}...")
        
        return gaps
    
    def _generate_recommendations(self, claims: List[AnalysisClaim],
                                mappings: List[EvidenceMapping], 
                                quality_score: float) -> List[str]:
        """Generate recommendations for improving evidence grounding"""
        
        recommendations = []
        
        if quality_score < 0.6:
            recommendations.append("Overall grounding quality is below standards - comprehensive review needed")
        
        # Coverage recommendations
        mapped_claims = set(mapping.claim_id for mapping in mappings)
        unmapped_ratio = 1 - (len(mapped_claims) / len(claims)) if claims else 0
        
        if unmapped_ratio > 0.3:
            recommendations.append("High percentage of unmapped claims - extract more evidence or refine claims")
        
        # Support strength recommendations
        weak_mappings = sum(1 for mapping in mappings if mapping.support_strength < 0.5)
        if weak_mappings > len(mappings) * 0.3:
            recommendations.append("Many weak evidence mappings - improve evidence extraction or claim specificity")
        
        # Verification level recommendations
        unsupported_count = sum(1 for mapping in mappings 
                              if mapping.verification_level == VerificationLevel.UNSUPPORTED)
        if unsupported_count > 0:
            recommendations.append("Remove or rework unsupported claims")
        
        return recommendations
    
    def _identify_quality_issues(self, mappings: List[EvidenceMapping], 
                               report: MappingReport) -> List[str]:
        """Identify specific quality issues with evidence grounding"""
        
        issues = []
        
        if report.quality_score < 0.7:
            issues.append(f"Overall quality score {report.quality_score:.2f} below target")
        
        if report.mapped_claims < report.total_claims * 0.8:
            issues.append(f"Only {report.mapped_claims}/{report.total_claims} claims mapped")
        
        strong_mappings = report.strength_distribution.get('strong', 0)
        if strong_mappings < len(mappings) * 0.5:
            issues.append("Less than 50% of mappings have strong support")
        
        return issues

# Global instance
evidence_mapper = EvidenceAnalysisMapper()

def get_evidence_mapper() -> EvidenceAnalysisMapper:
    """Get the global evidence-analysis mapper instance"""
    return evidence_mapper