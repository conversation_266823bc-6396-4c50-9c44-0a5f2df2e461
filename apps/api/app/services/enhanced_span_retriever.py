"""
Enhanced span retriever with clause-complete spans, mandate boosts, and budget classification
"""

import re
from typing import Dict, List, Any, Tuple
import logging

logger = logging.getLogger(__name__)

class EnhancedSpanRetriever:
    """Enhanced evidence retriever that provides high-quality, clause-complete spans"""
    
    def __init__(self):
        # Mandate and negation patterns (high priority)
        self.mandate_patterns = [
            r'\bshall\b',
            r'\bmust\b', 
            r'\bmay not\b',
            r'\bshall not\b',
            r'\bprohibit(?:ed|s)?\b',
            r'\bpenalty\b',
            r'\bfine\b',
            r'\benforce(?:d|ment)?\b',
            r'\bcivil penalty\b',
            r'\brequire(?:d|s)?\b',
            r'\bmandatory\b'
        ]
        
        # Budget patterns with classification
        self.budget_patterns = {
            'authorization': [
                r'authorized to be appropriated',
                r'authorization of appropriations',
                r'there is authorized',
                r'such sums as may be necessary'
            ],
            'appropriation': [
                r'there are appropriated',
                r'appropriated to',
                r'out of any money in the Treasury',
                r'from funds appropriated'
            ],
            'limit': [
                r'not to exceed',
                r'maximum amount',
                r'ceiling of',
                r'limited to'
            ],
            'penalty': [
                r'civil penalty',
                r'fine of not more than',
                r'penalty of',
                r'monetary penalty'
            ],
            'surcharge': [
                r'surcharge',
                r'fee',
                r'assessment',
                r'user fee'
            ]
        }
        
        # Generic phrases to avoid
        self.generic_phrases = [
            r'technical provisions',
            r'administrative details',
            r'various provisions',
            r'other requirements',
            r'as appropriate',
            r'as necessary'
        ]
        
        # Scope headers that provide context
        self.scope_headers = [
            r'In General',
            r'Applicability',
            r'Definitions',
            r'Requirements',
            r'Limitations',
            r'Exceptions'
        ]
    
    def extract_enhanced_spans(self, bill_text: str, bill_metadata: Dict) -> Dict[str, List[Dict]]:
        """Extract high-quality evidence spans with enhanced features"""
        
        # Parse bill structure
        sections = self._parse_bill_structure(bill_text)
        
        # Extract different types of spans
        mandate_spans = self._extract_mandate_spans(sections)
        budget_spans = self._extract_budget_spans(sections)
        scope_spans = self._extract_scope_spans(sections)
        
        # Combine and prioritize spans
        all_spans = {
            'mandate': mandate_spans,
            'budget': budget_spans,
            'scope': scope_spans,
            'general': self._extract_general_spans(sections)
        }
        
        # Apply routing logic
        routed_spans = self._route_spans_by_content(all_spans, bill_text)
        
        logger.info(f"📊 Extracted spans: mandate={len(mandate_spans)}, budget={len(budget_spans)}, scope={len(scope_spans)}")
        
        return routed_spans
    
    def _parse_bill_structure(self, bill_text: str) -> List[Dict]:
        """Parse bill into structured sections with headers"""
        sections = []
        
        # Split by section headers
        section_pattern = r'(SEC\.\s+\d+\.|SECTION\s+\d+\.)\s*([^\n]+)'
        matches = list(re.finditer(section_pattern, bill_text, re.IGNORECASE))
        
        for i, match in enumerate(matches):
            start_pos = match.start()
            end_pos = matches[i + 1].start() if i + 1 < len(matches) else len(bill_text)
            
            section_text = bill_text[start_pos:end_pos]
            section_number = match.group(1).strip()
            section_title = match.group(2).strip()
            
            sections.append({
                'number': section_number,
                'title': section_title,
                'text': section_text,
                'start_offset': start_pos,
                'end_offset': end_pos,
                'anchor_id': f"sec-{len(sections) + 1}"
            })
        
        return sections
    
    def _extract_mandate_spans(self, sections: List[Dict]) -> List[Dict]:
        """Extract spans containing mandates, requirements, and prohibitions"""
        mandate_spans = []
        
        for section in sections:
            text = section['text']
            
            # Find mandate patterns
            for pattern in self.mandate_patterns:
                for match in re.finditer(pattern, text, re.IGNORECASE):
                    # Extend to clause boundaries
                    span = self._extend_to_clause_boundary(text, match.start(), match.end())
                    
                    if span and len(span['quote']) >= 20:  # Minimum meaningful length
                        span.update({
                            'heading': section['title'],
                            'anchor_id': section['anchor_id'],
                            'start_offset': section['start_offset'] + span['start_offset'],
                            'end_offset': section['start_offset'] + span['end_offset'],
                            'type': 'mandate',
                            'priority': 'high'
                        })
                        mandate_spans.append(span)
        
        # De-duplicate and sort by priority
        return self._deduplicate_spans(mandate_spans)[:10]  # Top 10 mandate spans
    
    def _extract_budget_spans(self, sections: List[Dict]) -> List[Dict]:
        """Extract and classify budget-related spans"""
        budget_spans = []
        
        for section in sections:
            text = section['text']
            
            # Check each budget category
            for category, patterns in self.budget_patterns.items():
                for pattern in patterns:
                    for match in re.finditer(pattern, text, re.IGNORECASE):
                        # Extend to clause boundaries
                        span = self._extend_to_clause_boundary(text, match.start(), match.end())
                        
                        if span and len(span['quote']) >= 15:
                            span.update({
                                'heading': section['title'],
                                'anchor_id': section['anchor_id'],
                                'start_offset': section['start_offset'] + span['start_offset'],
                                'end_offset': section['start_offset'] + span['end_offset'],
                                'type': 'budget',
                                'budget_category': category,
                                'priority': 'high' if category in ['appropriation', 'authorization'] else 'medium'
                            })
                            budget_spans.append(span)
        
        return self._deduplicate_spans(budget_spans)[:8]  # Top 8 budget spans
    
    def _extract_scope_spans(self, sections: List[Dict]) -> List[Dict]:
        """Extract scope and context spans"""
        scope_spans = []
        
        for section in sections:
            text = section['text']
            
            # Find scope headers
            for scope_pattern in self.scope_headers:
                pattern = rf'\({scope_pattern}\)\.?'
                for match in re.finditer(pattern, text, re.IGNORECASE):
                    # Get the paragraph following the scope header
                    span = self._extract_scope_paragraph(text, match.end())
                    
                    if span and len(span['quote']) >= 25:
                        span.update({
                            'heading': f"{section['title']} - {match.group(0)}",
                            'anchor_id': section['anchor_id'],
                            'start_offset': section['start_offset'] + span['start_offset'],
                            'end_offset': section['start_offset'] + span['end_offset'],
                            'type': 'scope',
                            'priority': 'medium'
                        })
                        scope_spans.append(span)
        
        return self._deduplicate_spans(scope_spans)[:5]  # Top 5 scope spans
    
    def _extract_general_spans(self, sections: List[Dict]) -> List[Dict]:
        """Extract general high-quality spans"""
        general_spans = []
        
        for section in sections:
            text = section['text']
            
            # Skip generic sections
            if any(re.search(phrase, section['title'], re.IGNORECASE) for phrase in self.generic_phrases):
                continue
            
            # Extract sentences with specific content
            sentences = re.split(r'[.!?]+', text)
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) < 30:  # Too short
                    continue
                    
                # Check for specific indicators
                has_specifics = (
                    re.search(r'\b\d{1,2}/\d{1,2}/\d{4}|\b\d{4}\b|\bdays?\b|\bmonths?\b', sentence) or  # Dates
                    re.search(r'\$[\d,]+|\b\d+\s*percent|\b\d+%', sentence) or  # Amounts
                    re.search(r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b|Department|Agency|Commission', sentence)  # Entities
                )
                
                if has_specifics:
                    start_pos = text.find(sentence)
                    if start_pos != -1:
                        general_spans.append({
                            'quote': sentence,
                            'heading': section['title'],
                            'anchor_id': section['anchor_id'],
                            'start_offset': section['start_offset'] + start_pos,
                            'end_offset': section['start_offset'] + start_pos + len(sentence),
                            'type': 'general',
                            'priority': 'low'
                        })
        
        return self._deduplicate_spans(general_spans)[:5]  # Top 5 general spans
    
    def _extend_to_clause_boundary(self, text: str, start: int, end: int) -> Dict:
        """Extend span to complete clause boundaries"""
        # Find sentence start (look backward for . ! ? or start of text)
        clause_start = start
        while clause_start > 0 and text[clause_start - 1] not in '.!?':
            clause_start -= 1
        
        # Skip whitespace
        while clause_start < len(text) and text[clause_start].isspace():
            clause_start += 1
        
        # Find sentence end (look forward for . ! ? ; :)
        clause_end = end
        while clause_end < len(text) and text[clause_end] not in '.!?;:':
            clause_end += 1
        
        # Include the punctuation
        if clause_end < len(text):
            clause_end += 1
        
        quote = text[clause_start:clause_end].strip()
        
        return {
            'quote': quote,
            'start_offset': clause_start,
            'end_offset': clause_end
        }
    
    def _extract_scope_paragraph(self, text: str, start_pos: int) -> Dict:
        """Extract paragraph following a scope header"""
        # Skip whitespace and newlines
        while start_pos < len(text) and text[start_pos] in ' \t\n':
            start_pos += 1
        
        # Find end of paragraph (double newline or next section)
        end_pos = start_pos
        while end_pos < len(text):
            if text[end_pos:end_pos+2] == '\n\n' or text[end_pos:end_pos+4] == 'SEC.':
                break
            end_pos += 1
        
        quote = text[start_pos:end_pos].strip()
        
        return {
            'quote': quote,
            'start_offset': start_pos,
            'end_offset': end_pos
        }
    
    def _deduplicate_spans(self, spans: List[Dict]) -> List[Dict]:
        """Remove duplicate and overlapping spans"""
        if not spans:
            return []
        
        # Sort by priority and length
        priority_order = {'high': 3, 'medium': 2, 'low': 1}
        spans.sort(key=lambda x: (priority_order.get(x.get('priority', 'low'), 1), len(x['quote'])), reverse=True)
        
        # Remove overlaps
        unique_spans = []
        for span in spans:
            is_duplicate = False
            for existing in unique_spans:
                # Check for significant overlap
                overlap = min(span['end_offset'], existing['end_offset']) - max(span['start_offset'], existing['start_offset'])
                if overlap > min(len(span['quote']), len(existing['quote'])) * 0.7:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_spans.append(span)
        
        return unique_spans
    
    def _route_spans_by_content(self, all_spans: Dict[str, List[Dict]], bill_text: str) -> Dict[str, List[Dict]]:
        """Route spans based on content complexity"""
        
        # Analyze bill content
        has_money = bool(re.search(r'\$|authorized|appropriated|funding', bill_text, re.IGNORECASE))
        has_mandates = bool(re.search(r'shall|must|prohibit|penalty', bill_text, re.IGNORECASE))
        
        # Determine routing
        if has_money or has_mandates:
            route = 'high'
            max_spans = 15
        else:
            route = 'medium'
            max_spans = 10
        
        # Combine spans by priority
        combined_spans = []
        
        # Add high priority spans first
        for span_type in ['mandate', 'budget']:
            combined_spans.extend(all_spans.get(span_type, []))
        
        # Add medium priority spans
        combined_spans.extend(all_spans.get('scope', []))
        
        # Add general spans if needed
        if len(combined_spans) < max_spans:
            combined_spans.extend(all_spans.get('general', []))
        
        # Limit total spans
        final_spans = combined_spans[:max_spans]
        
        logger.info(f"📊 Routing: {route}, spans: {len(final_spans)}, money: {has_money}, mandates: {has_mandates}")
        
        return {
            'evidence': final_spans,
            'routing': route,
            'total_spans': len(final_spans),
            'has_money': has_money,
            'has_mandates': has_mandates
        }
