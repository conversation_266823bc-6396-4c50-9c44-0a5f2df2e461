#!/usr/bin/env python3
"""
Debug the exact analysis flow to find where progressive persistence breaks
"""
import sys
import os
sys.path.append('./apps/api')
os.environ['ENV_FILE'] = '.env.local'

import asyncio
from app.db.database import get_db
from app.services.ai_service import AIService
from app.models.bill import Bill

async def debug_analysis_flow():
    print("🔍 DEBUGGING: Analysis Flow from AI Service to Progressive Persistence")
    
    try:
        db = next(get_db())
        
        # Get HR4922 bill
        bill = db.query(Bill).filter(Bill.bill_number == 'HR4922').first()
        if not bill:
            print("❌ HR4922 bill not found")
            return
        
        print(f"📋 Bill: {bill.title}")
        print(f"📄 Full text: {len(bill.full_text or '')} chars")
        
        # Create AI service
        ai_service = AIService()
        print(f"🤖 AI service enabled: {ai_service.enabled}")
        print(f"📊 Balanced analyzer: {type(ai_service.balanced_analyzer)}")
        
        bill_metadata = {
            'title': bill.title,
            'bill_number': bill.bill_number,
            'session_year': bill.session_year,
            'bill_id': str(bill.id)
        }
        
        print(f"\n🎯 Calling ai_service.analyze_bill_balanced...")
        print(f"   This should log: '🎯 Starting balanced analysis'")
        
        # Call the AI service method (this routes to balanced analyzer)
        result = await ai_service.analyze_bill_balanced(
            bill_text=bill.full_text or '',
            bill_metadata=bill_metadata,
            source_index=None
        )
        
        print(f"\n📊 AI Service Result:")
        print(f"   Success: {result.get('success', False)}")
        print(f"   Error: {result.get('error', 'None')}")
        
        if result.get('success'):
            # Check if we have the Complete Analysis data
            details_payload = result.get('details_payload', {})
            print(f"   Details payload size: {len(str(details_payload))} chars")
            
            if details_payload:
                overview = details_payload.get('overview', {})
                complete_analysis = overview.get('complete_analysis', [])
                print(f"   Complete Analysis sections: {len(complete_analysis)}")
                
                chunks_completed = overview.get('chunks_completed', 0)
                total_chunks = overview.get('total_chunks', 0)
                print(f"   Progressive chunks: {chunks_completed}/{total_chunks}")
                
                if len(complete_analysis) > 0:
                    print(f"   ✅ SUCCESS: Progressive persistence data found!")
                    sample = complete_analysis[0]
                    print(f"      Sample: '{sample.get('title', 'No title')[:50]}...'")
                else:
                    print(f"   ❌ ISSUE: No complete_analysis sections found")
            else:
                print(f"   ❌ ISSUE: No details_payload in result")
        else:
            print(f"   ❌ FAILED: AI service returned failure")
            
        db.close()
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_analysis_flow())