import React from 'react';
import Link from 'next/link';
import { billApi } from '../../../services/apiClient';
import { normalizeBillNumber } from '../../../utils/slug';
import type { BillDetailsResponse } from '../../../types/billDetails';
import { ComprehensiveAnalysisSummary } from '../../../components/bills/ComprehensiveAnalysisSummary';
import { EnhancedCompleteAnalysis } from '../../../components/bills/EnhancedCompleteAnalysis';
import type { Metadata } from 'next';
import { 
  ChevronRightIcon, 
  ClockIcon, 
  DocumentTextIcon, 
  UsersIcon, 
  CurrencyDollarIcon, 
  CalendarIcon, 
  CheckCircleIcon, 
  ExclamationTriangleIcon,
  CogIcon,
  ShieldCheckIcon,
  ListBulletIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface PageProps { params: Promise<{ slug: string }> }

async function fetchDetailsBySlug(slug: string): Promise<BillDetailsResponse | null> {
  try {
    const data = await billApi.getBillDetailsBySlug(slug);
    return data as BillDetailsResponse;
  } catch {
    return null;
  }
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params;
  const details = await fetchDetailsBySlug(slug);
  const title = details?.seo_title || 'Bill Details';
  const description = details?.seo_meta_description || details?.hero_summary || '';
  const canonical = details?.canonical_url || `https://modernaction.io/bills/${slug}`;
  return {
    title,
    description,
    alternates: { canonical },
    openGraph: { title, description, url: canonical, type: 'article' },
    other: {
      'script:ld+json': JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'Legislation',
        name: title,
        description,
        url: canonical,
        isPartOf: {
          '@type': 'CreativeWorkSeries',
          name: '118th United States Congress'
        }
      }),
    },
  };
}

export default async function BillDetailsPage({ params }: PageProps) {
  const { slug } = await params;
  const details = await fetchDetailsBySlug(slug);

  if (!details) {
    return (
      <div className="min-h-screen bg-gray-50">
        <main className="max-w-3xl mx-auto px-4 py-10">
          <h1 className="text-2xl font-bold mb-4">Bill details not available yet</h1>
          <p className="text-gray-700 mb-6">
            We couldn&apos;t load the detailed analysis for this bill right now. Try again shortly.
          </p>
        </main>
      </div>
    );
  }

  const title = details.seo_title || 'Bill Details';
  const isComprehensiveAnalysis = details.other_details &&
    typeof details.other_details === 'object' &&
    'processing_method' in details.other_details &&
    details.other_details.processing_method === 'comprehensive_chunk_analysis';
  
  // Check for progressive analysis
  const isProgressiveAnalysis = !!(details.overview?.chunks_completed || details.overview?.total_chunks);
  const isInProgress = details.overview?.processing_status === 'in_progress';
  const hasEnhancedSections = details.overview?.complete_analysis && details.overview.complete_analysis.length > 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50/30">
      {/* Hero Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Breadcrumb */}
          <nav className="flex items-center text-sm text-gray-500 mb-6">
            <Link href="/bills" className="hover:text-gray-700 transition-colors">Bills</Link>
            <ChevronRightIcon className="h-4 w-4 mx-2" />
            <span className="text-gray-900 font-medium">{title}</span>
          </nav>
          
          {/* Bill Header */}
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-8">
            <div className="flex-1">
              <h1 className="heading-hero text-gray-900 mb-6">{title}</h1>
              {details.hero_summary && (
                <div className="space-y-4">
                  <p className="body-large text-gray-700 leading-relaxed max-w-4xl">{details.hero_summary}</p>
                  
                  {/* Detailed Analysis Indicator */}
                  {hasEnhancedSections && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <CheckCircleIcon className="h-4 w-4 text-blue-600" />
                        <h3 className="text-sm font-medium text-blue-900">
                          Detailed Analysis
                        </h3>
                      </div>
                      <p className="text-xs text-blue-700">
                        This bill includes {details.overview?.complete_analysis?.length || 0} detailed sections 
                        explaining each provision and its impact.
                      </p>
                    </div>
                  )}
                  
                  {isInProgress && !hasEnhancedSections && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <ClockIcon className="h-4 w-4 text-blue-600 animate-pulse" />
                        <h3 className="text-sm font-medium text-blue-900">
                          Analysis In Progress
                        </h3>
                      </div>
                      <p className="text-xs text-blue-700">
                        We're currently preparing a detailed analysis of this bill. Please check back shortly.
                      </p>
                    </div>
                  )}
                  
                  {details.hero_summary_citations && details.hero_summary_citations.length > 0 && (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <h3 className="text-sm font-medium text-gray-900 mb-2">Key Quotes from the Bill:</h3>
                      <div className="space-y-2">
                        {details.hero_summary_citations.slice(0, 2).map((citation, i) => (
                          <blockquote key={i} className="text-sm text-gray-700 italic border-l-4 border-gray-400 pl-3">
                            &ldquo;{citation.quote}&rdquo;
                          </blockquote>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
            
            {/* Status Card */}
            <div className="lg:w-80">
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-blue-900">Active Legislation</span>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <DocumentTextIcon className="h-4 w-4" />
                    <span>118th Congress</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <ClockIcon className="h-4 w-4" />
                    <span>In Committee</span>
                  </div>
                  {hasEnhancedSections && (
                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <CheckCircleIcon className="h-4 w-4" />
                      <span>Detailed Analysis Available</span>
                    </div>
                  )}
                  {isInProgress && (
                    <div className="flex items-center gap-2 text-sm text-blue-600">
                      <ClockIcon className="h-4 w-4 animate-pulse" />
                      <span>Analysis In Progress</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Quality Banner */}
      {isComprehensiveAnalysis && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="bg-green-50 border border-green-200 rounded-xl p-4">
            <div className="flex items-start gap-3">
              <CheckCircleIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="font-semibold text-green-900 mb-1">Comprehensive Analysis</h3>
                <p className="text-green-800 text-sm mb-3">
                  This bill has been analyzed using our world-class comprehensive system that breaks down every provision with specific mechanisms, enforcement details, and complete transparency.
                </p>
                {details.metrics && (
                  <div className="flex gap-6 text-xs text-green-700">
                    {details.metrics.coverage_ratio !== undefined && (
                      <div className="flex items-center gap-1">
                        <CheckCircleIcon className="h-3 w-3" />
                        <span>Coverage: {Math.round(details.metrics.coverage_ratio * 100)}%</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Comprehensive Analysis Summary */}
        <ComprehensiveAnalysisSummary details={details} />

        <div className="lg:grid lg:grid-cols-12 lg:gap-8">
          {/* Sidebar Navigation */}
          <aside className="lg:col-span-3">
            <div className="sticky top-8">
              <nav className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 className="heading-3 text-gray-900 mb-4">Contents</h2>
                <ul className="space-y-2">
                  {[
                    { id: 'overview-what', icon: DocumentTextIcon, label: 'What it does' },
                    { id: 'overview-who', icon: UsersIcon, label: 'Who it affects' },
                    { id: 'overview-why', icon: ExclamationTriangleIcon, label: 'Why it matters' },
                    { id: 'primary-mechanisms', icon: CogIcon, label: 'Primary mechanisms' },
                    { id: 'provisions', icon: CheckCircleIcon, label: 'Key provisions' },
                    { id: 'enforcement', icon: ShieldCheckIcon, label: 'Enforcement' },
                    { id: 'cost', icon: CurrencyDollarIcon, label: 'Cost impact' },
                    { id: 'timeline', icon: CalendarIcon, label: 'Timeline' },
                    { id: 'additional-details', icon: ListBulletIcon, label: 'Additional details' },
                    { id: 'detailed-bill-breakdown', icon: InformationCircleIcon, label: 'Detailed breakdown' }
                  ].map(({ id, icon: Icon, label }) => (
                    <li key={id}>
                      <a 
                        href={`#${id}`} 
                        className="flex items-center gap-3 px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200 group"
                      >
                        <Icon className="h-4 w-4 group-hover:text-blue-600" />
                        <span>{label}</span>
                      </a>
                    </li>
                  ))}
                </ul>
              </nav>
            </div>
          </aside>

          {/* Main Content */}
          <main className="lg:col-span-9 mt-8 lg:mt-0">
            <div className="space-y-8">
              {/* Traditional Overview Sections */}
              <ContentSection 
                id="overview-what" 
                title="What this bill does" 
                icon={DocumentTextIcon}
                content={details.overview?.what_does?.content}
                citations={details.overview?.what_does?.citations || []}
                emptyMessage="No content available"
              />

              <ContentSection 
                id="overview-who" 
                title="Who this affects" 
                icon={UsersIcon}
                content={details.overview?.who_affects?.content}
                citations={details.overview?.who_affects?.citations || []}
                emptyMessage="No information available"
              />

              <ContentSection 
                id="overview-why" 
                title="Why it matters" 
                icon={ExclamationTriangleIcon}
                content={details.overview?.why_matters?.content}
                citations={details.overview?.why_matters?.citations || []}
                emptyMessage="No information available"
              />

              {/* Primary Mechanisms Section */}
              {details.overview?.primary_mechanisms && details.overview.primary_mechanisms.length > 0 && (
                <section id="primary-mechanisms" className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <CogIcon className="h-5 w-5 text-blue-600" />
                    </div>
                    <h2 className="heading-2 text-gray-900">Primary Mechanisms</h2>
                  </div>
                  <div className="space-y-6">
                    {details.overview.primary_mechanisms.map((mechanism, i) => (
                      <div key={i} className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                        <h3 className="font-semibold text-gray-900 mb-3">{mechanism.mechanism}</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <span className="text-sm font-medium text-gray-600">Affected Parties:</span>
                            <p className="text-sm text-gray-900">{mechanism.affected_parties}</p>
                          </div>
                          <div>
                            <span className="text-sm font-medium text-gray-600">Requirements:</span>
                            <p className="text-sm text-gray-900">{mechanism.requirements}</p>
                          </div>
                          {mechanism.enforcement && (
                            <div>
                              <span className="text-sm font-medium text-gray-600">Enforcement:</span>
                              <p className="text-sm text-gray-900">{mechanism.enforcement}</p>
                            </div>
                          )}
                          {mechanism.timeline && (
                            <div>
                              <span className="text-sm font-medium text-gray-600">Timeline:</span>
                              <p className="text-sm text-gray-900">{mechanism.timeline}</p>
                            </div>
                          )}
                        </div>
                        <Citations citations={mechanism.citations} />
                      </div>
                    ))}
                  </div>
                </section>
              )}

              {/* Key provisions */}
              <section id="provisions" className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <CheckCircleIcon className="h-5 w-5 text-blue-600" />
                  </div>
                  <h2 className="heading-2 text-gray-900">Key provisions</h2>
                </div>
                {details.overview?.key_provisions?.length ? (
                  <div className="space-y-4">
                    {details.overview.key_provisions.map((p, i) => (
                      <div key={i} className="bg-gray-50 rounded-lg p-4 border-l-4 border-blue-500">
                        <p className="body-base text-gray-900 mb-2">{typeof p.content === 'string' ? p.content : 'Invalid content'}</p>
                        <Citations citations={p.citations || []} />
                      </div>
                    ))}
                  </div>
                ) : (
                  <EmptyState message="No provisions available" />
                )}
              </section>

              {/* Cost impact */}
              <ContentSection
                id="cost"
                title="Cost impact"
                icon={CurrencyDollarIcon}
                content={details.overview?.cost_impact?.content}
                citations={details.overview?.cost_impact?.citations || []}
                emptyMessage="No cost details available"
              />

              {/* Additional Details Section (Fluff) */}
              {details.overview?.additional_details && details.overview.additional_details.length > 0 && (
                <section id="additional-details" className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      <ListBulletIcon className="h-5 w-5 text-gray-600" />
                    </div>
                    <h2 className="heading-2 text-gray-900">Additional Details</h2>
                    <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                      Complete transparency
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-6">
                    This section covers all other provisions in the bill for complete transparency,
                    including technical details, definitions, and secondary requirements.
                  </p>
                  <div className="space-y-6">
                    {details.overview.additional_details.map((section, i) => (
                      <div key={i} className="border border-gray-200 rounded-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="font-semibold text-gray-900">{section.section_title}</h3>
                          <div className="flex gap-2">
                            <span className={`text-xs px-2 py-1 rounded ${
                              section.importance === 'primary' ? 'bg-blue-100 text-blue-800' :
                              section.importance === 'secondary' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {section.importance}
                            </span>
                            <span className="text-xs px-2 py-1 rounded bg-gray-100 text-gray-600">
                              {section.section_type}
                            </span>
                          </div>
                        </div>
                        <div className="space-y-3">
                          {section.provisions.map((provision, j) => (
                            <div key={j} className="bg-gray-50 rounded p-3">
                              <div className="flex items-start gap-2">
                                <span className={`text-xs px-2 py-1 rounded flex-shrink-0 ${
                                  provision.type === 'requirement' ? 'bg-red-100 text-red-700' :
                                  provision.type === 'enforcement' ? 'bg-orange-100 text-orange-700' :
                                  provision.type === 'funding' ? 'bg-green-100 text-green-700' :
                                  provision.type === 'legal_change' ? 'bg-purple-100 text-purple-700' :
                                  'bg-blue-100 text-blue-700'
                                }`}>
                                  {provision.type}
                                </span>
                                <div className="flex-1">
                                  <p className="text-sm text-gray-900 mb-1">{provision.provision}</p>
                                  {provision.affected_parties && (
                                    <p className="text-xs text-gray-600">Affects: {provision.affected_parties}</p>
                                  )}
                                  {provision.details && (
                                    <p className="text-xs text-gray-600 mt-1">{provision.details}</p>
                                  )}
                                  {provision.citations.length > 0 && (
                                    <div className="mt-2">
                                      <Citations citations={provision.citations} />
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </section>
              )}

              {/* Enhanced Complete Analysis Section */}
              <EnhancedCompleteAnalysis details={details} />

              {/* Timeline */}
              <section id="timeline" className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <CalendarIcon className="h-5 w-5 text-blue-600" />
                  </div>
                  <h2 className="heading-2 text-gray-900">Timeline</h2>
                </div>
                {details.overview?.timeline?.length ? (
                  <div className="space-y-4">
                    {details.overview.timeline.map((t, i) => (
                      <div key={i} className="flex gap-4">
                        <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div className="flex-1">
                          <p className="body-base text-gray-900 mb-2">{typeof t.content === 'string' ? t.content : 'Invalid content'}</p>
                          <Citations citations={t.citations || []} />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <EmptyState message="No timeline details available" />
                )}
              </section>

              {/* Positions */}
              <section id="positions" className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <h2 className="heading-2 text-gray-900 mb-8">Positions</h2>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <PositionCard 
                    title="Support" 
                    reasons={details.positions?.support_reasons || []} 
                    color="support"
                    icon={CheckCircleIcon}
                  />
                  <PositionCard 
                    title="Oppose" 
                    reasons={details.positions?.oppose_reasons || []} 
                    color="oppose"
                    icon={ExclamationTriangleIcon}
                  />
                  <PositionCard 
                    title="Amend" 
                    reasons={details.positions?.amend_reasons || []} 
                    color="amend"
                    icon={DocumentTextIcon}
                  />
                </div>
              </section>

              {/* Other Details (Legacy) */}
              {details.other_details?.length ? (
                <section id="other-content" className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                  <h2 className="heading-2 text-gray-900 mb-6">Additional Bill Content</h2>
                  <div className="space-y-6">
                    {details.other_details.map((section, i) => (
                      <div key={i} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                        <p className="body-base text-gray-900 leading-relaxed">{typeof section.content === 'string' ? section.content : 'Invalid content'}</p>
                        <Citations citations={section.citations || []} />
                      </div>
                    ))}
                  </div>
                </section>
              ) : null}

              {/* Source Index */}
              {details.source_index?.length ? (
                <section id="source" className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                  <h2 className="heading-2 text-gray-900 mb-6">Source Index</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {details.source_index.map((s, i) => (
                      <div key={i} id={s.anchor_id || undefined} className="bg-gray-50 rounded-lg p-4">
                        <div className="body-small font-medium text-gray-900">{s.heading || 'Section'}</div>
                        <div className="text-micro text-gray-500 mt-1">Characters {s.start_offset}–{s.end_offset}</div>
                      </div>
                    ))}
                  </div>
                </section>
              ) : null}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}

// Component Functions
function ContentSection({
  id,
  title,
  icon: Icon,
  content,
  citations,
  emptyMessage
}: {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  content?: string;
  citations: Array<{ quote: string; anchor_id?: string | null; heading?: string | null }>;
  emptyMessage: string;
}) {
  const safeContent = typeof content === 'string' ? content : undefined;

  return (
    <section id={id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
          <Icon className="h-5 w-5 text-blue-600" />
        </div>
        <h2 className="heading-2 text-gray-900">{title}</h2>
      </div>
      {safeContent ? (
        <div className="space-y-4">
          <p className="body-base text-gray-900 leading-relaxed">{safeContent}</p>
          <Citations citations={citations} />
        </div>
      ) : (
        <EmptyState message={emptyMessage} />
      )}
    </section>
  );
}

function EmptyState({ message }: { message: string }) {
  return (
    <div className="text-center py-8">
      <DocumentTextIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
      <p className="body-small text-gray-500">{message}</p>
    </div>
  );
}

function Citations({ citations }: { citations: Array<{ quote: string; anchor_id?: string | null; heading?: string | null }> }) {
  const list = citations || [];
  if (list.length === 0) return null;
  return (
    <div className="mt-4 space-y-2" data-testid="citations">
      {list.map((c, i) => (
        <div key={i} className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <blockquote className="text-sm text-blue-900 italic mb-2" data-testid="citation-quote">
            &ldquo;{c.quote}&rdquo;
          </blockquote>
          {c.heading && (
            <cite className="text-xs text-blue-700">
              — <a className="underline hover:no-underline" href={`#${c.anchor_id || ''}`} data-testid="citation-link">{c.heading}</a>
            </cite>
          )}
        </div>
      ))}
    </div>
  );
}

function PositionCard({ 
  title, 
  reasons, 
  color, 
  icon: Icon 
}: { 
  title: string; 
  reasons: Array<{ claim: string; justification?: string; citations?: Array<{ quote: string; anchor_id?: string | null; heading?: string | null }> }>; 
  color: 'support' | 'oppose' | 'amend';
  icon: React.ComponentType<{ className?: string }>;
}) {
  const lower = title.toLowerCase();
  const anchorId = lower.includes('support') ? 'support' : lower.includes('oppose') ? 'oppose' : lower.includes('amend') ? 'amend' : undefined;
  
  const colorClasses = {
    support: 'border-green-200 bg-green-50',
    oppose: 'border-red-200 bg-red-50',
    amend: 'border-purple-200 bg-purple-50'
  };
  
  const iconClasses = {
    support: 'text-green-600 bg-green-100',
    oppose: 'text-red-600 bg-red-100',
    amend: 'text-purple-600 bg-purple-100'
  };
  
  return (
    <div id={anchorId} className={`rounded-xl border-2 p-6 ${colorClasses[color]}`}>
      <div className="flex items-center gap-3 mb-4">
        <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${iconClasses[color]}`}>
          <Icon className="h-4 w-4" />
        </div>
        <h3 className="heading-3 text-gray-900">{title}</h3>
      </div>
      {reasons?.length ? (
        <div className="space-y-4">
          {reasons.map((r, i) => (
            <div key={i} className="bg-white rounded-lg p-4 border border-gray-200">
              <div className="body-small font-medium text-gray-900 mb-2">{typeof r.claim === 'string' ? r.claim : 'Invalid claim'}</div>
              {r.justification && r.justification.trim() && (
                <div className="text-sm text-gray-700 mb-3">{typeof r.justification === 'string' ? r.justification : 'Invalid justification'}</div>
              )}
              <Citations citations={r.citations || []} />
            </div>
          ))}
        </div>
      ) : (
        <p className="text-sm text-gray-500 text-center py-4">No {title.toLowerCase()} reasons available</p>
      )}
    </div>
  );
}
