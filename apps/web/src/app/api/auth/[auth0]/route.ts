import { handleAuth, handleLogin } from '@auth0/nextjs-auth0';

export const GET = handleAuth({
  login: handleLogin({
    returnTo: (req) => {
      // Get the returnTo parameter from the URL
      const url = new URL(req.url!);
      const returnTo = url.searchParams.get('returnTo');
      
      // If returnTo is provided and is a valid path on our domain, use it
      if (returnTo && returnTo.startsWith('/')) {
        return returnTo;
      }
      
      // Default fallback
      return '/';
    }
  })
});
