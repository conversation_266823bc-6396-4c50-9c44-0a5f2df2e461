#!/usr/bin/env python3
"""
Test the new span-grounded analysis approach
"""

import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

async def test_span_extraction():
    """Test span extraction without AI calls"""
    try:
        from app.services.ai_service import AIService
        
        ai_service = AIService()
        
        # Test bill with clear financial and mandate content
        bill_text = '''
        This bill authorizes the appropriation of $500 million annually for rural healthcare infrastructure.
        
        Section 1. The Secretary of Health and Human Services shall establish telemedicine programs in underserved areas.
        
        Section 2. Healthcare providers in rural areas shall receive federal funding for equipment upgrades.
        
        Section 3. Penalties of not more than $10,000 shall be imposed for non-compliance with reporting requirements.
        
        Section 4. This Act shall take effect on January 1, 2025.
        '''
        
        bill_metadata = {
            'title': 'Healthcare Access Improvement Act',
            'bill_id': 'hr-test-123'
        }
        
        print("🔍 Testing span extraction...")
        spans = await ai_service._extract_evidence_spans(bill_text, bill_metadata)
        
        print(f"📊 Extracted spans:")
        for category, span_list in spans.items():
            print(f"  {category}: {len(span_list)} spans")
            for i, span in enumerate(span_list):
                print(f"    {i+1}. {span['q']} (score: {span['utility_score']})")
        
        print("\n🔍 Testing span coverage validation...")
        coverage_ok = ai_service._validate_span_coverage(spans)
        print(f"Coverage sufficient: {coverage_ok}")
        
        if coverage_ok:
            print("✅ Span extraction and validation working correctly!")
        else:
            print("❌ Span coverage insufficient")
            
        return spans, coverage_ok
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return None, False

async def test_placeholder_detection():
    """Test placeholder bill detection"""
    try:
        from app.services.ai_service import AIService
        
        ai_service = AIService()
        
        # Test placeholder bill
        placeholder_metadata = {
            'title': 'Reserved for the Speaker',
            'bill_id': 'hr-placeholder-1'
        }
        
        print("\n🔍 Testing placeholder detection...")
        result = await ai_service.analyze_bill_cost_optimized(
            bill_text="This bill is reserved for future use.",
            bill_metadata=placeholder_metadata
        )
        
        print(f"Processing level: {result.get('processing_level', 'Unknown')}")
        print(f"Cost: ${result.get('_metadata', {}).get('cost', 0):.4f}")
        
        if result.get('processing_level') == 'placeholder':
            print("✅ Placeholder detection working correctly!")
        else:
            print("❌ Placeholder detection failed")
            
        return result
        
    except Exception as e:
        print(f"❌ Placeholder test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

async def main():
    """Run all tests"""
    print("🧪 Testing Span-Grounded Analysis Implementation")
    print("=" * 50)
    
    # Test 1: Span extraction
    spans, coverage_ok = await test_span_extraction()
    
    # Test 2: Placeholder detection
    placeholder_result = await test_placeholder_detection()
    
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"✅ Span extraction: {'PASS' if spans else 'FAIL'}")
    print(f"✅ Coverage validation: {'PASS' if coverage_ok else 'FAIL'}")
    print(f"✅ Placeholder detection: {'PASS' if placeholder_result and placeholder_result.get('processing_level') == 'placeholder' else 'FAIL'}")
    
    if spans and coverage_ok and placeholder_result:
        print("\n🎉 All core components working! Ready for full AI integration.")
    else:
        print("\n⚠️ Some components need attention before full deployment.")

if __name__ == "__main__":
    asyncio.run(main())
