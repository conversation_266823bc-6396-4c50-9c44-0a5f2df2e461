"""Add review override flag to bill_details

Revision ID: 009_add_review_override
Revises: 008_merge_heads
Create Date: 2025-08-10 00:20:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '009_add_review_override'
down_revision: Union[str, Sequence[str], None] = '008_merge_heads'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.add_column('bill_details', sa.Column('require_review_before_publish', sa.<PERSON>(), nullable=False, server_default=sa.text('false')))


def downgrade() -> None:
    op.drop_column('bill_details', 'require_review_before_publish')

