# app/models/bill_details.py
from sqlalchemy import Column, String, Text, DateTime, Boolean, ForeignKey, UniqueConstraint
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.base_class import Base

class BillDetails(Base):
    __tablename__ = "bill_details"

    # Relationships
    bill_id = Column(String(255), ForeignKey("bills.id"), nullable=False, index=True, unique=True)
    bill = relationship("Bill", backref="details", uselist=False)

    # SEO fields
    seo_slug = Column(String(255), nullable=True, unique=True, index=True)
    seo_title = Column(String(512), nullable=True)
    seo_meta_description = Column(Text, nullable=True)
    canonical_url = Column(String(512), nullable=True)

    # Readable overview
    hero_summary = Column(Text, nullable=True)
    hero_summary_citations = Column(postgresql.JSONB, nullable=True)

    # Structured sections with citations
    overview = Column(postgresql.JSONB, nullable=True)
    positions = Column(postgresql.JSONB, nullable=True)
    message_templates = Column(postgresql.JSONB, nullable=True)
    tags = Column(postgresql.JSONB, nullable=True)

    # Transparency: additional content
    other_details = Column(postgresql.JSONB, nullable=True)

    # Source index of the bill text for anchoring quotes
    source_index = Column(postgresql.JSONB, nullable=True)

    # Moderation and QA
    needs_human_review = Column(Boolean, nullable=False, default=False)
    reviewed_at = Column(DateTime, nullable=True)
    reviewed_by = Column(String(255), nullable=True)
    moderation_notes = Column(Text, nullable=True)
    require_review_before_publish = Column(Boolean, nullable=False, default=False)

    # Metrics (coverage ratio, unverified counts, etc.)
    metrics = Column(postgresql.JSONB, nullable=True)

    __table_args__ = (
        UniqueConstraint('bill_id', name='uq_bill_details_bill_id'),
    )

