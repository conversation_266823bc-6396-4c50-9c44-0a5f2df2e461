#!/usr/bin/env python3
"""
Phase 4 Real API Test - Adaptive Learning and Optimization System
Tests all Phase 4 components with real API calls and comprehensive integration
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Any
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.config import get_settings
from app.db.database import get_db

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_phase4_real_integration():
    """Test Phase 4 adaptive learning and optimization system with real API calls"""
    
    print("🚀 PHASE 4 REAL INTEGRATION TEST")
    print("=" * 70)
    
    # Check configuration
    settings = get_settings()
    print(f"✅ OpenAI API Key: {'Configured' if settings.OPENAI_API_KEY else 'Missing'}")
    print(f"✅ Environment: {settings.ENVIRONMENT}")
    print(f"✅ Database URL: {'Configured' if settings.DATABASE_URL else 'Missing'}")
    print()
    
    # Create database session
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        test_results = []
        
        # Test 1: Adaptive Learning Service
        print("1️⃣ Testing Adaptive Learning Service")
        try:
            from app.services.adaptive_learning_service import get_adaptive_learning_service
            
            learning_service = get_adaptive_learning_service(db)
            
            # Test bill metadata
            test_metadata = {
                'bill_id': 'test_ph4_1',
                'bill_number': 'HR9999',
                'bill_type': 'hr',
                'title': 'Climate Innovation Investment Act',
                'importance_score': 85
            }
            
            # Test processing result
            test_processing_result = {
                'success': True,
                'quality_metrics': {'overall_score': 0.87},
                'cost_breakdown': {'total_cost': 0.12},
                'processing_time': 95,
                'evidence_quality': {'average_quality_score': 0.82},
                'model_used': 'gpt-4o',
                'evidence_spans': ['ev1', 'ev2', 'ev3', 'ev4'],
                'analysis_type': 'enhanced'
            }
            
            # Record learning data
            await learning_service.record_processing_result(test_metadata, test_processing_result)
            
            # Get learning snapshot
            snapshot = await learning_service.get_learning_snapshot()
            
            print(f"   ✅ Learning service operational")
            print(f"   ✅ Bills processed: {snapshot.total_bills_processed}")
            print(f"   ✅ Average quality: {snapshot.avg_quality_score:.2f}")
            print(f"   ✅ Average cost: ${snapshot.avg_cost:.4f}")
            print(f"   ✅ Identified patterns: {snapshot.identified_patterns}")
            
            # Test optimization suggestions
            suggestions = await learning_service.get_optimization_suggestions(test_metadata)
            print(f"   ✅ Generated {len(suggestions)} optimization suggestions")
            
            test_results.append(("Adaptive Learning Service", True, f"Processed learning data successfully"))
            
        except Exception as e:
            print(f"   ❌ Adaptive learning failed: {e}")
            test_results.append(("Adaptive Learning Service", False, str(e)))
        
        print()
        
        # Test 2: Bill Pattern Recognition
        print("2️⃣ Testing Bill Pattern Recognition")
        try:
            from app.services.bill_pattern_recognition_service import get_bill_pattern_service
            
            pattern_service = get_bill_pattern_service(db)
            
            test_bill_text = """
            SEC. 1. SHORT TITLE.
            This Act may be cited as the "Climate Innovation Investment Act".
            
            SEC. 2. APPROPRIATIONS.
            There is authorized to be appropriated $5,000,000,000 for fiscal year 2025 
            to support climate innovation and clean energy technology development.
            
            SEC. 3. ENFORCEMENT.
            Any entity that fails to comply with the requirements of this Act 
            shall be subject to a civil penalty of not more than $50,000 per violation.
            
            SEC. 4. EFFECTIVE DATE.
            This Act takes effect 180 days after the date of enactment.
            """
            
            # Analyze bill patterns
            pattern_analysis = await pattern_service.analyze_bill_patterns(test_metadata, test_bill_text)
            
            print(f"   ✅ Pattern analysis complete")
            print(f"   ✅ Primary category: {pattern_analysis.identified_patterns[0].category.value}")
            print(f"   ✅ Complexity: {pattern_analysis.identified_patterns[0].complexity.value}")
            print(f"   ✅ Recommended strategy: {pattern_analysis.recommended_strategy.value}")
            print(f"   ✅ Confidence score: {pattern_analysis.confidence_score:.2f}")
            print(f"   ✅ Recommended budget: ${pattern_analysis.recommended_budget:.3f}")
            print(f"   ✅ Quality prediction: {pattern_analysis.quality_prediction:.2f}")
            
            test_results.append(("Bill Pattern Recognition", True, f"Analyzed patterns successfully"))
            
        except Exception as e:
            print(f"   ❌ Pattern recognition failed: {e}")
            test_results.append(("Bill Pattern Recognition", False, str(e)))
        
        print()
        
        # Test 3: Quality Feedback Service
        print("3️⃣ Testing Quality Feedback Service")
        try:
            from app.services.quality_feedback_service import get_quality_feedback_service
            
            feedback_service = get_quality_feedback_service(db)
            
            # Record quality measurements
            await feedback_service.record_quality_measurement(
                bill_id='test_ph4_1',
                bill_number='HR9999',
                processing_result=test_processing_result
            )
            
            # Get quality trends
            trends = await feedback_service.analyze_quality_trends(days=7)
            
            # Generate improvement recommendations
            recommendations = await feedback_service.generate_improvement_recommendations()
            
            # Generate quality report
            quality_report = await feedback_service.generate_quality_report(time_period="7d")
            
            print(f"   ✅ Quality feedback operational")
            print(f"   ✅ Analyzed trends for {len(trends)} metrics")
            print(f"   ✅ Generated {len(recommendations)} recommendations")
            print(f"   ✅ Overall quality score: {quality_report.overall_score:.2f}")
            print(f"   ✅ Active alerts: {len(quality_report.active_alerts)}")
            
            test_results.append(("Quality Feedback Service", True, f"Quality monitoring active"))
            
        except Exception as e:
            print(f"   ❌ Quality feedback failed: {e}")
            test_results.append(("Quality Feedback Service", False, str(e)))
        
        print()
        
        # Test 4: Predictive Cost Optimizer
        print("4️⃣ Testing Predictive Cost Optimizer")
        try:
            from app.services.predictive_cost_optimizer import get_predictive_cost_optimizer
            from app.services.predictive_cost_optimizer import OptimizationStrategy
            
            cost_optimizer = get_predictive_cost_optimizer(db)
            
            # Predict processing cost
            cost_prediction = await cost_optimizer.predict_processing_cost(
                test_metadata, test_bill_text, "balanced"
            )
            
            print(f"   ✅ Cost prediction complete")
            print(f"   ✅ Predicted total cost: ${cost_prediction.total_predicted_cost:.3f}")
            print(f"   ✅ Confidence: {cost_prediction.confidence.value}")
            print(f"   ✅ Optimization opportunities: {len(cost_prediction.optimization_opportunities)}")
            print(f"   ✅ Risk factors: {len(cost_prediction.risk_factors)}")
            
            # Test budget allocation
            budget_allocation = await cost_optimizer.optimize_budget_allocation(
                target_budget=0.20,
                quality_target=0.80,
                strategy=OptimizationStrategy.BALANCED
            )
            
            print(f"   ✅ Budget allocation: ${budget_allocation.total_budget:.3f}")
            print(f"   ✅ Expected quality: {budget_allocation.expected_quality:.2f}")
            
            # Record actual costs for learning
            await cost_optimizer.record_actual_costs(
                'test_ph4_1',
                {'total': 0.12, 'ai_tokens': 0.08, 'evidence': 0.03, 'validation': 0.01},
                0.87,
                95
            )
            
            test_results.append(("Predictive Cost Optimizer", True, f"Cost prediction and optimization working"))
            
        except Exception as e:
            print(f"   ❌ Cost optimizer failed: {e}")
            test_results.append(("Predictive Cost Optimizer", False, str(e)))
        
        print()
        
        # Test 5: Performance Analytics Dashboard
        print("5️⃣ Testing Performance Analytics Dashboard")
        try:
            from app.services.performance_analytics_dashboard import get_performance_dashboard
            from app.services.performance_analytics_dashboard import MetricCategory, TimeRange
            
            dashboard = get_performance_dashboard(db)
            
            # Record comprehensive metrics
            await dashboard.record_bill_processing_metrics('test_ph4_1', test_processing_result)
            
            # Get system health
            system_health = await dashboard.get_system_health()
            
            print(f"   ✅ System health score: {system_health.overall_score:.1f}/100")
            print(f"   ✅ Active alerts: {system_health.active_alerts}")
            print(f"   ✅ Uptime: {system_health.uptime_percentage:.1f}%")
            print(f"   ✅ Status: {system_health.performance_summary}")
            
            # Get analytics summary
            analytics = await dashboard.get_analytics_summary(TimeRange.LAST_24H)
            
            print(f"   ✅ Bills processed (24h): {analytics.bills_processed}")
            print(f"   ✅ Average quality: {analytics.average_quality:.2f}")
            print(f"   ✅ Cost efficiency: {analytics.cost_efficiency:.2f}")
            print(f"   ✅ Success rate: {analytics.success_rate:.1%}")
            
            # Get dashboard data
            dashboard_data = await dashboard.get_dashboard_data()
            
            print(f"   ✅ Dashboard data generated successfully")
            print(f"   ✅ Component scores: {len(dashboard_data.system_health.component_scores)}")
            
            test_results.append(("Performance Analytics Dashboard", True, f"Dashboard operational"))
            
        except Exception as e:
            print(f"   ❌ Performance dashboard failed: {e}")
            test_results.append(("Performance Analytics Dashboard", False, str(e)))
        
        print()
        
        # Test 6: Full Integration with Evidence-First Processing
        print("6️⃣ Testing Full Phase 4 Integration")
        try:
            from app.services.evidence_first_processor import get_evidence_first_processor
            from app.services.balanced_analysis_service import BalancedAnalysisService
            from app.services.ai_service import AIService
            
            # Create a comprehensive test with all Phase 4 components
            processor = get_evidence_first_processor()
            ai_service = AIService()
            balanced_service = BalancedAnalysisService(ai_service)
            
            # Extract evidence first
            raw_evidence = [
                {
                    'id': 'funding_ev',
                    'quote': '$5,000,000,000 for fiscal year 2025',
                    'heading': 'Appropriations',
                    'start_offset': 150,
                    'end_offset': 200
                },
                {
                    'id': 'penalty_ev', 
                    'quote': 'civil penalty of not more than $50,000 per violation',
                    'heading': 'Enforcement',
                    'start_offset': 350,
                    'end_offset': 410
                }
            ]
            
            evidence_spans, processing_report = await processor.process_evidence_first(
                test_bill_text, test_metadata, raw_evidence
            )
            
            print(f"   ✅ Evidence processing: {len(evidence_spans)} spans extracted")
            print(f"   ✅ Processing quality: {processing_report['quality_metrics']['avg_priority_score']:.2f}")
            
            # Get pattern-based optimization from pattern service
            pattern_analysis = await pattern_service.analyze_bill_patterns(test_metadata, test_bill_text)
            
            # Apply predictive cost optimization
            cost_prediction = await cost_optimizer.predict_processing_cost(
                test_metadata, test_bill_text, pattern_analysis.recommended_strategy.value
            )
            
            print(f"   ✅ Pattern-optimized strategy: {pattern_analysis.recommended_strategy.value}")
            print(f"   ✅ Predicted cost: ${cost_prediction.total_predicted_cost:.3f}")
            
            # Perform balanced analysis with optimized parameters
            limited_evidence = evidence_spans[:3]  # Limit for cost control
            
            analysis_result = await balanced_service.analyze_bill_balanced(
                bill_text=test_bill_text[:1000],  # Limit for cost
                bill_metadata=test_metadata,
                evidence_spans=[{
                    'id': span.id,
                    'quote': span.content[:100],  # Limit quote length
                    'heading': span.heading,
                    'start_offset': span.start_offset,
                    'end_offset': span.end_offset
                } for span in limited_evidence]
            )
            
            if analysis_result['success']:
                print(f"   ✅ Integrated analysis successful")
                print(f"   ✅ Final quality: {analysis_result['quality_metrics']['overall_score']:.2f}")
                print(f"   ✅ Final cost: ${analysis_result['cost_breakdown']['total_cost']:.3f}")
                print(f"   ✅ Processing time: {analysis_result.get('processing_time', 0):.1f}s")
                
                # Record final results in all Phase 4 systems
                await learning_service.record_processing_result(test_metadata, analysis_result)
                await feedback_service.record_quality_measurement('test_ph4_1', 'HR9999', analysis_result)
                await dashboard.record_bill_processing_metrics('test_ph4_1', analysis_result)
                await cost_optimizer.record_actual_costs(
                    'test_ph4_1',
                    analysis_result['cost_breakdown'],
                    analysis_result['quality_metrics']['overall_score'],
                    analysis_result.get('processing_time', 95)
                )
                
                print(f"   ✅ All Phase 4 systems updated with final results")
                
                integration_success = (
                    analysis_result['quality_metrics']['overall_score'] >= 0.75 and
                    analysis_result['cost_breakdown']['total_cost'] <= 0.25 and
                    cost_prediction.confidence.value in ['high', 'medium']
                )
                
                test_results.append(("Full Integration", integration_success, 
                                   f"Quality: {analysis_result['quality_metrics']['overall_score']:.2f}, "
                                   f"Cost: ${analysis_result['cost_breakdown']['total_cost']:.3f}"))
                
            else:
                print(f"   ❌ Integration analysis failed: {analysis_result.get('error')}")
                test_results.append(("Full Integration", False, analysis_result.get('error', 'Unknown error')))
            
        except Exception as e:
            print(f"   ❌ Integration test failed: {e}")
            import traceback
            traceback.print_exc()
            test_results.append(("Full Integration", False, str(e)))
        
        print()
        
        # Final Assessment
        print("🏆 PHASE 4 REAL TEST RESULTS")
        print("=" * 70)
        
        passed_tests = sum(1 for _, success, _ in test_results if success)
        total_tests = len(test_results)
        
        for test_name, success, details in test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{test_name}: {status} - {details}")
        
        print()
        print(f"Tests Passed: {passed_tests}/{total_tests}")
        
        if passed_tests == total_tests:
            print("🎉 PHASE 4 FULLY OPERATIONAL!")
            print("✅ Adaptive learning system working")
            print("✅ Bill pattern recognition active") 
            print("✅ Quality feedback loop running")
            print("✅ Predictive cost optimization enabled")
            print("✅ Performance analytics dashboard live")
            print("✅ Full integration with Phase 3 maintained")
            print("🚀 READY FOR PRODUCTION DEPLOYMENT!")
        elif passed_tests >= 4:
            print("🟡 PHASE 4 MOSTLY OPERATIONAL")
            print("Most systems working - review failed components")
        else:
            print("🔴 PHASE 4 NEEDS ATTENTION")
            print("Multiple system failures - comprehensive review required")
        
        return passed_tests == total_tests
        
    except Exception as e:
        print(f"❌ Phase 4 test framework failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    success = asyncio.run(test_phase4_real_integration())
    exit(0 if success else 1)