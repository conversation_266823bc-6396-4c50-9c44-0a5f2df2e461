#!/usr/bin/env python3
"""
Debug script to test bill_details creation for HR5-118
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
app_dir = Path(__file__).parent
sys.path.insert(0, str(app_dir))

from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from app.models.bill import Bill
from app.services.bill_details_service import BillDetailsService
from app.services.text_citation_service import TextCitationService
from app.core.config import settings

def debug_bill_details_creation():
    """Debug the bill_details creation process"""

    # Create database engine and session
    database_url = os.getenv("DATABASE_URL")
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Find the HR5-118 bill
        bill = db.query(Bill).filter(
            Bill.bill_number == "HR5",
            Bill.session_year == 118
        ).first()
        
        if not bill:
            print("❌ Bill HR5-118 not found")
            return
        
        print(f"✅ Found bill: {bill.bill_number} - {bill.title}")
        print(f"   Bill ID: {bill.id}")
        print(f"   Full text length: {len(bill.full_text) if bill.full_text else 0}")
        
        # Check if bill_details already exists
        from app.models.bill_details import BillDetails
        existing_details = db.query(BillDetails).filter(BillDetails.bill_id == bill.id).first()
        if existing_details:
            print(f"✅ Bill details already exist: {existing_details.seo_slug}")
            print(f"   Needs review: {existing_details.needs_human_review}")
            print(f"   Created at: {existing_details.created_at}")
            return existing_details
        
        # Create minimal test payload
        test_payload = {
            "hero_summary": "Test summary for HR5",
            "hero_summary_citations": [
                {
                    "quote": "Parents Bill of Rights Act",
                    "heading": "SEC. 101",
                    "anchor_id": "sec-1",
                    "start_offset": 100,
                    "end_offset": 125
                }
            ],
            "overview": {
                "what_does": {
                    "content": "This bill does something",
                    "citations": []
                }
            },
            "positions": {
                "support_reasons": [],
                "oppose_reasons": [],
                "amend_reasons": []
            },
            "message_templates": {},
            "tags": [],
            "other_details": []
        }
        
        # Test bill_details creation
        print("\n🔧 Testing bill_details creation...")
        details_service = BillDetailsService(db)
        
        try:
            result = details_service.create_or_update_details(
                bill=bill,
                full_text=bill.full_text,
                details_payload=test_payload
            )
            
            print(f"✅ Successfully created bill_details!")
            print(f"   ID: {result.id}")
            print(f"   Slug: {result.seo_slug}")
            print(f"   Needs review: {result.needs_human_review}")
            print(f"   Metrics: {result.metrics}")
            
            return result
            
        except Exception as e:
            print(f"❌ Error creating bill_details: {e}")
            import traceback
            traceback.print_exc()
            return None
            
    finally:
        db.close()


def test_text_citation_service():
    """Test the text citation service"""
    print("\n🔍 Testing TextCitationService...")

    # Create database engine and session
    database_url = os.getenv("DATABASE_URL")
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Find the HR5-118 bill
        bill = db.query(Bill).filter(
            Bill.bill_number == "HR5",
            Bill.session_year == 118
        ).first()
        
        if not bill or not bill.full_text:
            print("❌ Bill or full text not found")
            return
        
        citation_service = TextCitationService()
        source_index = citation_service.build_source_index(bill.full_text)
        
        print(f"✅ Built source index with {len(source_index)} sections")
        
        # Show first few sections
        for i, section in enumerate(source_index[:5]):
            print(f"   {i+1}. {section.heading} (offset: {section.start_offset}-{section.end_offset})")
        
        return source_index
        
    finally:
        db.close()


if __name__ == "__main__":
    print("🚀 Debugging Bill Details Creation for HR5-118")
    print("=" * 60)
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Test text citation service first
    source_index = test_text_citation_service()
    
    # Test bill details creation
    result = debug_bill_details_creation()
    
    if result:
        print(f"\n🎉 Success! Bill details created successfully")
        print(f"   Test the endpoint: http://localhost:8000/api/v1/bills/details/by-slug/{result.seo_slug}")
    else:
        print(f"\n💥 Failed to create bill details")
