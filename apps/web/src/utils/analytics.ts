// Simple analytics event tracking utility
// This can be extended to integrate with services like Google Analytics, Mixpanel, etc.

interface AnalyticsEvent {
  event: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  properties?: Record<string, any>;
}

class Analytics {
  private debug = process.env.NODE_ENV === 'development';

  track(event: AnalyticsEvent) {
    if (this.debug) {
      console.log('[Analytics]', event);
    }

    // In production, this would send to your analytics service
    if (typeof window !== 'undefined') {
      // Example: Google Analytics gtag
      if ('gtag' in window) {
        (window as any).gtag('event', event.action, {
          event_category: event.category,
          event_label: event.label,
          value: event.value,
          ...event.properties
        });
      }

      // Example: Custom analytics endpoint
      // fetch('/api/analytics', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(event)
      // });
    }
  }

  // Bills page specific events
  trackBillsPageView(totalCount: number, filteredCount: number) {
    this.track({
      event: 'bills_page_view',
      category: 'Bills',
      action: 'page_view',
      label: 'bills_page',
      properties: {
        total_bills: totalCount,
        filtered_bills: filteredCount
      }
    });
  }

  trackSearch(query: string, resultCount: number) {
    this.track({
      event: 'bills_search',
      category: 'Bills',
      action: 'search',
      label: 'search_query',
      value: resultCount,
      properties: {
        query: query,
        result_count: resultCount
      }
    });
  }

  trackFilterApply(filterType: string, filterValue: string, resultCount: number) {
    this.track({
      event: 'bills_filter_apply',
      category: 'Bills',
      action: 'filter_apply',
      label: `${filterType}:${filterValue}`,
      value: resultCount,
      properties: {
        filter_type: filterType,
        filter_value: filterValue,
        result_count: resultCount
      }
    });
  }

  trackFilterClear(filterType?: string) {
    this.track({
      event: 'bills_filter_clear',
      category: 'Bills',
      action: 'filter_clear',
      label: filterType || 'all_filters',
      properties: {
        filter_type: filterType
      }
    });
  }

  trackChipToggle(chipType: string, chipValue: string) {
    this.track({
      event: 'bills_chip_toggle',
      category: 'Bills',
      action: 'chip_toggle',
      label: `${chipType}:${chipValue}`,
      properties: {
        chip_type: chipType,
        chip_value: chipValue
      }
    });
  }

  trackCardAction(billId: string, action: string) {
    this.track({
      event: 'bills_card_action',
      category: 'Bills',
      action: action,
      label: billId,
      properties: {
        bill_id: billId
      }
    });
  }

  trackTimeToFirstAction(timeMs: number) {
    this.track({
      event: 'bills_time_to_first_action',
      category: 'Bills',
      action: 'time_to_first_action',
      value: timeMs,
      properties: {
        time_ms: timeMs
      }
    });
  }
}

export const analytics = new Analytics();