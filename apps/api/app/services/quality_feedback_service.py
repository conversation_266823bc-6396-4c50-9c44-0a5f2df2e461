"""
Quality Feedback Loop Service - Phase 4 Continuous Improvement
Tracks quality metrics, identifies trends, and provides real-time feedback for improvement
"""

import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from collections import deque, defaultdict
import asyncio

from sqlalchemy.orm import Session
from sqlalchemy import and_, func

logger = logging.getLogger(__name__)

class QualityMetric(Enum):
    """Types of quality metrics tracked"""
    OVERALL_SCORE = "overall_score"
    EVIDENCE_QUALITY = "evidence_quality" 
    MAPPING_QUALITY = "mapping_quality"
    ANALYSIS_DEPTH = "analysis_depth"
    FACTUAL_ACCURACY = "factual_accuracy"
    COVERAGE_COMPLETENESS = "coverage_completeness"
    RESPONSE_RELEVANCE = "response_relevance"
    USER_SATISFACTION = "user_satisfaction"

class TrendDirection(Enum):
    """Quality trend directions"""
    IMPROVING = "improving"
    STABLE = "stable"
    DECLINING = "declining"
    VOLATILE = "volatile"

class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class QualityMeasurement:
    """Individual quality measurement"""
    measurement_id: str
    timestamp: datetime
    bill_id: str
    bill_number: str
    metric_type: QualityMetric
    value: float  # 0.0 to 1.0
    context: Dict[str, Any]  # Additional context data
    processing_strategy: str
    cost: float
    processing_time: int

@dataclass
class QualityTrend:
    """Quality trend analysis"""
    metric_type: QualityMetric
    direction: TrendDirection
    current_value: float
    change_rate: float  # Rate of change per day
    confidence: float  # 0.0 to 1.0
    sample_size: int
    time_period: int  # Days
    significant_events: List[str]

@dataclass
class QualityAlert:
    """Quality alert for immediate attention"""
    alert_id: str
    timestamp: datetime
    level: AlertLevel
    metric_type: QualityMetric
    message: str
    current_value: float
    threshold_value: float
    suggested_actions: List[str]
    affected_bills: List[str]

@dataclass
class ImprovementRecommendation:
    """Actionable improvement recommendation"""
    recommendation_id: str
    priority: int  # 1-10, higher is more urgent
    category: str  # system, process, config, etc.
    title: str
    description: str
    expected_impact: float  # Expected quality improvement (0.0-1.0)
    implementation_effort: str  # low, medium, high
    success_metrics: List[str]
    implementation_steps: List[str]

@dataclass
class QualityReport:
    """Comprehensive quality report"""
    report_id: str
    timestamp: datetime
    time_period: str
    overall_score: float
    metric_trends: Dict[QualityMetric, QualityTrend]
    active_alerts: List[QualityAlert]
    recommendations: List[ImprovementRecommendation]
    improvement_progress: Dict[str, float]
    performance_highlights: List[str]
    areas_for_improvement: List[str]

class QualityFeedbackService:
    """
    Phase 4 Quality Feedback Loop System that continuously monitors,
    analyzes, and provides feedback on quality metrics for improvement
    """
    
    def __init__(self, db: Session):
        self.db = db
        
        # Quality measurement storage
        self.measurements = deque(maxlen=10000)  # Store last 10k measurements
        self.metric_history = defaultdict(lambda: deque(maxlen=1000))
        
        # Quality thresholds
        self.quality_thresholds = self._initialize_quality_thresholds()
        
        # Trend analysis configuration
        self.trend_config = {
            'short_term_days': 7,
            'medium_term_days': 30,
            'long_term_days': 90,
            'minimum_samples': 10,
            'significant_change_threshold': 0.1  # 10% change
        }
        
        # Alert configuration
        self.alert_config = self._initialize_alert_config()
        
        # Active alerts and recommendations
        self.active_alerts = {}
        self.active_recommendations = {}
        
        # Improvement tracking
        self.improvement_tracking = {
            'implemented_recommendations': {},
            'success_metrics': defaultdict(list),
            'baseline_metrics': {},
            'improvement_timeline': []
        }
    
    async def record_quality_measurement(self, bill_id: str, bill_number: str,
                                       processing_result: Dict[str, Any]) -> None:
        """
        Record quality measurements from a processing result
        """
        
        timestamp = datetime.utcnow()
        measurement_id_base = f"{bill_id}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
        
        # Extract quality metrics from processing result
        quality_metrics = processing_result.get('quality_metrics', {})
        evidence_quality = processing_result.get('evidence_quality', {})
        cost_breakdown = processing_result.get('cost_breakdown', {})
        
        # Create measurements for each metric type
        measurements = []
        
        # Overall Score
        if 'overall_score' in quality_metrics:
            measurements.append(QualityMeasurement(
                measurement_id=f"{measurement_id_base}_overall",
                timestamp=timestamp,
                bill_id=bill_id,
                bill_number=bill_number,
                metric_type=QualityMetric.OVERALL_SCORE,
                value=quality_metrics['overall_score'],
                context={
                    'bill_complexity': processing_result.get('bill_complexity', 'unknown'),
                    'processing_strategy': processing_result.get('processing_strategy', 'unknown')
                },
                processing_strategy=processing_result.get('processing_strategy', 'unknown'),
                cost=cost_breakdown.get('total_cost', 0.0),
                processing_time=processing_result.get('processing_time', 0)
            ))
        
        # Evidence Quality
        if 'average_quality_score' in evidence_quality:
            measurements.append(QualityMeasurement(
                measurement_id=f"{measurement_id_base}_evidence",
                timestamp=timestamp,
                bill_id=bill_id,
                bill_number=bill_number,
                metric_type=QualityMetric.EVIDENCE_QUALITY,
                value=evidence_quality['average_quality_score'],
                context={
                    'evidence_count': evidence_quality.get('total_spans', 0),
                    'high_quality_count': evidence_quality.get('high_quality_count', 0)
                },
                processing_strategy=processing_result.get('processing_strategy', 'unknown'),
                cost=cost_breakdown.get('total_cost', 0.0),
                processing_time=processing_result.get('processing_time', 0)
            ))
        
        # Mapping Quality
        mapping_report = processing_result.get('mapping_report', {})
        if 'quality_score' in mapping_report:
            measurements.append(QualityMeasurement(
                measurement_id=f"{measurement_id_base}_mapping",
                timestamp=timestamp,
                bill_id=bill_id,
                bill_number=bill_number,
                metric_type=QualityMetric.MAPPING_QUALITY,
                value=mapping_report['quality_score'],
                context={
                    'mapped_claims': mapping_report.get('mapped_claims', 0),
                    'total_claims': mapping_report.get('total_claims', 0),
                    'coverage_gaps': len(mapping_report.get('coverage_gaps', []))
                },
                processing_strategy=processing_result.get('processing_strategy', 'unknown'),
                cost=cost_breakdown.get('total_cost', 0.0),
                processing_time=processing_result.get('processing_time', 0)
            ))
        
        # Analysis Depth (based on content length and detail)
        analysis_content = processing_result.get('complete_analysis', [])
        if analysis_content:
            total_content_length = sum(len(section.get('detailed_summary', '')) 
                                     for section in analysis_content)
            # Normalize by bill complexity
            complexity_multiplier = {
                'simple': 1.0, 'moderate': 0.8, 'complex': 0.6, 
                'omnibus': 0.4, 'mega': 0.3
            }
            multiplier = complexity_multiplier.get(
                processing_result.get('bill_complexity', 'moderate'), 0.8
            )
            depth_score = min(1.0, (total_content_length / 2000) * multiplier)
            
            measurements.append(QualityMeasurement(
                measurement_id=f"{measurement_id_base}_depth",
                timestamp=timestamp,
                bill_id=bill_id,
                bill_number=bill_number,
                metric_type=QualityMetric.ANALYSIS_DEPTH,
                value=depth_score,
                context={
                    'content_length': total_content_length,
                    'section_count': len(analysis_content)
                },
                processing_strategy=processing_result.get('processing_strategy', 'unknown'),
                cost=cost_breakdown.get('total_cost', 0.0),
                processing_time=processing_result.get('processing_time', 0)
            ))
        
        # Store measurements
        for measurement in measurements:
            self.measurements.append(measurement)
            self.metric_history[measurement.metric_type].append(measurement)
        
        # Check for alerts
        await self._check_quality_alerts(measurements)
        
        # Update improvement tracking
        await self._update_improvement_tracking(measurements)
        
        logger.info(f"📊 Recorded {len(measurements)} quality measurements for {bill_number}")
    
    def _initialize_quality_thresholds(self) -> Dict[QualityMetric, Dict[str, float]]:
        """Initialize quality thresholds for alerts"""
        
        return {
            QualityMetric.OVERALL_SCORE: {
                'target': 0.80,
                'warning': 0.70,
                'critical': 0.60
            },
            QualityMetric.EVIDENCE_QUALITY: {
                'target': 0.75,
                'warning': 0.65,
                'critical': 0.55
            },
            QualityMetric.MAPPING_QUALITY: {
                'target': 0.70,
                'warning': 0.60,
                'critical': 0.50
            },
            QualityMetric.ANALYSIS_DEPTH: {
                'target': 0.65,
                'warning': 0.55,
                'critical': 0.45
            }
        }
    
    def _initialize_alert_config(self) -> Dict[str, Any]:
        """Initialize alert configuration"""
        
        return {
            'consecutive_failures_for_alert': 3,
            'trend_decline_alert_threshold': -0.05,  # 5% decline per day
            'volatility_alert_threshold': 0.2,  # 20% standard deviation
            'minimum_samples_for_trend_alert': 10
        }
    
    async def _check_quality_alerts(self, measurements: List[QualityMeasurement]) -> None:
        """Check for quality alerts based on new measurements"""
        
        for measurement in measurements:
            metric_type = measurement.metric_type
            value = measurement.value
            thresholds = self.quality_thresholds.get(metric_type, {})
            
            alert_level = None
            message = ""
            
            # Check threshold violations
            if value < thresholds.get('critical', 0.0):
                alert_level = AlertLevel.CRITICAL
                message = f"{metric_type.value} critically low: {value:.2f} < {thresholds['critical']:.2f}"
            elif value < thresholds.get('warning', 0.0):
                alert_level = AlertLevel.WARNING
                message = f"{metric_type.value} below warning threshold: {value:.2f} < {thresholds['warning']:.2f}"
            
            # Create alert if needed
            if alert_level:
                alert = QualityAlert(
                    alert_id=f"{metric_type.value}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                    timestamp=datetime.utcnow(),
                    level=alert_level,
                    metric_type=metric_type,
                    message=message,
                    current_value=value,
                    threshold_value=thresholds.get('warning' if alert_level == AlertLevel.WARNING else 'critical'),
                    suggested_actions=self._generate_alert_actions(metric_type, alert_level),
                    affected_bills=[measurement.bill_number]
                )
                
                self.active_alerts[alert.alert_id] = alert
                logger.warning(f"🚨 Quality Alert ({alert_level.value}): {message}")
        
        # Check for trend-based alerts
        await self._check_trend_alerts()
    
    def _generate_alert_actions(self, metric_type: QualityMetric, 
                              alert_level: AlertLevel) -> List[str]:
        """Generate suggested actions for quality alerts"""
        
        actions = []
        
        if metric_type == QualityMetric.OVERALL_SCORE:
            if alert_level == AlertLevel.CRITICAL:
                actions = [
                    "Immediately review processing pipeline configuration",
                    "Increase budget allocation for affected bill types",
                    "Enable enhanced evidence validation",
                    "Review and update quality control parameters"
                ]
            else:
                actions = [
                    "Review recent processing results for patterns",
                    "Consider adjusting evidence thresholds",
                    "Monitor next 10 bills closely"
                ]
        
        elif metric_type == QualityMetric.EVIDENCE_QUALITY:
            actions = [
                "Review evidence extraction patterns",
                "Check evidence validation rules",
                "Increase evidence quality thresholds",
                "Validate source text quality"
            ]
        
        elif metric_type == QualityMetric.MAPPING_QUALITY:
            actions = [
                "Review evidence-to-analysis mapping rules",
                "Check claim extraction patterns",
                "Validate semantic alignment algorithms",
                "Consider manual review of recent mappings"
            ]
        
        return actions
    
    async def _check_trend_alerts(self) -> None:
        """Check for trend-based quality alerts"""
        
        for metric_type in QualityMetric:
            if metric_type not in self.metric_history:
                continue
                
            recent_measurements = list(self.metric_history[metric_type])[-20:]  # Last 20
            
            if len(recent_measurements) < self.alert_config['minimum_samples_for_trend_alert']:
                continue
            
            # Calculate trend
            values = [m.value for m in recent_measurements]
            timestamps = [m.timestamp for m in recent_measurements]
            
            if len(values) >= 2:
                # Simple linear trend calculation
                time_diffs = [(timestamps[i] - timestamps[0]).days for i in range(len(timestamps))]
                if max(time_diffs) > 0:  # Avoid division by zero
                    trend_slope = self._calculate_trend_slope(time_diffs, values)
                    
                    # Alert if declining trend
                    if trend_slope < self.alert_config['trend_decline_alert_threshold']:
                        alert = QualityAlert(
                            alert_id=f"trend_{metric_type.value}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                            timestamp=datetime.utcnow(),
                            level=AlertLevel.WARNING,
                            metric_type=metric_type,
                            message=f"{metric_type.value} showing declining trend: {trend_slope:.3f} per day",
                            current_value=values[-1],
                            threshold_value=self.alert_config['trend_decline_alert_threshold'],
                            suggested_actions=[
                                "Investigate root cause of quality decline",
                                "Review recent configuration changes",
                                "Consider reverting recent modifications",
                                "Increase monitoring frequency"
                            ],
                            affected_bills=[m.bill_number for m in recent_measurements[-5:]]
                        )
                        
                        self.active_alerts[alert.alert_id] = alert
                        logger.warning(f"📉 Trend Alert: {alert.message}")
    
    def _calculate_trend_slope(self, time_points: List[float], values: List[float]) -> float:
        """Calculate simple linear trend slope"""
        
        n = len(time_points)
        sum_x = sum(time_points)
        sum_y = sum(values)
        sum_xy = sum(time_points[i] * values[i] for i in range(n))
        sum_x2 = sum(x * x for x in time_points)
        
        if n * sum_x2 - sum_x * sum_x == 0:
            return 0.0
        
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
        return slope
    
    async def _update_improvement_tracking(self, measurements: List[QualityMeasurement]) -> None:
        """Update improvement tracking based on new measurements"""
        
        # Track baseline metrics if not set
        for measurement in measurements:
            metric_key = f"{measurement.metric_type.value}"
            
            if metric_key not in self.improvement_tracking['baseline_metrics']:
                # Set baseline as average of first 10 measurements
                recent_values = [m.value for m in self.metric_history[measurement.metric_type]]
                if len(recent_values) >= 10:
                    baseline = sum(recent_values[:10]) / 10
                    self.improvement_tracking['baseline_metrics'][metric_key] = baseline
        
        # Track improvements vs baseline
        for measurement in measurements:
            metric_key = f"{measurement.metric_type.value}"
            baseline = self.improvement_tracking['baseline_metrics'].get(metric_key, 0)
            
            if baseline > 0:
                improvement = (measurement.value - baseline) / baseline
                self.improvement_tracking['success_metrics'][metric_key].append({
                    'timestamp': measurement.timestamp,
                    'improvement': improvement,
                    'absolute_value': measurement.value
                })
                
                # Keep only recent improvements
                recent_improvements = self.improvement_tracking['success_metrics'][metric_key]
                cutoff_date = datetime.utcnow() - timedelta(days=90)
                self.improvement_tracking['success_metrics'][metric_key] = [
                    imp for imp in recent_improvements 
                    if imp['timestamp'] > cutoff_date
                ]
    
    async def analyze_quality_trends(self, days: int = 30) -> Dict[QualityMetric, QualityTrend]:
        """Analyze quality trends over specified time period"""
        
        trends = {}
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        for metric_type in QualityMetric:
            recent_measurements = [
                m for m in self.metric_history[metric_type] 
                if m.timestamp > cutoff_date
            ]
            
            if len(recent_measurements) < 5:  # Need minimum data
                continue
            
            values = [m.value for m in recent_measurements]
            timestamps = [m.timestamp for m in recent_measurements]
            
            # Calculate trend statistics
            current_value = values[-1] if values else 0.0
            time_diffs = [(timestamps[i] - timestamps[0]).days for i in range(len(timestamps))]
            change_rate = self._calculate_trend_slope(time_diffs, values) if len(values) > 1 else 0.0
            
            # Determine trend direction
            if abs(change_rate) < 0.001:  # Very small change
                direction = TrendDirection.STABLE
            elif change_rate > 0.005:  # Improving
                direction = TrendDirection.IMPROVING
            elif change_rate < -0.005:  # Declining
                direction = TrendDirection.DECLINING
            else:
                # Check volatility
                if len(values) > 2:
                    mean_val = sum(values) / len(values)
                    variance = sum((v - mean_val) ** 2 for v in values) / len(values)
                    std_dev = variance ** 0.5
                    if std_dev > 0.1:  # High volatility
                        direction = TrendDirection.VOLATILE
                    else:
                        direction = TrendDirection.STABLE
                else:
                    direction = TrendDirection.STABLE
            
            # Calculate confidence based on sample size and consistency
            confidence = min(1.0, len(recent_measurements) / 50)  # Max confidence at 50 samples
            if direction == TrendDirection.VOLATILE:
                confidence *= 0.5  # Lower confidence for volatile trends
            
            # Identify significant events (large changes)
            significant_events = []
            for i in range(1, len(values)):
                change = abs(values[i] - values[i-1])
                if change > 0.15:  # 15% change
                    significant_events.append(
                        f"Large change on {timestamps[i].strftime('%Y-%m-%d')}: "
                        f"{values[i-1]:.2f} → {values[i]:.2f}"
                    )
            
            trends[metric_type] = QualityTrend(
                metric_type=metric_type,
                direction=direction,
                current_value=current_value,
                change_rate=change_rate,
                confidence=confidence,
                sample_size=len(recent_measurements),
                time_period=days,
                significant_events=significant_events[:5]  # Top 5 events
            )
        
        return trends
    
    async def generate_improvement_recommendations(self) -> List[ImprovementRecommendation]:
        """Generate actionable improvement recommendations"""
        
        recommendations = []
        
        # Analyze recent quality trends
        trends = await self.analyze_quality_trends(days=30)
        
        for metric_type, trend in trends.items():
            if trend.direction == TrendDirection.DECLINING and trend.confidence > 0.6:
                # Declining quality recommendations
                recommendations.extend(self._generate_decline_recommendations(metric_type, trend))
            
            elif trend.current_value < self.quality_thresholds.get(metric_type, {}).get('target', 0.8):
                # Below-target recommendations
                recommendations.extend(self._generate_improvement_recommendations(metric_type, trend))
        
        # Cost-quality optimization recommendations
        recommendations.extend(await self._generate_cost_optimization_recommendations())
        
        # Sort by priority and return top recommendations
        recommendations.sort(key=lambda r: r.priority, reverse=True)
        
        # Store active recommendations
        for rec in recommendations[:10]:  # Top 10
            self.active_recommendations[rec.recommendation_id] = rec
        
        return recommendations[:10]
    
    def _generate_decline_recommendations(self, metric_type: QualityMetric, 
                                        trend: QualityTrend) -> List[ImprovementRecommendation]:
        """Generate recommendations for declining quality metrics"""
        
        recommendations = []
        
        if metric_type == QualityMetric.OVERALL_SCORE:
            recommendations.append(ImprovementRecommendation(
                recommendation_id=f"decline_overall_{datetime.utcnow().strftime('%Y%m%d')}",
                priority=9,
                category="quality_control",
                title="Address Overall Quality Decline",
                description=f"Overall quality has declined {abs(trend.change_rate):.1%} per day over {trend.time_period} days",
                expected_impact=0.15,
                implementation_effort="medium",
                success_metrics=["Overall score > 0.80", "Trend slope > 0"],
                implementation_steps=[
                    "Review processing pipeline configuration",
                    "Increase evidence validation thresholds",
                    "Enable premium processing for critical bills",
                    "Monitor quality metrics daily for 2 weeks"
                ]
            ))
        
        elif metric_type == QualityMetric.EVIDENCE_QUALITY:
            recommendations.append(ImprovementRecommendation(
                recommendation_id=f"decline_evidence_{datetime.utcnow().strftime('%Y%m%d')}",
                priority=8,
                category="evidence_processing",
                title="Improve Evidence Quality Standards",
                description=f"Evidence quality declining at {abs(trend.change_rate):.1%} per day",
                expected_impact=0.12,
                implementation_effort="medium",
                success_metrics=["Evidence quality > 0.75", "High quality evidence ratio > 0.6"],
                implementation_steps=[
                    "Review evidence extraction patterns",
                    "Tighten evidence quality filters",
                    "Increase minimum evidence thresholds",
                    "Add evidence validation checkpoints"
                ]
            ))
        
        return recommendations
    
    def _generate_improvement_recommendations(self, metric_type: QualityMetric, 
                                           trend: QualityTrend) -> List[ImprovementRecommendation]:
        """Generate recommendations for below-target metrics"""
        
        recommendations = []
        target = self.quality_thresholds.get(metric_type, {}).get('target', 0.8)
        gap = target - trend.current_value
        
        if metric_type == QualityMetric.MAPPING_QUALITY and gap > 0.1:
            recommendations.append(ImprovementRecommendation(
                recommendation_id=f"improve_mapping_{datetime.utcnow().strftime('%Y%m%d')}",
                priority=7,
                category="evidence_mapping",
                title="Enhance Evidence-to-Analysis Mapping",
                description=f"Mapping quality {gap:.1%} below target",
                expected_impact=gap * 0.7,  # Expect 70% of gap closure
                implementation_effort="high",
                success_metrics=[f"Mapping quality > {target:.2f}", "Coverage gaps < 20%"],
                implementation_steps=[
                    "Refine semantic alignment algorithms",
                    "Improve claim extraction patterns",
                    "Add manual validation for complex bills",
                    "Enhance evidence categorization"
                ]
            ))
        
        return recommendations
    
    async def _generate_cost_optimization_recommendations(self) -> List[ImprovementRecommendation]:
        """Generate cost-quality optimization recommendations"""
        
        recommendations = []
        
        # Analyze cost vs quality correlation
        recent_measurements = list(self.measurements)[-100:]  # Last 100
        
        if len(recent_measurements) < 20:
            return recommendations
        
        # Find inefficient processing (high cost, low quality)
        inefficient_bills = [
            m for m in recent_measurements 
            if m.cost > 0.15 and m.value < 0.75 and m.metric_type == QualityMetric.OVERALL_SCORE
        ]
        
        if len(inefficient_bills) > len(recent_measurements) * 0.2:  # >20% inefficient
            recommendations.append(ImprovementRecommendation(
                recommendation_id=f"cost_optimization_{datetime.utcnow().strftime('%Y%m%d')}",
                priority=6,
                category="cost_optimization",
                title="Optimize Cost-Quality Balance",
                description=f"{len(inefficient_bills)} bills showing poor cost-quality ratio",
                expected_impact=0.1,
                implementation_effort="medium",
                success_metrics=["Cost per quality point < 0.20", "Inefficient processing < 10%"],
                implementation_steps=[
                    "Implement adaptive budget allocation",
                    "Use pattern recognition for processing strategy",
                    "Enable predictive cost optimization",
                    "Add real-time cost-quality monitoring"
                ]
            ))
        
        return recommendations
    
    async def generate_quality_report(self, time_period: str = "30d") -> QualityReport:
        """Generate comprehensive quality report"""
        
        # Parse time period
        if time_period.endswith('d'):
            days = int(time_period[:-1])
        else:
            days = 30
        
        # Analyze trends
        trends = await self.analyze_quality_trends(days)
        
        # Generate recommendations
        recommendations = await self.generate_improvement_recommendations()
        
        # Calculate overall score
        if trends:
            overall_score = sum(trend.current_value for trend in trends.values()) / len(trends)
        else:
            overall_score = 0.0
        
        # Get active alerts
        active_alerts = [alert for alert in self.active_alerts.values()]
        
        # Calculate improvement progress
        improvement_progress = {}
        for metric_key, improvements in self.improvement_tracking['success_metrics'].items():
            if improvements:
                recent_improvements = improvements[-10:]  # Last 10
                avg_improvement = sum(imp['improvement'] for imp in recent_improvements) / len(recent_improvements)
                improvement_progress[metric_key] = avg_improvement
        
        # Performance highlights
        highlights = []
        for metric_type, trend in trends.items():
            if trend.direction == TrendDirection.IMPROVING and trend.confidence > 0.7:
                highlights.append(f"{metric_type.value} improving at {trend.change_rate:.1%} per day")
            elif trend.current_value > self.quality_thresholds.get(metric_type, {}).get('target', 0.8):
                highlights.append(f"{metric_type.value} above target: {trend.current_value:.2f}")
        
        # Areas for improvement
        improvement_areas = []
        for metric_type, trend in trends.items():
            if trend.direction == TrendDirection.DECLINING:
                improvement_areas.append(f"{metric_type.value} declining")
            elif trend.current_value < self.quality_thresholds.get(metric_type, {}).get('warning', 0.7):
                improvement_areas.append(f"{metric_type.value} below threshold")
        
        return QualityReport(
            report_id=f"quality_report_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            timestamp=datetime.utcnow(),
            time_period=time_period,
            overall_score=overall_score,
            metric_trends=trends,
            active_alerts=active_alerts,
            recommendations=recommendations,
            improvement_progress=improvement_progress,
            performance_highlights=highlights[:5],
            areas_for_improvement=improvement_areas[:5]
        )
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of current quality metrics"""
        
        summary = {
            'total_measurements': len(self.measurements),
            'active_alerts': len(self.active_alerts),
            'active_recommendations': len(self.active_recommendations),
            'metric_counts': {}
        }
        
        for metric_type in QualityMetric:
            count = len(self.metric_history[metric_type])
            if count > 0:
                recent_values = [m.value for m in list(self.metric_history[metric_type])[-10:]]
                avg_value = sum(recent_values) / len(recent_values)
                summary['metric_counts'][metric_type.value] = {
                    'count': count,
                    'recent_average': avg_value
                }
        
        return summary

# Global instance
quality_feedback_service = None

def get_quality_feedback_service(db: Session) -> QualityFeedbackService:
    """Get or create the global quality feedback service instance"""
    global quality_feedback_service
    if quality_feedback_service is None:
        quality_feedback_service = QualityFeedbackService(db)
    return quality_feedback_service