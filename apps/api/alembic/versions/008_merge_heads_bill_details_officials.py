"""merge heads for bill_details and officials enhancements

Revision ID: 008_merge_heads
Revises: 007_add_bill_details, 18fff2d51f56
Create Date: 2025-08-10 00:05:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '008_merge_heads'
down_revision: Union[str, Sequence[str], None] = ('007_add_bill_details', '18fff2d51f56')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # No-op merge
    pass


def downgrade() -> None:
    # No-op merge
    pass

