#!/usr/bin/env python3
"""
Test script for enhanced unified processing with comprehensive analysis integration
"""

import asyncio
import sys
import os

# Add the API directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'apps', 'api'))

from app.db.database import get_db
from app.models.bill import Bill
from app.services.unified_bill_processing_service import UnifiedBillProcessingService

async def test_enhanced_analysis():
    """Test the enhanced analysis on an existing bill"""
    print("🧪 Testing Enhanced Unified Processing with Comprehensive Analysis Integration")
    print("=" * 80)
    
    # Get database session
    db = next(get_db())
    
    try:
        # Get an existing bill
        bill = db.query(Bill).filter(Bill.bill_number == 'HR5').first()
        if not bill:
            print("❌ No HR5 bill found in database")
            return
        
        print(f"📋 Found bill: {bill.title}")
        print(f"📊 Current action data:")
        print(f"   - AI Summary: {len(bill.ai_summary or '')} chars")
        print(f"   - Support Reasons: {len(bill.support_reasons or [])}")
        print(f"   - Oppose Reasons: {len(bill.oppose_reasons or [])}")
        print(f"   - Amend Reasons: {len(bill.amend_reasons or [])}")
        print()
        
        # Test enhanced analysis
        print("🚀 Running Enhanced Analysis...")
        service = UnifiedBillProcessingService(db)
        
        # Test the enhanced AI analysis method directly
        bill_metadata = {
            'title': bill.title,
            'summary': bill.summary or '',
            'bill_number': bill.bill_number,
            'congress_session': bill.session_year,
            'type': 'hr',
            'number': '5'
        }
        
        print("🔍 Testing enhanced AI analysis method...")
        enhanced_ai_results, enhanced_details_payload = await service._run_enhanced_ai_analysis(
            bill.full_text, bill_metadata
        )
        
        if enhanced_ai_results:
            print("✅ Enhanced AI analysis successful!")
            print(f"   - AI Summary: {len(enhanced_ai_results.get('ai_summary', ''))} chars")
            print(f"   - Support Reasons: {len(enhanced_ai_results.get('support_reasons', []))}")
            print(f"   - Oppose Reasons: {len(enhanced_ai_results.get('oppose_reasons', []))}")
            print(f"   - Amend Reasons: {len(enhanced_ai_results.get('amend_reasons', []))}")
            
            # Show sample reasons
            if enhanced_ai_results.get('support_reasons'):
                print(f"   - Sample Support Reason: {enhanced_ai_results['support_reasons'][0][:100]}...")
            if enhanced_ai_results.get('oppose_reasons'):
                print(f"   - Sample Oppose Reason: {enhanced_ai_results['oppose_reasons'][0][:100]}...")
        else:
            print("❌ Enhanced AI analysis failed")
        
        if enhanced_details_payload:
            print("✅ Enhanced details payload generated!")
            print(f"   - Hero Summary: {len(enhanced_details_payload.get('hero_summary', ''))} chars")
            print(f"   - Overview sections: {len(enhanced_details_payload.get('overview', {}))}")
        else:
            print("❌ Enhanced details payload failed")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_enhanced_analysis())
