# ModernAction Web Application

A modern React/Next.js frontend for the ModernAction civic engagement platform.

## Development

```bash
npm install
npm run dev
```

## Features

- **Campaign Management**: Browse and engage with legislative campaigns
- **Action Modal**: Interactive modal for contacting representatives
- **Representative Lookup**: Find officials by zip code
- **Real-time Notifications**: Toast notifications for user feedback
- **Responsive Design**: Mobile-first, accessible interface
- **Component Library**: Reusable UI components with Storybook

## Architecture

### Technology Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS 4
- **Components**: Headless UI for accessibility
- **Forms**: React Hook Form with validation
- **HTTP Client**: Axios with interceptors
- **Notifications**: React Hot Toast
- **Development**: Storybook for component development
- **Testing**: Jest + React Testing Library + Playwright

### Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── campaigns/         # Campaign pages
│   ├── globals.css        # Global styles
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── campaign/         # Campaign-specific components
│   └── shared/           # Reusable components
├── services/             # API client services
├── types/               # TypeScript type definitions
└── __tests__/           # Test files
```

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Access to ModernAction API

### Installation

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local

# Start development server
npm run dev
```

### Environment Variables

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_SOCIAL_SHARING=true
```

## Components

### ActionModal

Interactive modal for composing and sending messages to representatives.

**Location**: `src/components/shared/ActionModal.tsx`

**Features**:
- Email validation with React Hook Form
- Pre-populated message from campaign data
- Representative preview with photos
- Loading states and error handling
- Accessibility with focus management

**Usage**:
```tsx
import { ActionModal } from '@/components/shared';

<ActionModal
  isOpen={isModalOpen}
  onClose={handleClose}
  campaign={campaign}
  officials={officials}
  onSubmit={handleSubmit}
  isLoading={isSubmitting}
/>
```

**Props**:
- `isOpen`: Boolean - Controls modal visibility
- `onClose`: Function - Called when modal is closed
- `campaign`: Campaign - Campaign data object
- `officials`: Official[] - Array of representative data
- `onSubmit`: Function - Handles form submission
- `isLoading`: Boolean - Shows loading state

### ActionForm

Primary interface for taking action on campaigns.

**Location**: `src/components/campaign/ActionForm.tsx`

**Features**:
- Zip code input with validation
- Representative lookup via API
- Modal integration for message composition
- Toast notifications for feedback
- Custom message options

**Usage**:
```tsx
import ActionForm from '@/components/campaign/ActionForm';

<ActionForm
  campaign={campaign}
  onSubmit={handleSubmit}
  isLoading={isLoading}
/>
```

## Services

### API Client

Centralized HTTP client for all API interactions.

**Location**: `src/services/apiClient.ts`

**Features**:
- Axios instance with base configuration
- Request/response interceptors
- Error handling and retry logic
- TypeScript type safety

**Usage**:
```tsx
import { campaignApi, actionApi } from '@/services/apiClient';

// Get campaigns
const campaigns = await campaignApi.getCampaigns();

// Create action
const action = await actionApi.createAction(actionData);
```

### Action Service

Specialized service for action-related operations.

**Location**: `src/services/actionService.ts`

**Features**:
- Action creation with multiple officials
- Email validation and formatting
- Error handling and recovery
- Statistics and analytics

**Usage**:
```tsx
import { createAction } from '@/services/actionService';

const result = await createAction({
  formData,
  campaign,
  officials,
  userZipCode
});
```

## Development

### Running Storybook

```bash
# Start Storybook development server
npm run storybook

# Build Storybook for production
npm run build-storybook
```

### Testing

```bash
# Run unit tests
npm test

# Run unit tests in watch mode
npm run test:watch

# Run end-to-end tests
npm run test:e2e

# Run e2e tests with UI
npm run test:e2e:ui
```

### Linting and Formatting

```bash
# Run ESLint
npm run lint

# Build production bundle
npm run build

# Start production server
npm start
```

## API Integration

### Campaign Data Flow

1. **Campaign List**: Fetch campaigns from `/api/v1/campaigns`
2. **Campaign Details**: Get specific campaign from `/api/v1/campaigns/{id}`
3. **Representative Lookup**: Find officials via `/api/v1/officials/zip/{zipCode}`
4. **Action Creation**: Submit action via `/api/v1/actions`

### Error Handling

The application implements comprehensive error handling:

- **Network Errors**: Toast notifications with retry options
- **Validation Errors**: Form-level validation with clear messages
- **API Errors**: Graceful degradation with user-friendly messages
- **Loading States**: Clear indicators during async operations

### Data Types

TypeScript interfaces ensure type safety:

```tsx
interface Campaign {
  id: string;
  title: string;
  description: string;
  campaign_type: CampaignType;
  status: CampaignStatus;
  call_to_action: string;
  bill: Bill;
  talking_points: string[];
  // ... other fields
}

interface Official {
  id: string;
  name: string;
  level: OfficialLevel;
  chamber: Chamber;
  email: string;
  phone: string;
  // ... other fields
}
```

## Accessibility

### WCAG Compliance

- **Keyboard Navigation**: All interactive elements accessible via keyboard
- **Screen Reader Support**: Proper ARIA labels and roles
- **Color Contrast**: Meets WCAG AA standards
- **Focus Management**: Clear focus indicators and logical tab order

### Headless UI Integration

Components use Headless UI for accessibility:

- **Dialog**: Modal with focus trapping and escape handling
- **Form Elements**: Proper labeling and validation
- **Navigation**: Accessible routing and breadcrumbs

## Performance

### Optimization Strategies

- **Code Splitting**: Route-based splitting with Next.js
- **Image Optimization**: Next.js Image component
- **Bundle Analysis**: Webpack Bundle Analyzer
- **Lazy Loading**: Component-level lazy loading

### Monitoring

- **Core Web Vitals**: LCP, FID, CLS tracking
- **Error Tracking**: Error boundaries and reporting
- **Performance Metrics**: Real user monitoring
- **Analytics**: User behavior tracking

## Deployment

### Build Process

```bash
# Production build
npm run build

# Test production build locally
npm start
```

### Environment Configuration

Different configurations for each environment:

- **Development**: `.env.local`
- **Staging**: `.env.staging`
- **Production**: `.env.production`

### Deployment Checklist

- [ ] Environment variables configured
- [ ] API endpoints accessible
- [ ] SSL certificates installed
- [ ] CDN configured for static assets
- [ ] Error tracking enabled
- [ ] Analytics configured
- [ ] Performance monitoring active

## Contributing

### Development Workflow

1. **Feature Branch**: Create branch from `main`
2. **Development**: Write code with tests
3. **Storybook**: Document components
4. **Testing**: Run full test suite
5. **Review**: Submit pull request
6. **Deployment**: Merge to `main`

### Code Standards

- **TypeScript**: Strict type checking
- **ESLint**: Airbnb configuration
- **Prettier**: Code formatting
- **Husky**: Git hooks for quality checks

### Component Guidelines

- **Accessibility**: WCAG 2.1 AA compliance
- **Responsive**: Mobile-first design
- **Performance**: Optimize bundle size
- **Documentation**: Storybook stories required
- **Testing**: Unit tests for all components

## Troubleshooting

### Common Issues

1. **API Connection**: Check NEXT_PUBLIC_API_URL
2. **Build Errors**: Clear `.next` directory
3. **Type Errors**: Update TypeScript definitions
4. **Storybook Issues**: Check addon compatibility

### Debug Mode

Enable debug logging:

```env
DEBUG=true
NODE_ENV=development
```

### Support

- **Documentation**: Check `/docs` directory
- **Issues**: GitHub issue tracker
- **Discussions**: Team Slack channel