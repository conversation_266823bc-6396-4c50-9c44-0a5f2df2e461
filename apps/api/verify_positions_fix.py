#!/usr/bin/env python3
"""
Final verification script for positions data fix
"""

import sys
sys.path.append('/Users/<USER>/modern-action-2.0/apps/api')

from app.db.database import get_db
from app.models.bill_details import BillDetails
from app.models.bill import Bill
from datetime import datetime, timed<PERSON><PERSON>

def verify_positions_fix():
    """Verify that positions data is being saved correctly after our fix"""
    
    print("🔍 POSITIONS DATA FIX VERIFICATION")
    print("=" * 50)
    
    db = next(get_db())
    
    # Check recent bill details (last 15 minutes)
    cutoff_time = datetime.now() - timedelta(minutes=15)
    recent_details = db.query(BillDetails).filter(
        BillDetails.updated_at > cutoff_time
    ).order_by(BillDetails.updated_at.desc()).all()
    
    print(f"📊 Recent bill details (last 15 minutes): {len(recent_details)} records")
    
    success_count = 0
    total_count = 0
    
    for detail in recent_details:
        bill = db.query(Bill).filter(Bill.id == detail.bill_id).first()
        bill_name = bill.bill_number if bill else 'Unknown'
        
        # Count positions
        positions_count = 0
        if detail.positions and isinstance(detail.positions, dict):
            for key, value in detail.positions.items():
                if isinstance(value, list):
                    positions_count += len(value)
        
        # Count tags
        tags_count = len(detail.tags) if detail.tags else 0
        
        # Count analysis sections
        analysis_count = len(detail.overview.get('complete_analysis', [])) if detail.overview else 0
        
        # Check for secondary analysis fields (indicates merge path was used)
        secondary_fields = ['what_does', 'who_affects', 'why_matters', 'cost_impact', 'key_provisions', 'timeline']
        has_secondary = any(field in (detail.overview or {}) for field in secondary_fields)
        
        print(f"\n  📄 {detail.seo_slug} ({bill_name}) - {detail.updated_at}")
        print(f"     Positions: {positions_count}, Tags: {tags_count}, Analysis: {analysis_count}")
        print(f"     Has secondary: {has_secondary}")
        
        total_count += 1
        
        # Success criteria: has positions data OR is a placeholder bill (short content)
        bill_is_substantial = len(bill.full_text or '') > 1000 if bill else False
        
        if positions_count > 0:
            success_count += 1
            print(f"     ✅ SUCCESS: Has positions data!")
            
            # Show sample positions
            if detail.positions:
                for pos_type, reasons in detail.positions.items():
                    if isinstance(reasons, list) and len(reasons) > 0:
                        sample = reasons[0]
                        if isinstance(sample, dict):
                            content = sample.get('reason') or sample.get('suggestion') or str(sample)
                            print(f"        {pos_type}[0]: {content[:80]}...")
        elif not bill_is_substantial:
            print(f"     ⚪ EXPECTED: Placeholder bill (no positions expected)")
        else:
            print(f"     ❌ MISSING: Substantial bill but no positions data")
    
    print(f"\n" + "=" * 50)
    print(f"📋 VERIFICATION RESULTS:")
    print(f"   Bills with positions: {success_count}/{total_count}")
    
    if success_count > 0:
        print(f"   ✅ FIX WORKING: {success_count} bills have positions data!")
        print(f"   🎉 The merge logic fix is preserving positions successfully!")
    elif total_count == 0:
        print(f"   ⏳ NO RECENT PROCESSING: No bills processed in last 15 minutes")
        print(f"   💡 Wait for HR1 to complete processing, then re-run this script")
    else:
        print(f"   ❌ FIX NOT WORKING: No bills have positions data")
        print(f"   🔧 Need further investigation")
    
    db.close()
    return success_count > 0

if __name__ == "__main__":
    verify_positions_fix()