"""
AI Usage Tracking Service

Tracks token usage, costs, and performance metrics for all AI operations.
Provides cost monitoring, budget alerts, and usage analytics.
"""

import logging
import uuid
from datetime import datetime, timedelta, date
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc

from app.models.ai_usage import <PERSON><PERSON>sageLog, AIUsageSummary, AIBudgetAlert
from app.models.bill import Bill

logger = logging.getLogger(__name__)


class AIUsageTrackingService:
    """Service for tracking AI usage, costs, and performance"""

    # OpenAI pricing (as of 2024) - update these as needed
    PRICING = {
        'gpt-4-turbo': {
            'prompt': 0.01 / 1000,      # $0.01 per 1K prompt tokens
            'completion': 0.03 / 1000    # $0.03 per 1K completion tokens
        },
        'gpt-4': {
            'prompt': 0.03 / 1000,      # $0.03 per 1K prompt tokens
            'completion': 0.06 / 1000    # $0.06 per 1K completion tokens
        },
        'gpt-3.5-turbo': {
            'prompt': 0.0015 / 1000,    # $0.0015 per 1K prompt tokens
            'completion': 0.002 / 1000   # $0.002 per 1K completion tokens
        }
    }

    def __init__(self, db: Session):
        self.db = db

    def track_ai_usage(
        self,
        operation_type: str,
        model_name: str,
        prompt_tokens: int,
        completion_tokens: int,
        response_time_ms: float,
        success: bool = True,
        operation_subtype: str = None,
        bill_id: str = None,
        user_id: str = None,
        session_id: str = None,
        error_message: str = None,
        prompt_length: int = None,
        response_length: int = None
    ) -> AIUsageLog:
        """
        Track a single AI API call with usage and cost details
        
        Args:
            operation_type: Type of operation ('bill_analysis', 'reason_generation', etc.)
            model_name: AI model used ('gpt-4-turbo', 'gpt-3.5-turbo', etc.)
            prompt_tokens: Number of tokens in the prompt
            completion_tokens: Number of tokens in the completion
            response_time_ms: Response time in milliseconds
            success: Whether the operation was successful
            operation_subtype: Subtype of operation ('support_reasons', 'comprehensive_analysis', etc.)
            bill_id: Associated bill ID if applicable
            user_id: User who triggered the operation
            session_id: Session identifier
            error_message: Error message if operation failed
            prompt_length: Character length of prompt
            response_length: Character length of response
            
        Returns:
            AIUsageLog: The created usage log entry
        """
        try:
            # Calculate costs
            total_tokens = prompt_tokens + completion_tokens
            pricing = self.PRICING.get(model_name, self.PRICING['gpt-4-turbo'])  # Default to gpt-4-turbo pricing
            
            prompt_cost = prompt_tokens * pricing['prompt']
            completion_cost = completion_tokens * pricing['completion']
            total_cost = prompt_cost + completion_cost

            # Create usage log entry
            usage_log = AIUsageLog(
                id=str(uuid.uuid4()),
                operation_type=operation_type,
                operation_subtype=operation_subtype,
                bill_id=bill_id,
                model_name=model_name,
                provider='openai',
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                prompt_cost=prompt_cost,
                completion_cost=completion_cost,
                total_cost=total_cost,
                response_time_ms=response_time_ms,
                success=success,
                error_message=error_message,
                prompt_length=prompt_length,
                response_length=response_length,
                user_id=user_id,
                session_id=session_id,
                created_at=datetime.utcnow()
            )

            self.db.add(usage_log)
            self.db.commit()

            logger.info(f"Tracked AI usage: {operation_type} - {model_name} - {total_tokens} tokens - ${total_cost:.4f}")

            # Check budget alerts
            self._check_budget_alerts(usage_log)

            # Update daily summaries (async)
            self._update_daily_summary(usage_log)

            return usage_log

        except Exception as e:
            logger.error(f"Failed to track AI usage: {e}")
            self.db.rollback()
            raise

    def get_bill_ai_costs(self, bill_id: str) -> Dict[str, Any]:
        """Get AI costs and usage for a specific bill"""
        try:
            # Get all AI usage for this bill
            usage_logs = self.db.query(AIUsageLog).filter(
                AIUsageLog.bill_id == bill_id
            ).all()

            if not usage_logs:
                return {
                    'bill_id': bill_id,
                    'total_cost': 0.0,
                    'total_tokens': 0,
                    'operations': [],
                    'summary': 'No AI operations recorded for this bill'
                }

            # Calculate totals
            total_cost = sum(log.total_cost for log in usage_logs)
            total_tokens = sum(log.total_tokens for log in usage_logs)
            total_requests = len(usage_logs)
            successful_requests = sum(1 for log in usage_logs if log.success)

            # Group by operation type
            operations = {}
            for log in usage_logs:
                op_key = f"{log.operation_type}_{log.operation_subtype or 'default'}"
                if op_key not in operations:
                    operations[op_key] = {
                        'operation_type': log.operation_type,
                        'operation_subtype': log.operation_subtype,
                        'requests': 0,
                        'total_cost': 0.0,
                        'total_tokens': 0,
                        'avg_response_time': 0.0
                    }
                
                operations[op_key]['requests'] += 1
                operations[op_key]['total_cost'] += log.total_cost
                operations[op_key]['total_tokens'] += log.total_tokens
                operations[op_key]['avg_response_time'] += log.response_time_ms or 0

            # Calculate averages
            for op in operations.values():
                if op['requests'] > 0:
                    op['avg_response_time'] = op['avg_response_time'] / op['requests']
                    op['avg_cost_per_request'] = op['total_cost'] / op['requests']
                    op['avg_tokens_per_request'] = op['total_tokens'] / op['requests']

            return {
                'bill_id': bill_id,
                'total_cost': round(total_cost, 4),
                'total_tokens': total_tokens,
                'total_requests': total_requests,
                'successful_requests': successful_requests,
                'success_rate': round(successful_requests / total_requests * 100, 2) if total_requests > 0 else 0,
                'avg_cost_per_request': round(total_cost / total_requests, 4) if total_requests > 0 else 0,
                'operations': list(operations.values()),
                'first_operation': min(log.created_at for log in usage_logs),
                'last_operation': max(log.created_at for log in usage_logs)
            }

        except Exception as e:
            logger.error(f"Failed to get bill AI costs: {e}")
            return {'error': str(e)}

    def get_daily_usage_summary(self, days: int = 30) -> Dict[str, Any]:
        """Get daily AI usage summary for the last N days"""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Get daily summaries
            summaries = self.db.query(AIUsageSummary).filter(
                and_(
                    AIUsageSummary.date_bucket >= start_date,
                    AIUsageSummary.period_type == 'daily',
                    AIUsageSummary.operation_type == 'all'  # Total summaries
                )
            ).order_by(desc(AIUsageSummary.date_bucket)).all()

            # Calculate totals
            total_cost = sum(s.total_cost for s in summaries)
            total_tokens = sum(s.total_tokens for s in summaries)
            total_requests = sum(s.total_requests for s in summaries)
            bills_processed = sum(s.bills_processed for s in summaries)

            return {
                'period_days': days,
                'total_cost': round(total_cost, 4),
                'total_tokens': total_tokens,
                'total_requests': total_requests,
                'bills_processed': bills_processed,
                'avg_cost_per_day': round(total_cost / days, 4) if days > 0 else 0,
                'avg_cost_per_bill': round(total_cost / bills_processed, 4) if bills_processed > 0 else 0,
                'daily_breakdown': [
                    {
                        'date': s.date_bucket.date(),
                        'cost': round(s.total_cost, 4),
                        'tokens': s.total_tokens,
                        'requests': s.total_requests,
                        'bills': s.bills_processed,
                        'avg_response_time': s.avg_response_time_ms
                    } for s in summaries
                ]
            }

        except Exception as e:
            logger.error(f"Failed to get daily usage summary: {e}")
            return {'error': str(e)}

    def _check_budget_alerts(self, usage_log: AIUsageLog):
        """Check if any budget alerts should be triggered"""
        try:
            # Get active alerts
            alerts = self.db.query(AIBudgetAlert).filter(
                AIBudgetAlert.is_active == True
            ).all()

            for alert in alerts:
                should_trigger = False
                current_amount = 0.0

                if alert.alert_type == 'daily':
                    # Check daily spending
                    today = date.today()
                    daily_usage = self.db.query(func.sum(AIUsageLog.total_cost)).filter(
                        and_(
                            func.date(AIUsageLog.created_at) == today,
                            AIUsageLog.operation_type == alert.operation_type if alert.operation_type else True,
                            AIUsageLog.model_name == alert.model_name if alert.model_name else True
                        )
                    ).scalar() or 0.0
                    
                    current_amount = daily_usage
                    should_trigger = daily_usage >= alert.threshold_amount

                elif alert.alert_type == 'monthly':
                    # Check monthly spending
                    start_of_month = date.today().replace(day=1)
                    monthly_usage = self.db.query(func.sum(AIUsageLog.total_cost)).filter(
                        and_(
                            func.date(AIUsageLog.created_at) >= start_of_month,
                            AIUsageLog.operation_type == alert.operation_type if alert.operation_type else True,
                            AIUsageLog.model_name == alert.model_name if alert.model_name else True
                        )
                    ).scalar() or 0.0
                    
                    current_amount = monthly_usage
                    should_trigger = monthly_usage >= alert.threshold_amount

                elif alert.alert_type == 'per_bill' and usage_log.bill_id:
                    # Check per-bill spending
                    bill_usage = self.db.query(func.sum(AIUsageLog.total_cost)).filter(
                        and_(
                            AIUsageLog.bill_id == usage_log.bill_id,
                            AIUsageLog.operation_type == alert.operation_type if alert.operation_type else True,
                            AIUsageLog.model_name == alert.model_name if alert.model_name else True
                        )
                    ).scalar() or 0.0
                    
                    current_amount = bill_usage
                    should_trigger = bill_usage >= alert.threshold_amount

                if should_trigger:
                    self._trigger_budget_alert(alert, current_amount, usage_log)

        except Exception as e:
            logger.error(f"Failed to check budget alerts: {e}")

    def _trigger_budget_alert(self, alert: AIBudgetAlert, current_amount: float, usage_log: AIUsageLog):
        """Trigger a budget alert"""
        try:
            # Update alert
            alert.last_triggered = datetime.utcnow()
            alert.times_triggered += 1
            self.db.commit()

            # Log the alert
            logger.warning(f"Budget alert triggered: {alert.alert_name} - Current: ${current_amount:.4f} - Threshold: ${alert.threshold_amount:.4f}")

            # TODO: Send notifications (email, webhook, etc.)
            # This would integrate with your notification system

        except Exception as e:
            logger.error(f"Failed to trigger budget alert: {e}")

    def _update_daily_summary(self, usage_log: AIUsageLog):
        """Update daily usage summary"""
        try:
            today = date.today()
            
            # Update or create daily summary for 'all' operations
            summary = self.db.query(AIUsageSummary).filter(
                and_(
                    func.date(AIUsageSummary.date_bucket) == today,
                    AIUsageSummary.period_type == 'daily',
                    AIUsageSummary.operation_type == 'all'
                )
            ).first()

            if not summary:
                summary = AIUsageSummary(
                    id=str(uuid.uuid4()),
                    date_bucket=datetime.combine(today, datetime.min.time()),
                    period_type='daily',
                    operation_type='all',
                    model_name='all'
                )
                self.db.add(summary)

            # Update totals
            summary.total_requests += 1
            if usage_log.success:
                summary.successful_requests += 1
            else:
                summary.failed_requests += 1
            
            summary.total_prompt_tokens += usage_log.prompt_tokens
            summary.total_completion_tokens += usage_log.completion_tokens
            summary.total_tokens += usage_log.total_tokens
            summary.total_prompt_cost += usage_log.prompt_cost
            summary.total_completion_cost += usage_log.completion_cost
            summary.total_cost += usage_log.total_cost
            
            if usage_log.bill_id:
                # Check if this is a new bill for today
                existing_bill_today = self.db.query(AIUsageLog).filter(
                    and_(
                        AIUsageLog.bill_id == usage_log.bill_id,
                        func.date(AIUsageLog.created_at) == today,
                        AIUsageLog.id != usage_log.id
                    )
                ).first()
                
                if not existing_bill_today:
                    summary.bills_processed += 1

            # Update averages
            if summary.total_requests > 0:
                summary.avg_tokens_per_request = summary.total_tokens / summary.total_requests
                summary.avg_cost_per_request = summary.total_cost / summary.total_requests
            
            if summary.bills_processed > 0:
                summary.avg_cost_per_bill = summary.total_cost / summary.bills_processed

            summary.updated_at = datetime.utcnow()
            self.db.commit()

        except Exception as e:
            logger.error(f"Failed to update daily summary: {e}")
            self.db.rollback()
