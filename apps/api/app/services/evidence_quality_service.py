"""
Evidence Quality Service - Enhanced Evidence Processing for Phase 2
Ensures evidence spans meet HR5-118 quality standards for grounding analysis
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class EvidenceQuality(Enum):
    """Evidence quality levels"""
    EXCELLENT = "excellent"      # Perfect for grounding analysis
    GOOD = "good"               # Suitable for grounding
    ADEQUATE = "adequate"       # Minimally acceptable
    POOR = "poor"              # Should not be used for grounding
    UNUSABLE = "unusable"      # Cannot be used

@dataclass
class EvidenceMetrics:
    """Metrics for evidence quality assessment"""
    content_density_score: float       # How much substantive content
    specificity_score: float          # How specific vs generic
    grounding_value_score: float       # How useful for grounding claims
    legal_significance_score: float    # Legal/regulatory importance
    overall_quality_score: float       # Weighted overall score
    quality_level: EvidenceQuality
    issues: List[str]
    recommendations: List[str]

class EvidenceQualityService:
    """
    Validates and enhances evidence spans for optimal analysis grounding
    Implements HR5-118 evidence standards
    """
    
    def __init__(self):
        # Quality thresholds
        self.min_quote_length = 50         # Minimum meaningful quote length
        self.max_quote_length = 500        # Maximum for focus
        self.min_specificity_score = 0.6   # Minimum specificity
        self.min_grounding_score = 0.7     # Minimum grounding value
        
        # High-value content patterns
        self.high_value_patterns = [
            r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion))?',  # Money amounts
            r'(?:not later than|within \d+|deadline)',           # Specific deadlines  
            r'(?:shall|must|required|mandate)\s+\w+',           # Legal obligations
            r'(?:penalty|fine)\s+[^.]*\$[\d,]+',                # Specific penalties
            r'(?:appropriate|authorize)\s+[^.]*\$[\d,]+',       # Funding authorizations
            r'\d+\s*(?:days?|months?|years?)\s*(?:after|before)', # Time periods
            r'(?:section|subsection|paragraph)\s+\d+',           # Legal references
        ]
        
        # Low-value/generic patterns to avoid
        self.low_value_patterns = [
            r'^(?:the|this|such|any|all)\s+\w+\s+(?:may|shall|will)',  # Generic starts
            r'comprehensive\s+(?:review|analysis|approach)',           # Generic language
            r'appropriate\s+(?:measures|actions|steps)',               # Vague language
            r'various\s+(?:stakeholders|parties|entities)',            # Non-specific
            r'(?:improve|enhance|strengthen)\s+\w+\s+(?:capacity|ability)', # Generic improvements
        ]
    
    def validate_evidence_spans(self, evidence_spans: List[Dict]) -> Tuple[List[Dict], Dict[str, Any]]:
        """
        Validate and filter evidence spans for quality
        
        Returns:
            - Filtered list of high-quality evidence spans
            - Quality metrics and recommendations
        """
        logger.info(f"🔍 Validating {len(evidence_spans)} evidence spans for quality")
        
        validated_spans = []
        quality_scores = []
        total_issues = []
        
        for i, span in enumerate(evidence_spans):
            metrics = self._assess_evidence_quality(span, i)
            
            # Only include spans that meet minimum quality
            if metrics.quality_level not in [EvidenceQuality.POOR, EvidenceQuality.UNUSABLE]:
                validated_spans.append(self._enhance_evidence_span(span, metrics))
                quality_scores.append(metrics.overall_quality_score)
            else:
                logger.debug(f"Filtered out low-quality evidence span {i}: {metrics.quality_level.value}")
                total_issues.extend(metrics.issues)
        
        # Calculate overall evidence quality
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        
        quality_summary = {
            'total_spans_input': len(evidence_spans),
            'total_spans_validated': len(validated_spans),
            'average_quality_score': avg_quality,
            'quality_improvement_applied': len(validated_spans) != len(evidence_spans),
            'common_issues': self._summarize_issues(total_issues),
            'recommendations': self._generate_evidence_recommendations(avg_quality, len(validated_spans))
        }
        
        logger.info(f"✅ Evidence validation complete: {len(validated_spans)}/{len(evidence_spans)} spans validated (avg quality: {avg_quality:.2f})")
        
        return validated_spans, quality_summary
    
    def _assess_evidence_quality(self, span: Dict, span_index: int) -> EvidenceMetrics:
        """Assess quality of individual evidence span"""
        
        quote = span.get('quote', '')
        heading = span.get('heading', '')
        
        # Calculate component scores
        content_density = self._calculate_content_density(quote, heading)
        specificity = self._calculate_specificity_score(quote, heading)
        grounding_value = self._calculate_grounding_value(quote, heading)
        legal_significance = self._calculate_legal_significance(quote, heading)
        
        # Weighted overall score
        overall_score = (
            content_density * 0.25 +
            specificity * 0.30 +
            grounding_value * 0.25 +
            legal_significance * 0.20
        )
        
        # Determine quality level
        quality_level = self._determine_evidence_quality_level(overall_score)
        
        # Identify issues
        issues = self._identify_evidence_issues(span, {
            'content_density': content_density,
            'specificity': specificity,
            'grounding_value': grounding_value,
            'legal_significance': legal_significance
        })
        
        # Generate recommendations
        recommendations = self._generate_span_recommendations(issues, quality_level)
        
        return EvidenceMetrics(
            content_density_score=content_density,
            specificity_score=specificity,
            grounding_value_score=grounding_value,
            legal_significance_score=legal_significance,
            overall_quality_score=overall_score,
            quality_level=quality_level,
            issues=issues,
            recommendations=recommendations
        )
    
    def _calculate_content_density(self, quote: str, heading: str) -> float:
        """Calculate content density - how much substantive content vs filler - ENHANCED VERSION"""
        score = 0.0
        
        # Enhanced length check with more generous scoring
        if self.min_quote_length <= len(quote) <= self.max_quote_length:
            score += 0.4  # Increased from 0.3 -> 0.4
        elif len(quote) < self.min_quote_length:
            score += (len(quote) / self.min_quote_length) * 0.4  # Increased multiplier
        else:  # Too long - less penalty
            score += 0.3  # Increased from 0.2 -> 0.3
        
        # Enhanced substantive word ratio with better filtering
        words = quote.lower().split()
        # More comprehensive stop words list but more generous scoring
        stop_words = {'the', 'and', 'for', 'with', 'this', 'that', 'such', 'said', 'may', 'will', 'are', 'is', 'was', 'were', 'be', 'been', 'have', 'has', 'had'}
        substantive_words = [w for w in words if len(w) > 2 and w not in stop_words]  # Changed from len(w) > 3 to len(w) > 2
        if words:
            substantive_ratio = len(substantive_words) / len(words)
            score += substantive_ratio * 0.5  # Increased from 0.4 -> 0.5
        
        # Enhanced high-value content bonus with more patterns
        enhanced_high_value_patterns = self.high_value_patterns + [
            r'(?:amendment|modification|revision)\s+to',        # Legal changes
            r'(?:effective|enforcement)\s+date',                # Implementation details
            r'(?:authority|jurisdiction|power)\s+to',           # Legal authority
            r'(?:report|notification|disclosure)\s+require',    # Reporting requirements
        ]
        high_value_matches = sum(1 for pattern in enhanced_high_value_patterns if re.search(pattern, quote, re.IGNORECASE))
        score += min(high_value_matches * 0.1, 0.3)
        
        return min(score, 1.0)
    
    def _calculate_specificity_score(self, quote: str, heading: str) -> float:
        """Calculate how specific vs generic the content is - ENHANCED VERSION"""
        score = 0.0
        
        # Enhanced specific details bonus with more patterns and higher scoring
        specific_patterns = [
            r'\$[\d,]+',                           # Specific amounts
            r'\d+\s*(?:days?|months?|years?)',     # Specific timeframes
            r'section\s+\d+',                      # Specific sections
            r'(?:january|february|march|april|may|june|july|august|september|october|november|december)', # Specific dates
            r'\d{1,2}\/\d{1,2}\/\d{2,4}',         # Date formats
            r'\b[A-Z][a-z]+\s+[A-Z][a-z]+\b',     # Proper names (likely)
            r'(?:secretary|director|administrator|commissioner)', # Specific officials
            r'(?:within|not later than|by)\s+\d+',  # Specific deadlines
            r'(?:shall|must|required to)\s+[\w\s]{1,50}(?:establish|implement|create)', # Specific requirements
            r'(?:usfws|fish and wildlife service|department)', # Specific agencies
            r'(?:penalty|fine)\s+(?:of|up to|not exceeding)\s+\$[\d,]+', # Specific penalties
        ]
        
        specific_matches = sum(1 for pattern in specific_patterns if re.search(pattern, quote, re.IGNORECASE))
        score += min(specific_matches * 0.25, 0.8)  # Increased from 0.2 -> 0.25, max from 0.6 -> 0.8
        
        # Generic language penalty
        generic_matches = sum(1 for pattern in self.low_value_patterns if re.search(pattern, quote, re.IGNORECASE))
        score -= generic_matches * 0.15
        
        # Heading specificity
        if heading and len(heading) > 10:
            heading_words = heading.lower().split()
            specific_heading_words = [w for w in heading_words if w not in ['section', 'part', 'chapter', 'title']]
            if heading_words:
                heading_specificity = len(specific_heading_words) / len(heading_words)
                score += heading_specificity * 0.2
        
        return max(0, min(score, 1.0))
    
    def _calculate_grounding_value(self, quote: str, heading: str) -> float:
        """Calculate how valuable this evidence is for grounding analysis claims - ENHANCED VERSION"""
        score = 0.0
        
        # Enhanced action/requirement identification with higher base scores
        action_patterns = [
            r'(?:shall|must|required to|directed to)\s+[\w\s]+',
            r'(?:establish|create|implement|develop)\s+[\w\s]+',
            r'(?:prohibit|prevent|restrict)\s+[\w\s]+',
            r'(?:provide|ensure|maintain)\s+[\w\s]+',
            r'(?:authorize|empower|enable)\s+[\w\s]+',        # Additional empowerment language
            r'(?:mandate|compel|obligate)\s+[\w\s]+',         # Strong requirement language
        ]
        
        action_matches = sum(1 for pattern in action_patterns if re.search(pattern, quote, re.IGNORECASE))
        score += min(action_matches * 0.35, 0.7)  # Increased from 0.25 -> 0.35, max from 0.5 -> 0.7
        
        # Enhanced impact identification with broader patterns
        impact_patterns = [
            r'(?:affect|impact|influence)\s+[\w\s]+',
            r'(?:benefit|harm|help|hurt)\s+[\w\s]+',
            r'(?:increase|decrease|reduce|expand)\s+[\w\s]+',
            r'(?:cost|fee|charge|payment)\s+[\w\s]*\$',
            r'(?:penalty|fine|sanction|enforcement)\s+[\w\s]*\$',  # Penalty language
            r'(?:deadline|timeline|schedule|due date)',            # Time-sensitive content
            r'(?:compliance|violation|breach)\s+[\w\s]+',          # Compliance language
        ]
        
        impact_matches = sum(1 for pattern in impact_patterns if re.search(pattern, quote, re.IGNORECASE))
        score += min(impact_matches * 0.25, 0.4)  # Increased max from 0.3 -> 0.4
        
        # Causal language
        causal_patterns = [
            r'(?:because|due to|as a result|therefore|consequently)',
            r'(?:in order to|for the purpose of|intended to)',
            r'(?:if|when|upon|after)\s+[\w\s]+,\s*(?:then|shall|must)',
        ]
        
        causal_matches = sum(1 for pattern in causal_patterns if re.search(pattern, quote, re.IGNORECASE))
        score += min(causal_matches * 0.15, 0.2)
        
        return min(score, 1.0)
    
    def _calculate_legal_significance(self, quote: str, heading: str) -> float:
        """Calculate legal/regulatory significance of the evidence - ENHANCED VERSION"""
        score = 0.0
        
        # Enhanced legal authority indicators with higher scoring
        authority_patterns = [
            r'(?:secretary|director|administrator|commissioner)\s+(?:shall|may|must)',
            r'(?:federal|state|local)\s+(?:government|agency|authority)',
            r'(?:regulation|rule|guideline|standard|requirement)',
            r'(?:compliance|violation|enforcement|penalty)',
            r'(?:united states|federal)\s+(?:code|law|statute)',        # Legal code references
            r'(?:section|subsection|paragraph)\s+\d+',                  # Statutory references
            r'(?:act|bill|legislation)\s+(?:of|from)',                  # Legislative references
            r'(?:usfws|fish and wildlife service)',                     # Agency-specific (for Lacey Act)
        ]
        
        authority_matches = sum(1 for pattern in authority_patterns if re.search(pattern, quote, re.IGNORECASE))
        score += min(authority_matches * 0.3, 0.6)  # Increased from 0.25 -> 0.3, max from 0.5 -> 0.6
        
        # Enhanced legal consequences with broader patterns
        consequence_patterns = [
            r'(?:penalty|fine|sanction|punishment)\s+[^.]*\$[\d,]+',
            r'(?:criminal|civil)\s+(?:penalty|liability|action)',
            r'(?:suspend|revoke|terminate|cancel)\s+[\w\s]+',
            r'(?:court|judicial|legal)\s+(?:action|proceeding|review)',
            r'(?:forfeit|confiscate|seize)\s+[\w\s]+',                   # Asset seizure
            r'(?:imprisonment|jail|incarceration)\s+[\w\s]+',            # Criminal penalties
            r'(?:injunction|restraining order)\s+[\w\s]+',              # Court orders
        ]
        
        consequence_matches = sum(1 for pattern in consequence_patterns if re.search(pattern, quote, re.IGNORECASE))
        score += min(consequence_matches * 0.35, 0.5)  # Increased from 0.3 -> 0.35, max from 0.4 -> 0.5
        
        # Enhanced heading significance with more keywords
        if heading:
            heading_lower = heading.lower()
            significant_headings = [
                'penalty', 'enforcement', 'funding', 'appropriation', 'authorization', 
                'requirement', 'compliance', 'violation', 'prohibition', 'restriction',
                'mandate', 'obligation', 'amendment', 'modification', 'exception'
            ]
            if any(sig in heading_lower for sig in significant_headings):
                score += 0.15  # Increased from 0.1 -> 0.15
        
        return min(score, 1.0)
    
    def _determine_evidence_quality_level(self, overall_score: float) -> EvidenceQuality:
        """Determine evidence quality level from overall score - ENHANCED THRESHOLDS"""
        # More generous thresholds to classify more evidence as high quality
        if overall_score >= 0.75:  # Lowered from 0.85 -> 0.75
            return EvidenceQuality.EXCELLENT
        elif overall_score >= 0.6:  # Lowered from 0.7 -> 0.6
            return EvidenceQuality.GOOD
        elif overall_score >= 0.45:  # Lowered from 0.5 -> 0.45
            return EvidenceQuality.ADEQUATE
        elif overall_score >= 0.25:  # Lowered from 0.3 -> 0.25
            return EvidenceQuality.POOR
        else:
            return EvidenceQuality.UNUSABLE
    
    def _identify_evidence_issues(self, span: Dict, scores: Dict[str, float]) -> List[str]:
        """Identify specific issues with evidence span"""
        issues = []
        quote = span.get('quote', '')
        
        if scores['content_density'] < 0.5:
            issues.append("Low content density - too much filler or inappropriate length")
        
        if scores['specificity'] < 0.6:
            issues.append("Lacks specificity - too generic or vague")
        
        if scores['grounding_value'] < 0.5:
            issues.append("Low grounding value - doesn't support strong claims")
        
        if scores['legal_significance'] < 0.4:
            issues.append("Limited legal significance - weak regulatory content")
        
        if len(quote) < self.min_quote_length:
            issues.append(f"Quote too short ({len(quote)} chars, min {self.min_quote_length})")
        
        if len(quote) > self.max_quote_length:
            issues.append(f"Quote too long ({len(quote)} chars, max {self.max_quote_length})")
        
        # Check for problematic patterns
        for pattern in self.low_value_patterns:
            if re.search(pattern, quote, re.IGNORECASE):
                issues.append("Contains generic/low-value language patterns")
                break
        
        return issues
    
    def _generate_span_recommendations(self, issues: List[str], quality_level: EvidenceQuality) -> List[str]:
        """Generate recommendations for improving evidence span"""
        recommendations = []
        
        if quality_level in [EvidenceQuality.POOR, EvidenceQuality.UNUSABLE]:
            recommendations.append("Consider excluding this span from analysis")
            recommendations.append("Look for more specific evidence in nearby text")
        
        if "content density" in str(issues):
            recommendations.append("Extract more focused, substantive content")
        
        if "specificity" in str(issues):
            recommendations.append("Include specific numbers, dates, or named entities")
        
        if "grounding value" in str(issues):
            recommendations.append("Focus on actionable requirements or clear impacts")
        
        if "legal significance" in str(issues):
            recommendations.append("Prioritize enforcement, penalties, or authority provisions")
        
        return recommendations
    
    def _enhance_evidence_span(self, span: Dict, metrics: EvidenceMetrics) -> Dict:
        """Enhance evidence span with quality metrics"""
        enhanced_span = span.copy()
        enhanced_span['quality_metrics'] = {
            'overall_score': metrics.overall_quality_score,
            'quality_level': metrics.quality_level.value,
            'content_density': metrics.content_density_score,
            'specificity': metrics.specificity_score,
            'grounding_value': metrics.grounding_value_score,
            'legal_significance': metrics.legal_significance_score,
            'issues': metrics.issues,
            'recommendations': metrics.recommendations
        }
        return enhanced_span
    
    def _summarize_issues(self, all_issues: List[str]) -> List[str]:
        """Summarize common issues across all evidence spans"""
        issue_counts = {}
        for issue in all_issues:
            key_words = issue.split()[:3]  # First 3 words as key
            key = ' '.join(key_words)
            issue_counts[key] = issue_counts.get(key, 0) + 1
        
        # Return most common issues
        sorted_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)
        return [issue for issue, count in sorted_issues[:5]]
    
    def _generate_evidence_recommendations(self, avg_quality: float, span_count: int) -> List[str]:
        """Generate recommendations for overall evidence improvement"""
        recommendations = []
        
        if avg_quality < 0.6:
            recommendations.append("Evidence quality below standards - consider re-extraction with more specific criteria")
        
        if span_count < 5:
            recommendations.append("Insufficient evidence spans - extract more supporting evidence")
        
        if span_count > 15:
            recommendations.append("Too many evidence spans - focus on highest quality spans")
        
        recommendations.append("Prioritize evidence with specific amounts, deadlines, and enforcement mechanisms")
        recommendations.append("Ensure evidence directly supports analysis claims")
        
        return recommendations

# Global instance
evidence_quality_service = EvidenceQualityService()

def get_evidence_quality_service() -> EvidenceQualityService:
    """Get the global evidence quality service instance"""
    return evidence_quality_service