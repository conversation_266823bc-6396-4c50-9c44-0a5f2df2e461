# Environment Setup Guide

## Auth0 Configuration

### 1. Development Environment
- **Domain**: `dev-modernaction.us.auth0.com`
- **Web Application**: 
  - Name: `ModernAction Development Web`
  - Allowed Callback URLs: `http://localhost:3000/api/auth/callback, http://localhost:3001/api/auth/callback`
  - Allowed Logout URLs: `http://localhost:3000, http://localhost:3001`
  - Web Origins: `http://localhost:3000, http://localhost:3001`
- **API Application**:
  - Name: `ModernAction Development API`
  - Identifier: `https://api-dev.modernaction.io`
  
### 2. Staging Environment
- **Domain**: `staging-modernaction.us.auth0.com` (create new)
- **Web Application**:
  - Name: `ModernAction Staging Web`
  - Allowed Callback URLs: `https://staging.modernaction.io/api/auth/callback`
  - Allowed Logout URLs: `https://staging.modernaction.io`
  - Web Origins: `https://staging.modernaction.io`
- **API Application**:
  - Name: `ModernAction Staging API`
  - Identifier: `https://api-staging.modernaction.io`

### 3. Production Environment
- **Domain**: `prod-modernaction.us.auth0.com` (create new)
- **Web Application**:
  - Name: `ModernAction Production Web`
  - Allowed Callback URLs: `https://modernaction.io/api/auth/callback`
  - Allowed Logout URLs: `https://modernaction.io`
  - Web Origins: `https://modernaction.io`
- **API Application**:
  - Name: `ModernAction Production API`
  - Identifier: `https://api.modernaction.io`

## Database Configuration

### Development
- **Local PostgreSQL** or **Staging DB** (current setup)
- Connection: Uses staging DB for development convenience

### Staging
- **AWS RDS Instance**: `modernaction-staging-database`
- Current: `modernaction-staging-modernactiondatabasefdd241de-giohrnseld4l.csdoaiogadw5.us-east-1.rds.amazonaws.com`

### Production
- **AWS RDS Instance**: `modernaction-prod-database` (to be created)
- Separate from staging for data isolation

## Environment Variables Structure

Each environment will have its own configuration files with appropriate Auth0 and database credentials.