#!/usr/bin/env python3
"""
Test script for <PERSON>ails processing
Run with: poetry run python test_bill_details_processing.py
"""

import asyncio
from app.db.database import get_db
from app.models.bill import Bill
from app.models.bill_details import BillDetails
from app.services.bill_details_service import BillDetailsService
from app.services.text_citation_service import TextCitationService
from app.schemas.bill_details import BillDetailsResponse

def test_bill_details_creation():
    """Test creating bill details for an existing bill"""
    db = next(get_db())
    
    # Find an existing bill
    bill = db.query(Bill).first()
    if not bill:
        print("❌ No bills found in database. Please run bill ingestion first.")
        return
    
    print(f"📋 Found bill: {bill.bill_number} - {bill.title}")
    
    # Check if it has full text
    if not bill.full_text:
        print("❌ Bill has no full text. Need to fetch full text first.")
        return
    
    print(f"📄 Bill has full text ({len(bill.full_text)} characters)")
    
    # Test Citation Service
    print("\n🔍 Testing Citation Service...")
    citation_service = TextCitationService()
    source_index = citation_service.build_source_index(bill.full_text)
    print(f"✅ Built source index with {len(source_index)} sections")
    
    # Test Bill Details Service
    print("\n🤖 Testing Bill Details Service...")
    bill_details_service = BillDetailsService(db)
    
    # Create mock AI analysis data
    mock_analysis = {
        "overview": {
            "what_does": {
                "content": f"This bill, {bill.title}, introduces important legislative changes.",
                "citations": []
            },
            "who_affects": {
                "content": "This legislation affects various stakeholders and communities.",
                "citations": []
            }
        },
        "positions": {
            "support_reasons": [
                {
                    "claim": "Provides important benefits",
                    "justification": "This bill includes provisions that help constituents",
                    "citations": []
                }
            ],
            "oppose_reasons": [],
            "amend_reasons": []
        }
    }
    
    try:
        # Create bill details
        result = bill_details_service.create_or_update_details(
            bill=bill,
            full_text=bill.full_text,
            details_payload=mock_analysis
        )
        
        print(f"✅ Created bill details: {result.seo_slug}")
        print(f"   - Title: {result.seo_title}")
        print(f"   - Needs Review: {result.needs_human_review}")
        print(f"   - Coverage: {result.metrics.get('coverage_ratio', 0):.0%}")
        
        # Test the API endpoint
        print(f"\n🌐 Test the bill details page:")
        print(f"   Frontend: http://localhost:3000/bills/{result.seo_slug}")
        print(f"   API: http://localhost:8001/api/v1/bills/details/by-slug/{result.seo_slug}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error creating bill details: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_existing_bill_details():
    """Check what bill details already exist"""
    db = next(get_db())
    
    details = db.query(BillDetails).all()
    print(f"📊 Found {len(details)} bill details records:")
    
    for detail in details:
        print(f"   - {detail.seo_slug}: {detail.seo_title}")
        print(f"     Review needed: {detail.needs_human_review}")
        print(f"     Created: {detail.created_at}")
    
    return details

if __name__ == "__main__":
    print("🧪 Bill Details Processing Test")
    print("=" * 50)
    
    print("\n1. Checking existing bill details...")
    existing_details = check_existing_bill_details()
    
    print("\n2. Testing bill details creation...")
    new_details = test_bill_details_creation()
    
    print("\n✅ Test complete!")
    if new_details:
        print(f"\n🎉 Success! Bill details created for: {new_details.seo_slug}")
        print("\nNext steps:")
        print("1. Open admin UI: http://localhost:8001/api/v1/admin/")
        print(f"2. View bill details: http://localhost:3000/bills/{new_details.seo_slug}")
        print("3. Test the moderation banner and citation display")