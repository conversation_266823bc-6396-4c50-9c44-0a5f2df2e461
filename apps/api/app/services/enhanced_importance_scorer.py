"""
Enhanced Bill Importance Scorer - Phase 3 Evidence-Driven Scoring
Integrates evidence-first processing to provide more accurate importance scoring
"""

import re
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

from app.services.bill_importance_scorer import BillImportanceScorer, ImportanceScore, ImportanceLevel
from app.services.evidence_first_processor import EvidenceSpan, EvidenceType, get_evidence_first_processor

logger = logging.getLogger(__name__)

@dataclass
class EvidenceEnhancedScore(ImportanceScore):
    """Enhanced importance score with evidence metrics"""
    evidence_quality_score: float = 0.0  # 0-1, quality of evidence found
    evidence_diversity_score: float = 0.0  # 0-1, diversity of evidence types
    evidence_specificity_score: float = 0.0  # 0-1, how specific the evidence is
    evidence_impact_indicators: List[str] = None  # Evidence-based impact indicators
    critical_evidence_count: int = 0  # Number of critical evidence spans
    funding_evidence_strength: float = 0.0  # 0-1, strength of funding evidence
    enforcement_evidence_strength: float = 0.0  # 0-1, strength of enforcement evidence
    
    def __post_init__(self):
        if self.evidence_impact_indicators is None:
            self.evidence_impact_indicators = []

class EnhancedBillImportanceScorer:
    """
    Phase 3 Enhanced Bill Importance Scorer that leverages intelligent evidence
    to provide more accurate and nuanced importance scoring
    """
    
    def __init__(self):
        # Initialize base scorer
        self.base_scorer = BillImportanceScorer()
        
        # Evidence-based scoring weights
        self.evidence_type_weights = {
            EvidenceType.FUNDING: 0.25,        # Funding evidence is critical
            EvidenceType.ENFORCEMENT: 0.20,     # Legal consequences matter
            EvidenceType.MANDATE: 0.15,         # Requirements affect people
            EvidenceType.IMPACT: 0.15,          # Direct impacts are important
            EvidenceType.TIMELINE: 0.10,        # Urgency from deadlines
            EvidenceType.AUTHORITY: 0.05,       # Who has power matters less
            EvidenceType.SCOPE: 0.10,           # Who's affected is important
            EvidenceType.PROCESS: 0.0,          # Process details are technical
            EvidenceType.REPORTING: 0.0,        # Reporting is administrative
            EvidenceType.DEFINITION: 0.0        # Definitions are supporting
        }
        
        # Evidence quality thresholds for importance boosts
        self.quality_thresholds = {
            'high_quality': 0.8,      # 80%+ quality evidence
            'moderate_quality': 0.6,   # 60%+ quality evidence
            'diverse_evidence': 0.7,   # 70%+ evidence diversity
            'specific_evidence': 0.75  # 75%+ evidence specificity
        }
        
        # Critical evidence indicators
        self.critical_evidence_patterns = {
            'massive_funding': [
                r'\$[1-9]\d*\s*trillion',           # Trillion dollar amounts
                r'\$[5-9]\d{2}\s*billion',          # 500+ billion
                r'\$[1-9]\d+\s*billion'             # Multi-billion
            ],
            'broad_enforcement': [
                r'criminal\s+penalty',              # Criminal penalties
                r'imprisonment\s+(?:for\s+)?(?:not\s+more\s+than\s+)?[2-9]\d*\s*years',  # 2+ years prison
                r'fine[^.]*\$[1-9]\d{5,}',         # $100K+ fines
                r'felony',                          # Felony classification
            ],
            'urgent_timeline': [
                r'(?:immediately|within\s+\d{1,2}\s+days)',  # Very short deadlines
                r'emergency',                                 # Emergency designation
                r'not\s+later\s+than\s+\d{1,2}\s+days'       # Very tight deadlines
            ],
            'national_scope': [
                r'all\s+(?:states|americans|citizens)',     # National application
                r'nationwide',                              # Nationwide scope
                r'every\s+(?:state|citizen|person)',        # Universal coverage
                r'federal\s+(?:mandate|requirement)'        # Federal mandates
            ]
        }
    
    async def score_bill_with_evidence(self, bill_text: str, bill_metadata: Dict[str, Any],
                                     evidence_spans: Optional[List[EvidenceSpan]] = None) -> EvidenceEnhancedScore:
        """
        Score bill importance using both traditional methods and evidence analysis
        """
        
        logger.info(f"🧮 Starting enhanced importance scoring for {bill_metadata.get('title', 'Unknown')}")
        
        # Get traditional importance score first
        base_score = self.base_scorer.score_bill(
            title=bill_metadata.get('title', ''),
            summary=bill_metadata.get('summary', ''),
            bill_number=bill_metadata.get('bill_number', '')
        )
        
        # If evidence spans not provided, extract them
        if evidence_spans is None:
            logger.info("🔍 No evidence provided, extracting evidence for scoring...")
            # Extract basic evidence spans from bill text for scoring
            raw_evidence = await self._extract_basic_evidence(bill_text, bill_metadata)
            
            # Process with evidence-first processor
            processor = get_evidence_first_processor()
            evidence_spans, _ = await processor.process_evidence_first(
                bill_text, bill_metadata, raw_evidence
            )
        
        # Calculate evidence-based metrics
        evidence_metrics = await self._calculate_evidence_metrics(evidence_spans)
        
        # Calculate evidence-enhanced score
        enhanced_score = self._calculate_enhanced_score(base_score, evidence_metrics, evidence_spans)
        
        # Determine final importance level with evidence consideration
        final_level, auto_process, enhanced_reason = self._determine_enhanced_level(
            enhanced_score, evidence_metrics, base_score
        )
        
        logger.info(f"📊 Enhanced scoring complete: {enhanced_score}/100 (base: {base_score.score}) - {final_level.value}")
        
        return EvidenceEnhancedScore(
            score=enhanced_score,
            level=final_level,
            reason=enhanced_reason,
            auto_process=auto_process,
            key_indicators=base_score.key_indicators + evidence_metrics['impact_indicators'],
            budget_impact=base_score.budget_impact,
            affected_population=base_score.affected_population,
            evidence_quality_score=evidence_metrics['quality_score'],
            evidence_diversity_score=evidence_metrics['diversity_score'],
            evidence_specificity_score=evidence_metrics['specificity_score'],
            evidence_impact_indicators=evidence_metrics['impact_indicators'],
            critical_evidence_count=evidence_metrics['critical_count'],
            funding_evidence_strength=evidence_metrics['funding_strength'],
            enforcement_evidence_strength=evidence_metrics['enforcement_strength']
        )
    
    async def _extract_basic_evidence(self, bill_text: str, bill_metadata: Dict[str, Any]) -> List[Dict]:
        """Extract basic evidence spans for importance scoring"""
        
        # Simple evidence extraction for scoring purposes
        evidence_patterns = [
            # Funding evidence
            (r'(?:appropriated|authorized|allocated)[^.]*\$[\d,]+[^.]*\.', 'funding'),
            # Enforcement evidence  
            (r'(?:penalty|fine|imprisoned)[^.]*\.', 'enforcement'),
            # Timeline evidence
            (r'(?:not later than|within|deadline)[^.]*\.', 'timeline'),
            # Authority evidence
            (r'(?:secretary|administrator|director)[^.]*shall[^.]*\.', 'authority'),
            # Mandate evidence
            (r'(?:shall|must|required)[^.]*\.', 'mandate')
        ]
        
        raw_evidence = []
        evidence_id = 0
        
        for pattern, evidence_type in evidence_patterns:
            matches = re.finditer(pattern, bill_text, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                raw_evidence.append({
                    'id': f'basic_ev_{evidence_id}',
                    'quote': match.group(0),
                    'heading': f'{evidence_type.title()} Evidence',
                    'start_offset': match.start(),
                    'end_offset': match.end()
                })
                evidence_id += 1
        
        return raw_evidence[:10]  # Limit for performance
    
    async def _calculate_evidence_metrics(self, evidence_spans: List[EvidenceSpan]) -> Dict[str, Any]:
        """Calculate comprehensive evidence-based metrics"""
        
        if not evidence_spans:
            return {
                'quality_score': 0.0,
                'diversity_score': 0.0,
                'specificity_score': 0.0,
                'impact_indicators': [],
                'critical_count': 0,
                'funding_strength': 0.0,
                'enforcement_strength': 0.0
            }
        
        # 1. Evidence Quality Score (average of all span quality metrics)
        quality_scores = []
        for span in evidence_spans:
            # Composite quality from span metrics
            span_quality = (
                span.priority_score * 0.3 +
                span.specificity_score * 0.25 +
                span.legal_weight * 0.2 +
                span.actionability_score * 0.15 +
                span.context_relevance * 0.1
            )
            quality_scores.append(span_quality)
        
        avg_quality = sum(quality_scores) / len(quality_scores)
        
        # 2. Evidence Diversity Score (how many different types)
        evidence_types_present = set(span.evidence_type for span in evidence_spans)
        diversity_score = len(evidence_types_present) / len(EvidenceType)
        
        # 3. Evidence Specificity Score (average specificity)
        avg_specificity = sum(span.specificity_score for span in evidence_spans) / len(evidence_spans)
        
        # 4. Critical Evidence Detection
        critical_count = 0
        impact_indicators = []
        
        for span in evidence_spans:
            # Check for critical evidence patterns
            is_critical = await self._is_critical_evidence(span)
            if is_critical:
                critical_count += 1
                impact_indicators.extend(is_critical)
        
        # 5. Type-Specific Strength Scores
        funding_strength = self._calculate_type_strength(evidence_spans, EvidenceType.FUNDING)
        enforcement_strength = self._calculate_type_strength(evidence_spans, EvidenceType.ENFORCEMENT)
        
        return {
            'quality_score': avg_quality,
            'diversity_score': diversity_score,
            'specificity_score': avg_specificity,
            'impact_indicators': impact_indicators[:10],  # Top 10
            'critical_count': critical_count,
            'funding_strength': funding_strength,
            'enforcement_strength': enforcement_strength
        }
    
    async def _is_critical_evidence(self, span: EvidenceSpan) -> List[str]:
        """Check if evidence span indicates critical importance"""
        
        critical_indicators = []
        content = span.content.lower()
        
        # Check each critical evidence category
        for category, patterns in self.critical_evidence_patterns.items():
            for pattern in patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    critical_indicators.append(f"Critical {category.replace('_', ' ')}")
        
        # Additional critical checks based on evidence type and values
        if span.evidence_type == EvidenceType.FUNDING:
            extracted_amounts = span.extracted_values.get('amounts', [])
            for amount in extracted_amounts:
                if 'trillion' in amount.lower() or 'billion' in amount.lower():
                    critical_indicators.append("Major funding commitment")
        
        elif span.evidence_type == EvidenceType.ENFORCEMENT:
            if span.legal_weight > 0.8 and span.specificity_score > 0.7:
                critical_indicators.append("Severe enforcement provisions")
        
        elif span.evidence_type == EvidenceType.TIMELINE:
            deadlines = span.extracted_values.get('deadlines', [])
            for deadline in deadlines:
                if re.search(r'(?:\d{1,2}|few)\s*days?', deadline.lower()):
                    critical_indicators.append("Urgent implementation timeline")
        
        return critical_indicators
    
    def _calculate_type_strength(self, evidence_spans: List[EvidenceSpan], 
                               evidence_type: EvidenceType) -> float:
        """Calculate strength score for specific evidence type"""
        
        type_spans = [span for span in evidence_spans if span.evidence_type == evidence_type]
        
        if not type_spans:
            return 0.0
        
        # Weighted average of span qualities for this type
        strength_scores = []
        for span in type_spans:
            type_weight = self.evidence_type_weights.get(evidence_type, 0.1)
            span_strength = (
                span.priority_score * 0.4 +
                span.specificity_score * 0.3 +
                span.legal_weight * 0.2 +
                span.context_relevance * 0.1
            ) * type_weight
            strength_scores.append(span_strength)
        
        return sum(strength_scores) / len(strength_scores)
    
    def _calculate_enhanced_score(self, base_score: ImportanceScore, 
                                evidence_metrics: Dict[str, Any],
                                evidence_spans: List[EvidenceSpan]) -> int:
        """Calculate evidence-enhanced importance score"""
        
        enhanced_score = base_score.score
        
        # 1. Quality bonus (up to +15 points)
        quality_bonus = evidence_metrics['quality_score'] * 15
        enhanced_score += quality_bonus
        
        # 2. Diversity bonus (up to +10 points)
        diversity_bonus = evidence_metrics['diversity_score'] * 10
        enhanced_score += diversity_bonus
        
        # 3. Specificity bonus (up to +8 points)
        specificity_bonus = evidence_metrics['specificity_score'] * 8
        enhanced_score += specificity_bonus
        
        # 4. Critical evidence bonus (up to +20 points)
        critical_bonus = min(20, evidence_metrics['critical_count'] * 5)
        enhanced_score += critical_bonus
        
        # 5. Strong funding evidence bonus (up to +12 points)
        funding_bonus = evidence_metrics['funding_strength'] * 12
        enhanced_score += funding_bonus
        
        # 6. Strong enforcement evidence bonus (up to +10 points)
        enforcement_bonus = evidence_metrics['enforcement_strength'] * 10
        enhanced_score += enforcement_bonus
        
        # 7. Evidence volume consideration
        if len(evidence_spans) >= 8:  # Rich evidence
            enhanced_score += 5
        elif len(evidence_spans) >= 5:  # Moderate evidence
            enhanced_score += 2
        elif len(evidence_spans) <= 2:  # Sparse evidence
            enhanced_score -= 5
        
        # Normalize to 0-100
        enhanced_score = max(0, min(100, int(enhanced_score)))
        
        return enhanced_score
    
    def _determine_enhanced_level(self, enhanced_score: int, evidence_metrics: Dict[str, Any],
                                base_score: ImportanceScore) -> Tuple[ImportanceLevel, bool, str]:
        """Determine enhanced importance level with evidence consideration"""
        
        # Evidence quality can promote/demote importance levels
        quality_score = evidence_metrics['quality_score']
        critical_count = evidence_metrics['critical_count']
        
        # Base determination
        if enhanced_score >= 90:
            level = ImportanceLevel.CRITICAL
            auto_process = True
            reason = f"Critical legislation with exceptional evidence quality ({quality_score:.2f})"
        elif enhanced_score >= 75:
            level = ImportanceLevel.HIGH
            auto_process = True
            reason = f"High-impact legislation supported by strong evidence ({critical_count} critical indicators)"
        elif enhanced_score >= 50:
            level = ImportanceLevel.MEDIUM
            auto_process = True
            reason = f"Moderate impact with solid evidence foundation ({len(evidence_metrics['impact_indicators'])} impact indicators)"
        elif enhanced_score >= 25:
            level = ImportanceLevel.LOW
            auto_process = False
            reason = f"Lower priority but evidence suggests potential importance (score improved from {base_score.score} to {enhanced_score})"
        else:
            level = ImportanceLevel.MINIMAL
            auto_process = False
            reason = f"Minimal impact despite evidence analysis (limited evidence: {quality_score:.2f} quality)"
        
        # Evidence-based adjustments
        if critical_count >= 3 and level != ImportanceLevel.CRITICAL:
            # Promote to next level if we have lots of critical evidence
            level_values = [ImportanceLevel.MINIMAL, ImportanceLevel.LOW, ImportanceLevel.MEDIUM, ImportanceLevel.HIGH, ImportanceLevel.CRITICAL]
            current_index = level_values.index(level)
            if current_index < len(level_values) - 1:
                level = level_values[current_index + 1]
                auto_process = True
                reason += f" (promoted due to {critical_count} critical evidence indicators)"
        
        if quality_score < 0.3 and level in [ImportanceLevel.HIGH, ImportanceLevel.CRITICAL]:
            # Demote if evidence quality is very poor
            level_values = [ImportanceLevel.MINIMAL, ImportanceLevel.LOW, ImportanceLevel.MEDIUM, ImportanceLevel.HIGH, ImportanceLevel.CRITICAL]
            current_index = level_values.index(level)
            if current_index > 0:
                level = level_values[current_index - 1]
                reason += f" (demoted due to low evidence quality: {quality_score:.2f})"
        
        return level, auto_process, reason

# Global instance
enhanced_importance_scorer = EnhancedBillImportanceScorer()

def get_enhanced_importance_scorer() -> EnhancedBillImportanceScorer:
    """Get the global enhanced importance scorer instance"""
    return enhanced_importance_scorer

# Convenience function
async def score_bill_with_evidence(bill_text: str, bill_metadata: Dict[str, Any],
                                 evidence_spans: Optional[List[EvidenceSpan]] = None) -> EvidenceEnhancedScore:
    """Convenience function to score a bill with evidence enhancement"""
    scorer = get_enhanced_importance_scorer()
    return await scorer.score_bill_with_evidence(bill_text, bill_metadata, evidence_spans)