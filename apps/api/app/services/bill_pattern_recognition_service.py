"""
Bill Pattern Recognition Service - Phase 4 Intelligent Categorization
Automatically identifies bill patterns and categories to optimize processing strategies
"""

import logging
import re
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from collections import defaultdict, Counter
import asyncio

from sqlalchemy.orm import Session
from sqlalchemy import and_, func

logger = logging.getLogger(__name__)

class BillCategory(Enum):
    """Primary bill categories for pattern recognition"""
    HEALTHCARE = "healthcare"
    DEFENSE = "defense"
    EDUCATION = "education"
    ENVIRONMENT = "environment"
    ECONOMY = "economy"
    TECHNOLOGY = "technology"
    TRANSPORTATION = "transportation"
    JUDICIARY = "judiciary"
    AGRICULTURE = "agriculture"
    ENERGY = "energy"
    SOCIAL_SERVICES = "social_services"
    GOVERNMENT_OPERATIONS = "government_operations"
    FOREIGN_RELATIONS = "foreign_relations"
    CEREMONIAL = "ceremonial"
    OTHER = "other"

class BillComplexityTier(Enum):
    """Bill complexity tiers affecting processing strategy"""
    SIMPLE = "simple"           # Single-focus, straightforward bills
    MODERATE = "moderate"       # Multi-section bills with clear structure
    COMPLEX = "complex"         # Comprehensive bills with many provisions
    OMNIBUS = "omnibus"         # Large bills covering multiple policy areas
    MEGA = "mega"              # Massive appropriations or comprehensive reforms

class ProcessingStrategy(Enum):
    """Processing strategies based on patterns"""
    PREMIUM = "premium"         # Full quality, high budget ($0.25-0.30)
    ENHANCED = "enhanced"       # Good quality, medium budget ($0.15-0.20)
    STANDARD = "standard"       # Basic quality, low budget ($0.05-0.10)
    TEMPLATE = "template"       # Template-based, minimal budget ($0.01)

@dataclass
class BillPattern:
    """Identified pattern in bill characteristics"""
    pattern_id: str
    category: BillCategory
    complexity: BillComplexityTier
    funding_tier: str           # none, small, medium, large, massive
    enforcement_level: str      # none, civil, criminal, severe
    scope: str                 # local, state, national, international
    urgency: str               # routine, priority, urgent, emergency
    political_sensitivity: str  # low, medium, high, critical
    processing_history: Dict[str, Any] = field(default_factory=dict)
    success_metrics: Dict[str, float] = field(default_factory=dict)
    recommended_strategy: ProcessingStrategy = ProcessingStrategy.STANDARD

@dataclass
class PatternAnalysis:
    """Complete pattern analysis for a bill"""
    bill_id: str
    identified_patterns: List[BillPattern]
    confidence_score: float     # 0-1, confidence in pattern identification
    recommended_budget: float   # Recommended processing budget
    recommended_strategy: ProcessingStrategy
    quality_prediction: float  # Predicted quality score (0-1)
    processing_time_estimate: int  # Estimated processing time in seconds
    risk_factors: List[str]    # Potential processing risks
    optimization_suggestions: List[str]  # Specific optimization recommendations

class BillPatternRecognitionService:
    """
    Phase 4 Bill Pattern Recognition System that automatically identifies
    bill patterns and categories to optimize processing strategies
    """
    
    def __init__(self, db: Session):
        self.db = db
        
        # Pattern recognition models
        self.category_keywords = self._initialize_category_keywords()
        self.complexity_indicators = self._initialize_complexity_indicators()
        self.funding_patterns = self._initialize_funding_patterns()
        self.enforcement_patterns = self._initialize_enforcement_patterns()
        self.scope_patterns = self._initialize_scope_patterns()
        self.urgency_patterns = self._initialize_urgency_patterns()
        
        # Processing strategy rules
        self.strategy_rules = self._initialize_strategy_rules()
        
        # Pattern database for learning
        self.recognized_patterns = {}
        self.pattern_performance_history = defaultdict(list)
        
        # Budget allocation by tier
        self.budget_tiers = {
            ProcessingStrategy.PREMIUM: (0.25, 0.30),
            ProcessingStrategy.ENHANCED: (0.15, 0.20),
            ProcessingStrategy.STANDARD: (0.05, 0.10),
            ProcessingStrategy.TEMPLATE: (0.01, 0.02)
        }
    
    async def analyze_bill_patterns(self, bill_metadata: Dict[str, Any],
                                  bill_text: str) -> PatternAnalysis:
        """
        Perform comprehensive pattern analysis on a bill
        """
        
        logger.info(f"🔍 Starting pattern recognition for {bill_metadata.get('bill_number', 'Unknown')}")
        
        # Step 1: Identify primary category
        primary_category = await self._identify_primary_category(bill_metadata, bill_text)
        
        # Step 2: Determine complexity tier
        complexity_tier = await self._determine_complexity_tier(bill_metadata, bill_text)
        
        # Step 3: Analyze funding characteristics
        funding_tier = await self._analyze_funding_tier(bill_text)
        
        # Step 4: Assess enforcement level
        enforcement_level = await self._assess_enforcement_level(bill_text)
        
        # Step 5: Determine scope and urgency
        scope = await self._determine_scope(bill_metadata, bill_text)
        urgency = await self._assess_urgency(bill_metadata, bill_text)
        
        # Step 6: Evaluate political sensitivity
        political_sensitivity = await self._evaluate_political_sensitivity(bill_metadata, bill_text)
        
        # Step 7: Create bill pattern
        bill_pattern = BillPattern(
            pattern_id=f"{primary_category.value}_{complexity_tier.value}_{funding_tier}_{enforcement_level}",
            category=primary_category,
            complexity=complexity_tier,
            funding_tier=funding_tier,
            enforcement_level=enforcement_level,
            scope=scope,
            urgency=urgency,
            political_sensitivity=political_sensitivity
        )
        
        # Step 8: Recommend processing strategy
        recommended_strategy = await self._recommend_processing_strategy(bill_pattern)
        bill_pattern.recommended_strategy = recommended_strategy
        
        # Step 9: Calculate predictions and recommendations
        confidence_score = self._calculate_confidence_score(bill_pattern, bill_metadata)
        recommended_budget = self._calculate_recommended_budget(bill_pattern)
        quality_prediction = await self._predict_quality_score(bill_pattern)
        time_estimate = self._estimate_processing_time(bill_pattern)
        risk_factors = await self._identify_risk_factors(bill_pattern, bill_metadata)
        optimizations = await self._generate_optimization_suggestions(bill_pattern)
        
        pattern_analysis = PatternAnalysis(
            bill_id=bill_metadata.get('bill_id', 'unknown'),
            identified_patterns=[bill_pattern],
            confidence_score=confidence_score,
            recommended_budget=recommended_budget,
            recommended_strategy=recommended_strategy,
            quality_prediction=quality_prediction,
            processing_time_estimate=time_estimate,
            risk_factors=risk_factors,
            optimization_suggestions=optimizations
        )
        
        # Step 10: Learn from pattern for future improvements
        await self._record_pattern_for_learning(bill_pattern, bill_metadata)
        
        logger.info(f"✅ Pattern analysis complete: {primary_category.value} | {complexity_tier.value} | {recommended_strategy.value}")
        
        return pattern_analysis
    
    def _initialize_category_keywords(self) -> Dict[BillCategory, List[str]]:
        """Initialize keyword patterns for category identification"""
        
        return {
            BillCategory.HEALTHCARE: [
                'health', 'medical', 'medicare', 'medicaid', 'hospital', 'patient', 'disease',
                'drug', 'pharmaceutical', 'treatment', 'doctor', 'nurse', 'healthcare',
                'mental health', 'public health', 'insurance', 'coverage', 'therapy'
            ],
            
            BillCategory.DEFENSE: [
                'defense', 'military', 'armed forces', 'army', 'navy', 'air force',
                'veterans', 'security', 'weapon', 'missile', 'combat', 'deployment',
                'pentagon', 'soldier', 'marine', 'sailor', 'airman', 'national defense'
            ],
            
            BillCategory.EDUCATION: [
                'education', 'school', 'student', 'teacher', 'university', 'college',
                'learning', 'curriculum', 'graduation', 'scholarship', 'loan',
                'academic', 'classroom', 'principal', 'superintendent', 'textbook'
            ],
            
            BillCategory.ENVIRONMENT: [
                'environment', 'climate', 'pollution', 'conservation', 'wildlife',
                'forest', 'water', 'air quality', 'carbon', 'emission', 'renewable',
                'solar', 'wind', 'clean energy', 'ecosystem', 'endangered species'
            ],
            
            BillCategory.ECONOMY: [
                'economic', 'economy', 'business', 'commerce', 'trade', 'market',
                'financial', 'banking', 'credit', 'loan', 'investment', 'tax',
                'employment', 'job', 'unemployment', 'wage', 'salary', 'inflation'
            ],
            
            BillCategory.TECHNOLOGY: [
                'technology', 'internet', 'cyber', 'digital', 'computer', 'software',
                'artificial intelligence', 'ai', 'data', 'privacy', 'cybersecurity',
                'blockchain', 'innovation', 'research', 'development', 'tech'
            ],
            
            BillCategory.TRANSPORTATION: [
                'transportation', 'highway', 'road', 'bridge', 'airport', 'aviation',
                'railroad', 'transit', 'traffic', 'infrastructure', 'vehicle',
                'automobile', 'truck', 'bus', 'bicycle', 'pedestrian', 'transport'
            ],
            
            BillCategory.JUDICIARY: [
                'court', 'judge', 'justice', 'legal', 'law enforcement', 'crime',
                'criminal', 'civil', 'lawsuit', 'litigation', 'attorney', 'lawyer',
                'prison', 'jail', 'sentencing', 'bail', 'trial', 'verdict'
            ],
            
            BillCategory.AGRICULTURE: [
                'agriculture', 'farm', 'farmer', 'crop', 'livestock', 'cattle',
                'food', 'nutrition', 'rural', 'agricultural', 'harvest', 'grain',
                'dairy', 'meat', 'vegetable', 'fruit', 'farming', 'ranch'
            ],
            
            BillCategory.ENERGY: [
                'energy', 'power', 'electricity', 'gas', 'oil', 'coal', 'nuclear',
                'renewable', 'solar', 'wind', 'hydroelectric', 'geothermal',
                'pipeline', 'refinery', 'utility', 'grid', 'fossil fuel'
            ],
            
            BillCategory.CEREMONIAL: [
                'naming', 'designation', 'commemoration', 'recognition', 'honor',
                'memorial', 'tribute', 'celebration', 'awareness', 'appreciation',
                'acknowledge', 'commend', 'post office', 'building naming', 'day'
            ]
        }
    
    def _initialize_complexity_indicators(self) -> Dict[str, Any]:
        """Initialize indicators for complexity assessment"""
        
        return {
            'simple_indicators': {
                'max_sections': 5,
                'max_length': 5000,
                'single_topic': True,
                'straightforward_language': True
            },
            
            'moderate_indicators': {
                'max_sections': 15,
                'max_length': 20000,
                'related_topics': True,
                'some_technical_language': True
            },
            
            'complex_indicators': {
                'max_sections': 50,
                'max_length': 50000,
                'multiple_topics': True,
                'technical_language': True,
                'cross_references': True
            },
            
            'omnibus_indicators': {
                'max_sections': 100,
                'max_length': 100000,
                'many_topics': True,
                'highly_technical': True,
                'extensive_cross_references': True
            },
            
            'mega_indicators': {
                'min_sections': 100,
                'min_length': 100000,
                'appropriations_bill': True,
                'comprehensive_reform': True,
                'affects_multiple_agencies': True
            }
        }
    
    def _initialize_funding_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for funding tier identification"""
        
        return {
            'none': [
                r'no\s+appropriation',
                r'no\s+funds?\s+authorized',
                r'administrative\s+action\s+only'
            ],
            
            'small': [
                r'\$[1-9]\d{0,2}(?:,\d{3})*(?:\s+thousand)?',  # Up to millions
                r'\$[1-9]\.\d+\s+million'
            ],
            
            'medium': [
                r'\$[1-9]\d+\s+million',                        # Multi-millions
                r'\$[1-4]\d{2}\s+million'                       # Up to $499 million
            ],
            
            'large': [
                r'\$[5-9]\d{2}\s+million',                      # $500M - $999M
                r'\$[1-9]\.\d+\s+billion',                      # Single digit billions
                r'\$[1-9]\s+billion'
            ],
            
            'massive': [
                r'\$[1-9]\d+\s+billion',                        # Multi-billions
                r'\$[1-9]\d*\s+trillion',                       # Trillions
                r'such\s+sums\s+as\s+may\s+be\s+necessary'     # Open-ended funding
            ]
        }
    
    def _initialize_enforcement_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for enforcement level assessment"""
        
        return {
            'none': [
                r'voluntary\s+compliance',
                r'no\s+penalty',
                r'advisory\s+only'
            ],
            
            'civil': [
                r'civil\s+penalty',
                r'fine\s+not\s+(?:to\s+)?exceed\s+\$[1-9]\d{2,4}',  # Up to $99,999
                r'administrative\s+action',
                r'cease\s+and\s+desist'
            ],
            
            'criminal': [
                r'criminal\s+penalty',
                r'imprisonment\s+(?:for\s+)?(?:not\s+more\s+than\s+)?(?:up\s+to\s+)?[1-9]\d*\s*(?:months?|years?)',
                r'felony',
                r'misdemeanor'
            ],
            
            'severe': [
                r'imprisonment\s+(?:for\s+)?(?:not\s+more\s+than\s+)?(?:[5-9]|\d{2,})\s*years?',  # 5+ years
                r'fine\s+not\s+(?:to\s+)?exceed\s+\$[1-9]\d{5,}',  # $100K+ fines
                r'life\s+imprisonment',
                r'death\s+penalty'
            ]
        }
    
    def _initialize_scope_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for scope determination"""
        
        return {
            'local': [
                r'specific\s+(?:city|county|municipality)',
                r'pilot\s+program',
                r'demonstration\s+project'
            ],
            
            'state': [
                r'state\s+(?:of\s+)?\w+',
                r'within\s+the\s+state',
                r'state\s+implementation'
            ],
            
            'national': [
                r'united\s+states',
                r'nationwide',
                r'all\s+states',
                r'federal\s+(?:mandate|requirement|program)'
            ],
            
            'international': [
                r'international',
                r'foreign\s+(?:country|nation)',
                r'treaty',
                r'diplomatic'
            ]
        }
    
    def _initialize_urgency_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for urgency assessment"""
        
        return {
            'routine': [
                r'effective\s+(?:180|365)\s+days',
                r'within\s+(?:6\s+months|1\s+year)',
                r'at\s+the\s+discretion\s+of'
            ],
            
            'priority': [
                r'effective\s+(?:90|120)\s+days',
                r'within\s+(?:3|4)\s+months',
                r'as\s+soon\s+as\s+practicable'
            ],
            
            'urgent': [
                r'effective\s+(?:30|60)\s+days',
                r'within\s+(?:1|2)\s+months?',
                r'expedited\s+(?:review|process)'
            ],
            
            'emergency': [
                r'immediately',
                r'effective\s+(?:upon\s+)?enactment',
                r'emergency',
                r'within\s+(?:1|2|3)\s+days?'
            ]
        }
    
    def _initialize_strategy_rules(self) -> Dict[str, Any]:
        """Initialize rules for processing strategy recommendation"""
        
        return {
            ProcessingStrategy.PREMIUM: {
                'categories': [BillCategory.HEALTHCARE, BillCategory.DEFENSE, BillCategory.ECONOMY],
                'complexity_min': BillComplexityTier.COMPLEX,
                'funding_tiers': ['large', 'massive'],
                'enforcement_levels': ['criminal', 'severe'],
                'urgency_levels': ['urgent', 'emergency'],
                'political_sensitivity': ['high', 'critical']
            },
            
            ProcessingStrategy.ENHANCED: {
                'categories': [BillCategory.EDUCATION, BillCategory.ENVIRONMENT, BillCategory.TECHNOLOGY],
                'complexity_min': BillComplexityTier.MODERATE,
                'funding_tiers': ['medium', 'large'],
                'enforcement_levels': ['civil', 'criminal'],
                'urgency_levels': ['priority', 'urgent'],
                'political_sensitivity': ['medium', 'high']
            },
            
            ProcessingStrategy.STANDARD: {
                'categories': [BillCategory.TRANSPORTATION, BillCategory.AGRICULTURE, BillCategory.ENERGY],
                'complexity_min': BillComplexityTier.SIMPLE,
                'funding_tiers': ['small', 'medium'],
                'enforcement_levels': ['none', 'civil'],
                'urgency_levels': ['routine', 'priority'],
                'political_sensitivity': ['low', 'medium']
            },
            
            ProcessingStrategy.TEMPLATE: {
                'categories': [BillCategory.CEREMONIAL, BillCategory.OTHER],
                'complexity_max': BillComplexityTier.SIMPLE,
                'funding_tiers': ['none', 'small'],
                'enforcement_levels': ['none'],
                'urgency_levels': ['routine'],
                'political_sensitivity': ['low']
            }
        }
    
    async def _identify_primary_category(self, bill_metadata: Dict[str, Any], 
                                       bill_text: str) -> BillCategory:
        """Identify the primary category of the bill"""
        
        # Combine title, summary, and text for analysis
        full_text = ' '.join([
            bill_metadata.get('title', ''),
            bill_metadata.get('summary', ''),
            bill_text[:5000]  # First 5000 characters
        ]).lower()
        
        # Score each category based on keyword matches
        category_scores = {}
        
        for category, keywords in self.category_keywords.items():
            score = 0
            for keyword in keywords:
                # Count weighted by keyword importance
                if keyword in ['health', 'defense', 'education', 'environment']:
                    score += full_text.count(keyword) * 3  # High-weight keywords
                else:
                    score += full_text.count(keyword)
            
            category_scores[category] = score
        
        # Return category with highest score
        if category_scores:
            primary_category = max(category_scores.items(), key=lambda x: x[1])[0]
        else:
            primary_category = BillCategory.OTHER
        
        # Special ceremonial detection
        ceremonial_indicators = ['naming', 'designation', 'post office', 'commemoration']
        if any(indicator in full_text for indicator in ceremonial_indicators):
            primary_category = BillCategory.CEREMONIAL
        
        return primary_category
    
    async def _determine_complexity_tier(self, bill_metadata: Dict[str, Any],
                                       bill_text: str) -> BillComplexityTier:
        """Determine the complexity tier of the bill"""
        
        # Basic metrics
        text_length = len(bill_text)
        section_count = len(re.findall(r'SEC\.|SECTION', bill_text, re.IGNORECASE))
        title_count = len(re.findall(r'TITLE\s+[IVX]+', bill_text, re.IGNORECASE))
        
        # Check for appropriations bill
        is_appropriations = 'appropriation' in bill_metadata.get('title', '').lower()
        
        # Determine complexity based on metrics
        if is_appropriations and text_length > 100000:
            return BillComplexityTier.MEGA
        elif text_length > 100000 or section_count > 100:
            return BillComplexityTier.OMNIBUS
        elif text_length > 50000 or section_count > 50:
            return BillComplexityTier.COMPLEX
        elif text_length > 20000 or section_count > 15:
            return BillComplexityTier.MODERATE
        else:
            return BillComplexityTier.SIMPLE
    
    async def _analyze_funding_tier(self, bill_text: str) -> str:
        """Analyze the funding tier of the bill"""
        
        text_lower = bill_text.lower()
        
        # Check each funding tier pattern
        for tier in ['massive', 'large', 'medium', 'small', 'none']:
            for pattern in self.funding_patterns[tier]:
                if re.search(pattern, text_lower):
                    return tier
        
        # Default to none if no funding patterns found
        return 'none'
    
    async def _assess_enforcement_level(self, bill_text: str) -> str:
        """Assess the enforcement level of the bill"""
        
        text_lower = bill_text.lower()
        
        # Check each enforcement level pattern
        for level in ['severe', 'criminal', 'civil', 'none']:
            for pattern in self.enforcement_patterns[level]:
                if re.search(pattern, text_lower):
                    return level
        
        # Default to none if no enforcement patterns found
        return 'none'
    
    async def _determine_scope(self, bill_metadata: Dict[str, Any], bill_text: str) -> str:
        """Determine the scope of the bill"""
        
        full_text = (bill_metadata.get('title', '') + ' ' + bill_text[:2000]).lower()
        
        # Check each scope pattern
        for scope_level in ['international', 'national', 'state', 'local']:
            for pattern in self.scope_patterns[scope_level]:
                if re.search(pattern, full_text):
                    return scope_level
        
        # Default to national for federal bills
        return 'national'
    
    async def _assess_urgency(self, bill_metadata: Dict[str, Any], bill_text: str) -> str:
        """Assess the urgency level of the bill"""
        
        full_text = (bill_metadata.get('title', '') + ' ' + bill_text[:3000]).lower()
        
        # Check each urgency pattern
        for urgency_level in ['emergency', 'urgent', 'priority', 'routine']:
            for pattern in self.urgency_patterns[urgency_level]:
                if re.search(pattern, full_text):
                    return urgency_level
        
        # Default to routine
        return 'routine'
    
    async def _evaluate_political_sensitivity(self, bill_metadata: Dict[str, Any], 
                                            bill_text: str) -> str:
        """Evaluate political sensitivity level"""
        
        # Sensitive topic indicators
        sensitive_topics = [
            'abortion', 'immigration', 'gun', 'firearm', 'climate change',
            'medicare', 'social security', 'tax increase', 'deficit',
            'transgender', 'lgbtq', 'religious freedom', 'voter'
        ]
        
        full_text = (bill_metadata.get('title', '') + ' ' + 
                    bill_metadata.get('summary', '') + ' ' + 
                    bill_text[:5000]).lower()
        
        sensitive_count = sum(1 for topic in sensitive_topics if topic in full_text)
        
        if sensitive_count >= 3:
            return 'critical'
        elif sensitive_count >= 2:
            return 'high'
        elif sensitive_count >= 1:
            return 'medium'
        else:
            return 'low'
    
    async def _recommend_processing_strategy(self, pattern: BillPattern) -> ProcessingStrategy:
        """Recommend processing strategy based on bill pattern"""
        
        # Score each strategy based on pattern characteristics
        strategy_scores = {}
        
        for strategy, rules in self.strategy_rules.items():
            score = 0
            
            # Category match
            if pattern.category in rules.get('categories', []):
                score += 3
            
            # Complexity match
            complexity_order = [BillComplexityTier.SIMPLE, BillComplexityTier.MODERATE, 
                              BillComplexityTier.COMPLEX, BillComplexityTier.OMNIBUS, 
                              BillComplexityTier.MEGA]
            
            if 'complexity_min' in rules:
                min_complexity_index = complexity_order.index(rules['complexity_min'])
                pattern_complexity_index = complexity_order.index(pattern.complexity)
                if pattern_complexity_index >= min_complexity_index:
                    score += 2
            
            if 'complexity_max' in rules:
                max_complexity_index = complexity_order.index(rules['complexity_max'])
                pattern_complexity_index = complexity_order.index(pattern.complexity)
                if pattern_complexity_index <= max_complexity_index:
                    score += 2
            
            # Funding tier match
            if pattern.funding_tier in rules.get('funding_tiers', []):
                score += 2
            
            # Enforcement level match
            if pattern.enforcement_level in rules.get('enforcement_levels', []):
                score += 2
            
            # Urgency match
            if pattern.urgency in rules.get('urgency_levels', []):
                score += 1
            
            # Political sensitivity match
            if pattern.political_sensitivity in rules.get('political_sensitivity', []):
                score += 1
            
            strategy_scores[strategy] = score
        
        # Return strategy with highest score
        recommended_strategy = max(strategy_scores.items(), key=lambda x: x[1])[0]
        
        # Override for ceremonial bills
        if pattern.category == BillCategory.CEREMONIAL:
            recommended_strategy = ProcessingStrategy.TEMPLATE
        
        return recommended_strategy
    
    def _calculate_confidence_score(self, pattern: BillPattern, 
                                  bill_metadata: Dict[str, Any]) -> float:
        """Calculate confidence in pattern identification"""
        
        confidence_score = 0.5  # Base confidence
        
        # Boost confidence based on clear indicators
        if pattern.category != BillCategory.OTHER:
            confidence_score += 0.15
        
        if pattern.funding_tier != 'none':
            confidence_score += 0.1
        
        if pattern.enforcement_level != 'none':
            confidence_score += 0.1
        
        if pattern.urgency != 'routine':
            confidence_score += 0.05
        
        # Boost for complete metadata
        if bill_metadata.get('title'):
            confidence_score += 0.05
        if bill_metadata.get('summary'):
            confidence_score += 0.05
        
        return min(1.0, confidence_score)
    
    def _calculate_recommended_budget(self, pattern: BillPattern) -> float:
        """Calculate recommended processing budget"""
        
        strategy = pattern.recommended_strategy
        budget_range = self.budget_tiers[strategy]
        
        # Start with middle of range
        base_budget = (budget_range[0] + budget_range[1]) / 2
        
        # Adjust based on pattern characteristics
        if pattern.complexity == BillComplexityTier.MEGA:
            base_budget = budget_range[1]  # Use max for mega bills
        elif pattern.complexity == BillComplexityTier.SIMPLE:
            base_budget = budget_range[0]  # Use min for simple bills
        
        if pattern.funding_tier == 'massive':
            base_budget = min(0.30, base_budget + 0.05)
        
        if pattern.urgency == 'emergency':
            base_budget = min(0.30, base_budget + 0.03)
        
        return round(base_budget, 3)
    
    async def _predict_quality_score(self, pattern: BillPattern) -> float:
        """Predict quality score based on pattern"""
        
        # Base prediction by strategy
        strategy_quality_predictions = {
            ProcessingStrategy.PREMIUM: 0.90,
            ProcessingStrategy.ENHANCED: 0.80,
            ProcessingStrategy.STANDARD: 0.70,
            ProcessingStrategy.TEMPLATE: 0.60
        }
        
        base_prediction = strategy_quality_predictions[pattern.recommended_strategy]
        
        # Adjust based on complexity
        complexity_adjustments = {
            BillComplexityTier.SIMPLE: 0.05,
            BillComplexityTier.MODERATE: 0.0,
            BillComplexityTier.COMPLEX: -0.03,
            BillComplexityTier.OMNIBUS: -0.05,
            BillComplexityTier.MEGA: -0.08
        }
        
        adjustment = complexity_adjustments.get(pattern.complexity, 0.0)
        
        return max(0.5, min(1.0, base_prediction + adjustment))
    
    def _estimate_processing_time(self, pattern: BillPattern) -> int:
        """Estimate processing time in seconds"""
        
        # Base time by complexity
        base_times = {
            BillComplexityTier.SIMPLE: 30,
            BillComplexityTier.MODERATE: 60,
            BillComplexityTier.COMPLEX: 120,
            BillComplexityTier.OMNIBUS: 180,
            BillComplexityTier.MEGA: 300
        }
        
        base_time = base_times[pattern.complexity]
        
        # Adjust by strategy
        strategy_multipliers = {
            ProcessingStrategy.PREMIUM: 1.5,
            ProcessingStrategy.ENHANCED: 1.2,
            ProcessingStrategy.STANDARD: 1.0,
            ProcessingStrategy.TEMPLATE: 0.5
        }
        
        multiplier = strategy_multipliers[pattern.recommended_strategy]
        
        return int(base_time * multiplier)
    
    async def _identify_risk_factors(self, pattern: BillPattern, 
                                   bill_metadata: Dict[str, Any]) -> List[str]:
        """Identify potential processing risks"""
        
        risks = []
        
        # Complexity risks
        if pattern.complexity in [BillComplexityTier.OMNIBUS, BillComplexityTier.MEGA]:
            risks.append("High complexity may require extended processing time")
        
        # Budget risks
        if pattern.funding_tier == 'massive':
            risks.append("Massive funding amounts may require additional verification")
        
        # Political sensitivity risks
        if pattern.political_sensitivity in ['high', 'critical']:
            risks.append("High political sensitivity requires careful analysis")
        
        # Urgency risks
        if pattern.urgency in ['urgent', 'emergency']:
            risks.append("Urgent timeline may pressure quality vs speed tradeoff")
        
        # Category-specific risks
        if pattern.category == BillCategory.HEALTHCARE:
            risks.append("Healthcare legislation requires specialized terminology handling")
        
        if pattern.category == BillCategory.DEFENSE:
            risks.append("Defense bills may contain classified or sensitive information")
        
        return risks
    
    async def _generate_optimization_suggestions(self, pattern: BillPattern) -> List[str]:
        """Generate optimization suggestions based on pattern"""
        
        suggestions = []
        
        # Strategy-based suggestions
        if pattern.recommended_strategy == ProcessingStrategy.PREMIUM:
            suggestions.append("Use highest quality evidence extraction and validation")
            suggestions.append("Enable comprehensive cross-reference analysis")
            
        elif pattern.recommended_strategy == ProcessingStrategy.ENHANCED:
            suggestions.append("Focus evidence extraction on key policy areas")
            suggestions.append("Use balanced analysis with quality monitoring")
            
        elif pattern.recommended_strategy == ProcessingStrategy.TEMPLATE:
            suggestions.append("Use template-based processing for efficiency")
            suggestions.append("Focus on basic structure and key facts")
        
        # Complexity-based suggestions
        if pattern.complexity == BillComplexityTier.OMNIBUS:
            suggestions.append("Break analysis into title-based sections")
            suggestions.append("Use hierarchical evidence organization")
        
        # Category-based suggestions
        if pattern.category == BillCategory.HEALTHCARE:
            suggestions.append("Prioritize patient impact and cost evidence")
            
        elif pattern.category == BillCategory.ENVIRONMENT:
            suggestions.append("Focus on environmental impact and compliance evidence")
        
        return suggestions[:5]  # Top 5 suggestions
    
    async def _record_pattern_for_learning(self, pattern: BillPattern, 
                                         bill_metadata: Dict[str, Any]) -> None:
        """Record pattern for adaptive learning"""
        
        pattern_key = pattern.pattern_id
        
        # Store pattern in recognition database
        self.recognized_patterns[pattern_key] = {
            'pattern': pattern,
            'count': self.recognized_patterns.get(pattern_key, {}).get('count', 0) + 1,
            'last_seen': datetime.utcnow(),
            'bill_examples': self.recognized_patterns.get(pattern_key, {}).get('bill_examples', [])
        }
        
        # Add bill example (keep last 5)
        bill_example = {
            'bill_number': bill_metadata.get('bill_number'),
            'title': bill_metadata.get('title', '')[:100]  # First 100 chars
        }
        
        examples = self.recognized_patterns[pattern_key]['bill_examples']
        examples.append(bill_example)
        if len(examples) > 5:
            examples.pop(0)
        
        logger.debug(f"Recorded pattern {pattern_key} for learning (count: {self.recognized_patterns[pattern_key]['count']})")
    
    async def get_pattern_statistics(self) -> Dict[str, Any]:
        """Get statistics about recognized patterns"""
        
        total_patterns = len(self.recognized_patterns)
        
        # Category distribution
        category_counts = defaultdict(int)
        complexity_counts = defaultdict(int)
        strategy_counts = defaultdict(int)
        
        for pattern_data in self.recognized_patterns.values():
            pattern = pattern_data['pattern']
            count = pattern_data['count']
            
            category_counts[pattern.category.value] += count
            complexity_counts[pattern.complexity.value] += count
            strategy_counts[pattern.recommended_strategy.value] += count
        
        return {
            'total_patterns': total_patterns,
            'total_bills_analyzed': sum(data['count'] for data in self.recognized_patterns.values()),
            'category_distribution': dict(category_counts),
            'complexity_distribution': dict(complexity_counts),
            'strategy_distribution': dict(strategy_counts),
            'most_common_patterns': sorted(
                [(k, v['count']) for k, v in self.recognized_patterns.items()],
                key=lambda x: x[1], reverse=True
            )[:10]
        }

# Global instance
bill_pattern_service = None

def get_bill_pattern_service(db: Session) -> BillPatternRecognitionService:
    """Get or create the global bill pattern recognition service instance"""
    global bill_pattern_service
    if bill_pattern_service is None:
        bill_pattern_service = BillPatternRecognitionService(db)
    return bill_pattern_service