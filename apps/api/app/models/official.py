# app/models/official.py
from sqlalchemy import Column, String, Text, Boolean, Integer
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.db.types import get_json_type

class Official(Base):
    __tablename__ = "officials"

    # Basic official information
    name = Column(String, nullable=False, index=True)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    full_name = Column(String(255), nullable=True, index=True)
    title = Column(String, nullable=False)
    party = Column(String, nullable=True)

    # Contact information
    email = Column(String, nullable=True)
    phone = Column(String, nullable=True)
    dc_office_phone = Column(String(50), nullable=True)
    local_office_phone = Column(String(50), nullable=True)
    website = Column(String, nullable=True)
    homepage_url = Column(String(255), nullable=True)

    # Address information
    office_address = Column(Text, nullable=True)
    dc_office_address = Column(Text, nullable=True)
    local_office_address = Column(Text, nullable=True)
    office_city = Column(String, nullable=True)
    office_state = Column(String, nullable=True)
    office_zip = Column(String, nullable=True)

    # Government structure - using String instead of Enum to avoid data integrity issues
    level = Column(String, nullable=False)  # 'federal', 'state', 'local'
    chamber = Column(String, nullable=True)  # 'house', 'senate', 'executive', 'judicial', 'other'

    # Geographic representation
    state = Column(String, nullable=True)  # State abbreviation
    district = Column(String, nullable=True)  # District number or name

    # External identifiers
    bioguide_id = Column(String, nullable=True, unique=True, index=True)
    openstates_id = Column(String, nullable=True, unique=True, index=True)
    google_civic_id = Column(String, nullable=True, unique=True, index=True)
    five_calls_id = Column(String, nullable=True, unique=True, index=True)
    govtrack_id = Column(String(50), nullable=True, index=True)

    # Social media - individual fields for backward compatibility
    twitter_handle = Column(String, nullable=True)  # Twitter username without @
    facebook_url = Column(String, nullable=True)
    instagram_handle = Column(String, nullable=True)
    youtube_channel = Column(String(255), nullable=True)
    linkedin_url = Column(String(255), nullable=True)

    # Rich social media data - JSONB column for comprehensive social media information
    social_media = Column(Text, nullable=True)

    # Official profile
    bio = Column(Text, nullable=True)
    profile_picture_url = Column(String, nullable=True)
    official_photo_url = Column(String(255), nullable=True)  # Official government photo

    # Term information
    term_start = Column(String, nullable=True)  # Year as string
    term_end = Column(String, nullable=True)    # Year as string
    current_term_start = Column(String(50), nullable=True)
    current_term_end = Column(String(50), nullable=True)
    next_election_date = Column(String(50), nullable=True)

    # Status
    is_active = Column(Boolean, default=True, nullable=False)

    # Committee and legislative information
    committees = Column(Text, nullable=True)  # JSON array of committees
    leadership_positions = Column(Text, nullable=True)  # JSON array of leadership roles

    # Voting record and positions
    voting_record = Column(Text, nullable=True)  # Array of vote records
    positions = Column(Text, nullable=True)      # Array of position statements
    
    # Voting statistics for public profiles
    bills_sponsored_count = Column(Integer, nullable=True)
    bills_cosponsored_count = Column(Integer, nullable=True)
    votes_cast_count = Column(Integer, nullable=True)

    # Engagement metrics
    response_rate = Column(Integer, nullable=True)  # Response rate percentage
    avg_response_time = Column(Integer, nullable=True)  # Response time in hours

    # Metadata
    official_metadata = Column(Text, nullable=True)

    # Relationships
    actions = relationship("Action", back_populates="official", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Official(name='{self.name}', title='{self.title}', state='{self.state}')>"

    @property
    def full_title(self) -> str:
        if self.state:
            return f"{self.title} from {self.state}"
        return self.title

    @property
    def display_name(self) -> str:
        if self.party:
            return f"{self.name} ({self.party})"
        return self.name

    @property
    def contact_preference(self) -> str:
        """Return the preferred contact method"""
        if self.email:
            return "email"
        elif self.phone:
            return "phone"
        elif self.website:
            return "website"
        return "none"

    @property
    def parsed_social_media(self):
        """Parse social media JSON string into dictionary"""
        if not self.social_media:
            return None
        
        import json
        try:
            return json.loads(self.social_media)
        except (json.JSONDecodeError, TypeError):
            return None
