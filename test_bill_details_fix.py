#!/usr/bin/env python3
"""
Test script to verify the bill_details creation fix.
This script tests the specific issue where bill_id was None.
"""

import sys
import os

# Add the API directory to the path
api_dir = os.path.join(os.path.dirname(__file__), 'apps', 'api')
sys.path.insert(0, api_dir)

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.base import Base
from app.models.bill import Bill
from app.models.bill_details import BillDetails
from app.services.bill_details_service import BillDetailsService
import uuid

# Test database setup
DATABASE_URL = "postgresql://postgres:postgres@localhost:5432/modernaction_test"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def test_bill_details_creation():
    """Test that bill_details creation works with proper bill_id"""
    db = SessionLocal()
    
    try:
        # Create a test bill first
        test_bill = Bill(
            title="Test Bill for Details",
            bill_number="TEST123",
            bill_type="house_bill",
            session_year=118,
            chamber="house",
            state="federal",
            summary="Test bill summary",
            full_text="This is test bill text content."
        )
        
        db.add(test_bill)
        db.commit()
        db.refresh(test_bill)
        
        print(f"✅ Created test bill with ID: {test_bill.id}")
        
        # Test bill_details creation
        details_service = BillDetailsService(db)
        
        test_payload = {
            'hero_summary': 'Test hero summary',
            'overview': {
                'what_does': {'content': 'Test what does', 'citations': []},
                'who_affects': {'content': 'Test who affects', 'citations': []},
                'why_matters': {'content': 'Test why matters', 'citations': []},
            },
            'positions': {
                'support_reasons': [{'claim': 'Test support', 'justification': 'Test justification', 'citations': []}],
                'oppose_reasons': [{'claim': 'Test oppose', 'justification': 'Test justification', 'citations': []}],
            },
            'message_templates': {},
            'tags': ['test'],
            'other_details': [],
        }
        
        # This should work now with the fix
        result = details_service.create_or_update_details(
            bill=test_bill,
            full_text=test_bill.full_text,
            details_payload=test_payload
        )
        
        print(f"✅ Created bill_details with ID: {result.id}")
        print(f"✅ Bill ID in details: {result.bill_id}")
        print(f"✅ SEO slug: {result.seo_slug}")
        
        # Verify the relationship
        assert result.bill_id == test_bill.id, f"Bill ID mismatch: {result.bill_id} != {test_bill.id}"
        assert result.bill_id is not None, "Bill ID should not be None"
        
        print("✅ All tests passed!")
        
        # Clean up
        db.delete(result)
        db.delete(test_bill)
        db.commit()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
        return False
        
    finally:
        db.close()

def test_bill_with_none_id():
    """Test what happens when bill has None ID (should be caught by our fix)"""
    db = SessionLocal()
    
    try:
        # Create a bill object without saving it (so it has no ID)
        test_bill = Bill(
            title="Test Bill No ID",
            bill_number="NOID123",
            bill_type="house_bill",
            session_year=118,
            chamber="house",
            state="federal",
            summary="Test bill summary",
            full_text="This is test bill text content."
        )
        # Don't add to db, so it has no ID
        
        print(f"Test bill ID: {test_bill.id}")  # Should be None
        
        details_service = BillDetailsService(db)
        
        test_payload = {
            'hero_summary': 'Test hero summary',
            'overview': {},
            'positions': {},
            'message_templates': {},
            'tags': [],
            'other_details': [],
        }
        
        # This should fail gracefully now
        try:
            result = details_service.create_or_update_details(
                bill=test_bill,
                full_text=test_bill.full_text,
                details_payload=test_payload
            )
            print(f"❌ Expected this to fail, but got result: {result}")
            return False
        except Exception as e:
            print(f"✅ Correctly caught error for None bill_id: {e}")
            return True
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
        
    finally:
        db.close()

if __name__ == "__main__":
    print("Testing bill_details creation fix...")
    
    # Test 1: Normal case with valid bill
    print("\n=== Test 1: Normal bill_details creation ===")
    test1_passed = test_bill_details_creation()
    
    # Test 2: Edge case with None bill ID
    print("\n=== Test 2: Bill with None ID (should fail gracefully) ===")
    test2_passed = test_bill_with_none_id()
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The fix is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)
