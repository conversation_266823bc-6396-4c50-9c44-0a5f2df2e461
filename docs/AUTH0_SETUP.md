# Auth0 Setup Guide

## Overview
You need to create separate Auth0 tenants/applications for each environment to ensure proper data isolation and security.

## Step 1: Create Auth0 Tenants

### Development Tenant
- **Domain**: `dev-modernaction.us.auth0.com` (already exists)
- Use for: Local development and testing

### Staging Tenant  
- **Domain**: `staging-modernaction.us.auth0.com` (create new)
- Use for: staging.modernaction.io testing

### Production Tenant
- **Domain**: `prod-modernaction.us.auth0.com` (create new)  
- Use for: modernaction.io production

## Step 2: Configure Applications

For each tenant, create:

### Web Application (SPA)
- **Name**: ModernAction Web - [Environment]
- **Type**: Single Page Application
- **Technology**: React/Next.js

**Settings:**
- **Allowed Callback URLs**: 
  - Dev: `http://localhost:3000/api/auth/callback, http://localhost:3001/api/auth/callback`
  - Staging: `https://staging.modernaction.io/api/auth/callback`
  - Prod: `https://modernaction.io/api/auth/callback`

- **Allowed Logout URLs**:
  - Dev: `http://localhost:3000, http://localhost:3001`
  - Staging: `https://staging.modernaction.io`
  - Prod: `https://modernaction.io`

- **Web Origins**:
  - Dev: `http://localhost:3000, http://localhost:3001`
  - Staging: `https://staging.modernaction.io`
  - Prod: `https://modernaction.io`

### API Application
- **Name**: ModernAction API - [Environment]
- **Type**: API
- **Identifier** (Audience):
  - Dev: `https://api-dev.modernaction.io`
  - Staging: `https://api-staging.modernaction.io`
  - Prod: `https://api.modernaction.io`

## Step 3: Configure User Management

### User Database Connection
Each tenant should have its own database connection to maintain user separation:

- **Dev**: Allow social logins + email/password for testing
- **Staging**: Mirror production settings for realistic testing
- **Production**: Configure final authentication methods (Google, email, etc.)

### User Metadata
Configure custom user metadata fields:
```json
{
  "zip_code": "",
  "city": "",
  "state": "",
  "congressional_district": "",
  "senators": [],
  "representatives": []
}
```

## Step 4: Environment Variables

Update your environment files with the correct Auth0 credentials from each tenant:

```bash
# Development
AUTH0_DOMAIN=dev-modernaction.us.auth0.com
AUTH0_CLIENT_ID=[your_dev_client_id]
AUTH0_CLIENT_SECRET=[your_dev_client_secret]
AUTH0_AUDIENCE=https://api-dev.modernaction.io

# Staging  
AUTH0_DOMAIN=staging-modernaction.us.auth0.com
AUTH0_CLIENT_ID=[your_staging_client_id]
AUTH0_CLIENT_SECRET=[your_staging_client_secret]
AUTH0_AUDIENCE=https://api-staging.modernaction.io

# Production
AUTH0_DOMAIN=prod-modernaction.us.auth0.com
AUTH0_CLIENT_ID=[your_prod_client_id]
AUTH0_CLIENT_SECRET=[your_prod_client_secret]
AUTH0_AUDIENCE=https://api.modernaction.io
```

## Step 5: Rules and Hooks

Set up identical Auth0 rules/actions across environments:

### Add User Metadata Rule
```javascript
function addUserMetadata(user, context, callback) {
  const namespace = 'https://modernaction.io/';
  
  context.idToken[namespace + 'user_metadata'] = user.user_metadata || {};
  context.accessToken[namespace + 'user_metadata'] = user.user_metadata || {};
  
  callback(null, user, context);
}
```

### Geographic Data Enrichment (optional)
Add rules to automatically populate user location data based on IP or ZIP code.

## Step 6: Testing

After setup, test each environment:

```bash
# Development
npm run env:dev
cd apps/web && npm run dev

# Staging  
npm run env:staging
# Deploy and test staging.modernaction.io

# Production
npm run env:prod  
# Deploy and test modernaction.io
```

## Security Notes

1. **Never share credentials** between environments
2. **Use strong, unique secrets** for each environment
3. **Enable MFA** on Auth0 dashboard accounts
4. **Regularly rotate secrets** in production
5. **Monitor authentication logs** for suspicious activity

## Migration Plan

1. **Phase 1**: Set up staging tenant and test
2. **Phase 2**: Migrate existing users if needed
3. **Phase 3**: Set up production tenant
4. **Phase 4**: Go live with proper environment separation