"""
AI Usage Tracking Models

Tracks token usage and costs for all AI operations to monitor spending and optimize usage.
"""

from sqlalchemy import Column, String, Integer, Float, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
from app.db.base_class import Base


def utc_now():
    """Get current UTC time as naive datetime for SQLAlchemy default"""
    return datetime.now(timezone.utc).replace(tzinfo=None)


class AIUsageLog(Base):
    """Track individual AI API calls with token usage and costs"""
    __tablename__ = "ai_usage_logs"

    id = Column(String, primary_key=True)

    # Operation details
    operation_type = Column(String, nullable=False)  # 'bill_analysis', 'reason_generation', 'summary', etc.
    operation_subtype = Column(String, nullable=True)  # 'support_reasons', 'oppose_reasons', 'comprehensive_analysis', etc.
    bill_id = Column(String, ForeignKey("bills.id"), nullable=True)  # Associated bill if applicable

    # AI model details
    model_name = Column(String, nullable=False)  # 'gpt-4-turbo', 'gpt-3.5-turbo', etc.
    provider = Column(String, nullable=False, default='openai')  # 'openai', 'anthropic', etc.

    # Token usage
    prompt_tokens = Column(Integer, nullable=False, default=0)
    completion_tokens = Column(Integer, nullable=False, default=0)
    total_tokens = Column(Integer, nullable=False, default=0)

    # Cost tracking (in USD)
    prompt_cost = Column(Float, nullable=False, default=0.0)
    completion_cost = Column(Float, nullable=False, default=0.0)
    total_cost = Column(Float, nullable=False, default=0.0)

    # Performance metrics
    response_time_ms = Column(Float, nullable=True)  # Response time in milliseconds
    success = Column(Boolean, nullable=False, default=True)
    error_message = Column(Text, nullable=True)

    # Request/response details (for debugging)
    prompt_length = Column(Integer, nullable=True)  # Character length of prompt
    response_length = Column(Integer, nullable=True)  # Character length of response

    # Metadata
    created_at = Column(DateTime, nullable=False, default=utc_now)
    user_id = Column(String, nullable=True)  # User who triggered the operation
    session_id = Column(String, nullable=True)  # Session identifier

    # Override the Base class updated_at column since ai_usage_logs table doesn't have it
    updated_at = None

    # Relationships
    bill = relationship("Bill", back_populates="ai_usage_logs")

    def __init__(self, **kwargs):
        # Apply Python-level defaults
        kwargs.setdefault('provider', 'openai')
        kwargs.setdefault('prompt_tokens', 0)
        kwargs.setdefault('completion_tokens', 0)
        kwargs.setdefault('total_tokens', 0)
        kwargs.setdefault('prompt_cost', 0.0)
        kwargs.setdefault('completion_cost', 0.0)
        kwargs.setdefault('total_cost', 0.0)
        kwargs.setdefault('success', True)
        super().__init__(**kwargs)


class AIUsageSummary(Base):
    """Daily/monthly summaries of AI usage for cost monitoring"""
    __tablename__ = "ai_usage_summaries"

    id = Column(String, primary_key=True)
    
    # Time period
    date_bucket = Column(DateTime, nullable=False)  # Date for this summary (daily)
    period_type = Column(String, nullable=False, default='daily')  # 'daily', 'monthly'
    
    # Operation aggregates
    operation_type = Column(String, nullable=True)  # Specific operation or 'all' for total
    model_name = Column(String, nullable=True)  # Specific model or 'all' for total
    
    # Usage totals
    total_requests = Column(Integer, nullable=False, default=0)
    successful_requests = Column(Integer, nullable=False, default=0)
    failed_requests = Column(Integer, nullable=False, default=0)
    
    # Token totals
    total_prompt_tokens = Column(Integer, nullable=False, default=0)
    total_completion_tokens = Column(Integer, nullable=False, default=0)
    total_tokens = Column(Integer, nullable=False, default=0)
    
    # Cost totals (in USD)
    total_prompt_cost = Column(Float, nullable=False, default=0.0)
    total_completion_cost = Column(Float, nullable=False, default=0.0)
    total_cost = Column(Float, nullable=False, default=0.0)
    
    # Performance metrics
    avg_response_time_ms = Column(Float, nullable=True)
    avg_tokens_per_request = Column(Float, nullable=True)
    avg_cost_per_request = Column(Float, nullable=True)
    
    # Bill-specific metrics
    bills_processed = Column(Integer, nullable=False, default=0)
    avg_cost_per_bill = Column(Float, nullable=True)
    
    # Metadata
    created_at = Column(DateTime, nullable=False, default=utc_now)
    updated_at = Column(DateTime, nullable=False, default=utc_now)

    def __init__(self, **kwargs):
        # Apply Python-level defaults
        kwargs.setdefault('period_type', 'daily')
        kwargs.setdefault('total_requests', 0)
        kwargs.setdefault('successful_requests', 0)
        kwargs.setdefault('failed_requests', 0)
        kwargs.setdefault('total_prompt_tokens', 0)
        kwargs.setdefault('total_completion_tokens', 0)
        kwargs.setdefault('total_tokens', 0)
        kwargs.setdefault('total_prompt_cost', 0.0)
        kwargs.setdefault('total_completion_cost', 0.0)
        kwargs.setdefault('total_cost', 0.0)
        kwargs.setdefault('bills_processed', 0)
        super().__init__(**kwargs)


class AIBudgetAlert(Base):
    """Budget alerts and thresholds for AI spending"""
    __tablename__ = "ai_budget_alerts"

    id = Column(String, primary_key=True)
    
    # Alert configuration
    alert_name = Column(String, nullable=False)
    alert_type = Column(String, nullable=False)  # 'daily', 'monthly', 'per_bill', 'total'
    threshold_amount = Column(Float, nullable=False)  # USD threshold
    threshold_tokens = Column(Integer, nullable=True)  # Optional token threshold
    
    # Scope
    operation_type = Column(String, nullable=True)  # Specific operation or null for all
    model_name = Column(String, nullable=True)  # Specific model or null for all
    
    # Alert status
    is_active = Column(Boolean, nullable=False, default=True)
    last_triggered = Column(DateTime, nullable=True)
    times_triggered = Column(Integer, nullable=False, default=0)
    
    # Notification settings
    notification_emails = Column(postgresql.JSONB, nullable=True)  # List of emails to notify
    webhook_url = Column(String, nullable=True)  # Optional webhook for alerts
    
    # Metadata
    created_at = Column(DateTime, nullable=False, default=utc_now)
    updated_at = Column(DateTime, nullable=False, default=utc_now)

    def __init__(self, **kwargs):
        # Apply Python-level defaults
        kwargs.setdefault('is_active', True)
        kwargs.setdefault('times_triggered', 0)
        super().__init__(**kwargs)


# Add relationship to Bill model
def add_ai_usage_relationship():
    """Add AI usage relationship to Bill model"""
    from app.models.bill import Bill
    if not hasattr(Bill, 'ai_usage_logs'):
        Bill.ai_usage_logs = relationship("AIUsageLog", back_populates="bill")
