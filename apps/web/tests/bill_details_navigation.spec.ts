import { test, expect } from '@playwright/test';

// E2E: Action modal → Learn more → Details page → Citation anchor scroll

test.describe('Bill Details - Action to Details to Citation', () => {
  test.beforeEach(async ({ page }) => {
    // Mock bills list endpoints to render at least one card
    const sampleBills = [
      { id: 'bill_123', title: 'Healthcare Access Act', bill_number: 'H.R.5', session_year: 118, status: 'introduced', chamber: 'house', is_featured: true, priority_score: 90, seo_slug: 'hr5-118' }
    ];
    await page.route('**/api/v1/bills/simple?*', async (route) => {
      await route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify(sampleBills) });
    });
    await page.route('**/api/v1/bills?*', async (route) => {
      await route.fulfill({ status: 200, contentType: 'application/json', body: JSON.stringify(sampleBills) });
    });

    // Also match search endpoint used by some UIs
    await page.route('**/api/v1/bills/search*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          { id: 'bill_123', title: 'Healthcare Access Act', bill_number: 'H.R.5', session_year: 118, status: 'introduced', chamber: 'house', is_featured: true, priority_score: 90, seo_slug: 'hr5-118' }
        ]),
      });
    });

    // Mock bill action data so modal content renders
    await page.route('**/api/v1/bills/*/action-data', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          bill_id: 'bill_123',
          title: 'Healthcare Access Act',
          bill_number: 'H.R.5',
          ai_summary: 'AI summary',
          support_reasons: ['Reason A', 'Reason B'],
          oppose_reasons: ['Oppose A'],
          amend_reasons: ['Amend A'],
          ai_tags: ['healthcare'],
          message_templates: {},
        }),
      });
    });

    // Mock bill details by slug (hr5-118)
    await page.route('**/api/v1/bills/details/by-slug/*', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'bill_123',
          bill_id: 'bill_123',
          seo_slug: 'hr5-118',
          seo_title: 'Healthcare Access Act - Bill Details',
          hero_summary: 'Summary',
          needs_human_review: true,
          metrics: { coverage_ratio: 0.7, unverified_count: 1 },
          overview: {
            what_does: {
              content: 'Expands Medicaid eligibility...',
              citations: [
                { quote: 'Expands Medicaid eligibility', start_offset: 0, end_offset: 20, heading: 'SEC. 101', anchor_id: 'sec-1' }
              ]
            }
          },
          positions: {
            support_reasons: [
              {
                claim: 'Expands coverage',
                justification: 'More people covered',
                citations: [
                  { quote: '12 million previously uninsured', start_offset: 50, end_offset: 80, heading: 'SEC. 101', anchor_id: 'sec-1' }
                ]
              }
            ]
          },
          source_index: [
            { heading: 'SEC. 101', start_offset: 0, end_offset: 100, anchor_id: 'sec-1' }
          ]
        }),
      });
    });
  });

  test('from action modal to details to citation anchor', async ({ page }) => {
    // Go to bills listing (this page uses the modal flow when taking action)
    await page.goto('/bills');

    // Wait for bill card and open action modal via the Take Action button
    await page.waitForSelector('[data-testid="bill-card"]');
    const firstCard = page.locator('[data-testid="bill-card"]').first();
    await expect(firstCard).toBeVisible();
    const takeActionButton = firstCard.locator('button:has-text("Take Action")');
    await expect(takeActionButton).toBeVisible();
    await takeActionButton.click();
    await expect(page.locator('[data-testid="bill-action-modal"]')).toBeVisible();

    // Learn more link
    const learnMore = page.locator('[data-testid="learn-more-link"]').first();
    await expect(learnMore).toBeVisible();

    // Ensure same tab for test
    await learnMore.evaluate((el: HTMLAnchorElement) => el.removeAttribute('target'));

    const learnHref = await learnMore.getAttribute('href');
    expect(learnHref).toMatch(/\/bills\/hr5-118#/);

    await learnMore.click();

    // On details page
    await expect(page).toHaveURL(/\/bills\/hr5-118/);

    // Click first citation link and verify scroll
    const citationLink = page.locator('[data-testid="citation-link"]').first();
    await expect(citationLink).toBeVisible();
    const href = await citationLink.getAttribute('href');
    expect(href).toMatch(/#sec-\d+/);
    const anchor = href!.split('#')[1];

    await citationLink.click();
    const target = page.locator(`#${anchor}`);
    await expect(target).toBeVisible();
    await expect(target).toBeInViewport();
  });
});

