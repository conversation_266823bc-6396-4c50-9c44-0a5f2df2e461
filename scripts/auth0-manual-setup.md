# Auth0 Manual Setup Guide - Step by Step

Since the Auth0 CLI is having authentication issues, let's set up the environments manually through the Auth0 Dashboard.

## Current Status ✅
- **Development**: Uses `dev-vvwd64m28nwqm871.us.auth0.com` (working)
- **Staging**: Need to create `staging-modernaction`
- **Production**: Need to create `prod-modernaction`

## Step 1: Create Staging Environment

### A. Create New Tenant
1. Go to **[Auth0 Dashboard](https://manage.auth0.com/)**
2. Click your current tenant dropdown (top left)
3. Click **"+ Create Tenant"**
4. **Tenant Name**: `staging-modernaction`
5. **Region**: `US` (same as your current)
6. **Environment Tag**: `Staging`
7. Click **"Create"**

### B. Configure Staging Applications

#### Create Web Application (SPA)
1. In your new staging tenant, go to **Applications > Applications**
2. Click **"+ Create Application"**
3. **Name**: `ModernAction Staging Web`
4. **Type**: `Single Page Web Applications`
5. Click **"Create"**

**Settings Tab:**
- **Allowed Callback URLs**: `https://staging.modernaction.io/api/auth/callback`
- **Allowed Logout URLs**: `https://staging.modernaction.io`
- **Allowed Web Origins**: `https://staging.modernaction.io`
- **Allowed Origins (CORS)**: `https://staging.modernaction.io`

**Save Changes**

#### Create API
1. Go to **Applications > APIs**
2. Click **"+ Create API"**
3. **Name**: `ModernAction Staging API`
4. **Identifier**: `https://api.modernaction.io`
5. **Signing Algorithm**: `RS256`
6. Click **"Create"**

### C. Copy Staging Credentials
From your staging tenant applications, copy:
- **Domain**: `staging-modernaction.us.auth0.com`
- **Client ID**: (from the web app)
- **Client Secret**: (from the web app)
- **API Audience**: `https://api.modernaction.io`

## Step 2: Create Production Environment

### A. Create New Tenant
1. Go to **[Auth0 Dashboard](https://manage.auth0.com/)**
2. Click tenant dropdown (top left)
3. Click **"+ Create Tenant"**
4. **Tenant Name**: `prod-modernaction`
5. **Region**: `US`
6. **Environment Tag**: `Production`
7. Click **"Create"**

### B. Configure Production Applications

#### Create Web Application (SPA)
1. In your new production tenant, go to **Applications > Applications**
2. Click **"+ Create Application"**
3. **Name**: `ModernAction Production Web`
4. **Type**: `Single Page Web Applications`
5. Click **"Create"**

**Settings Tab:**
- **Allowed Callback URLs**: `https://modernaction.io/api/auth/callback`
- **Allowed Logout URLs**: `https://modernaction.io`
- **Allowed Web Origins**: `https://modernaction.io`
- **Allowed Origins (CORS)**: `https://modernaction.io`

**Save Changes**

#### Create API
1. Go to **Applications > APIs**
2. Click **"+ Create API"**
3. **Name**: `ModernAction Production API`
4. **Identifier**: `https://api.modernaction.io`
5. **Signing Algorithm**: `RS256`
6. Click **"Create"**

### C. Copy Production Credentials
From your production tenant applications, copy:
- **Domain**: `prod-modernaction.us.auth0.com`
- **Client ID**: (from the web app)
- **Client Secret**: (from the web app)
- **API Audience**: `https://api.modernaction.io`

## Step 3: Update Environment Files

Once you have the credentials, I'll help you update these files:

### Staging Environment
```bash
# Update: .env.staging
AUTH0_DOMAIN=staging-modernaction.us.auth0.com
AUTH0_CLIENT_ID=[STAGING_CLIENT_ID]
AUTH0_CLIENT_SECRET=[STAGING_CLIENT_SECRET]

# Update: apps/web/.env.staging
AUTH0_ISSUER_BASE_URL=https://staging-modernaction.us.auth0.com
AUTH0_CLIENT_ID=[STAGING_CLIENT_ID]
AUTH0_CLIENT_SECRET=[STAGING_CLIENT_SECRET]
```

### Production Environment
```bash
# Update: .env.production
AUTH0_DOMAIN=prod-modernaction.us.auth0.com
AUTH0_CLIENT_ID=[PROD_CLIENT_ID]
AUTH0_CLIENT_SECRET=[PROD_CLIENT_SECRET]

# Update: apps/web/.env.production
AUTH0_ISSUER_BASE_URL=https://prod-modernaction.us.auth0.com
AUTH0_CLIENT_ID=[PROD_CLIENT_ID]
AUTH0_CLIENT_SECRET=[PROD_CLIENT_SECRET]
```

## Step 4: Test Environments

After updating credentials:

```bash
# Test staging
npm run env:staging

# Test production
npm run env:prod
```

## Priority Order
1. **Create Staging Tenant** (for staging.modernaction.io)
2. **Update staging environment files**
3. **Test staging deployment**
4. **Create Production Tenant** (for final launch)

Let me know when you've created the staging tenant and have the credentials - I'll help update the environment files!