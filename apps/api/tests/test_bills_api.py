import pytest
from app.models.bill import Bill, <PERSON><PERSON><PERSON><PERSON>, BillType

class TestBillsAPI:
    """Test suite for bills API endpoints"""

    def test_get_bills_empty(self, test_client):
        """Test getting bills when database is empty"""
        response = test_client.get("/api/v1/bills/")
        assert response.status_code == 200
        assert response.json() == []

    def test_create_bill(self, test_client, sample_bill_data):
        """Test creating a new bill"""
        response = test_client.post("/api/v1/bills/", json=sample_bill_data)
        assert response.status_code == 201

        data = response.json()
        assert data["title"] == sample_bill_data["title"]
        assert data["bill_number"] == sample_bill_data["bill_number"]
        assert data["status"] == sample_bill_data["status"]
        assert "id" in data

        # Verify bill was created in database
        bill_id = data["id"]
        response = test_client.get(f"/api/v1/bills/{bill_id}")
        assert response.status_code == 200
        assert response.json()["id"] == bill_id

    @pytest.mark.skip(reason="API returning 500 instead of 404 - needs investigation")
    def test_get_bill_not_found(self, test_client):
        """Test getting a bill that doesn't exist"""
        response = test_client.get("/api/v1/bills/00000000-0000-0000-0000-000000000000")
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()

    def test_update_bill(self, test_client, test_db_session, sample_bill_data):
        """Test updating a bill"""
        # Create a bill first
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        # Update the bill
        update_data = {
            "title": "Updated Bill Title",
            "summary": "This is an updated summary",
            "status": "committee"
        }

        response = test_client.put(f"/api/v1/bills/{bill.id}", json=update_data)
        assert response.status_code == 200

        data = response.json()
        assert data["title"] == update_data["title"]
        assert data["summary"] == update_data["summary"]
        assert data["status"] == update_data["status"]
        assert data["bill_number"] == sample_bill_data["bill_number"]  # Unchanged field

    @pytest.mark.skip(reason="API returning 500 instead of 404 - needs investigation") 
    def test_delete_bill(self, test_client, test_db_session, sample_bill_data):
        """Test deleting a bill"""
        # Create a bill first
        bill = Bill(**sample_bill_data)
        test_db_session.add(bill)
        test_db_session.commit()

        # Delete the bill
        response = test_client.delete(f"/api/v1/bills/{bill.id}")
        assert response.status_code == 200
        assert "deleted successfully" in response.json()["message"].lower()

        # Verify bill was deleted
        response = test_client.get(f"/api/v1/bills/{bill.id}")
        assert response.status_code == 404

    def test_search_bills_by_title(self, test_client, test_db_session):
        """Test searching bills by title"""
        # Create test bills
        bills = [
            Bill(
                title="Healthcare Reform Act",
                description="A bill about healthcare reform",
                bill_number="HR-101",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.INTRODUCED,
                session_year=2024,
                chamber="house",
                state="federal"
            ),
            Bill(
                title="Education Funding Bill",
                description="A bill about education funding",
                bill_number="S-202",
                bill_type=BillType.SENATE_BILL,
                status=BillStatus.INTRODUCED,
                session_year=2024,
                chamber="senate",
                state="federal"
            ),
            Bill(
                title="Infrastructure Investment Act",
                description="A bill about infrastructure",
                bill_number="HR-303",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.COMMITTEE,
                session_year=2024,
                chamber="house",
                state="federal"
            )
        ]

        for bill in bills:
            test_db_session.add(bill)
        test_db_session.commit()

        # Search for "Healthcare"
        response = test_client.get("/api/v1/bills/search?query=Healthcare")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Healthcare Reform Act"

        # Search for "Funding" (should match one bill)
        response = test_client.get("/api/v1/bills/search?query=Funding")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Education Funding Bill"

    def test_search_bills_by_status(self, test_client, test_db_session):
        """Test searching bills by status"""
        # Create test bills with different statuses
        bills = [
            Bill(
                title="Bill 1",
                bill_number="HR-1",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.INTRODUCED,
                session_year=2024,
                chamber="house",
                state="federal"
            ),
            Bill(
                title="Bill 2",
                bill_number="HR-2",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.COMMITTEE,
                session_year=2024,
                chamber="house",
                state="federal"
            ),
            Bill(
                title="Bill 3",
                bill_number="HR-3",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.PASSED,
                session_year=2024,
                chamber="house",
                state="federal"
            )
        ]

        for bill in bills:
            test_db_session.add(bill)
        test_db_session.commit()

        # Search by status
        response = test_client.get("/api/v1/bills/search?status=committee")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["status"] == "committee"

    def test_get_bills_by_status_endpoint(self, test_client, test_db_session):
        """Test the get bills by status endpoint"""
        # Create test bills
        bills = [
            Bill(
                title="Introduced Bill",
                bill_number="HR-1",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.INTRODUCED,
                session_year=2024,
                chamber="house",
                state="federal"
            ),
            Bill(
                title="Committee Bill",
                bill_number="HR-2",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.COMMITTEE,
                session_year=2024,
                chamber="house",
                state="federal"
            )
        ]

        for bill in bills:
            test_db_session.add(bill)
        test_db_session.commit()

        response = test_client.get("/api/v1/bills/status/introduced")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Introduced Bill"

    def test_get_bills_by_type_endpoint(self, test_client, test_db_session):
        """Test the get bills by type endpoint"""
        # Create test bills
        bills = [
            Bill(
                title="House Bill",
                bill_number="HR-1",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.INTRODUCED,
                session_year=2024,
                chamber="house",
                state="federal"
            ),
            Bill(
                title="Senate Bill",
                bill_number="S-1",
                bill_type=BillType.SENATE_BILL,
                status=BillStatus.INTRODUCED,
                session_year=2024,
                chamber="senate",
                state="federal"
            )
        ]

        for bill in bills:
            test_db_session.add(bill)
        test_db_session.commit()

        response = test_client.get("/api/v1/bills/type/house_bill")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "House Bill"

    def test_get_featured_bills(self, test_client, test_db_session):
        """Test getting featured bills"""
        # Create test bills
        bills = [
            Bill(
                title="Featured Bill",
                bill_number="HR-1",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.INTRODUCED,
                session_year=2024,
                chamber="house",
                state="federal",
                is_featured=True,
                priority_score=100
            ),
            Bill(
                title="Regular Bill",
                bill_number="HR-2",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.INTRODUCED,
                session_year=2024,
                chamber="house",
                state="federal",
                is_featured=False,
                priority_score=50
            )
        ]

        for bill in bills:
            test_db_session.add(bill)
        test_db_session.commit()

        response = test_client.get("/api/v1/bills/featured")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "Featured Bill"
        assert data[0]["is_featured"] is True

    def test_get_bills_by_session_year(self, test_client, test_db_session):
        """Test getting bills by session year"""
        # Create test bills
        bills = [
            Bill(
                title="2024 Bill",
                bill_number="HR-1",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.INTRODUCED,
                session_year=2024,
                chamber="house",
                state="federal"
            ),
            Bill(
                title="2023 Bill",
                bill_number="HR-2",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.INTRODUCED,
                session_year=2023,
                chamber="house",
                state="federal"
            )
        ]

        for bill in bills:
            test_db_session.add(bill)
        test_db_session.commit()

        response = test_client.get("/api/v1/bills/session/2024")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["title"] == "2024 Bill"

    def test_get_bills_by_sponsor(self, test_client, test_db_session):
        """Test getting bills by sponsor"""
        # Create test bills
        bills = [
            Bill(
                title="Smith Bill",
                bill_number="HR-1",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.INTRODUCED,
                session_year=2024,
                chamber="house",
                state="federal",
                sponsor_name="John Smith"
            ),
            Bill(
                title="Jones Bill",
                bill_number="HR-2",
                bill_type=BillType.HOUSE_BILL,
                status=BillStatus.INTRODUCED,
                session_year=2024,
                chamber="house",
                state="federal",
                sponsor_name="Jane Jones"
            )
        ]

        for bill in bills:
            test_db_session.add(bill)
        test_db_session.commit()

        response = test_client.get("/api/v1/bills/sponsor/Smith")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["sponsor_name"] == "John Smith"

    def test_get_bill_by_external_id(self, test_client, test_db_session):
        """Test getting bill by external ID"""
        # Create a bill with external IDs
        bill = Bill(
            title="External Bill",
            bill_number="HR-1",
            bill_type=BillType.HOUSE_BILL,
            status=BillStatus.INTRODUCED,
            session_year=2024,
            chamber="house",
            state="federal",
            openstates_id="ocd-bill/12345",
            congress_gov_id="hr1-117"
        )
        test_db_session.add(bill)
        test_db_session.commit()

        # Test openstates ID lookup
        response = test_client.get("/api/v1/bills/external/openstates/ocd-bill/12345")
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == "External Bill"

        # Test congress_gov ID lookup
        response = test_client.get("/api/v1/bills/external/congress_gov/hr1-117")
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == "External Bill"

    def test_invalid_external_id_type(self, test_client):
        """Test invalid external ID type"""
        response = test_client.get("/api/v1/bills/external/invalid_type/12345")
        assert response.status_code == 400
        assert "Invalid ID type" in response.json()["detail"]

    def test_external_id_not_found(self, test_client):
        """Test external ID not found"""
        response = test_client.get("/api/v1/bills/external/openstates/nonexistent")
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()

    def test_create_bill_with_duplicate_external_id(self, test_client, test_db_session, sample_bill_data):
        """Test creating bill with duplicate external ID"""
        # Create a bill with external ID
        bill = Bill(**sample_bill_data, openstates_id="ocd-bill/duplicate")
        test_db_session.add(bill)
        test_db_session.commit()

        # Try to create another bill with same external ID
        duplicate_data = sample_bill_data.copy()
        duplicate_data["bill_number"] = "HR-999"
        duplicate_data["openstates_id"] = "ocd-bill/duplicate"

        response = test_client.post("/api/v1/bills/", json=duplicate_data)
        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]

    def test_update_bill_not_found(self, test_client):
        """Test updating a bill that doesn't exist"""
        update_data = {"title": "Updated Title"}
        response = test_client.put("/api/v1/bills/00000000-0000-0000-0000-000000000000", json=update_data)
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()

    def test_delete_bill_not_found(self, test_client):
        """Test deleting a bill that doesn't exist"""
        response = test_client.delete("/api/v1/bills/00000000-0000-0000-0000-000000000000")
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()

    def test_get_bills_count(self, test_client, test_db_session, sample_bill_data):
        """Test getting bills count"""
        # Initially should be 0
        response = test_client.get("/api/v1/bills/stats/count")
        assert response.status_code == 200
        assert response.json()["total_bills"] == 0

        # Add some bills
        for i in range(3):
            bill_data = sample_bill_data.copy()
            bill_data["bill_number"] = f"HR-{i+1}"
            bill = Bill(**bill_data)
            test_db_session.add(bill)
        test_db_session.commit()

        # Should now be 3
        response = test_client.get("/api/v1/bills/stats/count")
        assert response.status_code == 200
        assert response.json()["total_bills"] == 3

    def test_pagination(self, test_client, test_db_session, sample_bill_data):
        """Test pagination functionality"""
        # Create 25 bills
        for i in range(25):
            bill_data = sample_bill_data.copy()
            bill_data["bill_number"] = f"HR-{i+1:03d}"
            bill_data["title"] = f"Test Bill {i+1}"
            bill = Bill(**bill_data)
            test_db_session.add(bill)
        test_db_session.commit()

        # Test first page (default limit is 20)
        response = test_client.get("/api/v1/bills/")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 20

        # Test second page
        response = test_client.get("/api/v1/bills/?skip=20&limit=10")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 5  # Remaining 5 bills

    def test_create_bill_with_tags_and_categories(self, test_client):
        """Test creating bill with tags and categories"""
        bill_data = {
            "title": "Tagged Bill",
            "bill_number": "HR-123",
            "bill_type": "house_bill",
            "status": "introduced",
            "session_year": 2024,
            "chamber": "house",
            "state": "federal",
            "tags": ["healthcare", "reform", "urgent"],
            "categories": ["health", "policy"]
        }

        response = test_client.post("/api/v1/bills/", json=bill_data)
        assert response.status_code == 201

        data = response.json()
        assert data["title"] == "Tagged Bill"
        # Note: tags and categories are stored as JSON strings in the database
        # but should be returned as arrays in the API response

    def test_create_bill_schedules_ai_summary_generation(self, test_client):
        """Test that creating a bill schedules AI summary generation"""
        from unittest.mock import patch, MagicMock

        # Mock the background tasks
        with patch('app.api.v1.endpoints.bills.BackgroundTasks'):
            mock_background_tasks = MagicMock()

            # Override the dependency to use our mock

            def mock_get_background_tasks():
                return mock_background_tasks

            bill_data = {
                "title": "Test Bill with Full Text",
                "bill_number": "HR-999",
                "bill_type": "house_bill",
                "status": "introduced",
                "session_year": 2024,
                "chamber": "house",
                "state": "federal",
                "full_text": "This is a comprehensive bill with full text that should trigger AI summarization.",
                "is_featured": False,
                "priority_score": 0
            }

            # We need to mock the BackgroundTasks in the actual endpoint
            with patch('app.api.v1.endpoints.bills.task_generate_summary_for_bill'):
                response = test_client.post("/api/v1/bills/", json=bill_data)

                # Should create the bill successfully
                assert response.status_code == 201

                # The background task should have been called
                # Note: This is a simplified test - in real scenarios, we'd need more complex mocking
                # to verify the BackgroundTasks.add_task was called correctly

                data = response.json()
                assert data["title"] == "Test Bill with Full Text"
                assert "id" in data

    def test_regenerate_ai_summary_endpoint(self, test_client, test_db_session):
        """Test the regenerate AI summary endpoint"""
        from app.models.bill import Bill, BillStatus, BillType

        # Create a bill first
        bill = Bill(
            title="Test Bill",
            bill_number="HR-REGEN-1",
            bill_type=BillType.HOUSE_BILL,
            status=BillStatus.INTRODUCED,
            session_year=2024,
            chamber="house",
            state="federal",
            full_text="This is the full text of the bill.",
            ai_summary="Old summary"
        )
        test_db_session.add(bill)
        test_db_session.commit()

        # Test regenerate endpoint
        response = test_client.post(f"/api/v1/bills/{bill.id}/regenerate-summary")
        assert response.status_code == 200

        data = response.json()
        assert data["message"] == "AI summary regeneration scheduled"
        assert data["bill_id"] == str(bill.id)

    def test_ai_summary_status_endpoint(self, test_client, test_db_session):
        """Test the AI summary status endpoint"""
        from app.models.bill import Bill, BillStatus, BillType

        # Create a bill with AI summary
        bill = Bill(
            title="Test Bill",
            bill_number="HR-STATUS-1",
            bill_type=BillType.HOUSE_BILL,
            status=BillStatus.INTRODUCED,
            session_year=2024,
            chamber="house",
            state="federal",
            full_text="This is the full text of the bill.",
            ai_summary="This is the AI-generated summary."
        )
        test_db_session.add(bill)
        test_db_session.commit()

        # Test status endpoint
        response = test_client.get(f"/api/v1/bills/{bill.id}/ai-summary-status")
        assert response.status_code == 200

        data = response.json()
        assert data["bill_id"] == str(bill.id)
        assert data["has_ai_summary"] is True
        assert data["has_full_text"] is True
        assert data["ai_summary_length"] > 0
        assert data["full_text_length"] > 0
        assert data["ai_summary_preview"] is not None
        assert data["can_generate_summary"] is False  # Already has summary

    def test_ai_summary_status_no_summary(self, test_client, test_db_session):
        """Test AI summary status for bill without summary"""
        from app.models.bill import Bill, BillStatus, BillType

        # Create a bill without AI summary
        bill = Bill(
            title="Test Bill",
            bill_number="HR-NO-SUMMARY-1",
            bill_type=BillType.HOUSE_BILL,
            status=BillStatus.INTRODUCED,
            session_year=2024,
            chamber="house",
            state="federal",
            full_text="This is the full text of the bill."
        )
        test_db_session.add(bill)
        test_db_session.commit()

        # Test status endpoint
        response = test_client.get(f"/api/v1/bills/{bill.id}/ai-summary-status")
        assert response.status_code == 200

        data = response.json()
        assert data["bill_id"] == str(bill.id)
        assert data["has_ai_summary"] is False
        assert data["has_full_text"] is True
        assert data["ai_summary_length"] == 0
        assert data["full_text_length"] > 0
        assert data["ai_summary_preview"] is None
        assert data["can_generate_summary"] is True  # Can generate summary

    def test_ai_summary_status_no_full_text(self, test_client, test_db_session):
        """Test AI summary status for bill without full text"""
        from app.models.bill import Bill, BillStatus, BillType

        # Create a bill without full text
        bill = Bill(
            title="Test Bill",
            bill_number="HR-NO-TEXT-1",
            bill_type=BillType.HOUSE_BILL,
            status=BillStatus.INTRODUCED,
            session_year=2024,
            chamber="house",
            state="federal"
        )
        test_db_session.add(bill)
        test_db_session.commit()

        # Test status endpoint
        response = test_client.get(f"/api/v1/bills/{bill.id}/ai-summary-status")
        assert response.status_code == 200

        data = response.json()
        assert data["bill_id"] == str(bill.id)
        assert data["has_ai_summary"] is False
        assert data["has_full_text"] is False
        assert data["ai_summary_length"] == 0
        assert data["full_text_length"] == 0
        assert data["ai_summary_preview"] is None
        assert data["can_generate_summary"] is False  # Cannot generate without full text
