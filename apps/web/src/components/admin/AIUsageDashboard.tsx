import React, { useState, useEffect } from 'react';
import { 
  CurrencyDollarIcon, 
  CpuChipIcon, 
  ClockIcon, 
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface AIUsageStats {
  overall: {
    total_requests: number;
    total_cost: number;
    total_tokens: number;
    avg_cost_per_request: number;
    avg_tokens_per_request: number;
  };
  by_operation: Array<{
    operation_type: string;
    requests: number;
    total_cost: number;
    total_tokens: number;
    avg_response_time_ms: number;
  }>;
  by_model: Array<{
    model_name: string;
    requests: number;
    total_cost: number;
    total_tokens: number;
  }>;
}

interface DailySummary {
  period_days: number;
  total_cost: number;
  total_tokens: number;
  total_requests: number;
  bills_processed: number;
  avg_cost_per_day: number;
  avg_cost_per_bill: number;
  daily_breakdown: Array<{
    date: string;
    cost: number;
    tokens: number;
    requests: number;
    bills: number;
  }>;
}

export function AIUsageDashboard() {
  const [stats, setStats] = useState<AIUsageStats | null>(null);
  const [dailySummary, setDailySummary] = useState<DailySummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchUsageData();
  }, []);

  const fetchUsageData = async () => {
    try {
      setLoading(true);

      // Use the proper API client with correct base URL
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

      // Fetch overall stats
      const statsResponse = await fetch(`${apiUrl}/ai-usage/usage/stats`);
      const statsData = await statsResponse.json();

      // Fetch daily summary
      const summaryResponse = await fetch(`${apiUrl}/ai-usage/usage/daily-summary?days=30`);
      const summaryData = await summaryResponse.json();

      if (statsData.success) {
        setStats(statsData.data);
      }

      if (summaryData.success) {
        setDailySummary(summaryData.data);
      }

    } catch (err) {
      setError('Failed to fetch AI usage data');
      console.error('AI usage fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center gap-2 text-red-600">
          <ExclamationTriangleIcon className="h-5 w-5" />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount: number) => `$${amount.toFixed(4)}`;
  const formatNumber = (num: number) => num.toLocaleString();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">AI Usage & Cost Monitoring</h2>
            <p className="text-gray-600 mt-1">Track token usage, costs, and performance across all AI operations</p>
          </div>
          <button
            onClick={fetchUsageData}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Refresh Data
          </button>
        </div>
      </div>

      {/* Overall Stats */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <CurrencyDollarIcon className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Cost</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.overall.total_cost)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <CpuChipIcon className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Tokens</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.overall.total_tokens)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <ChartBarIcon className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Requests</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.overall.total_requests)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <CurrencyDollarIcon className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Avg Cost/Request</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.overall.avg_cost_per_request)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                <CpuChipIcon className="h-5 w-5 text-indigo-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Avg Tokens/Request</p>
                <p className="text-2xl font-bold text-gray-900">{Math.round(stats.overall.avg_tokens_per_request)}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Daily Summary */}
      {dailySummary && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">30-Day Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(dailySummary.total_cost)}</p>
              <p className="text-sm text-gray-600">Total Cost (30 days)</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{dailySummary.bills_processed}</p>
              <p className="text-sm text-gray-600">Bills Processed</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(dailySummary.avg_cost_per_day)}</p>
              <p className="text-sm text-gray-600">Avg Cost/Day</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(dailySummary.avg_cost_per_bill)}</p>
              <p className="text-sm text-gray-600">Avg Cost/Bill</p>
            </div>
          </div>
          
          {/* Recent Daily Breakdown */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tokens</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requests</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bills</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {dailySummary.daily_breakdown.slice(0, 7).map((day, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{day.date}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(day.cost)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatNumber(day.tokens)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{day.requests}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{day.bills}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Operation Breakdown */}
      {stats && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">By Operation Type</h3>
            <div className="space-y-3">
              {stats.by_operation.map((op, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{op.operation_type}</p>
                    <p className="text-sm text-gray-600">{op.requests} requests</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">{formatCurrency(op.total_cost)}</p>
                    <p className="text-sm text-gray-600">{formatNumber(op.total_tokens)} tokens</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">By Model</h3>
            <div className="space-y-3">
              {stats.by_model.map((model, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{model.model_name}</p>
                    <p className="text-sm text-gray-600">{model.requests} requests</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">{formatCurrency(model.total_cost)}</p>
                    <p className="text-sm text-gray-600">{formatNumber(model.total_tokens)} tokens</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
