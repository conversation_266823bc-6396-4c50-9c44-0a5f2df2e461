# Environment Quick Start Guide

## 🚀 Quick Setup

### 1. Set Up Development Environment
```bash
# Load development configuration
npm run env:dev

# Start both API and web servers
cd apps/api && poetry run uvicorn app.main:app --reload &
cd apps/web && npm run dev
```

### 2. Set Up Staging Environment
```bash
# Load staging configuration
npm run env:staging

# Deploy to AWS (requires AWS CLI configured)
cd infrastructure
cdk deploy ModernAction-staging
```

### 3. Set Up Production Environment
```bash
# Load production configuration  
npm run env:prod

# Deploy to AWS (requires AWS CLI configured)
cd infrastructure
cdk deploy ModernAction-prod
```

## 🔧 Configuration Required

### Auth0 Setup (Required)
1. Create 3 separate Auth0 tenants:
   - `dev-modernaction.us.auth0.com`
   - `staging-modernaction.us.auth0.com` 
   - `prod-modernaction.us.auth0.com`

2. Update environment files with Auth0 credentials:
   - `.env.development`
   - `.env.staging` 
   - `.env.production`
   - `apps/web/.env.development`
   - `apps/web/.env.staging`
   - `apps/web/.env.production`

### Database Setup
- **Development**: Use local PostgreSQL or staging DB
- **Staging**: Already configured (AWS RDS)
- **Production**: Create separate AWS RDS instance

### API Keys (Update in environment files)
- OpenStates API
- Google Civic Info API
- Congress.gov API
- Action Network API
- AWS credentials

## 🔄 Environment Switching

### Quick Commands
```bash
# Switch to development
npm run env:dev

# Switch to staging  
npm run env:staging

# Switch to production
npm run env:prod
```

### Manual Switching
```bash
# Copy specific environment files
./scripts/load-env.sh development
./scripts/load-env.sh staging
./scripts/load-env.sh production
```

## ✅ Testing Each Environment

### Development
```bash
npm run env:dev
cd apps/api && poetry run uvicorn app.main:app --reload
cd apps/web && npm run dev
# Test: http://localhost:3000
```

### Staging
```bash
npm run env:staging
cd infrastructure && cdk deploy ModernAction-staging
# Test: https://staging.modernaction.io
```

### Production
```bash
npm run env:prod
cd infrastructure && cdk deploy ModernAction-prod
# Test: https://modernaction.io
```

## 🚨 Important Notes

1. **Never commit environment files** - They're in .gitignore for security
2. **Use strong, unique secrets** for each environment
3. **Test staging before production** deployments
4. **Monitor logs** in production for issues

## 🐛 Troubleshooting

### Auth0 Issues
- Verify correct domain in environment files
- Check callback URLs match environment
- Ensure audience matches API identifier

### Database Issues  
- Verify database credentials and connectivity
- Run migrations: `cd apps/api && poetry run alembic upgrade head`
- Check AWS RDS security groups for staging/production

### API Issues
- Check environment variables are loaded: `echo $ENVIRONMENT`
- Verify external API keys are valid
- Check logs: `cd apps/api && poetry run uvicorn app.main:app --reload --log-level debug`

## 📞 Support
- Check logs in `apps/api/logs/` and browser console
- Verify environment variables are loaded correctly
- Test API endpoints individually with curl or Postman