# app/services/bills.py
from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import or_, func, desc
from app.models.bill import Bill, BillStatus, BillType
from app.schemas.bill import BillCreate, BillUpdate

import json
import logging

logger = logging.getLogger(__name__)

class BillService:
    """Service layer for bill operations"""

    def __init__(self, db: Session, background_tasks=None):
        self.db = db
        self.background_tasks = background_tasks

    def _deserialize_json_fields(self, bill: Bill) -> None:
        """Convert JSON string fields back to Python objects for API responses"""
        if bill.tags and isinstance(bill.tags, str):
            try:
                parsed = json.loads(bill.tags)
                # CRITICAL: Ensure we always return a list, even if DB has corrupted data
                bill.tags = parsed if isinstance(parsed, list) else []
            except (json.JSONDecodeError, TypeError):
                bill.tags = []

        if bill.categories and isinstance(bill.categories, str):
            try:
                parsed = json.loads(bill.categories)
                # CRITICAL: Ensure we always return a list, even if DB has corrupted data
                bill.categories = parsed if isinstance(parsed, list) else []
            except (json.JSONDecodeError, TypeError):
                bill.categories = []

        # Deserialize AI-generated analysis fields (Sprint A)
        if bill.reasons_for_support and isinstance(bill.reasons_for_support, str):
            try:
                parsed = json.loads(bill.reasons_for_support)
                # CRITICAL: Ensure we always return a list, even if DB has corrupted data
                bill.reasons_for_support = parsed if isinstance(parsed, list) else []
            except (json.JSONDecodeError, TypeError):
                bill.reasons_for_support = []

        if bill.reasons_for_opposition and isinstance(bill.reasons_for_opposition, str):
            try:
                parsed = json.loads(bill.reasons_for_opposition)
                # CRITICAL: Ensure we always return a list, even if DB has corrupted data
                bill.reasons_for_opposition = parsed if isinstance(parsed, list) else []
            except (json.JSONDecodeError, TypeError):
                bill.reasons_for_opposition = []

    def _deserialize_json_fields_list(self, bills: List[Bill]) -> None:
        """Convert JSON string fields back to Python objects for a list of bills"""
        for bill in bills:
            self._deserialize_json_fields(bill)

    def get_bill(self, bill_id: str) -> Optional[Bill]:
        """Get a single bill by ID"""
        bill = self.db.query(Bill).filter(Bill.id == bill_id).first()
        if bill:
            self._deserialize_json_fields(bill)
        return bill

    def get_bills(self, skip: int = 0, limit: int = 20) -> List[Bill]:
        """Get a list of bills with pagination"""
        bills = (
            self.db.query(Bill)
            .order_by(desc(Bill.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(bills)
        return bills

    def create_bill(self, bill_data: BillCreate) -> Bill:
        """Create a new bill"""
        # Convert lists to JSON strings for database storage
        bill_dict = bill_data.model_dump()

        # Handle JSON fields
        if bill_dict.get('tags'):
            bill_dict['tags'] = json.dumps(bill_dict['tags'])
        if bill_dict.get('categories'):
            bill_dict['categories'] = json.dumps(bill_dict['categories'])

        # Handle AI-generated analysis JSON fields (Sprint A)
        # These come pre-serialized from BillDataService but handle list input too
        if bill_dict.get('reasons_for_support') and isinstance(bill_dict['reasons_for_support'], list):
            bill_dict['reasons_for_support'] = json.dumps(bill_dict['reasons_for_support'])
        if bill_dict.get('reasons_for_opposition') and isinstance(bill_dict['reasons_for_opposition'], list):
            bill_dict['reasons_for_opposition'] = json.dumps(bill_dict['reasons_for_opposition'])

        bill = Bill(**bill_dict)
        self.db.add(bill)
        self.db.commit()
        self.db.refresh(bill)

        # Schedule values analysis as background task (non-blocking)
        # This follows our established pattern for long-running operations
        if hasattr(self, 'background_tasks') and self.background_tasks:
            from app.tasks.values_analysis_tasks import task_analyze_bill_values
            self.background_tasks.add_task(task_analyze_bill_values, bill.id)
            logger.info(f"Values analysis scheduled for new bill {bill.id}")
        else:
            logger.warning(f"No background tasks available, values analysis not scheduled for bill {bill.id}")

        # Convert JSON strings back to lists for API response
        self._deserialize_json_fields(bill)
        return bill

    def update_bill(self, bill_id: str, bill_data: BillUpdate) -> Optional[Bill]:
        """Update an existing bill"""
        bill = self.db.query(Bill).filter(Bill.id == bill_id).first()
        if not bill:
            return None

        # Get only the fields that were provided (not None)
        update_data = bill_data.model_dump(exclude_unset=True)

        # Handle JSON fields
        if 'tags' in update_data and update_data['tags'] is not None:
            update_data['tags'] = json.dumps(update_data['tags'])
        if 'categories' in update_data and update_data['categories'] is not None:
            update_data['categories'] = json.dumps(update_data['categories'])

        # Update the bill with provided data
        for field, value in update_data.items():
            setattr(bill, field, value)

        self.db.commit()
        self.db.refresh(bill)
        self._deserialize_json_fields(bill)
        return bill

    def delete_bill(self, bill_id: str) -> bool:
        """Delete a bill and all related records"""
        try:
            bill = self.get_bill(bill_id)
            if not bill:
                return False

            # Delete related records first to avoid foreign key constraint issues
            # Delete AI usage logs
            from app.models.ai_usage import AIUsageLog
            self.db.query(AIUsageLog).filter(AIUsageLog.bill_id == bill_id).delete()
            
            # Delete bill details
            from app.models.bill_details import BillDetails
            self.db.query(BillDetails).filter(BillDetails.bill_id == bill_id).delete()
            
            # Delete bill values analysis
            try:
                from app.models.bill_values import BillValuesAnalysis
                self.db.query(BillValuesAnalysis).filter(BillValuesAnalysis.bill_id == bill_id).delete()
            except ImportError:
                pass  # bill_values might not exist in all environments
            
            # Delete any actions related to this bill
            try:
                from app.models.action import Action
                self.db.query(Action).filter(Action.bill_id == bill_id).delete()
            except ImportError:
                pass  # actions might not exist in all environments
            
            # Finally delete the bill itself
            self.db.delete(bill)
            self.db.commit()
            return True
            
        except Exception as e:
            self.db.rollback()
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error deleting bill {bill_id}: {e}")
            raise  # Re-raise to let the endpoint handle it

    def search_bills(
        self,
        query: Optional[str] = None,
        bill_type: Optional[BillType] = None,
        status: Optional[BillStatus] = None,
        session_year: Optional[int] = None,
        chamber: Optional[str] = None,
        state: Optional[str] = None,
        sponsor_name: Optional[str] = None,
        is_featured: Optional[bool] = None,
        skip: int = 0,
        limit: int = 20
    ) -> List[Bill]:
        """Search bills based on various criteria"""
        query_builder = self.db.query(Bill)

        # Text search across title, description, and summary
        if query:
            search_term = f"%{query.lower()}%"
            query_builder = query_builder.filter(
                or_(
                    func.lower(Bill.title).contains(search_term),
                    func.lower(Bill.description).contains(search_term),
                    func.lower(Bill.summary).contains(search_term),
                    func.lower(Bill.bill_number).contains(search_term)
                )
            )

        # Filter by bill type
        if bill_type:
            query_builder = query_builder.filter(Bill.bill_type == bill_type)

        # Filter by status
        if status:
            query_builder = query_builder.filter(Bill.status == status)

        # Filter by session year
        if session_year:
            query_builder = query_builder.filter(Bill.session_year == session_year)

        # Filter by chamber
        if chamber:
            query_builder = query_builder.filter(Bill.chamber == chamber)

        # Filter by state
        if state:
            query_builder = query_builder.filter(Bill.state == state)

        # Filter by sponsor name
        if sponsor_name:
            sponsor_term = f"%{sponsor_name.lower()}%"
            query_builder = query_builder.filter(
                func.lower(Bill.sponsor_name).contains(sponsor_term)
            )

        # Filter by featured status
        if is_featured is not None:
            query_builder = query_builder.filter(Bill.is_featured == is_featured)

        bills = (
            query_builder
            .order_by(desc(Bill.priority_score), desc(Bill.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(bills)
        return bills

    def get_bills_by_status(self, status: BillStatus, skip: int = 0, limit: int = 20) -> List[Bill]:
        """Get bills by status"""
        bills = (
            self.db.query(Bill)
            .filter(Bill.status == status)
            .order_by(desc(Bill.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(bills)
        return bills

    def get_bills_by_type(self, bill_type: BillType, skip: int = 0, limit: int = 20) -> List[Bill]:
        """Get bills by type"""
        bills = (
            self.db.query(Bill)
            .filter(Bill.bill_type == bill_type)
            .order_by(desc(Bill.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(bills)
        return bills

    def get_featured_bills(self, skip: int = 0, limit: int = 20) -> List[Bill]:
        """Get featured bills"""
        bills = (
            self.db.query(Bill)
            .filter(Bill.is_featured == True)  # noqa: E712
            .order_by(desc(Bill.priority_score), desc(Bill.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(bills)
        return bills

    def get_bills_by_session_year(self, session_year: int, skip: int = 0, limit: int = 20) -> List[Bill]:
        """Get bills by session year"""
        bills = (
            self.db.query(Bill)
            .filter(Bill.session_year == session_year)
            .order_by(desc(Bill.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(bills)
        return bills

    def get_bill_by_external_id(self, external_id: str, id_type: str) -> Optional[Bill]:
        """Get bill by external ID (openstates_id, congress_gov_id)"""
        bill = None
        if id_type == "openstates":
            bill = self.db.query(Bill).filter(Bill.openstates_id == external_id).first()
        elif id_type == "congress_gov":
            bill = self.db.query(Bill).filter(Bill.congress_gov_id == external_id).first()

        if bill:
            self._deserialize_json_fields(bill)
        return bill

    def get_bills_count(self) -> int:
        """Get total count of bills"""
        return self.db.query(Bill).count()

    def get_bills_by_sponsor(self, sponsor_name: str, skip: int = 0, limit: int = 20) -> List[Bill]:
        """Get bills by sponsor name"""
        sponsor_term = f"%{sponsor_name.lower()}%"
        bills = (
            self.db.query(Bill)
            .filter(func.lower(Bill.sponsor_name).contains(sponsor_term))
            .order_by(desc(Bill.created_at))
            .offset(skip)
            .limit(limit)
            .all()
        )
        self._deserialize_json_fields_list(bills)
        return bills
