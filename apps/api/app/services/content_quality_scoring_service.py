"""
Content Quality Scoring Service - Consolidated Quality Assessment
Provides unified quality scoring and benchmarking against HR5-118 standards
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class QualityBenchmark(Enum):
    """Quality benchmarks for comparison"""
    HR5118_GOLD = "hr5118_gold"        # HR5-118 gold standard
    INDUSTRY_HIGH = "industry_high"     # High industry standard
    ACCEPTABLE = "acceptable"           # Minimum acceptable
    BASELINE = "baseline"              # Basic quality threshold

@dataclass
class QualityScore:
    """Unified quality score with breakdown"""
    overall_score: float               # 0-100 scale
    category_scores: Dict[str, float]  # Individual category scores
    benchmark_comparison: Dict[str, bool]  # How we compare to benchmarks
    strengths: List[str]               # What we're doing well
    weaknesses: List[str]              # What needs improvement
    grade: str                         # Letter grade (A, B, C, D, F)
    percentile: float                  # Percentile ranking

class ContentQualityScoringService:
    """
    Unified content quality scoring system that consolidates all quality metrics
    into a single, comprehensive score with detailed breakdown
    """
    
    def __init__(self):
        # Benchmark thresholds (0-1 scale)
        self.benchmarks = {
            QualityBenchmark.HR5118_GOLD: {
                'overall': 0.9,
                'specificity': 0.85,
                'evidence_grounding': 0.9,
                'comprehensiveness': 0.8,
                'clarity': 0.85,
                'actionability': 0.8
            },
            QualityBenchmark.INDUSTRY_HIGH: {
                'overall': 0.8,
                'specificity': 0.75,
                'evidence_grounding': 0.8,
                'comprehensiveness': 0.7,
                'clarity': 0.75,
                'actionability': 0.7
            },
            QualityBenchmark.ACCEPTABLE: {
                'overall': 0.65,
                'specificity': 0.6,
                'evidence_grounding': 0.65,
                'comprehensiveness': 0.6,
                'clarity': 0.6,
                'actionability': 0.6
            },
            QualityBenchmark.BASELINE: {
                'overall': 0.4,
                'specificity': 0.4,
                'evidence_grounding': 0.4,
                'comprehensiveness': 0.4,
                'clarity': 0.4,
                'actionability': 0.4
            }
        }
        
        # Category weights for overall score
        self.category_weights = {
            'evidence_grounding': 0.25,    # Most important
            'specificity': 0.25,           # Second most important
            'comprehensiveness': 0.20,     # Coverage of key areas
            'clarity': 0.15,               # Understandability
            'actionability': 0.15          # Usefulness for citizens
        }
    
    def score_content_quality(self, quality_metrics: Dict[str, Any], 
                            evidence_quality: Dict[str, Any]) -> QualityScore:
        """
        Generate comprehensive quality score from all quality metrics
        
        Args:
            quality_metrics: Quality metrics from QualityValidationService
            evidence_quality: Evidence quality metrics from EvidenceQualityService
            
        Returns:
            QualityScore with detailed breakdown and benchmarking
        """
        
        # Extract component scores (0-1 scale)
        component_scores = {
            'specificity': quality_metrics.get('specificity_score', 0.0),
            'evidence_grounding': quality_metrics.get('evidence_grounding_score', 0.0),
            'comprehensiveness': quality_metrics.get('comprehensiveness_score', 0.0),
            'clarity': quality_metrics.get('clarity_score', 0.0),
            'actionability': quality_metrics.get('actionability_score', 0.0)
        }
        
        # Add evidence quality factor
        evidence_avg = evidence_quality.get('average_quality_score', 0.0)
        component_scores['evidence_grounding'] = (
            component_scores['evidence_grounding'] * 0.7 + evidence_avg * 0.3
        )
        
        # Calculate weighted overall score
        overall_score = sum(
            score * self.category_weights[category] 
            for category, score in component_scores.items()
        )
        
        # Convert to 0-100 scale
        overall_score_100 = overall_score * 100
        category_scores_100 = {k: v * 100 for k, v in component_scores.items()}
        
        # Compare against benchmarks
        benchmark_comparison = self._compare_to_benchmarks(component_scores, overall_score)
        
        # Identify strengths and weaknesses
        strengths, weaknesses = self._identify_strengths_weaknesses(
            component_scores, quality_metrics, evidence_quality
        )
        
        # Assign letter grade
        grade = self._assign_letter_grade(overall_score)
        
        # Calculate percentile (simplified - in real system would use historical data)
        percentile = self._calculate_percentile(overall_score)
        
        return QualityScore(
            overall_score=overall_score_100,
            category_scores=category_scores_100,
            benchmark_comparison=benchmark_comparison,
            strengths=strengths,
            weaknesses=weaknesses,
            grade=grade,
            percentile=percentile
        )
    
    def get_quality_recommendations(self, quality_score: QualityScore) -> List[str]:
        """Get specific recommendations based on quality score"""
        
        recommendations = []
        
        # Overall score recommendations
        if quality_score.overall_score < 65:
            recommendations.append("🚨 CRITICAL: Overall quality below acceptable standards - comprehensive review needed")
        elif quality_score.overall_score < 80:
            recommendations.append("⚠️ Quality improvement needed to reach industry standards")
        
        # Category-specific recommendations
        for category, score in quality_score.category_scores.items():
            if score < 60:
                recommendations.append(f"📊 {category.replace('_', ' ').title()}: Score {score:.0f}/100 - immediate attention required")
            elif score < 75:
                recommendations.append(f"📈 {category.replace('_', ' ').title()}: Score {score:.0f}/100 - has room for improvement")
        
        # Benchmark-based recommendations
        if not quality_score.benchmark_comparison.get(QualityBenchmark.HR5118_GOLD.value, False):
            recommendations.append("🏆 Focus on achieving HR5-118 gold standard requirements")
        
        if not quality_score.benchmark_comparison.get(QualityBenchmark.INDUSTRY_HIGH.value, False):
            recommendations.append("📊 Work towards industry high standards for competitive quality")
        
        # Strength-based recommendations
        if quality_score.strengths:
            recommendations.append(f"✅ Continue leveraging strengths in: {', '.join(quality_score.strengths[:3])}")
        
        return recommendations[:6]  # Limit to 6 most important recommendations
    
    def get_score_summary(self, quality_score: QualityScore) -> Dict[str, Any]:
        """Get comprehensive score summary for reporting"""
        
        return {
            'overall_assessment': {
                'score': quality_score.overall_score,
                'grade': quality_score.grade,
                'percentile': quality_score.percentile,
                'status': self._get_status_description(quality_score.overall_score)
            },
            'category_breakdown': quality_score.category_scores,
            'benchmark_status': {
                'hr5118_compliant': quality_score.benchmark_comparison.get(QualityBenchmark.HR5118_GOLD.value, False),
                'industry_competitive': quality_score.benchmark_comparison.get(QualityBenchmark.INDUSTRY_HIGH.value, False),
                'acceptable_quality': quality_score.benchmark_comparison.get(QualityBenchmark.ACCEPTABLE.value, False)
            },
            'performance_indicators': {
                'strengths_count': len(quality_score.strengths),
                'weaknesses_count': len(quality_score.weaknesses),
                'top_strength': quality_score.strengths[0] if quality_score.strengths else None,
                'main_weakness': quality_score.weaknesses[0] if quality_score.weaknesses else None
            },
            'improvement_priority': self._get_improvement_priority(quality_score)
        }
    
    def _compare_to_benchmarks(self, component_scores: Dict[str, float], 
                             overall_score: float) -> Dict[str, bool]:
        """Compare scores to quality benchmarks"""
        
        comparison = {}
        
        for benchmark, thresholds in self.benchmarks.items():
            # Check if overall score meets benchmark
            overall_meets = overall_score >= thresholds['overall']
            
            # Check if all component scores meet benchmark
            components_meet = all(
                component_scores[category] >= thresholds[category]
                for category in component_scores.keys()
                if category in thresholds
            )
            
            # Must meet both overall and component requirements
            comparison[benchmark.value] = overall_meets and components_meet
        
        return comparison
    
    def _identify_strengths_weaknesses(self, component_scores: Dict[str, float],
                                     quality_metrics: Dict[str, Any],
                                     evidence_quality: Dict[str, Any]) -> Tuple[List[str], List[str]]:
        """Identify specific strengths and weaknesses"""
        
        strengths = []
        weaknesses = []
        
        # Analyze component scores
        for category, score in component_scores.items():
            category_name = category.replace('_', ' ').title()
            
            if score >= 0.85:
                strengths.append(f"Excellent {category_name} ({score*100:.0f}/100)")
            elif score >= 0.75:
                strengths.append(f"Strong {category_name} ({score*100:.0f}/100)")
            elif score < 0.6:
                weaknesses.append(f"Poor {category_name} ({score*100:.0f}/100)")
            elif score < 0.7:
                weaknesses.append(f"Weak {category_name} ({score*100:.0f}/100)")
        
        # Evidence-specific analysis
        evidence_avg = evidence_quality.get('average_quality_score', 0.0)
        if evidence_avg >= 0.8:
            strengths.append("High-quality evidence extraction")
        elif evidence_avg < 0.6:
            weaknesses.append("Low evidence quality")
        
        # Issue-based analysis
        issues = quality_metrics.get('issues', [])
        if len(issues) == 0:
            strengths.append("No significant quality issues")
        elif len(issues) > 3:
            weaknesses.append(f"Multiple quality issues ({len(issues)} identified)")
        
        return strengths[:5], weaknesses[:5]  # Limit to top 5 each
    
    def _assign_letter_grade(self, overall_score: float) -> str:
        """Assign letter grade based on overall score"""
        
        if overall_score >= 0.93:
            return "A+"
        elif overall_score >= 0.90:
            return "A"
        elif overall_score >= 0.87:
            return "A-"
        elif overall_score >= 0.83:
            return "B+"
        elif overall_score >= 0.80:
            return "B"
        elif overall_score >= 0.77:
            return "B-"
        elif overall_score >= 0.73:
            return "C+"
        elif overall_score >= 0.70:
            return "C"
        elif overall_score >= 0.67:
            return "C-"
        elif overall_score >= 0.63:
            return "D+"
        elif overall_score >= 0.60:
            return "D"
        elif overall_score >= 0.57:
            return "D-"
        else:
            return "F"
    
    def _calculate_percentile(self, overall_score: float) -> float:
        """Calculate approximate percentile ranking"""
        
        # Simplified percentile calculation
        # In production, this would use historical data
        if overall_score >= 0.9:
            return 95.0
        elif overall_score >= 0.8:
            return 80.0 + (overall_score - 0.8) * 150  # 80-95th percentile
        elif overall_score >= 0.65:
            return 50.0 + (overall_score - 0.65) * 200  # 50-80th percentile
        elif overall_score >= 0.4:
            return 20.0 + (overall_score - 0.4) * 120   # 20-50th percentile
        else:
            return (overall_score / 0.4) * 20            # 0-20th percentile
    
    def _get_status_description(self, score: float) -> str:
        """Get human-readable status description"""
        
        if score >= 90:
            return "Excellent - Exceeds all quality standards"
        elif score >= 80:
            return "Good - Meets high quality standards"
        elif score >= 65:
            return "Acceptable - Meets minimum quality standards"
        elif score >= 40:
            return "Needs Improvement - Below quality standards"
        else:
            return "Poor - Significant quality issues"
    
    def _get_improvement_priority(self, quality_score: QualityScore) -> str:
        """Get improvement priority level"""
        
        if quality_score.overall_score < 40:
            return "Critical"
        elif quality_score.overall_score < 65:
            return "High"
        elif quality_score.overall_score < 80:
            return "Medium"
        elif quality_score.overall_score < 90:
            return "Low"
        else:
            return "Maintenance"

# Global instance
content_quality_scorer = ContentQualityScoringService()

def get_content_quality_scorer() -> ContentQualityScoringService:
    """Get the global content quality scoring service instance"""
    return content_quality_scorer