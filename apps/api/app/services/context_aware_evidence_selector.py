"""
Context-Aware Evidence Selector - Phase 3 Intelligent Selection
Dynamically selects optimal evidence based on context, goals, and adaptive learning
"""

import logging
import re
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import asyncio
from collections import defaultdict

from app.services.evidence_first_processor import EvidenceSpan, EvidenceType

logger = logging.getLogger(__name__)

class AnalysisGoal(Enum):
    """Different analysis goals requiring different evidence selection"""
    CITIZEN_SUMMARY = "citizen_summary"           # Focus on actionable, understandable evidence
    EXPERT_ANALYSIS = "expert_analysis"           # Include technical and detailed evidence
    LEGISLATIVE_REVIEW = "legislative_review"     # Focus on legal and procedural evidence
    IMPACT_ASSESSMENT = "impact_assessment"       # Focus on consequences and affected parties
    COMPLIANCE_GUIDE = "compliance_guide"         # Focus on requirements and deadlines
    COST_ANALYSIS = "cost_analysis"              # Focus on funding and economic impact

class BillComplexity(Enum):
    """Bill complexity levels affecting evidence selection"""
    SIMPLE = "simple"           # Clear, single-purpose bills
    MODERATE = "moderate"       # Bills with multiple related provisions
    COMPLEX = "complex"         # Comprehensive bills with many sections
    OMNIBUS = "omnibus"         # Large bills covering multiple areas

@dataclass
class SelectionContext:
    """Context for evidence selection"""
    bill_type: str
    bill_complexity: BillComplexity
    primary_topic: str
    analysis_goal: AnalysisGoal
    target_audience: str
    time_constraints: bool          # Whether analysis has time limits
    quality_requirements: str       # 'standard', 'high', 'premium'
    budget_constraints: float       # Available budget for analysis
    user_preferences: Dict[str, Any]

@dataclass
class EvidenceBundle:
    """A curated bundle of evidence for specific analysis needs"""
    core_evidence: List[EvidenceSpan]        # Essential evidence spans
    supporting_evidence: List[EvidenceSpan]  # Additional context evidence
    technical_evidence: List[EvidenceSpan]   # Technical/legal details
    citizen_evidence: List[EvidenceSpan]     # Citizen-relevant evidence
    bundle_score: float                      # Overall bundle quality score
    selection_rationale: str                 # Why this bundle was selected

class ContextAwareEvidenceSelector:
    """
    Advanced evidence selection system that dynamically adapts selection
    based on context, goals, and learned preferences for Phase 3
    """
    
    def __init__(self):
        # Selection strategies for different goals
        self.goal_strategies = self._initialize_goal_strategies()
        
        # Complexity-based selection rules
        self.complexity_rules = self._initialize_complexity_rules()
        
        # Evidence type priorities by context
        self.context_priorities = self._initialize_context_priorities()
        
        # Quality thresholds by requirements
        self.quality_thresholds = self._initialize_quality_thresholds()
        
        # Selection history for adaptive learning
        self.selection_history = []
    
    async def select_context_aware_evidence(self, evidence_spans: List[EvidenceSpan],
                                           bill_metadata: Dict[str, Any],
                                           analysis_requirements: Dict[str, Any]) -> EvidenceBundle:
        """
        Select optimal evidence bundle based on context and requirements
        """
        
        logger.info(f"🎯 Starting context-aware evidence selection for {bill_metadata.get('title', 'Unknown')}")
        
        # Step 1: Create selection context
        context = self._create_selection_context(bill_metadata, analysis_requirements)
        
        # Step 2: Apply goal-specific selection strategy
        goal_filtered = await self._apply_goal_strategy(evidence_spans, context)
        
        # Step 3: Apply complexity-based filtering
        complexity_filtered = await self._apply_complexity_filtering(goal_filtered, context)
        
        # Step 4: Apply audience-specific adaptation
        audience_adapted = await self._apply_audience_adaptation(complexity_filtered, context)
        
        # Step 5: Create optimal evidence bundle
        evidence_bundle = await self._create_evidence_bundle(audience_adapted, context)
        
        # Step 6: Learn from selection for future improvements
        await self._record_selection_for_learning(evidence_bundle, context, bill_metadata)
        
        logger.info(f"✅ Context-aware selection complete: {len(evidence_bundle.core_evidence)} core + {len(evidence_bundle.supporting_evidence)} supporting evidence")
        
        return evidence_bundle
    
    def _initialize_goal_strategies(self) -> Dict[AnalysisGoal, Dict[str, Any]]:
        """Initialize selection strategies for different analysis goals"""
        
        return {
            AnalysisGoal.CITIZEN_SUMMARY: {
                'priority_types': [EvidenceType.FUNDING, EvidenceType.ENFORCEMENT, EvidenceType.IMPACT, EvidenceType.SCOPE],
                'max_evidence': 8,
                'min_actionability': 0.6,
                'focus_areas': ['money', 'penalties', 'effects', 'who_affected'],
                'avoid_technical': True,
                'prefer_specific': True
            },
            
            AnalysisGoal.EXPERT_ANALYSIS: {
                'priority_types': [EvidenceType.AUTHORITY, EvidenceType.MANDATE, EvidenceType.PROCESS, EvidenceType.DEFINITION],
                'max_evidence': 15,
                'min_legal_weight': 0.7,
                'focus_areas': ['authority', 'procedures', 'definitions', 'cross_references'],
                'include_technical': True,
                'prefer_comprehensive': True
            },
            
            AnalysisGoal.LEGISLATIVE_REVIEW: {
                'priority_types': [EvidenceType.MANDATE, EvidenceType.AUTHORITY, EvidenceType.ENFORCEMENT, EvidenceType.TIMELINE],
                'max_evidence': 12,
                'min_legal_weight': 0.8,
                'focus_areas': ['legal_obligations', 'authority', 'enforcement', 'deadlines'],
                'require_citations': True,
                'prefer_specific': True
            },
            
            AnalysisGoal.IMPACT_ASSESSMENT: {
                'priority_types': [EvidenceType.IMPACT, EvidenceType.SCOPE, EvidenceType.FUNDING, EvidenceType.TIMELINE],
                'max_evidence': 10,
                'min_actionability': 0.5,
                'focus_areas': ['consequences', 'affected_parties', 'costs', 'timeline'],
                'prefer_quantified': True,
                'include_scope': True
            },
            
            AnalysisGoal.COMPLIANCE_GUIDE: {
                'priority_types': [EvidenceType.MANDATE, EvidenceType.TIMELINE, EvidenceType.ENFORCEMENT, EvidenceType.PROCESS],
                'max_evidence': 12,
                'min_specificity': 0.7,
                'focus_areas': ['requirements', 'deadlines', 'penalties', 'procedures'],
                'prefer_actionable': True,
                'require_specificity': True
            },
            
            AnalysisGoal.COST_ANALYSIS: {
                'priority_types': [EvidenceType.FUNDING, EvidenceType.ENFORCEMENT, EvidenceType.IMPACT],
                'max_evidence': 8,
                'min_specificity': 0.8,
                'focus_areas': ['appropriations', 'costs', 'financial_impact'],
                'require_quantified': True,
                'prefer_specific': True
            }
        }
    
    def _initialize_complexity_rules(self) -> Dict[BillComplexity, Dict[str, Any]]:
        """Initialize rules based on bill complexity"""
        
        return {
            BillComplexity.SIMPLE: {
                'max_total_evidence': 8,
                'prefer_core_sections': True,
                'avoid_cross_references': True,
                'focus_main_provisions': True
            },
            
            BillComplexity.MODERATE: {
                'max_total_evidence': 12,
                'include_cross_references': True,
                'balance_breadth_depth': True,
                'group_related_evidence': True
            },
            
            BillComplexity.COMPLEX: {
                'max_total_evidence': 18,
                'require_comprehensive': True,
                'include_technical_details': True,
                'provide_multiple_perspectives': True
            },
            
            BillComplexity.OMNIBUS: {
                'max_total_evidence': 20,
                'prioritize_by_importance': True,
                'group_by_topic': True,
                'provide_overview_structure': True
            }
        }
    
    def _initialize_context_priorities(self) -> Dict[str, Dict[EvidenceType, float]]:
        """Initialize evidence type priorities by context"""
        
        return {
            'healthcare': {
                EvidenceType.FUNDING: 0.9,
                EvidenceType.SCOPE: 0.85,
                EvidenceType.IMPACT: 0.8,
                EvidenceType.ENFORCEMENT: 0.7,
                EvidenceType.TIMELINE: 0.6
            },
            
            'environment': {
                EvidenceType.ENFORCEMENT: 0.95,
                EvidenceType.MANDATE: 0.9,
                EvidenceType.FUNDING: 0.85,
                EvidenceType.IMPACT: 0.8,
                EvidenceType.TIMELINE: 0.75
            },
            
            'regulatory': {
                EvidenceType.MANDATE: 0.95,
                EvidenceType.ENFORCEMENT: 0.9,
                EvidenceType.AUTHORITY: 0.85,
                EvidenceType.PROCESS: 0.8,
                EvidenceType.TIMELINE: 0.75
            },
            
            'economic': {
                EvidenceType.FUNDING: 0.95,
                EvidenceType.IMPACT: 0.9,
                EvidenceType.SCOPE: 0.85,
                EvidenceType.TIMELINE: 0.7,
                EvidenceType.AUTHORITY: 0.65
            }
        }
    
    def _initialize_quality_thresholds(self) -> Dict[str, Dict[str, float]]:
        """Initialize quality thresholds by requirements"""
        
        return {
            'standard': {
                'min_priority_score': 0.5,
                'min_specificity': 0.4,
                'min_legal_weight': 0.4,
                'min_actionability': 0.3
            },
            
            'high': {
                'min_priority_score': 0.7,
                'min_specificity': 0.6,
                'min_legal_weight': 0.6,
                'min_actionability': 0.5
            },
            
            'premium': {
                'min_priority_score': 0.8,
                'min_specificity': 0.7,
                'min_legal_weight': 0.7,
                'min_actionability': 0.6
            }
        }
    
    def _create_selection_context(self, bill_metadata: Dict[str, Any],
                                analysis_requirements: Dict[str, Any]) -> SelectionContext:
        """Create comprehensive selection context"""
        
        # Determine bill complexity
        bill_text_length = len(bill_metadata.get('full_text', ''))
        section_count = len(re.findall(r'SEC\.|SECTION', bill_metadata.get('full_text', ''), re.IGNORECASE))
        
        if bill_text_length < 5000 and section_count < 5:
            complexity = BillComplexity.SIMPLE
        elif bill_text_length < 20000 and section_count < 15:
            complexity = BillComplexity.MODERATE
        elif bill_text_length < 50000:
            complexity = BillComplexity.COMPLEX
        else:
            complexity = BillComplexity.OMNIBUS
        
        # Determine analysis goal
        goal_mapping = {
            'citizen_summary': AnalysisGoal.CITIZEN_SUMMARY,
            'expert_analysis': AnalysisGoal.EXPERT_ANALYSIS,
            'legislative_review': AnalysisGoal.LEGISLATIVE_REVIEW,
            'impact_assessment': AnalysisGoal.IMPACT_ASSESSMENT,
            'compliance_guide': AnalysisGoal.COMPLIANCE_GUIDE,
            'cost_analysis': AnalysisGoal.COST_ANALYSIS
        }
        
        analysis_goal = goal_mapping.get(
            analysis_requirements.get('analysis_type', 'citizen_summary'),
            AnalysisGoal.CITIZEN_SUMMARY
        )
        
        return SelectionContext(
            bill_type=bill_metadata.get('bill_type', 'hr'),
            bill_complexity=complexity,
            primary_topic=bill_metadata.get('primary_topic', 'general'),
            analysis_goal=analysis_goal,
            target_audience=analysis_requirements.get('target_audience', 'citizens'),
            time_constraints=analysis_requirements.get('time_constraints', False),
            quality_requirements=analysis_requirements.get('quality', 'standard'),
            budget_constraints=analysis_requirements.get('budget_limit', 0.25),
            user_preferences=analysis_requirements.get('preferences', {})
        )
    
    async def _apply_goal_strategy(self, evidence_spans: List[EvidenceSpan],
                                 context: SelectionContext) -> List[EvidenceSpan]:
        """Apply goal-specific selection strategy"""
        
        strategy = self.goal_strategies[context.analysis_goal]
        filtered_evidence = []
        
        for evidence in evidence_spans:
            # Check if evidence type is prioritized for this goal
            if evidence.evidence_type in strategy['priority_types']:
                # Apply goal-specific filters
                if self._meets_goal_criteria(evidence, strategy):
                    filtered_evidence.append(evidence)
        
        # Sort by goal-specific priorities
        filtered_evidence.sort(key=lambda e: self._calculate_goal_score(e, strategy), reverse=True)
        
        # Limit by goal max evidence
        max_evidence = strategy.get('max_evidence', 12)
        return filtered_evidence[:max_evidence]
    
    async def _apply_complexity_filtering(self, evidence_spans: List[EvidenceSpan],
                                        context: SelectionContext) -> List[EvidenceSpan]:
        """Apply complexity-based filtering"""
        
        rules = self.complexity_rules[context.bill_complexity]
        
        # Apply complexity-specific limits
        max_total = rules.get('max_total_evidence', 15)
        evidence_spans = evidence_spans[:max_total]
        
        # Apply complexity-specific preferences
        if rules.get('prefer_core_sections', False):
            # Prioritize evidence from main sections
            evidence_spans.sort(key=lambda e: self._is_core_section(e), reverse=True)
        
        if rules.get('group_related_evidence', False):
            # Group related evidence together
            evidence_spans = self._group_related_evidence(evidence_spans)
        
        return evidence_spans
    
    async def _apply_audience_adaptation(self, evidence_spans: List[EvidenceSpan],
                                       context: SelectionContext) -> List[EvidenceSpan]:
        """Apply audience-specific adaptations"""
        
        if context.target_audience == 'citizens':
            # Prioritize actionable, understandable evidence
            evidence_spans = [e for e in evidence_spans if e.actionability_score > 0.4]
            evidence_spans.sort(key=lambda e: e.actionability_score, reverse=True)
        
        elif context.target_audience == 'experts':
            # Include technical and comprehensive evidence
            evidence_spans.sort(key=lambda e: e.legal_weight, reverse=True)
        
        elif context.target_audience == 'legislators':
            # Focus on legal implications and authority
            evidence_spans = [e for e in evidence_spans 
                            if e.evidence_type in [EvidenceType.AUTHORITY, EvidenceType.MANDATE, EvidenceType.ENFORCEMENT]]
            evidence_spans.sort(key=lambda e: e.legal_weight, reverse=True)
        
        return evidence_spans
    
    async def _create_evidence_bundle(self, evidence_spans: List[EvidenceSpan],
                                    context: SelectionContext) -> EvidenceBundle:
        """Create optimized evidence bundle"""
        
        # Categorize evidence by role
        core_evidence = []
        supporting_evidence = []
        technical_evidence = []
        citizen_evidence = []
        
        for evidence in evidence_spans:
            # Core evidence: highest priority, most relevant (relaxed thresholds)
            if evidence.priority_score > 0.7 or evidence.context_relevance > 0.6:
                core_evidence.append(evidence)
            
            # Citizen evidence: actionable and understandable
            elif evidence.actionability_score > 0.5 and evidence.evidence_type in [
                EvidenceType.FUNDING, EvidenceType.ENFORCEMENT, EvidenceType.IMPACT
            ]:
                citizen_evidence.append(evidence)
            
            # Technical evidence: detailed legal/procedural info
            elif evidence.legal_weight > 0.6 and evidence.evidence_type in [
                EvidenceType.AUTHORITY, EvidenceType.PROCESS, EvidenceType.DEFINITION
            ]:
                technical_evidence.append(evidence)
            
            # Supporting evidence: additional context
            else:
                supporting_evidence.append(evidence)
        
        # Apply bundle optimization
        core_evidence = self._optimize_evidence_selection(core_evidence, max_count=6)
        supporting_evidence = self._optimize_evidence_selection(supporting_evidence, max_count=4)
        technical_evidence = self._optimize_evidence_selection(technical_evidence, max_count=3)
        citizen_evidence = self._optimize_evidence_selection(citizen_evidence, max_count=5)
        
        # Calculate bundle score
        bundle_score = self._calculate_bundle_score(
            core_evidence, supporting_evidence, technical_evidence, citizen_evidence, context
        )
        
        # Generate selection rationale
        rationale = self._generate_selection_rationale(context, len(core_evidence), len(supporting_evidence))
        
        return EvidenceBundle(
            core_evidence=core_evidence,
            supporting_evidence=supporting_evidence,
            technical_evidence=technical_evidence,
            citizen_evidence=citizen_evidence,
            bundle_score=bundle_score,
            selection_rationale=rationale
        )
    
    def _meets_goal_criteria(self, evidence: EvidenceSpan, strategy: Dict[str, Any]) -> bool:
        """Check if evidence meets goal-specific criteria"""
        
        # Check minimum thresholds
        if 'min_actionability' in strategy:
            if evidence.actionability_score < strategy['min_actionability']:
                return False
        
        if 'min_legal_weight' in strategy:
            if evidence.legal_weight < strategy['min_legal_weight']:
                return False
        
        if 'min_specificity' in strategy:
            if evidence.specificity_score < strategy['min_specificity']:
                return False
        
        # Check focus areas
        if 'focus_areas' in strategy:
            content_lower = evidence.content.lower()
            has_focus = any(area.replace('_', ' ') in content_lower 
                          for area in strategy['focus_areas'])
            if not has_focus:
                return False
        
        # Check technical preferences
        if strategy.get('avoid_technical', False):
            if evidence.legal_weight > 0.8 and evidence.actionability_score < 0.4:
                return False
        
        return True
    
    def _calculate_goal_score(self, evidence: EvidenceSpan, strategy: Dict[str, Any]) -> float:
        """Calculate goal-specific score for evidence"""
        
        score = evidence.priority_score
        
        # Boost for goal-preferred characteristics
        if strategy.get('prefer_specific', False):
            score += evidence.specificity_score * 0.2
        
        if strategy.get('prefer_actionable', False):
            score += evidence.actionability_score * 0.2
        
        if strategy.get('prefer_comprehensive', False):
            score += evidence.legal_weight * 0.2
        
        if strategy.get('require_quantified', False):
            # Boost for quantified evidence (money, numbers, dates)
            if any(char.isdigit() or char == '$' for char in evidence.content):
                score += 0.1
        
        return min(1.0, score)
    
    def _is_core_section(self, evidence: EvidenceSpan) -> bool:
        """Check if evidence is from a core section"""
        
        core_indicators = ['section 1', 'section 2', 'section 3', 'funding', 'appropriation', 'enforcement']
        heading_lower = evidence.heading.lower()
        
        return any(indicator in heading_lower for indicator in core_indicators)
    
    def _group_related_evidence(self, evidence_spans: List[EvidenceSpan]) -> List[EvidenceSpan]:
        """Group related evidence together"""
        
        # Group by evidence type first
        type_groups = defaultdict(list)
        for evidence in evidence_spans:
            type_groups[evidence.evidence_type].append(evidence)
        
        # Flatten back to list with groups together
        grouped = []
        for evidence_type in [EvidenceType.FUNDING, EvidenceType.ENFORCEMENT, EvidenceType.MANDATE, 
                            EvidenceType.TIMELINE, EvidenceType.AUTHORITY]:
            if evidence_type in type_groups:
                grouped.extend(sorted(type_groups[evidence_type], 
                                   key=lambda e: e.priority_score, reverse=True))
        
        # Add remaining evidence types
        for evidence_type, evidence_list in type_groups.items():
            if evidence_type not in [EvidenceType.FUNDING, EvidenceType.ENFORCEMENT, 
                                   EvidenceType.MANDATE, EvidenceType.TIMELINE, EvidenceType.AUTHORITY]:
                grouped.extend(sorted(evidence_list, key=lambda e: e.priority_score, reverse=True))
        
        return grouped
    
    def _optimize_evidence_selection(self, evidence_spans: List[EvidenceSpan], 
                                   max_count: int) -> List[EvidenceSpan]:
        """Optimize evidence selection for diversity and quality"""
        
        if len(evidence_spans) <= max_count:
            return evidence_spans
        
        # Ensure diversity of evidence types
        type_counts = defaultdict(int)
        selected = []
        
        # First pass: select best from each type
        for evidence in sorted(evidence_spans, key=lambda e: e.priority_score, reverse=True):
            if type_counts[evidence.evidence_type] < 2 and len(selected) < max_count:
                selected.append(evidence)
                type_counts[evidence.evidence_type] += 1
        
        # Second pass: fill remaining slots with highest priority
        remaining_slots = max_count - len(selected)
        if remaining_slots > 0:
            remaining_evidence = [e for e in evidence_spans if e not in selected]
            remaining_evidence.sort(key=lambda e: e.priority_score, reverse=True)
            selected.extend(remaining_evidence[:remaining_slots])
        
        return selected
    
    def _calculate_bundle_score(self, core: List[EvidenceSpan], supporting: List[EvidenceSpan],
                              technical: List[EvidenceSpan], citizen: List[EvidenceSpan],
                              context: SelectionContext) -> float:
        """Calculate overall bundle quality score"""
        
        # Weighted scores for different evidence categories
        core_score = sum(e.priority_score for e in core) / max(len(core), 1)
        supporting_score = sum(e.priority_score for e in supporting) / max(len(supporting), 1)
        technical_score = sum(e.legal_weight for e in technical) / max(len(technical), 1)
        citizen_score = sum(e.actionability_score for e in citizen) / max(len(citizen), 1)
        
        # Weight based on analysis goal
        if context.analysis_goal == AnalysisGoal.CITIZEN_SUMMARY:
            bundle_score = (core_score * 0.4 + citizen_score * 0.4 + supporting_score * 0.2)
        elif context.analysis_goal == AnalysisGoal.EXPERT_ANALYSIS:
            bundle_score = (core_score * 0.3 + technical_score * 0.4 + supporting_score * 0.3)
        else:
            bundle_score = (core_score * 0.4 + supporting_score * 0.3 + technical_score * 0.2 + citizen_score * 0.1)
        
        return bundle_score
    
    def _generate_selection_rationale(self, context: SelectionContext, 
                                    core_count: int, supporting_count: int) -> str:
        """Generate explanation for evidence selection"""
        
        rationale_parts = [
            f"Selected {core_count} core evidence spans for {context.analysis_goal.value} analysis",
            f"Added {supporting_count} supporting evidence spans for context",
            f"Optimized for {context.target_audience} audience",
            f"Adapted for {context.bill_complexity.value} bill complexity"
        ]
        
        return ". ".join(rationale_parts)
    
    async def _record_selection_for_learning(self, bundle: EvidenceBundle, 
                                           context: SelectionContext,
                                           bill_metadata: Dict[str, Any]) -> None:
        """Record selection for adaptive learning"""
        
        selection_record = {
            'timestamp': asyncio.get_event_loop().time(),
            'analysis_goal': context.analysis_goal.value,
            'bill_complexity': context.bill_complexity.value,
            'target_audience': context.target_audience,
            'bundle_score': bundle.bundle_score,
            'evidence_counts': {
                'core': len(bundle.core_evidence),
                'supporting': len(bundle.supporting_evidence),
                'technical': len(bundle.technical_evidence),
                'citizen': len(bundle.citizen_evidence)
            }
        }
        
        self.selection_history.append(selection_record)
        
        # Keep only recent history for performance
        if len(self.selection_history) > 100:
            self.selection_history = self.selection_history[-100:]

# Global instance  
context_aware_selector = ContextAwareEvidenceSelector()

def get_context_aware_selector() -> ContextAwareEvidenceSelector:
    """Get the global context-aware evidence selector instance"""
    return context_aware_selector