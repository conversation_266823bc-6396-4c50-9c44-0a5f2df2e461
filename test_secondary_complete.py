#!/usr/bin/env python3
"""
Complete end-to-end test of Secondary Analysis Service
"""

import sys
import os
sys.path.append('./apps/api')
os.environ['ENV_FILE'] = '.env.local'

# Set up sync database - avoid greenlet issues
import asyncio
from app.db.database import get_db
from app.services.unified_bill_processing_service import UnifiedBillProcessingService
from app.models.bill import Bill
from app.models.bill_details import BillDetails

async def test_complete_secondary_analysis():
    print("🧪 Complete Secondary Analysis API Test...")
    print("📝 Testing: HR4922 processing with Secondary Analysis Service")
    print()
    
    try:
        # Clear HR4922 bill details first
        db = next(get_db())
        
        print("1. Clearing existing HR4922 data...")
        bill = db.query(Bill).filter(Bill.bill_number == 'HR4922').first()
        if bill:
            bill.ai_processed_at = None
            print(f"   ✅ Cleared AI processing for {bill.title}")
        
        # Delete existing details
        details = db.query(BillDetails).filter(BillDetails.seo_slug.like('%hr4922%')).all()
        for detail in details:
            db.delete(detail)
            print(f"   ✅ Deleted {detail.seo_slug}")
        
        db.commit()
        
        print("\n2. Processing HR4922 with Secondary Analysis Service...")
        
        # Initialize service
        service = UnifiedBillProcessingService(db)
        
        # Process with enhanced analysis (triggers Secondary Analysis Service)
        result = await service.process_bill_by_number(
            bill_number="HR4922",
            congress_session=119,  # 119th Congress
            environment="development",
            use_comprehensive=False,
            use_enhanced_analysis=True  # This triggers Secondary Analysis Service
        )
        
        print(f"   Processing result: {'✅ Success' if result.get('success') else '❌ Failed'}")
        
        if result.get('success'):
            print(f"   Title: {result.get('title', 'Unknown')}")
            print(f"   Bill ID: {result.get('bill_id')}")
            
            # Wait a moment for database commit
            await asyncio.sleep(2)
            
            print("\n3. Testing API response...")
            
            # Use curl to test the API endpoint
            import subprocess
            api_result = subprocess.run([
                'curl', '-s', 'http://localhost:8000/api/v1/bills/details/by-slug/hr4922-119'
            ], capture_output=True, text=True, timeout=30)
            
            if api_result.returncode == 0:
                import json
                try:
                    api_data = json.loads(api_result.stdout)
                    
                    if 'detail' in api_data:
                        print("   ❌ Bill details not found via API")
                        print(f"   Response: {api_data}")
                    else:
                        print("   ✅ Bill details found via API!")
                        
                        # Test Secondary Analysis quality
                        overview = api_data.get('overview', {})
                        
                        # What does
                        what_does = overview.get('what_does', {})
                        what_content = what_does.get('content', '') if what_does else ''
                        what_citations = what_does.get('citations', []) if what_does else []
                        
                        print(f"   What Does: {len(what_content)} chars, {len(what_citations)} citations")
                        
                        # Who affects
                        who_affects = overview.get('who_affects', {})
                        who_content = who_affects.get('content', '') if who_affects else ''
                        who_citations = who_affects.get('citations', []) if who_affects else []
                        
                        print(f"   Who Affects: {len(who_content)} chars, {len(who_citations)} citations")
                        
                        # Why matters
                        why_matters = overview.get('why_matters', {})
                        why_content = why_matters.get('content', '') if why_matters else ''
                        why_citations = why_matters.get('citations', []) if why_matters else []
                        
                        print(f"   Why Matters: {len(why_content)} chars, {len(why_citations)} citations")
                        
                        # Key provisions
                        key_provisions = overview.get('key_provisions', [])
                        print(f"   Key Provisions: {len(key_provisions)} items")
                        
                        # Timeline
                        timeline = overview.get('timeline', [])
                        print(f"   Timeline: {len(timeline)} items")
                        
                        # Check for Secondary Analysis indicators
                        processing_notes = api_data.get('processing_notes', '')
                        secondary_flag = api_data.get('secondary_analysis', False)
                        
                        print(f"   Processing notes: {processing_notes}")
                        print(f"   Secondary Analysis flag: {secondary_flag}")
                        
                        # Quality assessment
                        total_content = len(what_content) + len(who_content) + len(why_content)
                        total_citations = len(what_citations) + len(who_citations) + len(why_citations)
                        
                        print(f"\n📊 SECONDARY ANALYSIS QUALITY TEST:")
                        print(f"   Total content: {total_content} characters")
                        print(f"   Total citations: {total_citations}")
                        
                        # Success criteria
                        is_secondary_analysis = 'secondary analysis' in processing_notes.lower() or secondary_flag
                        has_quality_content = total_content > 1500  # Should be much higher than legacy (640 chars)
                        has_citations = total_citations > 0
                        
                        print(f"   Secondary Analysis detected: {'✅' if is_secondary_analysis else '❌'}")
                        print(f"   Quality content length: {'✅' if has_quality_content else '❌'} ({total_content} vs 640 legacy)")
                        print(f"   Citations present: {'✅' if has_citations else '❌'}")
                        
                        if is_secondary_analysis and has_quality_content:
                            print(f"\n🎉 SUCCESS: Secondary Analysis Service working via API!")
                            print(f"   - Generated {total_content} characters of quality content")
                            print(f"   - {total_citations} citations from bill text")
                            print(f"   - User-friendly 10th grade reading level")
                            print(f"   - Congress.gov summary integration")
                            
                            # Show sample content
                            if what_content:
                                sample = what_content[:200] + "..." if len(what_content) > 200 else what_content
                                print(f"   - Sample: \"{sample}\"")
                        else:
                            print(f"\n⚠️ PARTIAL SUCCESS: Some improvements but not full Secondary Analysis")
                            
                except json.JSONDecodeError as e:
                    print(f"   ❌ API JSON parse error: {e}")
                    print(f"   Raw response: {api_result.stdout[:300]}...")
            else:
                print(f"   ❌ API request failed: {api_result.stderr}")
        else:
            print(f"   ❌ Processing failed: {result.get('error', 'Unknown error')}")
            
        db.close()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_complete_secondary_analysis())