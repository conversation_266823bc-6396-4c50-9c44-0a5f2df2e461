"""
Adaptive Learning Service - Phase 4 Intelligent Learning System
Learns from processing history to continuously improve quality and efficiency
"""

import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from collections import defaultdict, deque
import asyncio
from enum import Enum

from sqlalchemy.orm import Session
from sqlalchemy import and_, func

logger = logging.getLogger(__name__)

class LearningMetric(Enum):
    """Types of metrics we learn from"""
    QUALITY_SCORE = "quality_score"
    COST_EFFICIENCY = "cost_efficiency"
    PROCESSING_TIME = "processing_time"
    USER_FEEDBACK = "user_feedback"
    ERROR_RATE = "error_rate"
    EVIDENCE_QUALITY = "evidence_quality"
    IMPORTANCE_ACCURACY = "importance_accuracy"

@dataclass
class LearningPattern:
    """Pattern identified through adaptive learning"""
    pattern_id: str
    pattern_type: str  # bill_type, topic, complexity, etc.
    pattern_value: str  # specific value of the pattern
    success_rate: float
    avg_quality: float
    avg_cost: float
    sample_count: int
    recommendations: List[str]
    confidence: float
    last_updated: datetime = field(default_factory=datetime.utcnow)

@dataclass
class OptimizationSuggestion:
    """Suggestion for optimizing processing based on learned patterns"""
    suggestion_id: str
    category: str  # cost, quality, speed, etc.
    description: str
    expected_improvement: float  # percentage improvement expected
    confidence: float
    implementation_details: Dict[str, Any]
    priority: int  # 1-10, higher is more important

@dataclass
class LearningSnapshot:
    """Snapshot of current learning state"""
    timestamp: datetime
    total_bills_processed: int
    avg_quality_score: float
    avg_cost: float
    avg_processing_time: float
    identified_patterns: int
    active_optimizations: int
    quality_trend: str  # improving, stable, declining
    cost_trend: str  # increasing, stable, decreasing
    recommendations: List[OptimizationSuggestion]

class AdaptiveLearningService:
    """
    Phase 4 Adaptive Learning System that learns from processing history
    to continuously improve quality and efficiency
    """
    
    def __init__(self, db: Session):
        self.db = db
        
        # Learning history storage
        self.processing_history = deque(maxlen=1000)  # Last 1000 processing results
        self.pattern_database = {}  # Identified patterns
        self.optimization_rules = {}  # Active optimization rules
        
        # Performance baselines
        self.quality_baseline = 0.75  # Target quality score
        self.cost_baseline = 0.10  # Target cost per bill
        self.time_baseline = 30.0  # Target processing time in seconds
        
        # Learning configuration
        self.min_samples_for_pattern = 5  # Minimum samples to identify a pattern
        self.confidence_threshold = 0.7  # Minimum confidence for suggestions
        self.learning_window = 30  # Days of history to consider
        
        # Performance metrics
        self.metrics_cache = {
            'quality_scores': deque(maxlen=100),
            'costs': deque(maxlen=100),
            'processing_times': deque(maxlen=100),
            'error_rates': deque(maxlen=100)
        }
        
        # Pattern recognition categories
        self.pattern_categories = {
            'bill_type': ['hr', 's', 'hjres', 'sjres', 'hres', 'sres'],
            'topic': ['healthcare', 'defense', 'education', 'environment', 'economy', 'technology'],
            'complexity': ['simple', 'moderate', 'complex', 'omnibus'],
            'length': ['short', 'medium', 'long', 'very_long'],
            'urgency': ['routine', 'priority', 'urgent', 'emergency']
        }
    
    async def record_processing_result(self, bill_metadata: Dict[str, Any],
                                      processing_result: Dict[str, Any]) -> None:
        """
        Record a bill processing result for learning
        """
        
        try:
            # Extract key metrics
            learning_record = {
                'timestamp': datetime.utcnow(),
                'bill_id': bill_metadata.get('bill_id'),
                'bill_type': bill_metadata.get('bill_type'),
                'bill_number': bill_metadata.get('bill_number'),
                'title': bill_metadata.get('title'),
                'quality_score': processing_result.get('quality_metrics', {}).get('overall_score', 0),
                'cost': processing_result.get('cost_breakdown', {}).get('total_cost', 0),
                'processing_time': processing_result.get('processing_time', 0),
                'evidence_quality': processing_result.get('evidence_quality', {}).get('average_quality_score', 0),
                'importance_score': bill_metadata.get('importance_score', 0),
                'success': processing_result.get('success', False),
                'error': processing_result.get('error'),
                'model_used': processing_result.get('model_used', 'unknown'),
                'evidence_count': len(processing_result.get('evidence_spans', [])),
                'analysis_type': processing_result.get('analysis_type', 'standard')
            }
            
            # Categorize the bill
            learning_record['category'] = self._categorize_bill(bill_metadata, processing_result)
            
            # Add to processing history
            self.processing_history.append(learning_record)
            
            # Update metrics cache
            self.metrics_cache['quality_scores'].append(learning_record['quality_score'])
            self.metrics_cache['costs'].append(learning_record['cost'])
            self.metrics_cache['processing_times'].append(learning_record['processing_time'])
            self.metrics_cache['error_rates'].append(0 if learning_record['success'] else 1)
            
            # Check for patterns
            await self._identify_patterns(learning_record)
            
            # Update optimization rules
            await self._update_optimization_rules(learning_record)
            
            logger.info(f"📊 Recorded learning data for {bill_metadata.get('bill_number')}: "
                       f"Q={learning_record['quality_score']:.2f}, "
                       f"C=${learning_record['cost']:.4f}, "
                       f"T={learning_record['processing_time']:.1f}s")
            
        except Exception as e:
            logger.error(f"Failed to record processing result for learning: {e}")
    
    async def get_optimization_suggestions(self, bill_metadata: Dict[str, Any]) -> List[OptimizationSuggestion]:
        """
        Get optimization suggestions for a specific bill based on learned patterns
        """
        
        suggestions = []
        
        try:
            # Categorize the bill
            bill_category = self._categorize_bill(bill_metadata, {})
            
            # Find relevant patterns
            relevant_patterns = self._find_relevant_patterns(bill_category)
            
            # Generate suggestions based on patterns
            for pattern in relevant_patterns:
                if pattern.confidence >= self.confidence_threshold:
                    suggestions.extend(self._generate_suggestions_from_pattern(pattern, bill_metadata))
            
            # Add general optimization suggestions
            suggestions.extend(await self._generate_general_suggestions())
            
            # Sort by priority
            suggestions.sort(key=lambda s: s.priority, reverse=True)
            
            # Limit to top 5 suggestions
            return suggestions[:5]
            
        except Exception as e:
            logger.error(f"Failed to get optimization suggestions: {e}")
            return []
    
    async def get_learning_snapshot(self) -> LearningSnapshot:
        """
        Get current snapshot of learning state and performance
        """
        
        try:
            # Calculate current metrics
            if self.metrics_cache['quality_scores']:
                avg_quality = sum(self.metrics_cache['quality_scores']) / len(self.metrics_cache['quality_scores'])
            else:
                avg_quality = 0
            
            if self.metrics_cache['costs']:
                avg_cost = sum(self.metrics_cache['costs']) / len(self.metrics_cache['costs'])
            else:
                avg_cost = 0
            
            if self.metrics_cache['processing_times']:
                avg_time = sum(self.metrics_cache['processing_times']) / len(self.metrics_cache['processing_times'])
            else:
                avg_time = 0
            
            # Determine trends
            quality_trend = self._calculate_trend(self.metrics_cache['quality_scores'])
            cost_trend = self._calculate_trend(self.metrics_cache['costs'])
            
            # Get top recommendations
            recommendations = await self._generate_snapshot_recommendations()
            
            return LearningSnapshot(
                timestamp=datetime.utcnow(),
                total_bills_processed=len(self.processing_history),
                avg_quality_score=avg_quality,
                avg_cost=avg_cost,
                avg_processing_time=avg_time,
                identified_patterns=len(self.pattern_database),
                active_optimizations=len(self.optimization_rules),
                quality_trend=quality_trend,
                cost_trend=cost_trend,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Failed to get learning snapshot: {e}")
            return LearningSnapshot(
                timestamp=datetime.utcnow(),
                total_bills_processed=0,
                avg_quality_score=0,
                avg_cost=0,
                avg_processing_time=0,
                identified_patterns=0,
                active_optimizations=0,
                quality_trend="unknown",
                cost_trend="unknown",
                recommendations=[]
            )
    
    async def apply_learned_optimizations(self, bill_metadata: Dict[str, Any],
                                         processing_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Apply learned optimizations to processing configuration
        """
        
        optimized_config = processing_config.copy()
        
        try:
            # Get bill category
            bill_category = self._categorize_bill(bill_metadata, {})
            
            # Find applicable optimization rules
            applicable_rules = self._find_applicable_rules(bill_category)
            
            # Apply each rule
            for rule_id, rule in applicable_rules.items():
                optimized_config = self._apply_optimization_rule(optimized_config, rule)
            
            # Log optimizations applied
            if applicable_rules:
                logger.info(f"🎯 Applied {len(applicable_rules)} learned optimizations for {bill_metadata.get('bill_number')}")
            
            return optimized_config
            
        except Exception as e:
            logger.error(f"Failed to apply learned optimizations: {e}")
            return processing_config
    
    def _categorize_bill(self, bill_metadata: Dict[str, Any], 
                        processing_result: Dict[str, Any]) -> Dict[str, str]:
        """Categorize a bill for pattern matching"""
        
        category = {}
        
        # Bill type
        bill_type = bill_metadata.get('bill_type', '').lower()
        category['bill_type'] = bill_type if bill_type in self.pattern_categories['bill_type'] else 'other'
        
        # Topic detection
        title = bill_metadata.get('title', '').lower()
        for topic in self.pattern_categories['topic']:
            if topic in title:
                category['topic'] = topic
                break
        else:
            category['topic'] = 'general'
        
        # Complexity based on text length
        text_length = len(bill_metadata.get('bill_text', ''))
        if text_length < 5000:
            category['complexity'] = 'simple'
        elif text_length < 20000:
            category['complexity'] = 'moderate'
        elif text_length < 50000:
            category['complexity'] = 'complex'
        else:
            category['complexity'] = 'omnibus'
        
        # Length category
        if text_length < 2000:
            category['length'] = 'short'
        elif text_length < 10000:
            category['length'] = 'medium'
        elif text_length < 30000:
            category['length'] = 'long'
        else:
            category['length'] = 'very_long'
        
        # Urgency (simplified for now)
        category['urgency'] = 'routine'
        
        return category
    
    async def _identify_patterns(self, learning_record: Dict[str, Any]) -> None:
        """Identify patterns from processing history"""
        
        try:
            category = learning_record['category']
            
            # Group records by category
            for cat_type, cat_value in category.items():
                pattern_key = f"{cat_type}:{cat_value}"
                
                # Find similar records
                similar_records = [
                    r for r in self.processing_history
                    if r.get('category', {}).get(cat_type) == cat_value
                ]
                
                # Need minimum samples to identify pattern
                if len(similar_records) >= self.min_samples_for_pattern:
                    # Calculate pattern metrics
                    success_rate = sum(1 for r in similar_records if r['success']) / len(similar_records)
                    avg_quality = sum(r['quality_score'] for r in similar_records) / len(similar_records)
                    avg_cost = sum(r['cost'] for r in similar_records) / len(similar_records)
                    
                    # Create or update pattern
                    pattern = LearningPattern(
                        pattern_id=pattern_key,
                        pattern_type=cat_type,
                        pattern_value=cat_value,
                        success_rate=success_rate,
                        avg_quality=avg_quality,
                        avg_cost=avg_cost,
                        sample_count=len(similar_records),
                        recommendations=self._generate_pattern_recommendations(
                            cat_type, cat_value, avg_quality, avg_cost
                        ),
                        confidence=min(1.0, len(similar_records) / 20)  # Confidence increases with samples
                    )
                    
                    self.pattern_database[pattern_key] = pattern
                    
                    # Log significant patterns
                    if pattern.confidence >= self.confidence_threshold:
                        logger.info(f"📈 Pattern identified: {pattern_key} "
                                  f"(Q={avg_quality:.2f}, C=${avg_cost:.4f}, n={len(similar_records)})")
        
        except Exception as e:
            logger.error(f"Failed to identify patterns: {e}")
    
    async def _update_optimization_rules(self, learning_record: Dict[str, Any]) -> None:
        """Update optimization rules based on learning"""
        
        try:
            # Check if this record suggests new optimization
            quality = learning_record['quality_score']
            cost = learning_record['cost']
            
            # If high quality and low cost, create optimization rule
            if quality >= self.quality_baseline and cost <= self.cost_baseline:
                rule_key = f"{learning_record['model_used']}:{learning_record['analysis_type']}"
                
                if rule_key not in self.optimization_rules:
                    self.optimization_rules[rule_key] = {
                        'model': learning_record['model_used'],
                        'analysis_type': learning_record['analysis_type'],
                        'evidence_count': learning_record['evidence_count'],
                        'avg_quality': quality,
                        'avg_cost': cost,
                        'usage_count': 1
                    }
                else:
                    # Update existing rule
                    rule = self.optimization_rules[rule_key]
                    rule['usage_count'] += 1
                    rule['avg_quality'] = (rule['avg_quality'] * (rule['usage_count'] - 1) + quality) / rule['usage_count']
                    rule['avg_cost'] = (rule['avg_cost'] * (rule['usage_count'] - 1) + cost) / rule['usage_count']
        
        except Exception as e:
            logger.error(f"Failed to update optimization rules: {e}")
    
    def _find_relevant_patterns(self, bill_category: Dict[str, str]) -> List[LearningPattern]:
        """Find patterns relevant to a bill category"""
        
        relevant_patterns = []
        
        for pattern_key, pattern in self.pattern_database.items():
            # Check if pattern matches any category aspect
            for cat_type, cat_value in bill_category.items():
                if pattern.pattern_type == cat_type and pattern.pattern_value == cat_value:
                    relevant_patterns.append(pattern)
        
        return relevant_patterns
    
    def _generate_suggestions_from_pattern(self, pattern: LearningPattern,
                                          bill_metadata: Dict[str, Any]) -> List[OptimizationSuggestion]:
        """Generate optimization suggestions from a pattern"""
        
        suggestions = []
        
        # Quality improvement suggestion
        if pattern.avg_quality < self.quality_baseline:
            suggestions.append(OptimizationSuggestion(
                suggestion_id=f"quality_{pattern.pattern_id}",
                category="quality",
                description=f"Bills of type '{pattern.pattern_value}' average {pattern.avg_quality:.2f} quality. "
                           f"Consider using enhanced analysis for this category.",
                expected_improvement=(self.quality_baseline - pattern.avg_quality) / pattern.avg_quality * 100,
                confidence=pattern.confidence,
                implementation_details={
                    'use_enhanced_analysis': True,
                    'increase_evidence_count': True,
                    'pattern': pattern.pattern_id
                },
                priority=8 if pattern.avg_quality < 0.6 else 5
            ))
        
        # Cost optimization suggestion
        if pattern.avg_cost > self.cost_baseline * 1.5:
            suggestions.append(OptimizationSuggestion(
                suggestion_id=f"cost_{pattern.pattern_id}",
                category="cost",
                description=f"Bills of type '{pattern.pattern_value}' cost ${pattern.avg_cost:.4f} on average. "
                           f"Consider using cost-optimized models for this category.",
                expected_improvement=(pattern.avg_cost - self.cost_baseline) / pattern.avg_cost * 100,
                confidence=pattern.confidence,
                implementation_details={
                    'use_mini_model': True,
                    'reduce_evidence_count': True,
                    'pattern': pattern.pattern_id
                },
                priority=6 if pattern.avg_cost > self.cost_baseline * 2 else 4
            ))
        
        return suggestions
    
    async def _generate_general_suggestions(self) -> List[OptimizationSuggestion]:
        """Generate general optimization suggestions based on overall metrics"""
        
        suggestions = []
        
        # Check overall quality trend
        if self.metrics_cache['quality_scores']:
            recent_quality = list(self.metrics_cache['quality_scores'])[-10:]
            if recent_quality and sum(recent_quality) / len(recent_quality) < self.quality_baseline:
                suggestions.append(OptimizationSuggestion(
                    suggestion_id="general_quality",
                    category="quality",
                    description="Recent quality scores are below target. Consider increasing evidence requirements.",
                    expected_improvement=10,
                    confidence=0.8,
                    implementation_details={
                        'min_evidence_quality': 0.6,
                        'require_evidence_validation': True
                    },
                    priority=7
                ))
        
        # Check cost trend
        if self.metrics_cache['costs']:
            recent_costs = list(self.metrics_cache['costs'])[-10:]
            if recent_costs and sum(recent_costs) / len(recent_costs) > self.cost_baseline:
                suggestions.append(OptimizationSuggestion(
                    suggestion_id="general_cost",
                    category="cost",
                    description="Recent costs are above target. Consider more aggressive cost optimization.",
                    expected_improvement=15,
                    confidence=0.8,
                    implementation_details={
                        'prefer_mini_model': True,
                        'max_evidence_count': 5
                    },
                    priority=6
                ))
        
        return suggestions
    
    def _calculate_trend(self, metrics: deque) -> str:
        """Calculate trend from metrics (improving, stable, declining)"""
        
        if len(metrics) < 10:
            return "insufficient_data"
        
        # Compare recent average to older average
        recent = list(metrics)[-5:]
        older = list(metrics)[-10:-5]
        
        recent_avg = sum(recent) / len(recent) if recent else 0
        older_avg = sum(older) / len(older) if older else 0
        
        if recent_avg > older_avg * 1.05:
            return "improving"
        elif recent_avg < older_avg * 0.95:
            return "declining"
        else:
            return "stable"
    
    async def _generate_snapshot_recommendations(self) -> List[OptimizationSuggestion]:
        """Generate recommendations for learning snapshot"""
        
        recommendations = []
        
        # Get all recent suggestions
        for pattern in self.pattern_database.values():
            if pattern.confidence >= self.confidence_threshold:
                recommendations.extend(self._generate_pattern_recommendations(
                    pattern.pattern_type,
                    pattern.pattern_value,
                    pattern.avg_quality,
                    pattern.avg_cost
                )[:1])  # Only top recommendation per pattern
        
        # Sort by expected improvement
        recommendations = [
            OptimizationSuggestion(
                suggestion_id=f"snapshot_{i}",
                category="general",
                description=rec,
                expected_improvement=10,
                confidence=0.7,
                implementation_details={},
                priority=5
            )
            for i, rec in enumerate(recommendations[:3])
        ]
        
        return recommendations
    
    def _generate_pattern_recommendations(self, pattern_type: str, pattern_value: str,
                                         avg_quality: float, avg_cost: float) -> List[str]:
        """Generate text recommendations for a pattern"""
        
        recommendations = []
        
        if avg_quality < self.quality_baseline:
            recommendations.append(
                f"Increase quality for {pattern_type}='{pattern_value}' bills "
                f"(current: {avg_quality:.2f}, target: {self.quality_baseline:.2f})"
            )
        
        if avg_cost > self.cost_baseline:
            recommendations.append(
                f"Reduce costs for {pattern_type}='{pattern_value}' bills "
                f"(current: ${avg_cost:.4f}, target: ${self.cost_baseline:.4f})"
            )
        
        return recommendations
    
    def _find_applicable_rules(self, bill_category: Dict[str, str]) -> Dict[str, Dict[str, Any]]:
        """Find optimization rules applicable to a bill"""
        
        applicable_rules = {}
        
        # For now, return all high-performing rules
        for rule_key, rule in self.optimization_rules.items():
            if rule['avg_quality'] >= self.quality_baseline and rule['avg_cost'] <= self.cost_baseline:
                applicable_rules[rule_key] = rule
        
        return applicable_rules
    
    def _apply_optimization_rule(self, config: Dict[str, Any], 
                                rule: Dict[str, Any]) -> Dict[str, Any]:
        """Apply an optimization rule to configuration"""
        
        # Apply model preference
        if rule['model'] and rule['avg_quality'] >= self.quality_baseline:
            config['preferred_model'] = rule['model']
        
        # Apply evidence count optimization
        if rule['evidence_count'] and rule['avg_cost'] <= self.cost_baseline:
            config['target_evidence_count'] = rule['evidence_count']
        
        return config

# Global instance
adaptive_learning_service = None

def get_adaptive_learning_service(db: Session) -> AdaptiveLearningService:
    """Get or create the global adaptive learning service instance"""
    global adaptive_learning_service
    if adaptive_learning_service is None:
        adaptive_learning_service = AdaptiveLearningService(db)
    return adaptive_learning_service