#!/bin/bash

##############################################################################
# Deploy Officials Data to Staging Environment
#
# This script populates the staging database with comprehensive officials data
# including social media and contact information from OpenStates API.
#
# Prerequisites:
# - staging.modernaction.io deployment is running
# - OPENSTATES_API_KEY is configured in the staging environment
# - Database migrations have been applied
#
# Usage:
#   ./deploy_officials_staging.sh [--dry-run] [--limit N]
#
# Examples:
#   ./deploy_officials_staging.sh                    # Full deployment
#   ./deploy_officials_staging.sh --dry-run          # Test without changes
#   ./deploy_officials_staging.sh --limit 50         # Deploy first 50 officials
##############################################################################

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
DRY_RUN=""
LIMIT=""
STAGING_URL="staging.modernaction.io"
API_BASE_URL="https://${STAGING_URL}/api/v1"

# Function to print colored output
print_step() {
    echo -e "${BLUE}==>${NC} $1"
}

print_success() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Function to check if staging is accessible
check_staging_health() {
    print_step "Checking staging environment health..."
    
    # Check if the staging URL responds
    if ! curl -s --max-time 10 "https://${STAGING_URL}" > /dev/null 2>&1; then
        print_error "Staging environment is not accessible at https://${STAGING_URL}"
        print_error "Please ensure staging.modernaction.io is deployed and running"
        exit 1
    fi
    
    # Check if API is responding
    if ! curl -s --max-time 10 "${API_BASE_URL}/health" > /dev/null 2>&1; then
        print_error "Staging API is not accessible at ${API_BASE_URL}"
        print_error "Please ensure the API service is running"
        exit 1
    fi
    
    print_success "Staging environment is accessible"
}

# Function to check OpenStates API configuration
check_openstates_config() {
    print_step "Checking OpenStates API configuration..."
    
    # Try to get health status from the staging API
    response=$(curl -s --max-time 15 "${API_BASE_URL}/debug/openstates-health" 2>/dev/null || echo '{"error": "request_failed"}')
    
    # Parse the response to check if OpenStates is enabled
    if echo "$response" | grep -q '"enabled": *true'; then
        print_success "OpenStates API is properly configured in staging"
    else
        print_error "OpenStates API is not properly configured in staging"
        print_error "Please ensure OPENSTATES_API_KEY is set in the staging environment"
        print_error "Response: $response"
        exit 1
    fi
}

# Function to run the backfill via AWS ECS task
run_ecs_backfill() {
    print_step "Running officials backfill on staging via AWS ECS..."
    
    # Get the staging cluster and service info
    CLUSTER_NAME="modernaction-staging"
    SERVICE_NAME="modernaction-api-staging"
    TASK_DEFINITION="modernaction-api-staging"
    
    # Build the backfill command
    backfill_cmd="cd /app && python backfill_officials.py"
    
    # Add flags based on parameters
    if [ -n "$DRY_RUN" ]; then
        backfill_cmd="$backfill_cmd --dry-run"
        print_warning "Running in DRY RUN mode - no data will be modified"
    fi
    
    if [ -n "$LIMIT" ]; then
        backfill_cmd="$backfill_cmd --limit $LIMIT"
        print_warning "Limiting to $LIMIT officials"
    fi
    
    print_step "Executing: $backfill_cmd"
    
    # Run the task using AWS ECS
    print_step "Starting ECS task on cluster: $CLUSTER_NAME"
    
    # Get the task definition ARN
    task_def_arn=$(aws ecs describe-task-definition \
        --task-definition "$TASK_DEFINITION" \
        --query 'taskDefinition.taskDefinitionArn' \
        --output text 2>/dev/null || echo "")
    
    if [ -z "$task_def_arn" ]; then
        print_error "Could not find task definition: $TASK_DEFINITION"
        print_error "Please ensure the staging environment is properly deployed"
        exit 1
    fi
    
    # Get subnet and security group from the running service
    service_info=$(aws ecs describe-services \
        --cluster "$CLUSTER_NAME" \
        --services "$SERVICE_NAME" \
        --query 'services[0].networkConfiguration.awsvpcConfiguration' 2>/dev/null || echo "{}")
    
    if [ "$service_info" = "{}" ]; then
        print_error "Could not get network configuration from service: $SERVICE_NAME"
        print_error "Please ensure the staging service is running"
        exit 1
    fi
    
    # Extract network configuration
    subnets=$(echo "$service_info" | jq -r '.subnets | join(",")')
    security_groups=$(echo "$service_info" | jq -r '.securityGroups | join(",")')
    
    print_step "Running backfill task..."
    
    # Start the ECS task
    task_arn=$(aws ecs run-task \
        --cluster "$CLUSTER_NAME" \
        --task-definition "$task_def_arn" \
        --launch-type FARGATE \
        --network-configuration "awsvpcConfiguration={subnets=[$subnets],securityGroups=[$security_groups],assignPublicIp=ENABLED}" \
        --overrides "{
            \"containerOverrides\": [{
                \"name\": \"modernaction-api\",
                \"command\": [\"bash\", \"-c\", \"$backfill_cmd\"]
            }]
        }" \
        --query 'tasks[0].taskArn' \
        --output text 2>/dev/null || echo "")
    
    if [ -z "$task_arn" ]; then
        print_error "Failed to start ECS task"
        exit 1
    fi
    
    print_success "Started ECS task: $task_arn"
    
    # Wait for task to complete and show logs
    print_step "Waiting for task to complete (this may take several minutes)..."
    
    # Wait for task completion
    aws ecs wait tasks-stopped \
        --cluster "$CLUSTER_NAME" \
        --tasks "$task_arn"
    
    # Get the exit code
    exit_code=$(aws ecs describe-tasks \
        --cluster "$CLUSTER_NAME" \
        --tasks "$task_arn" \
        --query 'tasks[0].containers[0].exitCode' \
        --output text 2>/dev/null || echo "1")
    
    if [ "$exit_code" = "0" ]; then
        print_success "Officials backfill completed successfully!"
    else
        print_error "Officials backfill failed with exit code: $exit_code"
        
        # Try to get logs if possible
        print_step "Attempting to retrieve logs..."
        
        # Get log group and stream
        log_group="/ecs/modernaction-api-staging"
        task_id=$(echo "$task_arn" | cut -d'/' -f3)
        log_stream="ecs/modernaction-api/$task_id"
        
        # Try to get recent log events
        aws logs get-log-events \
            --log-group-name "$log_group" \
            --log-stream-name "$log_stream" \
            --query 'events[-20:].message' \
            --output text 2>/dev/null || print_warning "Could not retrieve logs"
        
        exit 1
    fi
}

# Function to run backfill via direct API calls (alternative method)
run_api_backfill() {
    print_step "Running officials backfill via direct API calls..."
    
    # This is an alternative method if ECS tasks are not available
    # We'll make API calls to trigger the backfill process
    
    print_warning "Direct API backfill method not yet implemented"
    print_warning "Please use ECS task method or run backfill manually on the staging server"
    exit 1
}

# Function to verify the deployment
verify_deployment() {
    print_step "Verifying officials data deployment..."
    
    # Check if we can retrieve officials from the API
    response=$(curl -s --max-time 15 "${API_BASE_URL}/officials/zip/90210" 2>/dev/null || echo '{"error": "request_failed"}')
    
    # Count the number of officials returned
    official_count=$(echo "$response" | jq length 2>/dev/null || echo "0")
    
    if [ "$official_count" -gt "0" ]; then
        print_success "Found $official_count officials for ZIP code 90210"
        
        # Show sample official data
        print_step "Sample official data:"
        echo "$response" | jq '.[0] | {name: .name, title: .title, party: .party, has_social_media: (.social_media != null)}' 2>/dev/null || echo "Could not parse official data"
    else
        print_error "No officials found in staging database"
        print_error "Response: $response"
        exit 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN="--dry-run"
            shift
            ;;
        --limit)
            LIMIT="$2"
            shift 2
            ;;
        --staging-url)
            STAGING_URL="$2"
            API_BASE_URL="https://${STAGING_URL}/api/v1"
            shift 2
            ;;
        -h|--help)
            echo "Deploy Officials Data to Staging"
            echo ""
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --dry-run              Run in dry-run mode (no changes)"
            echo "  --limit N              Limit to N officials"
            echo "  --staging-url URL      Use custom staging URL (default: staging.modernaction.io)"
            echo "  -h, --help             Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                     # Full deployment"
            echo "  $0 --dry-run           # Test without changes"
            echo "  $0 --limit 50          # Deploy first 50 officials"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            print_error "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Main deployment process
main() {
    echo "=================================="
    echo "Deploy Officials Data to Staging"
    echo "=================================="
    echo ""
    echo "Target: ${STAGING_URL}"
    echo "API: ${API_BASE_URL}"
    if [ -n "$DRY_RUN" ]; then
        echo "Mode: DRY RUN"
    else
        echo "Mode: LIVE DEPLOYMENT"
    fi
    if [ -n "$LIMIT" ]; then
        echo "Limit: $LIMIT officials"
    else
        echo "Limit: No limit (all officials)"
    fi
    echo ""
    
    # Step 1: Health checks
    check_staging_health
    check_openstates_config
    
    # Step 2: Run backfill
    if command -v aws &> /dev/null; then
        print_step "AWS CLI detected, using ECS task method"
        run_ecs_backfill
    else
        print_warning "AWS CLI not found, falling back to API method"
        run_api_backfill
    fi
    
    # Step 3: Verify deployment
    if [ -z "$DRY_RUN" ]; then
        verify_deployment
    fi
    
    # Success message
    echo ""
    print_success "Officials deployment completed successfully!"
    print_success "Staging environment: https://${STAGING_URL}"
    print_success "Officials search: https://${STAGING_URL}/officials"
    echo ""
}

# Run main function
main "$@"