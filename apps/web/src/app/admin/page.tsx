'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import apiClient from '../../services/apiClient';
import { AIUsageDashboard } from '../../components/admin/AIUsageDashboard';

interface ActionStats {
  total_actions: number;
  by_stance: { [key: string]: number };
  by_status: { [key: string]: number };
  recent_actions: Array<{
    id: string;
    bill_title: string;
    user_email: string;
    stance: string;
    created_at: string;
    selected_reasons: string[];
    custom_reason?: string;
  }>;
}

interface DetailedAction {
  id: string;
  bill_id: string;
  bill_title: string;
  user_id: string;
  user_email: string;
  user_name: string;
  stance: string;
  status: string;
  subject: string;
  message: string;
  created_at: string;
  sent_at?: string;
  delivered_at?: string;
  delivery_method?: string;
  error_message?: string;
  selected_reasons: Array<{
    id: string;
    reason_text: string;
  }>;
  custom_reason?: string;
  user_location: {
    zip_code?: string;
    city?: string;
    state?: string;
    address?: string;
  };
  representatives_contacted: Array<{
    name: string;
    title: string;
    party: string;
  }>;
  action_metadata?: any;
}

interface BillAnalytics {
  bill_id: string;
  bill_title: string;
  stats: {
    support: number;
    oppose: number;
    amend: number;
    total: number;
  };
  top_reasons: Array<{
    reason_text: string;
    count: number;
    stance: string;
  }>;
  custom_reasons: string[];
}

export default function AdminPage() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [billId, setBillId] = useState('hr9-118');
  const [results, setResults] = useState<any>(null);
  const [actionStats, setActionStats] = useState<ActionStats | null>(null);
  const [billAnalytics, setBillAnalytics] = useState<BillAnalytics[]>([]);
  const [selectedBillId, setSelectedBillId] = useState('bd9c4dfb-a7b7-406d-ac41-263f36548c50');
  const [loadingAnalytics, setLoadingAnalytics] = useState(false);
  const [detailedActions, setDetailedActions] = useState<DetailedAction[]>([]);
  const [showDetailedView, setShowDetailedView] = useState(false);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [selectedActionId, setSelectedActionId] = useState<string | null>(null);

  // Unified processing state
  const [unifiedProcessing, setUnifiedProcessing] = useState(false);
  const [unifiedResults, setUnifiedResults] = useState<any>(null);
  const [processingConfig, setProcessingConfig] = useState({
    maxBills: 5,
    environment: 'development',
    specificBills: ''
  });

  // Processing status state
  const [processingStatus, setProcessingStatus] = useState<any>(null);
  const [loadingStatus, setLoadingStatus] = useState(false);

  // Bills management state
  const [bills, setBills] = useState<any[]>([]);
  const [loadingBills, setLoadingBills] = useState(false);
  const [editingBill, setEditingBill] = useState<any>(null);
  const [editFormData, setEditFormData] = useState<any>({});
  const [workflowSummary, setWorkflowSummary] = useState<any>(null);
  const [selectedBillsForProcessing, setSelectedBillsForProcessing] = useState<string[]>([]);

  // Load analytics data on component mount
  useEffect(() => {
    loadActionAnalytics();
  }, []);

  const loadActionAnalytics = async () => {
    setLoadingAnalytics(true);
    try {
      // For now, skip action analytics since these endpoints don't exist yet
      // Set some placeholder data so the UI doesn't break
      setActionStats({
        total_actions: 0,
        by_stance: { support: 0, oppose: 0, amend: 0 },
        by_status: { sent: 0, pending: 0, failed: 0 },
        recent_actions: []
      });

      setBillAnalytics([{
        bill_id: selectedBillId,
        bill_title: 'Sample Bill Analytics', 
        stats: { support: 0, oppose: 0, amend: 0, total: 0 },
        top_reasons: [],
        custom_reasons: []
      }]);
      
    } catch (error) {
      console.error('Error loading analytics:', error);
      // Don't show error toast for analytics - it's not critical for bill processing
    } finally {
      setLoadingAnalytics(false);
    }
  };

  const loadDetailedActions = async (billId?: string) => {
    setLoadingDetails(true);
    try {
      // For now, skip detailed actions since these endpoints don't exist yet
      // Set empty data so the UI doesn't break
      setDetailedActions([]);
      
    } catch (error) {
      console.error('Error loading detailed actions:', error);
      toast.error('Failed to load detailed action data');
    } finally {
      setLoadingDetails(false);
    }
  };

  const handlePullBill = async () => {
    setIsProcessing(true);
    setResults(null);

    try {
      // Parse bill ID (e.g., "hr9-118" -> congress=118, bill_type=hr, bill_number=9)
      const match = billId.match(/^([a-z]+)(\d+)-(\d+)$/i);
      if (!match) {
        throw new Error('Invalid bill ID format. Use format like: hr9-118, s1234-118');
      }

      const [, billType, billNumber, congress] = match;

      const response = await apiClient.post(`/admin/process-and-save-bill?congress=${congress}&bill_type=${billType}&bill_number=${billNumber}`, {}, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status !== 200) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = response.data;
      setResults(result);
      toast.success('Bill processed successfully!');
    } catch (error) {
      console.error('Error processing bill:', error);
      toast.error('Failed to process bill');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleTestPreview = async () => {
    setIsProcessing(true);
    
    try {
      const response = await apiClient.post('/actions/preview-message', {
        bill_id: results?.bill_id || '25177ae6-9916-456b-aec2-7d3b84f87647', // Use processed bill ID or fallback
        stance: 'support',
        selected_reasons: ['This bill improves safety standards'],
        custom_reasons: ['My custom reason for supporting this'],
        zip_code: '60302'
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status !== 200) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = response.data;
      setResults(result);
      toast.success('Message preview generated successfully!');
    } catch (error) {
      console.error('Error generating preview:', error);
      toast.error('Failed to generate message preview');
    } finally {
      setIsProcessing(false);
    }
  };

  // Unified processing function
  const handleUnifiedProcess = async () => {
    setUnifiedProcessing(true);
    setUnifiedResults(null);
    
    try {
      let response;
      
      if (processingConfig.specificBills && processingConfig.specificBills.trim()) {
        // Process specific bills - send as JSON array
        const billNumbers = processingConfig.specificBills.split(',').map(s => s.trim()).filter(s => s);
        response = await apiClient.post('/admin/process-bills', billNumbers, {
          headers: {
            'Content-Type': 'application/json',
          },
        });
      } else {
        // Process recent bills - use query parameters
        const params = new URLSearchParams({
          limit: processingConfig.maxBills.toString(),
          environment: processingConfig.environment
        });

        response = await apiClient.post(`/admin/process-bills?${params}`, null, {
          headers: {
            'Content-Type': 'application/json',
          },
        });
      }

      if (response.status !== 200) {
        const errorText = response.data || response.statusText;
        console.error('API Error Response:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = response.data;
      setUnifiedResults(data);
      
      if (data.success) {
        const processedCount = data.processed_bills?.length || 0;
        const skippedCount = data.skipped_bills?.length || 0;
        const failedCount = data.failed_bills?.length || 0;
        
        if (processedCount > 0) {
          toast.success(`Successfully processed ${processedCount} bills with unified pipeline!`);
        } else if (skippedCount > 0) {
          toast.success(`Found ${skippedCount} bills, but all were already processed. No new bills to process.`);
        } else if (failedCount > 0) {
          toast.error(`Processing failed for ${failedCount} bills. Check results for details.`);
        } else {
          toast.success('Processing completed - no bills found to process.');
        }
      } else {
        toast.error('Unified processing completed with errors');
      }
    } catch (error) {
      console.error('Unified processing error:', error);
      setUnifiedResults({ error: error instanceof Error ? error.message : 'Unknown error' });
      toast.error('Failed to process bills with unified pipeline');
    } finally {
      setUnifiedProcessing(false);
    }
  };

  // Processing status function
  const fetchProcessingStatus = async () => {
    setLoadingStatus(true);
    
    try {
      const response = await fetch('http://localhost:8000/api/v1/admin/processing-status');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      setProcessingStatus(data);
    } catch (error) {
      console.error('Status fetch error:', error);
      setProcessingStatus({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setLoadingStatus(false);
    }
  };

  // Trigger values analysis function
  const handleTriggerValuesAnalysis = async () => {
    try {
      const response = await apiClient.post('/admin/trigger-values-analysis', {
        environment: processingConfig.environment
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status !== 200) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = response.data;
      toast.success(`Values analysis triggered: ${data.message}`);
      fetchProcessingStatus(); // Refresh status
    } catch (error) {
      console.error('Values analysis trigger error:', error);
      toast.error(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Bills management functions
  const loadBills = async () => {
    setLoadingBills(true);
    try {
      const response = await apiClient.get('/bills/simple?limit=100');
      if (response.status === 200) {
        setBills(response.data);
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error loading bills:', error);
      toast.error('Failed to load bills');
    } finally {
      setLoadingBills(false);
    }
  };

  // Load bills with workflow status and importance scoring
  const loadBillsWithWorkflowStatus = async () => {
    setLoadingBills(true);
    try {
      const response = await apiClient.get('/admin/bills-processing-status?limit=100&include_scores=true');
      if (response.status === 200) {
        const data = response.data;
        setBills(data.bills);
        setWorkflowSummary(data.summary);
        toast.success(`Loaded ${data.bills.length} bills with workflow status`);
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error loading bills with workflow status:', error);
      toast.error('Failed to load bills workflow status');
    } finally {
      setLoadingBills(false);
    }
  };

  // Batch process selected bills through process-bill-details workflow
  const batchProcessBillDetails = async () => {
    if (selectedBillsForProcessing.length === 0) {
      toast.error('Please select bills to process');
      return;
    }

    setLoadingBills(true);
    try {
      toast(`Processing ${selectedBillsForProcessing.length} bills through AI workflow...`, { icon: 'ℹ️' });
      
      const response = await apiClient.post('/admin/batch-process-bill-details', selectedBillsForProcessing, {
        params: {
          environment: 'development'
        }
      });

      if (response.status === 200) {
        const data = response.data;
        if (data.success) {
          const processedCount = data.processed_bills?.length || 0;
          const skippedCount = data.skipped_bills?.length || 0;
          const failedCount = data.failed_bills?.length || 0;

          let message = `Processed ${processedCount} bills`;
          if (skippedCount > 0) message += `, skipped ${skippedCount}`;
          if (failedCount > 0) message += `, failed ${failedCount}`;
          
          toast.success(message);
          setSelectedBillsForProcessing([]);
          loadBillsWithWorkflowStatus(); // Refresh with updated status
        } else {
          toast.error(`Batch processing failed: ${data.message || 'Unknown error'}`);
        }
      }
    } catch (error) {
      console.error('Error in batch processing:', error);
      toast.error('Failed to process bills');
    } finally {
      setLoadingBills(false);
    }
  };

  // Toggle bill selection for batch processing
  const toggleBillSelection = (billId: string) => {
    setSelectedBillsForProcessing(prev => 
      prev.includes(billId) 
        ? prev.filter(id => id !== billId)
        : [...prev, billId]
    );
  };

  // Fetch all current bills from Congress.gov
  const fetchAllCurrentBills = async () => {
    setLoadingBills(true);
    try {
      toast('Fetching current bills from Congress.gov...', { icon: 'ℹ️' });
      const response = await apiClient.post('/admin/process-bills', null, {
        params: {
          limit: 50,
          environment: 'development'
        }
      });

      if (response.status === 200) {
        const data = response.data;
        if (data.success) {
          toast.success(`Fetched ${data.processed_bills?.length || 0} current bills!`);
          loadBills(); // Refresh the list
        } else {
          toast.error('Failed to fetch bills');
        }
      }
    } catch (error) {
      console.error('Error fetching current bills:', error);
      toast.error('Failed to fetch current bills');
    } finally {
      setLoadingBills(false);
    }
  };

  // Run AI analysis on all existing bills
  const runAIAnalysisOnAllBills = async () => {
    setLoadingBills(true);
    try {
      toast('Running span-grounded AI analysis on all bills...', { icon: '🤖' });
      const response = await apiClient.post('/admin/process-existing-bills-with-ai', null, {
        params: {
          limit: 20
        }
      });

      if (response.status === 200) {
        const data = response.data;
        if (data.success) {
          toast.success(`AI analysis completed on ${data.processed_bills?.length || 0} bills!`);
          loadBills(); // Refresh the list
        } else {
          toast.error(`AI analysis failed: ${data.error || 'Unknown error'}`);
        }
      }
    } catch (error) {
      console.error('Error running AI analysis:', error);
      toast.error('Failed to run AI analysis');
    } finally {
      setLoadingBills(false);
    }
  };

  // Run AI analysis on a specific bill
  const runAIAnalysisOnBill = async (billId: string, billNumber: string) => {
    try {
      toast(`Running AI analysis on ${billNumber}...`, { icon: '🤖' });
      const response = await apiClient.post('/admin/process-bill-details', {
        bill_number: billNumber,
        session: '118',
        environment: 'development',
        use_enhanced_analysis: false // Use cost-optimized span-grounded analysis
      });

      if (response.status === 200) {
        const data = response.data;
        if (data.success) {
          toast.success(`AI analysis completed for ${billNumber}!`);
          loadBills(); // Refresh the list
        } else {
          toast.error(`AI analysis failed for ${billNumber}: ${data.detail || 'Unknown error'}`);
        }
      }
    } catch (error) {
      console.error(`Error running AI analysis on ${billNumber}:`, error);
      toast.error(`Failed to run AI analysis on ${billNumber}`);
    }
  };

  const handleEditBill = (bill: any) => {
    setEditingBill(bill);
    setEditFormData({
      title: bill.title || '',
      bill_number: bill.bill_number || '',
      ai_summary: bill.ai_summary || '',
      status: bill.status || 'introduced',
      is_featured: bill.is_featured || false
    });
  };

  const handleSaveBill = async () => {
    if (!editingBill) return;

    try {
      const response = await apiClient.put(`/bills/${editingBill.id}`, editFormData);
      
      if (response.status === 200) {
        toast.success('Bill updated successfully');
        setEditingBill(null);
        setEditFormData({});
        loadBills(); // Refresh the list
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error updating bill:', error);
      toast.error('Failed to update bill');
    }
  };

  const handleDeleteBill = async (billId: string, billNumber: string) => {
    if (!confirm(`Are you sure you want to delete bill ${billNumber}? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await apiClient.delete(`/bills/${billId}`);
      
      if (response.status === 200) {
        toast.success('Bill deleted successfully');
        loadBills(); // Refresh the list
      } else {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error deleting bill:', error);
      toast.error('Failed to delete bill');
    }
  };

  const cancelEdit = () => {
    setEditingBill(null);
    setEditFormData({});
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
            <p className="mt-1 text-sm text-gray-600">
              Manage bills, test AI processing, and monitor system analytics
            </p>
          </div>

          <div className="p-6 space-y-8">
            {/* Bill Details System Section - NEW */}
            <div className="p-6 bg-purple-50 rounded-lg border border-purple-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-purple-900">🏗️ Bill Details System</h3>
                <div className="flex gap-2">
                  <span className="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">NEW SYSTEM</span>
                </div>
              </div>
              
              <p className="text-sm text-purple-800 mb-6">
                Citation-based bill analysis with AI-generated content, moderation workflows, and SEO optimization.
                Access: <a href="http://localhost:8001/api/v1/admin/" target="_blank" className="underline font-medium">API Admin UI</a>
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Bill Details Processing */}
                <div className="bg-white rounded-lg p-4 border">
                  <h4 className="font-medium text-purple-900 mb-3">Process Bill Details</h4>
                  <div className="space-y-3">
                    <div className="flex gap-2">
                      <input
                        type="text"
                        id="billDetailsNumber"
                        placeholder="HR1"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-purple-500"
                      />
                      <button
                        onClick={() => {
                          const billNumber = (document.getElementById('billDetailsNumber') as HTMLInputElement).value;
                          if (billNumber.trim()) {
                            // Call the new bill details API
                            fetch(`${process.env.NEXT_PUBLIC_API_URL?.replace('/api/v1', '') || 'http://localhost:8001'}/api/v1/admin/process-bill-details`, {
                              method: 'POST',
                              headers: { 'Content-Type': 'application/json' },
                              body: JSON.stringify({ bill_number: billNumber.trim(), session: '118', environment: 'development' })
                            })
                            .then(res => res.json())
                            .then(data => {
                              if (data.success) {
                                toast.success(`Bill details processed: ${data.title}`);
                                // Open the bill details page
                                const slug = `${billNumber.toLowerCase().replace(/[^a-z0-9]/g, '')}-118`;
                                window.open(`/bills/${slug}`, '_blank');
                              } else {
                                toast.error(`Processing failed: ${data.detail || data.message}`);
                              }
                            })
                            .catch(err => toast.error(`Error: ${err.message}`));
                          }
                        }}
                        className="px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700"
                      >
                        Process
                      </button>
                    </div>
                    
                    <div className="text-xs text-purple-700">
                      <p><strong>Features:</strong> AI analysis, citations, moderation banner, SEO optimization</p>
                    </div>
                  </div>
                </div>

                {/* System Status & Links */}
                <div className="bg-white rounded-lg p-4 border">
                  <h4 className="font-medium text-purple-900 mb-3">Quick Links & Status</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between items-center">
                      <span>API Admin Interface:</span>
                      <a href="http://localhost:8001/api/v1/admin/" target="_blank" className="text-purple-600 hover:text-purple-500 underline">
                        Open →
                      </a>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Example Bill Details:</span>
                      <a href="/bills/5-118" target="_blank" className="text-purple-600 hover:text-purple-500 underline">
                        View HR5-118 →
                      </a>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>OpenAI Status:</span>
                      <span className="text-red-600 text-xs">⚠️ Key needed</span>
                    </div>
                    
                    <div className="mt-3 p-2 bg-purple-50 rounded text-xs">
                      <p><strong>Setup:</strong> Add real OpenAI API key to <code>/apps/api/.env</code> for full AI processing</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-white rounded border">
                <p className="text-xs text-purple-700">
                  <strong>Bill Details vs Legacy System:</strong> The Bill Details system creates SEO pages with citations and moderation workflows. 
                  The legacy system below processes bills for action tracking. Both can work together.
                </p>
              </div>
            </div>

            {/* Bills Management Section - Moved to top */}
            <div className="p-6 bg-orange-50 rounded-lg border border-orange-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-orange-900">📄 Bills Management & Workflow Tracking</h3>
                <div className="flex gap-2">
                  <button
                    onClick={fetchAllCurrentBills}
                    disabled={loadingBills}
                    className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                      loadingBills
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    {loadingBills ? 'Fetching...' : '🔄 Fetch All Current Bills'}
                  </button>
                  <button
                    onClick={loadBillsWithWorkflowStatus}
                    disabled={loadingBills}
                    className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                      loadingBills
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-orange-600 hover:bg-orange-700'
                    }`}
                  >
                    {loadingBills ? 'Loading...' : '📋 Refresh with Status'}
                  </button>
                </div>
              </div>
              
              <p className="text-sm text-orange-800 mb-6">
                View, edit, and delete bills in the database. Track workflow status and run importance scoring.
              </p>

              {/* Workflow Summary */}
              {workflowSummary && (
                <div className="mb-6 p-4 bg-white rounded-lg border">
                  <h4 className="font-medium text-gray-900 mb-3">📊 Workflow Summary ({workflowSummary.total_bills} bills)</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{workflowSummary.workflow_breakdown.fully_processed}</div>
                      <div className="text-xs text-gray-600">Fully Processed</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{workflowSummary.workflow_breakdown.ai_complete}</div>
                      <div className="text-xs text-gray-600">AI Complete</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">{workflowSummary.workflow_breakdown.scored_only}</div>
                      <div className="text-xs text-gray-600">Scored Only</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-600">{workflowSummary.workflow_breakdown.pulled_only}</div>
                      <div className="text-xs text-gray-600">Pulled Only</div>
                    </div>
                  </div>
                  
                  {workflowSummary.importance_breakdown && (
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex justify-between">
                        <span>Auto-process eligible:</span>
                        <span className="font-medium text-green-600">{workflowSummary.importance_breakdown.auto_process_eligible}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Manual processing available:</span>
                        <span className="font-medium text-blue-600">{workflowSummary.importance_breakdown.manual_process_available}</span>
                      </div>
                    </div>
                  )}
                  
                  <div className="mt-3 pt-3 border-t">
                    <div className="flex justify-between text-sm">
                      <span>Total AI costs:</span>
                      <span className="font-medium">${workflowSummary.total_ai_costs}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Batch Processing Controls */}
              {selectedBillsForProcessing.length > 0 && (
                <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-medium text-blue-900">
                        {selectedBillsForProcessing.length} bills selected for processing
                      </span>
                      <div className="text-sm text-blue-700 mt-1">
                        These bills will be sent through the complete AI analysis workflow
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setSelectedBillsForProcessing([])}
                        className="px-3 py-1 text-sm text-blue-700 hover:text-blue-900"
                      >
                        Clear Selection
                      </button>
                      <button
                        onClick={batchProcessBillDetails}
                        disabled={loadingBills}
                        className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                          loadingBills
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-blue-600 hover:bg-blue-700'
                        }`}
                      >
                        {loadingBills ? 'Processing...' : '🤖 Run AI Analysis'}
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Bills List */}
              {bills.length > 0 && (
                <div className="bg-white rounded-lg border overflow-hidden">
                  <div className="px-4 py-3 bg-gray-50 border-b">
                    <p className="text-sm text-gray-700">
                      Showing {bills.length} bills from the database
                    </p>
                  </div>
                  <div className="max-h-96 overflow-y-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50 sticky top-0">
                        <tr>
                          <th className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Select
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Bill
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Workflow Status
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Importance
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            AI Cost
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {bills.map((bill) => (
                          <tr key={bill.id} className="hover:bg-gray-50">
                            <td className="px-2 py-4 whitespace-nowrap">
                              <input
                                type="checkbox"
                                checked={selectedBillsForProcessing.includes(bill.id)}
                                onChange={() => toggleBillSelection(bill.id)}
                                disabled={bill.workflow_status === 'fully_processed'}
                                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                              />
                            </td>
                            <td className="px-4 py-4">
                              <div className="text-sm font-medium text-gray-900">{bill.bill_number}</div>
                              <div className="text-sm text-gray-500 max-w-xs truncate" title={bill.title}>
                                {bill.title}
                              </div>
                              <div className="text-xs text-gray-400">ID: {bill.id.substring(0, 8)}...</div>
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap">
                              <div className="flex flex-col gap-1">
                                <span className={`px-2 py-1 text-xs rounded-full ${
                                  bill.workflow_status === 'fully_processed' ? 'bg-green-100 text-green-800' :
                                  bill.workflow_status === 'ai_complete' ? 'bg-blue-100 text-blue-800' :
                                  bill.workflow_status === 'scored' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {bill.workflow_status === 'fully_processed' ? '✅ Complete' :
                                   bill.workflow_status === 'ai_complete' ? '🤖 AI Done' :
                                   bill.workflow_status === 'scored' ? '📊 Scored' :
                                   '⭕ Pulled Only'}
                                </span>
                                {bill.details_created_at && (
                                  <div className="text-xs text-gray-500">
                                    {new Date(bill.details_created_at).toLocaleDateString()}
                                  </div>
                                )}
                              </div>
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap">
                              {bill.importance ? (
                                <div className="flex flex-col gap-1">
                                  <div className="flex items-center gap-2">
                                    <span className={`px-2 py-1 text-xs rounded-full ${
                                      bill.importance.level === 'critical' ? 'bg-red-100 text-red-800' :
                                      bill.importance.level === 'high' ? 'bg-orange-100 text-orange-800' :
                                      bill.importance.level === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                      bill.importance.level === 'low' ? 'bg-blue-100 text-blue-800' :
                                      'bg-gray-100 text-gray-800'
                                    }`}>
                                      {bill.importance.score}/100
                                    </span>
                                    <span className="text-xs text-gray-500">
                                      {bill.importance.level}
                                    </span>
                                  </div>
                                  <div className="text-xs text-gray-400">
                                    {bill.importance.auto_process ? '🔄 Auto' : '👆 Manual'}
                                  </div>
                                </div>
                              ) : (
                                <span className="text-xs text-gray-400">Not scored</span>
                              )}
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap">
                              <div className="text-sm">
                                {bill.total_ai_cost > 0 ? (
                                  <div className="flex flex-col">
                                    <span className="text-green-600 font-medium">
                                      ${bill.total_ai_cost}
                                    </span>
                                    <span className="text-xs text-gray-500">
                                      {bill.ai_usage_count} call{bill.ai_usage_count !== 1 ? 's' : ''}
                                    </span>
                                  </div>
                                ) : (
                                  <span className="text-gray-400">$0.0000</span>
                                )}
                              </div>
                            </td>
                            <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex flex-col gap-1">
                                <div className="flex gap-1">
                                  {bill.workflow_status !== 'fully_processed' && (
                                    <button
                                      onClick={() => runAIAnalysisOnBill(bill.id, bill.bill_number)}
                                      className={`px-2 py-1 text-xs rounded ${
                                        bill.has_ai_summary
                                          ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                                          : 'bg-green-100 text-green-800 hover:bg-green-200'
                                      }`}
                                      title={bill.has_ai_summary ? 'Re-run AI analysis' : 'Run AI analysis'}
                                    >
                                      {bill.has_ai_summary ? '🔄' : '🤖'}
                                    </button>
                                  )}
                                  <button
                                    onClick={() => handleEditBill(bill)}
                                    className="text-blue-600 hover:text-blue-900 px-2 py-1 text-xs rounded hover:bg-blue-50"
                                    title="Edit bill"
                                  >
                                    ✏️
                                  </button>
                                  <button
                                    onClick={() => handleDeleteBill(bill.id, bill.bill_number)}
                                    className="text-red-600 hover:text-red-900 px-2 py-1 text-xs rounded hover:bg-red-50"
                                    title="Delete bill"
                                  >
                                    🗑️
                                  </button>
                                </div>
                                {bill.importance && bill.importance.key_indicators && (
                                  <div className="text-xs text-gray-400 max-w-40 truncate" title={bill.importance.key_indicators.join(', ')}>
                                    {bill.importance.key_indicators[0]}
                                  </div>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Loading State */}
              {loadingBills && (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto"></div>
                  <p className="text-orange-800 mt-2">Loading bills...</p>
                </div>
              )}

              {/* Empty State */}
              {!loadingBills && bills.length === 0 && (
                <div className="text-center py-8 text-orange-800">
                  <p>No bills loaded. Click "Load Bills" to view bills from the database.</p>
                </div>
              )}
            </div>

            {/* AI Usage & Cost Monitoring Section */}
            <div className="p-6 bg-emerald-50 rounded-lg border border-emerald-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-emerald-900">💰 AI Usage & Cost Monitoring</h3>
                <div className="flex gap-2">
                  <span className="px-2 py-1 text-xs bg-emerald-100 text-emerald-800 rounded-full">COST TRACKING</span>
                </div>
              </div>

              <p className="text-sm text-emerald-800 mb-6">
                Monitor AI token usage, costs, and performance across all operations. Track spending per bill and set budget alerts.
              </p>

              <AIUsageDashboard />
            </div>

            {/* Individual Bill Processing Section */}
            <div className="p-6 bg-blue-50 rounded-lg border border-blue-200">
              <h3 className="text-lg font-medium text-blue-900 mb-4">🔧 Individual Bill Processing</h3>
              <p className="text-sm text-blue-800 mb-4">
                Process a single bill from Congress.gov by bill ID (e.g., hr9-118)
              </p>

              {/* Bill ID Input */}
              <div className="mb-4">
                <label htmlFor="billId" className="block text-sm font-medium text-gray-700 mb-2">
                  Bill ID
                </label>
                <input
                  type="text"
                  id="billId"
                  value={billId}
                  onChange={(e) => setBillId(e.target.value)}
                  placeholder="hr9-118"
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4">
                <button
                  onClick={handlePullBill}
                  disabled={isProcessing || !billId}
                  className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                    isProcessing || !billId
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700'
                  }`}
                >
                  {isProcessing ? 'Processing...' : '🚀 Pull & Process Bill'}
                </button>

                <button
                  onClick={handleTestPreview}
                  disabled={isProcessing || !billId}
                  className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                    isProcessing || !billId
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-green-600 hover:bg-green-700'
                  }`}
                >
                  {isProcessing ? 'Processing...' : '📝 Test Message Preview'}
                </button>
              </div>
            </div>

            {/* Unified Bill Processing Section */}
            <div className="p-6 bg-green-50 rounded-lg border border-green-200">
              <h3 className="text-lg font-medium text-green-900 mb-4">🏭 Unified Bill Processing Pipeline</h3>
              <p className="text-sm text-green-800 mb-6">
                Process bills through the complete pipeline: Congress.gov → AI Analysis → Values Scoring → Database Storage
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Configuration Panel */}
                <div className="space-y-4">
                  <h4 className="font-medium text-blue-900">Configuration</h4>

                  {/* Environment Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Environment
                    </label>
                    <select
                      value={processingConfig.environment}
                      onChange={(e) => setProcessingConfig(prev => ({ ...prev, environment: e.target.value }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                      <option value="development">Development</option>
                      <option value="staging">Staging</option>
                      <option value="production">Production</option>
                    </select>
                  </div>

                  {/* Max Bills */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Number of Bills to Process
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="20"
                      value={processingConfig.maxBills}
                      onChange={(e) => setProcessingConfig(prev => ({ ...prev, maxBills: parseInt(e.target.value) || 5 }))}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Fetches the most recent bills from Congress.gov (recommended: 5-10)
                    </p>
                  </div>

                  {/* Specific Bills */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Specific Bills (Optional)
                    </label>
                    <input
                      type="text"
                      value={processingConfig.specificBills}
                      onChange={(e) => setProcessingConfig(prev => ({ ...prev, specificBills: e.target.value }))}
                      placeholder="hr9-118, s1234-118"
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Comma-separated list of specific bill IDs to process
                    </p>
                  </div>
                </div>

                {/* Action Panel */}
                <div className="space-y-4">
                  <h4 className="font-medium text-blue-900">Actions</h4>

                  <button
                    onClick={handleUnifiedProcess}
                    disabled={unifiedProcessing}
                    className={`w-full px-4 py-3 text-sm font-medium text-white rounded-md ${
                      unifiedProcessing
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    {unifiedProcessing ? 'Processing Bills...' : 'Start Unified Processing'}
                  </button>

                  <button
                    onClick={fetchProcessingStatus}
                    disabled={loadingStatus}
                    className={`w-full px-4 py-3 text-sm font-medium text-white rounded-md ${
                      loadingStatus
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-green-600 hover:bg-green-700'
                    }`}
                  >
                    {loadingStatus ? 'Loading...' : 'Check Processing Status'}
                  </button>

                  <button
                    onClick={handleTriggerValuesAnalysis}
                    className="w-full px-4 py-3 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md"
                  >
                    Trigger Values Analysis
                  </button>

                  <div className="text-xs text-gray-600">
                    <p>Unified pipeline includes:</p>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li>Fetch bills from Congress.gov</li>
                      <li>Download full bill text</li>
                      <li>Generate AI summaries & analysis</li>
                      <li>Run values scoring system</li>
                      <li>Store bills only (no campaigns)</li>
                    </ul>
                  </div>

                  {/* Processing Status Display */}
                  {processingStatus && (
                    <div className="mt-4 p-4 bg-white rounded-lg border">
                      <h5 className="font-medium text-gray-900 mb-2">System Status</h5>
                      {processingStatus.error ? (
                        <div className="text-red-700">
                          <p>❌ Status check failed: {processingStatus.error}</p>
                        </div>
                      ) : (
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Environment:</span>
                            <span className="font-medium">{processingStatus.environment}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Bills pending values analysis:</span>
                            <span className="font-medium">{processingStatus.bills_pending_values || 0}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Last processing run:</span>
                            <span className="font-medium">{processingStatus.last_run || 'Never'}</span>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Results Display */}
                  {unifiedResults && (
                    <div className="mt-4 p-4 bg-white rounded-lg border">
                      <h5 className="font-medium text-gray-900 mb-2">Processing Results</h5>
                      {unifiedResults.success ? (
                        <div className="space-y-2">
                          {/* Processed Bills */}
                          {unifiedResults.processed_bills && unifiedResults.processed_bills.length > 0 && (
                            <div className="text-green-700">
                              <p>✅ Successfully processed {unifiedResults.processed_bills.length} bills</p>
                              <div className="mt-2">
                                <p className="font-medium">New bills processed:</p>
                                <ul className="text-sm list-disc list-inside">
                                  {unifiedResults.processed_bills.slice(0, 5).map((bill: any, idx: number) => (
                                    <li key={idx}>{bill.bill_number || bill} - {bill.title || 'Processing...'}</li>
                                  ))}
                                  {unifiedResults.processed_bills.length > 5 && (
                                    <li>... and {unifiedResults.processed_bills.length - 5} more</li>
                                  )}
                                </ul>
                              </div>
                            </div>
                          )}
                          
                          {/* Skipped Bills */}
                          {unifiedResults.skipped_bills && unifiedResults.skipped_bills.length > 0 && (
                            <div className="text-blue-700">
                              <p>ℹ️ Skipped {unifiedResults.skipped_bills.length} bills (already exist)</p>
                              <div className="mt-1">
                                <p className="font-medium text-sm">Bills already in database:</p>
                                <ul className="text-sm list-disc list-inside">
                                  {unifiedResults.skipped_bills.slice(0, 5).map((bill: string, idx: number) => (
                                    <li key={idx}>{bill}</li>
                                  ))}
                                  {unifiedResults.skipped_bills.length > 5 && (
                                    <li>... and {unifiedResults.skipped_bills.length - 5} more</li>
                                  )}
                                </ul>
                              </div>
                            </div>
                          )}
                          
                          {/* Failed Bills */}
                          {unifiedResults.failed_bills && unifiedResults.failed_bills.length > 0 && (
                            <div className="text-red-700">
                              <p>❌ Failed to process {unifiedResults.failed_bills.length} bills</p>
                              <div className="mt-1">
                                <p className="font-medium text-sm">Failed bills:</p>
                                <ul className="text-sm list-disc list-inside">
                                  {unifiedResults.failed_bills.slice(0, 5).map((bill: string, idx: number) => (
                                    <li key={idx}>{bill}</li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          )}
                          
                          {/* Summary */}
                          <div className="text-gray-700 text-sm pt-2 border-t">
                            <p><strong>Summary:</strong> {unifiedResults.message}</p>
                            <p><strong>Environment:</strong> {unifiedResults.environment}</p>
                            <p><strong>Processing Type:</strong> {unifiedResults.processing_type}</p>
                          </div>
                        </div>
                      ) : (
                        <div className="text-red-700">
                          <p>❌ Processing failed: {unifiedResults.error}</p>
                        </div>
                      )}

                      {unifiedResults.errors && unifiedResults.errors.length > 0 && (
                        <div className="mt-2">
                          <p className="font-medium text-red-700">Errors:</p>
                          <ul className="text-sm text-red-600 list-disc list-inside">
                            {unifiedResults.errors.slice(0, 3).map((error: string, idx: number) => (
                              <li key={idx}>{error}</li>
                            ))}
                            {unifiedResults.errors.length > 3 && (
                              <li>... and {unifiedResults.errors.length - 3} more errors</li>
                            )}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Values Analysis Review Section */}
            <div className="mt-8 p-6 bg-purple-50 rounded-lg border border-purple-200">
              <h3 className="text-lg font-medium text-purple-900 mb-4">🔍 Values Analysis Review</h3>
              <p className="text-sm text-purple-800 mb-4">
                Review AI-flagged bills and approve final tags and scores for the values analysis system.
              </p>
              <a
                href="/admin/review"
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700"
              >
                Open Review Queue
              </a>
            </div>


            {/* Action Tracking Analytics Section */}
            <div className="mt-8 p-6 bg-indigo-50 rounded-lg border border-indigo-200">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-indigo-900">📊 Action Tracking Analytics</h3>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      setShowDetailedView(!showDetailedView);
                      if (!showDetailedView && detailedActions.length === 0) {
                        loadDetailedActions();
                      }
                    }}
                    className="px-3 py-1 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md"
                  >
                    {showDetailedView ? 'Show Analytics' : 'Show All Actions'}
                  </button>
                  <button
                    onClick={showDetailedView ? () => loadDetailedActions() : loadActionAnalytics}
                    disabled={loadingAnalytics || loadingDetails}
                    className={`px-3 py-1 text-sm font-medium text-white rounded-md ${
                      loadingAnalytics || loadingDetails
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    {loadingAnalytics || loadingDetails ? 'Loading...' : 'Refresh Data'}
                  </button>
                </div>
              </div>
              
              <p className="text-sm text-indigo-800 mb-6">
                View user action data, reasoning patterns, and engagement analytics for bills.
              </p>

              {/* Bill Selection */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-blue-900 mb-2">
                  Select Bill for Analysis
                </label>
                <select
                  value={selectedBillId}
                  onChange={(e) => {
                    setSelectedBillId(e.target.value);
                    // Refresh data when bill selection changes
                    setTimeout(() => loadActionAnalytics(), 100);
                  }}
                  className="block w-full max-w-md px-3 py-2 border border-blue-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="bd9c4dfb-a7b7-406d-ac41-263f36548c50">Affordable Housing Development Act (Test Bill)</option>
                </select>
              </div>

              {/* Overall Statistics */}
              {actionStats && (
                <div className="mb-8 bg-white rounded-lg p-6 shadow-sm">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Overall Platform Statistics</h4>
                  
                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-blue-100 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-blue-800">{actionStats.total_actions}</div>
                      <div className="text-sm text-blue-600">Total Actions</div>
                    </div>
                    <div className="bg-green-100 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-green-800">{actionStats.by_stance?.support || 0}</div>
                      <div className="text-sm text-green-600">Support Actions</div>
                    </div>
                    <div className="bg-purple-100 p-4 rounded-lg">
                      <div className="text-2xl font-bold text-purple-800">{actionStats.recent_actions.length}</div>
                      <div className="text-sm text-purple-600">Recent Activities</div>
                    </div>
                  </div>

                  {/* Recent Actions */}
                  {actionStats.recent_actions.length > 0 && (
                    <div>
                      <h5 className="text-md font-semibold text-gray-800 mb-3">Recent User Actions</h5>
                      <div className="space-y-2 max-h-64 overflow-y-auto">
                        {actionStats.recent_actions.slice(0, 5).map((action, idx) => (
                          <div key={action.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div className="flex-1">
                              <div className="text-sm font-medium text-gray-900">{action.bill_title}</div>
                              <div className="text-xs text-gray-500">
                                {action.user_email} • {action.stance} • {new Date(action.created_at || '').toLocaleDateString()}
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                action.stance === 'support' ? 'bg-green-100 text-green-800' :
                                action.stance === 'oppose' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {action.stance}
                              </span>
                              {action.selected_reasons && action.selected_reasons.length > 0 && (
                                <span className="text-xs text-gray-500">
                                  {action.selected_reasons.length} reasons
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Bill-Specific Analytics */}
              {billAnalytics.length > 0 && (
                <div className="space-y-6">
                  {billAnalytics.map((bill, index) => (
                    <div key={bill.bill_id} className="bg-white rounded-lg p-6 shadow-sm">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">{bill.bill_title}</h4>
                      
                      {/* Action Stats */}
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div className="bg-green-100 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-green-800">{bill.stats.support}</div>
                          <div className="text-sm text-green-600">Support Actions</div>
                        </div>
                        <div className="bg-red-100 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-red-800">{bill.stats.oppose}</div>
                          <div className="text-sm text-red-600">Oppose Actions</div>
                        </div>
                        <div className="bg-yellow-100 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-yellow-800">{bill.stats.amend}</div>
                          <div className="text-sm text-yellow-600">Amend Actions</div>
                        </div>
                        <div className="bg-blue-100 p-4 rounded-lg">
                          <div className="text-2xl font-bold text-blue-800">{bill.stats.total}</div>
                          <div className="text-sm text-blue-600">Total Actions</div>
                        </div>
                      </div>

                      {/* Top Reasons */}
                      {bill.top_reasons.length > 0 && (
                        <div className="mb-6">
                          <h5 className="text-md font-semibold text-gray-800 mb-3">Top Reasons (Support)</h5>
                          <div className="space-y-2">
                            {bill.top_reasons.map((reason, idx) => (
                              <div key={idx} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <span className="text-sm text-gray-700">{reason.reason_text}</span>
                                <span className="text-sm font-medium text-gray-900">{reason.count} users</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Custom Reasons */}
                      {bill.custom_reasons.length > 0 && (
                        <div>
                          <h5 className="text-md font-semibold text-gray-800 mb-3">Recent Custom Reasons</h5>
                          <div className="space-y-2 max-h-48 overflow-y-auto">
                            {bill.custom_reasons.map((reason, idx) => (
                              <div key={idx} className="p-3 bg-gray-50 rounded-lg">
                                <span className="text-sm text-gray-700 italic">"{reason}"</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* No data message */}
                      {bill.stats.total === 0 && (
                        <div className="text-center py-8 text-gray-500">
                          <p>No action data available for this bill yet.</p>
                          <p className="text-sm mt-2">Actions will appear here once users start engaging with this bill.</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Loading State */}
              {loadingAnalytics && (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-blue-800 mt-4">Loading analytics data...</p>
                </div>
              )}

              {/* No data state */}
              {!loadingAnalytics && !showDetailedView && billAnalytics.length === 0 && (
                <div className="text-center py-8 text-blue-800">
                  <p>No analytics data loaded. Click "Refresh Data" to load the latest metrics.</p>
                </div>
              )}

              {/* Detailed Actions View */}
              {showDetailedView && (
                <div className="mt-6">
                  {loadingDetails ? (
                    <div className="text-center py-8">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="text-blue-800 mt-4">Loading detailed actions...</p>
                    </div>
                  ) : (
                    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                      <div className="px-6 py-4 border-b border-gray-200">
                        <h4 className="text-lg font-semibold text-gray-900">All Action Details</h4>
                        <p className="text-sm text-gray-600 mt-1">
                          Complete information for all user actions ({detailedActions.length} actions)
                        </p>
                      </div>
                      
                      {detailedActions.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {detailedActions.map((action) => (
                                <React.Fragment key={action.id}>
                                  <tr className="hover:bg-gray-50">
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex items-center">
                                      <span className={`px-2 py-1 text-xs rounded-full ${
                                        action.stance === 'support' ? 'bg-green-100 text-green-800' :
                                        action.stance === 'oppose' ? 'bg-red-100 text-red-800' :
                                        'bg-yellow-100 text-yellow-800'
                                      }`}>
                                        {action.stance}
                                      </span>
                                      <span className="ml-2 text-xs text-gray-500">
                                        {new Date(action.created_at || '').toLocaleDateString()}
                                      </span>
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">{action.user_name}</div>
                                    <div className="text-sm text-gray-500">{action.user_email}</div>
                                    <div className="text-xs text-gray-400">ID: {action.user_id?.substring(0, 8)}...</div>
                                  </td>
                                  <td className="px-6 py-4">
                                    <div className="text-sm font-medium text-gray-900 max-w-xs truncate">{action.bill_title}</div>
                                    <div className="text-xs text-gray-400">{action.bill_id?.substring(0, 8)}...</div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <span className={`px-2 py-1 text-xs rounded-full ${
                                      action.status === 'SENT' ? 'bg-green-100 text-green-800' :
                                      action.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                                      action.status === 'FAILED' ? 'bg-red-100 text-red-800' :
                                      'bg-gray-100 text-gray-800'
                                    }`}>
                                      {action.status}
                                    </span>
                                  </td>
                                  <td className="px-6 py-4">
                                    <button
                                      onClick={() => setSelectedActionId(selectedActionId === action.id ? null : action.id)}
                                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                    >
                                      {selectedActionId === action.id ? 'Hide Details' : 'View Details'}
                                    </button>
                                  </td>
                                </tr>
                                {selectedActionId === action.id && (
                                  <tr>
                                    <td colSpan={5} className="px-6 py-4 bg-gray-50">
                                      <div className="space-y-4">
                                        {/* Message Content */}
                                        <div>
                                          <h5 className="font-semibold text-gray-900 mb-2">Message Sent</h5>
                                          <div className="bg-white p-4 rounded-lg border">
                                            <div className="text-sm font-medium text-gray-900 mb-2">Subject: {action.subject}</div>
                                            <div className="text-sm text-gray-700 whitespace-pre-wrap max-h-48 overflow-y-auto">
                                              {action.message}
                                            </div>
                                          </div>
                                        </div>

                                        {/* User Reasoning */}
                                        {(action.selected_reasons.length > 0 || action.custom_reason) && (
                                          <div>
                                            <h5 className="font-semibold text-gray-900 mb-2">User's Reasoning</h5>
                                            <div className="bg-white p-4 rounded-lg border">
                                              {action.selected_reasons.length > 0 && (
                                                <div className="mb-3">
                                                  <div className="text-sm font-medium text-gray-700 mb-1">Selected Reasons:</div>
                                                  <ul className="list-disc list-inside space-y-1">
                                                    {action.selected_reasons.map((reason, idx) => (
                                                      <li key={idx} className="text-sm text-gray-600">{reason.reason_text}</li>
                                                    ))}
                                                  </ul>
                                                </div>
                                              )}
                                              {action.custom_reason && (
                                                <div>
                                                  <div className="text-sm font-medium text-gray-700 mb-1">Custom Reason:</div>
                                                  <div className="text-sm text-gray-600 italic">"{action.custom_reason}"</div>
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        )}

                                        {/* Representatives Contacted */}
                                        {action.representatives_contacted.length > 0 && (
                                          <div>
                                            <h5 className="font-semibold text-gray-900 mb-2">Representatives Contacted</h5>
                                            <div className="bg-white p-4 rounded-lg border">
                                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                                {action.representatives_contacted.map((rep, idx) => (
                                                  <div key={idx} className="text-sm">
                                                    <span className="font-medium">{rep.name}</span> ({rep.title}, {rep.party})
                                                  </div>
                                                ))}
                                              </div>
                                            </div>
                                          </div>
                                        )}

                                        {/* User Location */}
                                        {action.user_location && Object.keys(action.user_location).length > 0 && (
                                          <div>
                                            <h5 className="font-semibold text-gray-900 mb-2">User Location</h5>
                                            <div className="bg-white p-4 rounded-lg border">
                                              <div className="text-sm text-gray-600">
                                                {action.user_location.address && <div>Address: {action.user_location.address}</div>}
                                                <div>
                                                  {action.user_location.city}, {action.user_location.state} {action.user_location.zip_code}
                                                </div>
                                              </div>
                                            </div>
                                          </div>
                                        )}

                                        {/* Technical Details */}
                                        <div>
                                          <h5 className="font-semibold text-gray-900 mb-2">Technical Details</h5>
                                          <div className="bg-white p-4 rounded-lg border">
                                            <div className="grid grid-cols-2 gap-4 text-sm">
                                              <div>
                                                <span className="font-medium">Action ID:</span> {action.id}
                                              </div>
                                              <div>
                                                <span className="font-medium">Delivery Method:</span> {action.delivery_method || 'N/A'}
                                              </div>
                                              <div>
                                                <span className="font-medium">Created:</span> {new Date(action.created_at || '').toLocaleString()}
                                              </div>
                                              <div>
                                                <span className="font-medium">Sent:</span> {action.sent_at ? new Date(action.sent_at).toLocaleString() : 'Not sent'}
                                              </div>
                                              {action.error_message && (
                                                <div className="col-span-2">
                                                  <span className="font-medium text-red-600">Error:</span> 
                                                  <span className="text-red-600 ml-1">{action.error_message}</span>
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </td>
                                  </tr>
                                )}
                                </React.Fragment>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-gray-500">
                          <p>No detailed actions found.</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Results Display */}
            {results && (
              <div className="mt-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Results</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <pre className="text-sm text-gray-700 whitespace-pre-wrap overflow-auto max-h-96">
                    {JSON.stringify(results, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {/* Instructions */}
            <div className="mt-8 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">📋 Quick Guide:</h4>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• <strong>Bills Management:</strong> Load, edit, and delete bills directly from the database</li>
                <li>• <strong>Individual Processing:</strong> Fetch and process specific bills by ID (e.g., hr9-118)</li>
                <li>• <strong>Batch Processing:</strong> Process multiple recent bills automatically with AI analysis</li>
                <li>• <strong>Values Analysis:</strong> Review AI-flagged bills and approve final tags/scores</li>
                <li>• <strong>Analytics:</strong> Monitor user engagement and action patterns</li>
                <li>• <strong>⚠️ Caution:</strong> Bill deletions are permanent - no undo available</li>
                <li>• Check browser console for detailed error messages</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Edit Bill Modal */}
        {editingBill && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  ✏️ Edit Bill: {editingBill.bill_number}
                </h3>
                <div className="text-xs text-gray-500">
                  ID: {editingBill.id.substring(0, 8)}...
                </div>
              </div>
              
              <div className="space-y-4">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Title
                  </label>
                  <input
                    type="text"
                    value={editFormData.title}
                    onChange={(e) => setEditFormData({...editFormData, title: e.target.value})}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Bill Number */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bill Number
                  </label>
                  <input
                    type="text"
                    value={editFormData.bill_number}
                    onChange={(e) => setEditFormData({...editFormData, bill_number: e.target.value})}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={editFormData.status}
                    onChange={(e) => setEditFormData({...editFormData, status: e.target.value})}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="introduced">Introduced</option>
                    <option value="committee">In Committee</option>
                    <option value="passed_house">Passed House</option>
                    <option value="passed_senate">Passed Senate</option>
                    <option value="passed">Passed Both Chambers</option>
                    <option value="failed">Failed</option>
                    <option value="signed">Signed into Law</option>
                    <option value="vetoed">Vetoed</option>
                  </select>
                </div>

                {/* Featured */}
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={editFormData.is_featured}
                      onChange={(e) => setEditFormData({...editFormData, is_featured: e.target.checked})}
                      className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    />
                    <span className="ml-2 text-sm text-gray-700">Featured Bill</span>
                  </label>
                </div>

                {/* AI Summary */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    AI Summary
                  </label>
                  <textarea
                    rows={4}
                    value={editFormData.ai_summary}
                    onChange={(e) => setEditFormData({...editFormData, ai_summary: e.target.value})}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="AI-generated summary of the bill..."
                  />
                </div>
              </div>

              {/* Modal Actions */}
              <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                <button
                  onClick={cancelEdit}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveBill}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
