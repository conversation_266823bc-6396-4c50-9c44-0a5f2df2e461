# Officials Identity System - Complete Documentation

## Overview

The Officials Identity System is a comprehensive data foundation that provides authoritative, up-to-date information about elected officials in the United States. Built on the OpenStates API, it implements a sophisticated caching and refresh architecture to maintain data integrity while supporting public profile pages and user engagement features.

## Architecture

### Core Components

1. **Database Layer** - Enhanced PostgreSQL schema with rich official data
2. **Service Layer** - Cache-or-fetch logic with OpenStates API integration  
3. **API Layer** - RESTful endpoints for official lookup and management
4. **Administrative Tools** - Backfill scripts and automated refresh processes
5. **AWS Lambda Integration** - Automated weekly data refresh

### Key Features

- **Comprehensive Official Data**: Federal and state legislators with social media, committees, voting records
- **Smart Caching**: 30-day TTL with automatic refresh from OpenStates API
- **Social Media Integration**: Structured parsing of Twitter, Facebook, Instagram, YouTube, LinkedIn
- **Public Profile Ready**: Committee memberships, leadership positions, voting statistics
- **Administrative Tools**: CLI scripts for bulk data population and maintenance
- **Automated Maintenance**: Weekly Lambda function for data freshness

## Database Schema

### Enhanced Officials Table

The `officials` table was enhanced with the following new fields:

```sql
-- Core Identity Enhancement
first_name VARCHAR(100)           -- Parsed first name
last_name VARCHAR(100)            -- Parsed last name  
full_name VARCHAR(255)            -- Complete formatted name

-- Additional External IDs
govtrack_id VARCHAR(50)           -- GovTrack.us identifier

-- Enhanced Contact Information
dc_office_phone VARCHAR(50)       -- Washington D.C. office phone
dc_office_address TEXT            -- D.C. office address
local_office_phone VARCHAR(50)    -- Local/state office phone
local_office_address TEXT         -- Local office address

-- Committee and Legislative Information
committees TEXT                   -- JSON array of committee memberships
leadership_positions TEXT         -- JSON array of leadership roles

-- Enhanced Term Information  
current_term_start VARCHAR(50)    -- Current term start date
current_term_end VARCHAR(50)      -- Current term end date
next_election_date VARCHAR(50)    -- Next election date

-- Additional Social Media
youtube_channel VARCHAR(255)      -- YouTube channel URL
linkedin_url VARCHAR(255)         -- LinkedIn profile URL

-- Public Profile Enhancement
official_photo_url VARCHAR(255)   -- Official government photo
homepage_url VARCHAR(255)         -- Official homepage URL

-- Voting Statistics
bills_sponsored_count INTEGER     -- Number of bills sponsored
bills_cosponsored_count INTEGER   -- Number of bills co-sponsored  
votes_cast_count INTEGER          -- Number of votes cast
```

### Performance Indexes

```sql
-- Name-based searches
CREATE INDEX idx_officials_first_last_name ON officials(first_name, last_name);
CREATE INDEX idx_officials_full_name ON officials(full_name);

-- External ID lookups
CREATE INDEX idx_officials_govtrack_id ON officials(govtrack_id);

-- Term-based queries
CREATE INDEX idx_officials_current_term ON officials(current_term_start, current_term_end);
```

## Service Architecture

### OfficialService Enhancement

The `OfficialService` class implements sophisticated cache-or-fetch logic:

```python
def get_officials_by_zip_code(self, zip_code: str) -> List[Official]:
    """
    Smart caching implementation:
    1. Check for cached officials (updated within 30 days)
    2. If cache miss, fetch from OpenStates API
    3. Create/update official records with enhanced data
    4. Return enriched official objects
    """
```

**Key Features:**
- 30-day cache TTL for optimal performance vs. freshness
- Automatic fallback to OpenStates API on cache miss
- Comprehensive error handling and logging
- Transaction safety with rollback on failures

### OpenStates API Integration

Enhanced `OpenStatesOfficialsAPI` with comprehensive data parsing:

```python
@dataclass
class OpenStatesOfficial:
    # Core fields
    id: str
    name: str
    party: Optional[str]
    title: str
    
    # Enhanced fields for public profiles
    first_name: Optional[str]
    last_name: Optional[str] 
    full_name: Optional[str]
    govtrack_id: Optional[str]
    bioguide_id: Optional[str]
    committees: Optional[str]  # JSON string
    leadership_positions: Optional[str]  # JSON string
    official_photo_url: Optional[str]
    homepage_url: Optional[str]
    youtube_channel: Optional[str]
    linkedin_url: Optional[str]
    # ... and more
```

**Social Media Parsing:**
The system intelligently parses social media links from OpenStates data:

```python
# Example parsed social_media JSON:
{
    "Twitter": "https://twitter.com/SenSchiff",
    "Facebook": "https://facebook.com/CongressmanSchiff", 
    "YouTube": "https://youtube.com/c/AdamSchiff",
    "LinkedIn": "https://linkedin.com/in/adamschiff",
    "Official Website": "https://schiff.house.gov"
}
```

## API Endpoints

### Core Endpoints

#### GET /api/v1/officials/by-zip/{zip_code}
**Primary user-facing endpoint for finding representatives**

```json
// Request
GET /api/v1/officials/by-zip/90210

// Response
[
  {
    "id": "d7c0f0a2-3ed9-42df-8b4b-86255ef75a5d",
    "name": "Adam Schiff", 
    "title": "Senator",
    "party": "Democratic",
    "email": "<EMAIL>",
    "phone": "(*************",
    "website": "https://schiff.senate.gov",
    "social_media": {
      "Twitter": "https://twitter.com/SenSchiff",
      "Facebook": "https://facebook.com/SenSchiff"
    },
    "committees": ["Intelligence", "Appropriations"],
    "bills_sponsored_count": 47,
    "preferred_contact_method": "email"
  }
  // ... more officials
]
```

#### GET /api/v1/officials/{official_id}
**Detailed official information for public profiles**

Returns complete `OfficialResponse` schema with all enhanced fields.

#### GET /api/v1/officials/search
**Advanced search functionality**

Supports filtering by:
- Name/title text search
- Government level (federal, state, local)  
- Chamber (house, senate, executive)
- State and district
- Political party

## Administrative Tools

### Backfill Script

**Location**: `/apps/api/backfill_officials.py`

Comprehensive CLI tool for populating the officials database:

```bash
# Backfill all states  
python backfill_officials.py

# Dry run to see what would happen
python backfill_officials.py --dry-run

# Process specific state
python backfill_officials.py --state CA

# Limit for testing
python backfill_officials.py --limit 10 --verbose

# Combined options
python backfill_officials.py --dry-run --state NY --limit 5
```

**Features:**
- Processes all 50 states + D.C. (51 jurisdictions)
- Uses representative ZIP codes for each state
- Comprehensive logging and error handling
- Statistics tracking and summary reporting
- Dry-run mode for safe testing
- Configurable limits for development

**State Coverage:**
The script includes ZIP codes for major cities in all states:
- CA: 90210 (Beverly Hills) → Federal reps for California
- NY: 10001 (Manhattan) → Federal reps for New York  
- TX: 75201 (Dallas) → Federal reps for Texas
- ... and 48 more states + D.C.

### Example Output

```
============================================================
BACKFILL SUMMARY
============================================================
Mode: LIVE
Duration: 0:15:42
States processed: 51
Officials created: 847
Officials updated: 23
Officials skipped: 0
Errors: 2
Total officials: 870
============================================================
```

## AWS Lambda Integration

### Automated Weekly Refresh

**Location**: `/apps/lambda/officials_refresh/handler.py`

Serverless function that automatically refreshes stale official data:

```python
def lambda_handler(event, context):
    """
    Weekly scheduled function that:
    1. Finds officials with data older than 30 days
    2. Refreshes their information from OpenStates API
    3. Updates database with fresh data
    4. Handles officials who have left office
    5. Returns comprehensive statistics
    """
```

**Key Features:**
- Finds stale officials (>30 days old)
- Batch processing with error handling
- API rate limiting and retry logic
- Comprehensive logging to CloudWatch
- Statistics reporting
- Handles edge cases (officials leaving office)

**CloudWatch Event Trigger:**
```json
{
  "version": "0",
  "id": "weekly-officials-refresh",
  "detail-type": "Scheduled Event", 
  "source": "aws.events",
  "account": "************",
  "time": "2025-08-06T09:00:00Z",
  "region": "us-east-1",
  "detail": {}
}
```

**Dependencies:**
- `psycopg2-binary==2.9.7` - PostgreSQL connectivity
- `requests==2.31.0` - HTTP client for OpenStates API

## Data Flow Architecture

### User Request Flow

```mermaid
graph TD
    A[User Request: GET /officials/by-zip/90210] --> B[OfficialService.get_officials_by_zip_code]
    B --> C{Check Cache: Officials updated < 30 days?}
    C -->|Cache Hit| D[Return Cached Officials]
    C -->|Cache Miss| E[OpenStatesAPI.get_officials_by_zip]
    E --> F[Parse & Enhance Data]
    F --> G[Create/Update Database Records]
    G --> H[Return Fresh Officials]
```

### Data Refresh Flow

```mermaid
graph TD
    A[Weekly Lambda Trigger] --> B[Find Stale Officials > 30 days]
    B --> C[For Each Stale Official]
    C --> D[Fetch from OpenStates API]
    D --> E{Official Found?}
    E -->|Yes| F[Update Database Record]
    E -->|No| G[Mark as Inactive]
    F --> H[Update Statistics]
    G --> H
    H --> I[Complete Batch]
    I --> J[Return Summary Report]
```

## Schema Enhancements

### Pydantic Models

Enhanced response schemas support all new fields:

```python
class OfficialResponse(OfficialBase):
    """Complete API response schema"""
    id: str
    created_at: datetime
    updated_at: datetime
    
    # External identifiers
    bioguide_id: Optional[str] = None
    openstates_id: Optional[str] = None
    google_civic_id: Optional[str] = None
    govtrack_id: Optional[str] = None
    
    # Rich social media data
    social_media: Optional[Dict[str, Any]] = None
    
    # Public profile enhancements
    committees: Optional[List[Dict[str, Any]]] = None
    leadership_positions: Optional[List[Dict[str, Any]]] = None
    bills_sponsored_count: Optional[int] = None
    bills_cosponsored_count: Optional[int] = None
    votes_cast_count: Optional[int] = None
```

## Performance Considerations

### Database Optimization

1. **Strategic Indexing**: Indexes on commonly queried fields (name, state, external IDs)
2. **JSON Storage**: Efficient storage of complex data (committees, social media) as JSON
3. **TTL Strategy**: 30-day cache reduces API calls while maintaining freshness

### API Rate Limiting

1. **OpenStates API**: Respectful usage with error handling and retries
2. **Batch Processing**: Lambda processes officials in manageable batches
3. **Geocoding**: Uses free Nominatim service for ZIP→coordinates conversion

### Caching Strategy

```python
# Cache hierarchy:
# 1. Database cache (30-day TTL)
# 2. API fallback (OpenStates)
# 3. Graceful degradation on failures

cache_cutoff = datetime.utcnow() - timedelta(days=30)
cached_officials = db.query(Official).filter(
    Official.updated_at >= cache_cutoff
).all()
```

## Error Handling

### Comprehensive Logging

All components include structured logging:

```python
logger.info(f"Found {len(officials)} officials for zip {zip_code}")
logger.warning(f"No cached data for zip {zip_code}, fetching from API")
logger.error(f"API request failed: {e}")
```

### Graceful Degradation

1. **API Failures**: System continues with cached data
2. **Invalid ZIP Codes**: Clear error messages to users
3. **Missing Officials**: Proper 404 handling
4. **Database Issues**: Transaction rollbacks and recovery

## Security Considerations

### API Key Management

```python
# Environment variable required
OPENSTATES_API_KEY = os.environ.get('OPENSTATES_API_KEY')

# Proper validation
if not self.api_key:
    logger.warning("API key not configured, features disabled")
    self.enabled = False
```

### Data Privacy

1. **Public Data Only**: System only stores publicly available official information
2. **No PII**: No personal identifiable information beyond public contact details
3. **Rate Limiting**: Respectful API usage prevents abuse

## Monitoring & Maintenance

### Health Checks

```python
def health_check(self) -> Dict[str, Any]:
    """Verify OpenStates API connectivity"""
    return {
        "status": "healthy",
        "message": "OpenStates API accessible", 
        "enabled": True
    }
```

### Statistics Tracking

The system tracks comprehensive metrics:
- Officials processed/created/updated
- API call counts and success rates  
- Error rates and types
- Processing duration and performance

### Automated Alerts

Lambda function provides detailed status reporting:
```json
{
  "statusCode": 200,
  "body": {
    "success": true,
    "duration_seconds": 45.2,
    "stale_officials_found": 23,
    "officials_refreshed": 21,
    "officials_failed": 2,
    "api_calls_made": 23,
    "timestamp": "2025-08-06T19:44:53.524Z"
  }
}
```

## Future Enhancements

### Planned Features

1. **Bill Tracking Integration**: Connect officials to their voting records
2. **Contact Success Metrics**: Track user engagement success rates
3. **Real-time Updates**: WebSocket integration for live data updates
4. **Advanced Search**: Full-text search across bio and position data
5. **Historical Data**: Track official position changes over time

### Scalability Improvements

1. **Redis Caching**: Add Redis layer for sub-second response times
2. **CDN Integration**: Cache static official photos and documents  
3. **Horizontal Scaling**: Support multiple API regions
4. **Bulk Operations**: Optimize for large-scale data operations

## Deployment Guide

### Prerequisites

1. **PostgreSQL Database**: Version 12+ with JSONB support
2. **OpenStates API Key**: Register at openstates.org/api/
3. **Python Environment**: Python 3.9+ with Poetry
4. **AWS Account**: For Lambda deployment (optional)

### Installation Steps

1. **Apply Database Migration**:
```bash
cd apps/api
poetry run alembic upgrade head
```

2. **Configure Environment Variables**:
```bash
export OPENSTATES_API_KEY=your_api_key_here
export DATABASE_URL=postgresql://user:pass@localhost:5432/modernaction
```

3. **Test the System**:
```bash
# Start API server
poetry run uvicorn app.main:app --reload

# Test endpoint
curl "http://localhost:8000/api/v1/officials/by-zip/90210"
```

4. **Run Backfill Script**:
```bash
# Dry run first
python backfill_officials.py --dry-run --limit 10

# Full backfill
python backfill_officials.py
```

5. **Deploy Lambda Function** (Optional):
```bash
cd apps/lambda/officials_refresh
pip install -r requirements.txt -t .
zip -r officials-refresh.zip .
aws lambda create-function --function-name officials-refresh --zip-file fileb://officials-refresh.zip
```

## Troubleshooting

### Common Issues

1. **OpenStates API Rate Limiting**:
   - Solution: Implement exponential backoff in requests
   - Check API key validity and quota

2. **Database Connection Errors**:
   - Verify DATABASE_URL format
   - Check database permissions
   - Ensure PostgreSQL is running

3. **ZIP Code Not Found**:
   - Verify ZIP code format (5 digits)
   - Check Nominatim geocoding service availability
   - Review ZIP code coverage in state mappings

4. **No Officials Returned**:
   - Check OpenStates API response for the area
   - Verify geographic coordinates are correct
   - Review OpenStates jurisdiction coverage

### Debugging Tools

1. **Verbose Logging**:
```bash
python backfill_officials.py --verbose
```

2. **API Health Check**:
```python
from app.services.openstates_officials_api import get_openstates_officials_client
client = get_openstates_officials_client()
print(client.health_check())
```

3. **Database Inspection**:
```sql
-- Check recent officials
SELECT name, title, updated_at, social_media 
FROM officials 
WHERE updated_at > NOW() - INTERVAL '1 day'
ORDER BY updated_at DESC;
```

## Conclusion

The Officials Identity System provides a robust, scalable foundation for official data management. With its sophisticated caching architecture, comprehensive data model, and automated maintenance tools, it ensures your platform has access to accurate, up-to-date information about elected officials across the United States.

The system is production-ready and designed to handle the full scope of civic engagement applications, from simple representative lookup to complex public profile pages with detailed voting records and social media integration.