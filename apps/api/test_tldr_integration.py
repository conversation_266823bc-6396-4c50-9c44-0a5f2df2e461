#!/usr/bin/env python3
"""
Test script to verify TL;DR integration is working correctly.

This script tests the complete flow:
1. Bill creation with AI processing
2. TL;DR generation and database save
3. Frontend data retrieval
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the app directory to the Python path
app_dir = Path(__file__).parent / "app"
sys.path.insert(0, str(app_dir))

from sqlalchemy.orm import Session
from app.db.database import SessionLocal
from app.services.bill_data_service import BillDataService
from app.services.bills import BillService
from app.models.bill import Bill

async def test_tldr_integration():
    """Test the complete TL;DR integration flow"""
    print("🧪 Testing TL;DR Integration")
    print("=" * 50)
    
    db = SessionLocal()
    
    try:
        # Test 1: Create a mock bill with AI processing
        print("\n📋 Test 1: Creating mock bill with AI processing...")
        
        bill_service = BillDataService(db)
        
        # Create a simple test bill data
        test_bill_info = {
            'title': 'Test TL;DR Integration Act',
            'number': 'TEST123',
            'bill_type': 'hr',
            'congress_session': 118,
            'summary': 'This is a test bill to verify that TL;DR generation is working correctly in our system.',
            'congress_gov_id': 'test-tldr-123',
            'sponsor': {'name': 'Test Sponsor', 'party': 'Independent', 'state': 'CA'}
        }
        
        test_full_text = """
        TEST TL;DR INTEGRATION ACT
        
        SECTION 1. SHORT TITLE.
        This Act may be cited as the "Test TL;DR Integration Act".
        
        SECTION 2. PURPOSE.
        The purpose of this Act is to test whether our TL;DR generation system is working correctly.
        This bill would establish a simple framework for testing AI integration in our platform.
        
        SECTION 3. IMPLEMENTATION.
        The system shall generate a simple, 8th-grade reading level summary of this bill
        that explains what it does in plain English.
        """
        
        # Generate AI analysis (this should include TL;DR)
        ai_analysis = await bill_service._generate_ai_analysis(test_full_text, test_bill_info['title'])
        
        print(f"✅ AI Analysis generated:")
        print(f"   TL;DR: {ai_analysis.get('tldr', 'NOT GENERATED')}")
        print(f"   Support reasons: {len(ai_analysis.get('support_reasons', []))}")
        print(f"   Oppose reasons: {len(ai_analysis.get('oppose_reasons', []))}")
        
        # Test 2: Prepare bill data (should include TL;DR field)
        print("\n📊 Test 2: Preparing bill data for database...")
        
        bill_data = bill_service._prepare_bill_data(test_bill_info, test_full_text, ai_analysis)
        
        print(f"✅ Bill data prepared:")
        print(f"   TL;DR field present: {'tldr' in bill_data}")
        print(f"   TL;DR content: {bill_data.get('tldr', 'MISSING')[:100]}...")
        
        # Test 3: Create bill record
        print("\n💾 Test 3: Creating bill record in database...")
        
        from app.schemas.bill import BillCreate
        bill_create = BillCreate(**bill_data)
        
        bills_service = BillService(db)
        created_bill = bills_service.create_bill(bill_create)
        
        print(f"✅ Bill created with ID: {created_bill.id}")
        print(f"   TL;DR saved: {bool(created_bill.tldr)}")
        print(f"   TL;DR content: {created_bill.tldr[:100] if created_bill.tldr else 'MISSING'}...")
        
        # Test 4: Retrieve bill (simulating frontend request)
        print("\n🔍 Test 4: Retrieving bill (frontend simulation)...")
        
        retrieved_bill = bills_service.get_bill(str(created_bill.id))
        
        if retrieved_bill and retrieved_bill.tldr:
            print("✅ SUCCESS: TL;DR retrieved successfully!")
            print(f"   Full TL;DR: {retrieved_bill.tldr}")
        else:
            print("❌ FAILURE: TL;DR not found in retrieved bill")
            return False
        
        # Test 5: Cleanup
        print("\n🧹 Test 5: Cleaning up test data...")
        bills_service.delete_bill(str(created_bill.id))
        print("✅ Test bill deleted")
        
        print("\n🎉 ALL TESTS PASSED!")
        print("TL;DR integration is working correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

async def main():
    """Main test function"""
    print("🚀 Starting TL;DR Integration Tests")
    
    success = await test_tldr_integration()
    
    if success:
        print("\n✅ Integration test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Integration test failed!")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())