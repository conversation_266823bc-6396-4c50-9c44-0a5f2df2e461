'use client';

// Analytics service for tracking social media engagement
export interface SocialEngagementEvent {
  userId?: string;
  actionType: 'direct-message' | 'public-share' | 'follow-up' | 'profile-view' | 'quick-contact';
  platform: 'twitter' | 'facebook' | 'instagram' | 'linkedin';
  official: string;
  officialId?: string;
  bill?: string;
  stance?: 'support' | 'oppose' | 'amend';
  userLocation: string;
  messageLength: number;
  customization: boolean;
  context: 'bill-action' | 'official-profile' | 'dashboard' | 'campaign';
  timestamp: Date;
}

export interface EngagementMetrics {
  contactSuccessRate: number;
  platformDistribution: Record<string, number>;
  messageCustomizationRate: number;
  multiPlatformUsage: number;
  amplificationRate: number;
  followUpEngagement: number;
  averageTimeToContact: number;
}

class AnalyticsService {
  private events: SocialEngagementEvent[] = [];
  private isEnabled = typeof window !== 'undefined';

  /**
   * Track a social media engagement event
   */
  trackSocialEngagement(event: Omit<SocialEngagementEvent, 'timestamp'>): void {
    if (!this.isEnabled) return;

    const fullEvent: SocialEngagementEvent = {
      ...event,
      timestamp: new Date()
    };

    // Store locally for now
    this.events.push(fullEvent);

    // Send to analytics backend (if available)
    this.sendToBackend(fullEvent);

    // Also log for debugging in development
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Social Engagement Event:', fullEvent);
    }
  }

  /**
   * Track when a user views an official's profile
   */
  trackOfficialProfileView(official: { id: string; name: string }, userLocation?: string): void {
    this.trackSocialEngagement({
      actionType: 'profile-view',
      platform: 'twitter', // Default platform for tracking purposes
      official: official.name,
      officialId: official.id,
      userLocation: userLocation || 'unknown',
      messageLength: 0,
      customization: false,
      context: 'official-profile'
    });
  }

  /**
   * Track quick contact actions from dashboard
   */
  trackQuickContact(
    official: { id: string; name: string }, 
    platform: string,
    userLocation?: string
  ): void {
    this.trackSocialEngagement({
      actionType: 'quick-contact',
      platform: platform as any,
      official: official.name,
      officialId: official.id,
      userLocation: userLocation || 'unknown',
      messageLength: 0,
      customization: false,
      context: 'dashboard'
    });
  }

  /**
   * Track bill-related social engagement
   */
  trackBillSocialEngagement(
    bill: { id: string; bill_number: string },
    official: { id: string; name: string },
    platform: string,
    actionType: 'direct-message' | 'public-share',
    messageLength: number,
    customization: boolean,
    stance?: 'support' | 'oppose' | 'amend',
    userLocation?: string
  ): void {
    this.trackSocialEngagement({
      actionType,
      platform: platform as any,
      official: official.name,
      officialId: official.id,
      bill: bill.bill_number,
      stance,
      userLocation: userLocation || 'unknown',
      messageLength,
      customization,
      context: 'bill-action'
    });
  }

  /**
   * Get engagement metrics for analytics dashboard
   */
  getEngagementMetrics(): EngagementMetrics {
    if (this.events.length === 0) {
      return {
        contactSuccessRate: 0,
        platformDistribution: {},
        messageCustomizationRate: 0,
        multiPlatformUsage: 0,
        amplificationRate: 0,
        followUpEngagement: 0,
        averageTimeToContact: 0
      };
    }

    const directMessages = this.events.filter(e => e.actionType === 'direct-message');
    const publicShares = this.events.filter(e => e.actionType === 'public-share');
    const followUps = this.events.filter(e => e.actionType === 'follow-up');

    // Platform distribution
    const platformDistribution: Record<string, number> = {};
    this.events.forEach(event => {
      platformDistribution[event.platform] = (platformDistribution[event.platform] || 0) + 1;
    });

    // Customization rate
    const customizedMessages = this.events.filter(e => e.customization).length;
    const messageCustomizationRate = this.events.length > 0 
      ? (customizedMessages / this.events.length) * 100 
      : 0;

    // Multi-platform usage (users who used more than one platform)
    const userPlatforms = new Map<string, Set<string>>();
    this.events.forEach(event => {
      if (event.userId) {
        if (!userPlatforms.has(event.userId)) {
          userPlatforms.set(event.userId, new Set());
        }
        userPlatforms.get(event.userId)?.add(event.platform);
      }
    });

    const multiPlatformUsers = Array.from(userPlatforms.values())
      .filter(platforms => platforms.size > 1).length;
    const totalUsers = userPlatforms.size;
    const multiPlatformUsage = totalUsers > 0 ? (multiPlatformUsers / totalUsers) * 100 : 0;

    return {
      contactSuccessRate: 95, // Assume high success rate for social media
      platformDistribution,
      messageCustomizationRate,
      multiPlatformUsage,
      amplificationRate: publicShares.length > 0 ? (publicShares.length / directMessages.length) * 100 : 0,
      followUpEngagement: followUps.length > 0 ? (followUps.length / directMessages.length) * 100 : 0,
      averageTimeToContact: 30 // Average seconds to complete contact
    };
  }

  /**
   * Get platform-specific performance data
   */
  getPlatformMetrics(): Record<string, { usage: number; effectiveness: number }> {
    const platformMetrics: Record<string, { usage: number; effectiveness: number }> = {};

    const totalEvents = this.events.length;
    if (totalEvents === 0) return platformMetrics;

    ['twitter', 'facebook', 'instagram', 'linkedin'].forEach(platform => {
      const platformEvents = this.events.filter(e => e.platform === platform);
      const usage = (platformEvents.length / totalEvents) * 100;
      
      // Effectiveness based on customization rate and message length
      const customizedCount = platformEvents.filter(e => e.customization).length;
      const avgMessageLength = platformEvents.length > 0 
        ? platformEvents.reduce((sum, e) => sum + e.messageLength, 0) / platformEvents.length 
        : 0;
      
      const customizationScore = platformEvents.length > 0 ? (customizedCount / platformEvents.length) * 50 : 0;
      const lengthScore = Math.min(avgMessageLength / 10, 50); // Up to 50 points for message length
      
      platformMetrics[platform] = {
        usage: Math.round(usage * 100) / 100,
        effectiveness: Math.round(customizationScore + lengthScore)
      };
    });

    return platformMetrics;
  }

  /**
   * Send event to analytics backend
   */
  private async sendToBackend(event: SocialEngagementEvent): Promise<void> {
    try {
      // Only send in production or if analytics endpoint is configured
      if (process.env.NODE_ENV !== 'development' && process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT) {
        await fetch(process.env.NEXT_PUBLIC_ANALYTICS_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            event_type: 'social_engagement',
            properties: event,
            timestamp: event.timestamp.toISOString()
          })
        });
      }
    } catch (error) {
      console.warn('Failed to send analytics event:', error);
    }
  }

  /**
   * Export events for external analysis
   */
  exportEvents(): SocialEngagementEvent[] {
    return [...this.events];
  }

  /**
   * Clear stored events (for privacy compliance)
   */
  clearEvents(): void {
    this.events = [];
  }
}

// Singleton instance
export const analytics = new AnalyticsService();

// Convenience functions for common tracking scenarios
export const trackSocialEngagement = (event: Omit<SocialEngagementEvent, 'timestamp'>) => {
  analytics.trackSocialEngagement(event);
};

export const trackOfficialView = (official: { id: string; name: string }, userLocation?: string) => {
  analytics.trackOfficialProfileView(official, userLocation);
};

export const trackDashboardContact = (official: { id: string; name: string }, platform: string, userLocation?: string) => {
  analytics.trackQuickContact(official, platform, userLocation);
};

export const trackBillSocialAction = (
  bill: { id: string; bill_number: string },
  official: { id: string; name: string },
  platform: string,
  actionType: 'direct-message' | 'public-share',
  messageLength: number,
  customization: boolean,
  stance?: 'support' | 'oppose' | 'amend',
  userLocation?: string
) => {
  analytics.trackBillSocialEngagement(
    bill, 
    official, 
    platform, 
    actionType, 
    messageLength, 
    customization, 
    stance, 
    userLocation
  );
};

export default analytics;