# app/schemas/official.py
from pydantic import BaseModel, EmailStr, ConfigDict, HttpUrl, field_serializer, field_validator
from typing import Optional, List, Dict, Any
from datetime import datetime

class OfficialBase(BaseModel):
    """Base official schema with common fields"""
    name: str
    title: str
    party: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    website: Optional[HttpUrl] = None
    twitter_handle: Optional[str] = None
    office_address: Optional[str] = None
    office_city: Optional[str] = None
    office_state: Optional[str] = None
    office_zip: Optional[str] = None
    level: str  # 'federal', 'state', 'local'
    chamber: Optional[str] = None  # 'house', 'senate', 'executive', 'judicial', 'other'
    state: Optional[str] = None
    district: Optional[str] = None
    bio: Optional[str] = None
    profile_picture_url: Optional[HttpUrl] = None
    term_start: Optional[str] = None
    term_end: Optional[str] = None
    is_active: bool = True
    
    # Enhanced fields for public profiles
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    full_name: Optional[str] = None
    dc_office_phone: Optional[str] = None
    dc_office_address: Optional[str] = None
    local_office_phone: Optional[str] = None
    local_office_address: Optional[str] = None
    homepage_url: Optional[HttpUrl] = None
    official_photo_url: Optional[HttpUrl] = None
    youtube_channel: Optional[str] = None
    linkedin_url: Optional[HttpUrl] = None
    current_term_start: Optional[str] = None
    current_term_end: Optional[str] = None
    next_election_date: Optional[str] = None

    @field_serializer('website', 'profile_picture_url', 'homepage_url', 'official_photo_url', 'linkedin_url')
    def serialize_urls(self, value):
        return str(value) if value else None

class OfficialCreate(OfficialBase):
    """Schema for creating a new official"""
    bioguide_id: Optional[str] = None
    openstates_id: Optional[str] = None
    google_civic_id: Optional[str] = None

class OfficialUpdate(BaseModel):
    """Schema for updating official information"""
    name: Optional[str] = None
    title: Optional[str] = None
    party: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    website: Optional[HttpUrl] = None
    twitter_handle: Optional[str] = None
    office_address: Optional[str] = None
    office_city: Optional[str] = None
    office_state: Optional[str] = None
    office_zip: Optional[str] = None
    bio: Optional[str] = None
    profile_picture_url: Optional[HttpUrl] = None
    is_active: Optional[bool] = None
    
    # Enhanced fields for updates
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    full_name: Optional[str] = None
    dc_office_phone: Optional[str] = None
    dc_office_address: Optional[str] = None
    local_office_phone: Optional[str] = None
    local_office_address: Optional[str] = None
    homepage_url: Optional[HttpUrl] = None
    official_photo_url: Optional[HttpUrl] = None
    youtube_channel: Optional[str] = None
    linkedin_url: Optional[HttpUrl] = None
    current_term_start: Optional[str] = None
    current_term_end: Optional[str] = None
    next_election_date: Optional[str] = None
    govtrack_id: Optional[str] = None

    @field_serializer('website', 'profile_picture_url', 'homepage_url', 'official_photo_url', 'linkedin_url')
    def serialize_urls(self, value):
        return str(value) if value else None

class OfficialResponse(OfficialBase):
    """Schema for official API responses"""
    id: str
    created_at: datetime
    updated_at: datetime
    bioguide_id: Optional[str] = None
    openstates_id: Optional[str] = None
    google_civic_id: Optional[str] = None
    govtrack_id: Optional[str] = None
    twitter_handle: Optional[str] = None
    facebook_url: Optional[HttpUrl] = None
    instagram_handle: Optional[str] = None
    social_media: Optional[Dict[str, Any]] = None  # Rich social media data
    response_rate: Optional[int] = None
    avg_response_time: Optional[int] = None
    
    # Enhanced fields for public profiles
    committees: Optional[List[Dict[str, Any]]] = None
    leadership_positions: Optional[List[Dict[str, Any]]] = None
    bills_sponsored_count: Optional[int] = None
    bills_cosponsored_count: Optional[int] = None
    votes_cast_count: Optional[int] = None

    @field_serializer('website', 'profile_picture_url', 'facebook_url', 'homepage_url', 'official_photo_url', 'linkedin_url')
    def serialize_urls(self, value):
        return str(value) if value else None

    @field_validator('social_media', mode='before')
    @classmethod
    def parse_social_media(cls, value):
        """Parse social media JSON string if needed"""
        if value is None:
            return None
        if isinstance(value, str):
            import json
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                return None
        return value

    model_config = ConfigDict(from_attributes=True)

class Official(OfficialResponse):
    """Complete official schema for internal use"""
    voting_record: Optional[List[Dict[str, Any]]] = None
    positions: Optional[List[Dict[str, Any]]] = None
    official_metadata: Optional[Dict[str, Any]] = None

class OfficialSummary(BaseModel):
    """Lightweight official summary for lists"""
    id: str
    name: str
    title: str
    party: Optional[str] = None
    level: str
    state: Optional[str] = None
    district: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

class OfficialSearch(BaseModel):
    """Schema for official search parameters"""
    query: Optional[str] = None
    level: Optional[str] = None
    chamber: Optional[str] = None
    state: Optional[str] = None
    district: Optional[str] = None
    party: Optional[str] = None
    is_active: Optional[bool] = True
    zip_code: Optional[str] = None
    limit: int = 20
    offset: int = 0

class OfficialContact(BaseModel):
    """Contact information for an official"""
    id: str
    name: str
    title: str
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    website: Optional[HttpUrl] = None
    office_address: Optional[str] = None
    preferred_contact_method: str

    @field_serializer('website')
    def serialize_urls(self, value):
        return str(value) if value else None

    model_config = ConfigDict(from_attributes=True)
