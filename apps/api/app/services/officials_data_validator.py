#!/usr/bin/env python3
"""
Officials Data Validator

This service validates officials data against authoritative sources to ensure 100% accuracy
before seeding staging/production databases.

Data Sources (in order of authority):
1. congress.gov API - Most authoritative for federal officials
2. Senate.gov and House.gov official websites
3. Bioguide (Biographical Directory of the United States Congress)
4. OpenStates API - For state-level officials
5. Official government websites

Validation Strategy:
- Multi-source verification
- Real-time API validation
- Web scraping verification for social media
- Data consistency checks
- Manual review flagging for discrepancies
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import aiohttp
import re
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

class ValidationResult(Enum):
    VERIFIED = "verified"
    FLAGGED = "flagged"
    FAILED = "failed"
    MANUAL_REVIEW = "manual_review"

@dataclass
class OfficialValidationReport:
    """Comprehensive validation report for an official"""
    official_id: str
    name: str
    overall_result: ValidationResult
    confidence_score: float  # 0-100
    verified_fields: Dict[str, bool]
    flagged_issues: List[str]
    data_sources: List[str]
    last_verified: datetime
    recommended_actions: List[str]

class OfficialsDataValidator:
    """
    Validates officials data against multiple authoritative sources
    """
    
    def __init__(self):
        self.congress_api_key = None  # Set from environment
        self.session = None
        
        # Authoritative data source endpoints
        self.data_sources = {
            "congress_gov": "https://api.congress.gov/v3/member",
            "senate_gov": "https://www.senate.gov/senators",
            "house_gov": "https://clerk.house.gov/member_info",
            "bioguide": "http://bioguide.congress.gov/search/bio",
            "openstates": "https://v3.openstates.org/people"
        }
        
        # Social media domain validation patterns
        self.social_media_patterns = {
            "twitter": r"^https?://(twitter\.com|x\.com)/[A-Za-z0-9_]+/?$",
            "facebook": r"^https?://(?:www\.)?facebook\.com/[A-Za-z0-9\._\-]+/?$",
            "instagram": r"^https?://(?:www\.)?instagram\.com/[A-Za-z0-9_\.]+/?$",
            "youtube": r"^https?://(?:www\.)?youtube\.com/(c/|channel/|user/)?[A-Za-z0-9_\-]+/?$",
            "linkedin": r"^https?://(?:www\.)?linkedin\.com/in/[A-Za-z0-9\-_]+/?$"
        }

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def validate_official(self, official_data: Dict[str, Any]) -> OfficialValidationReport:
        """
        Comprehensive validation of a single official's data
        """
        report = OfficialValidationReport(
            official_id=official_data.get('id', 'unknown'),
            name=official_data.get('name', 'Unknown'),
            overall_result=ValidationResult.VERIFIED,
            confidence_score=0.0,
            verified_fields={},
            flagged_issues=[],
            data_sources=[],
            last_verified=datetime.utcnow(),
            recommended_actions=[]
        )

        # Validate basic information
        await self._validate_basic_info(official_data, report)
        
        # Validate contact information
        await self._validate_contact_info(official_data, report)
        
        # Validate social media accounts
        await self._validate_social_media(official_data, report)
        
        # Validate against authoritative sources
        if official_data.get('level') == 'federal':
            await self._validate_against_congress_api(official_data, report)
        
        # Calculate overall confidence and result
        self._calculate_overall_result(report)
        
        return report

    async def _validate_basic_info(self, official_data: Dict, report: OfficialValidationReport):
        """Validate basic official information"""
        
        # Name validation
        name = official_data.get('name')
        if not name or len(name.strip()) < 2:
            report.flagged_issues.append("Invalid or missing name")
            report.verified_fields['name'] = False
        else:
            report.verified_fields['name'] = True

        # Title validation
        valid_titles = [
            'Representative', 'Senator', 'U.S. Representative', 'U.S. Senator',
            'Governor', 'Lieutenant Governor', 'Mayor', 'Council Member',
            'State Senator', 'State Representative', 'Assembly Member'
        ]
        
        title = official_data.get('title')
        if not title or not any(valid_title.lower() in title.lower() for valid_title in valid_titles):
            report.flagged_issues.append(f"Questionable title: {title}")
            report.verified_fields['title'] = False
        else:
            report.verified_fields['title'] = True

        # Party validation
        valid_parties = ['Democratic', 'Republican', 'Independent', 'Green', 'Libertarian']
        party = official_data.get('party')
        if party and not any(valid_party.lower() in party.lower() for valid_party in valid_parties):
            report.flagged_issues.append(f"Questionable party affiliation: {party}")
            report.verified_fields['party'] = False
        else:
            report.verified_fields['party'] = True

    async def _validate_contact_info(self, official_data: Dict, report: OfficialValidationReport):
        """Validate contact information"""
        
        # Email validation
        email = official_data.get('email')
        if email:
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                report.flagged_issues.append(f"Invalid email format: {email}")
                report.verified_fields['email'] = False
            else:
                # Check if it's a government domain
                domain = email.split('@')[1].lower()
                gov_domains = ['.gov', '.senate.gov', '.house.gov', '.state.', '.city.']
                is_gov_domain = any(gov_domain in domain for gov_domain in gov_domains)
                
                if not is_gov_domain:
                    report.flagged_issues.append(f"Non-government email domain: {email}")
                    report.verified_fields['email'] = False
                else:
                    report.verified_fields['email'] = True
        
        # Phone validation
        phone = official_data.get('phone')
        if phone:
            # Basic US phone number validation
            phone_clean = re.sub(r'[^\d]', '', phone)
            if len(phone_clean) != 10 and len(phone_clean) != 11:
                report.flagged_issues.append(f"Invalid phone number format: {phone}")
                report.verified_fields['phone'] = False
            else:
                report.verified_fields['phone'] = True

        # Website validation
        website = official_data.get('website') or official_data.get('homepage_url')
        if website:
            try:
                parsed = urlparse(website)
                if not parsed.scheme or not parsed.netloc:
                    report.flagged_issues.append(f"Invalid website URL: {website}")
                    report.verified_fields['website'] = False
                else:
                    # Check if it's a government domain
                    domain = parsed.netloc.lower()
                    gov_domains = ['.gov', '.senate.gov', '.house.gov', '.state.', '.city.']
                    is_gov_domain = any(gov_domain in domain for gov_domain in gov_domains)
                    
                    if not is_gov_domain:
                        report.flagged_issues.append(f"Non-government website domain: {website}")
                    
                    # Try to verify the URL is accessible
                    try:
                        async with self.session.head(website, timeout=10) as response:
                            if response.status == 200:
                                report.verified_fields['website'] = True
                            else:
                                report.flagged_issues.append(f"Website not accessible (HTTP {response.status}): {website}")
                                report.verified_fields['website'] = False
                    except:
                        report.flagged_issues.append(f"Website not accessible: {website}")
                        report.verified_fields['website'] = False
            except:
                report.flagged_issues.append(f"Invalid website URL format: {website}")
                report.verified_fields['website'] = False

    async def _validate_social_media(self, official_data: Dict, report: OfficialValidationReport):
        """Validate social media accounts"""
        
        social_media = official_data.get('social_media')
        if not social_media:
            return
        
        if isinstance(social_media, str):
            try:
                social_media = json.loads(social_media)
            except:
                report.flagged_issues.append("Invalid social media JSON format")
                report.verified_fields['social_media'] = False
                return
        
        for platform, url in social_media.items():
            platform_key = platform.lower()
            
            # Check URL format
            pattern_found = False
            for pattern_name, pattern in self.social_media_patterns.items():
                if pattern_name in platform_key and re.match(pattern, url):
                    pattern_found = True
                    break
            
            if not pattern_found:
                report.flagged_issues.append(f"Invalid {platform} URL format: {url}")
                report.verified_fields[f'social_media_{platform_key}'] = False
                continue
            
            # Try to verify the URL is accessible
            try:
                async with self.session.head(url, timeout=10) as response:
                    if response.status in [200, 301, 302]:
                        report.verified_fields[f'social_media_{platform_key}'] = True
                    else:
                        report.flagged_issues.append(f"{platform} account not accessible (HTTP {response.status}): {url}")
                        report.verified_fields[f'social_media_{platform_key}'] = False
            except:
                report.flagged_issues.append(f"{platform} account not accessible: {url}")
                report.verified_fields[f'social_media_{platform_key}'] = False

    async def _validate_against_congress_api(self, official_data: Dict, report: OfficialValidationReport):
        """Validate federal officials against Congress.gov API"""
        
        bioguide_id = official_data.get('bioguide_id')
        if not bioguide_id:
            report.flagged_issues.append("Missing bioguide_id for federal official")
            return
        
        try:
            # Mock Congress API call (would need actual API key)
            # For now, just validate bioguide ID format
            if not re.match(r'^[A-Z]\d{6}$', bioguide_id):
                report.flagged_issues.append(f"Invalid bioguide_id format: {bioguide_id}")
                report.verified_fields['bioguide_id'] = False
            else:
                report.verified_fields['bioguide_id'] = True
                report.data_sources.append("bioguide")
                
        except Exception as e:
            report.flagged_issues.append(f"Error validating against Congress API: {str(e)}")

    def _calculate_overall_result(self, report: OfficialValidationReport):
        """Calculate overall validation result and confidence score"""
        
        total_fields = len(report.verified_fields)
        verified_count = sum(1 for verified in report.verified_fields.values() if verified)
        
        if total_fields == 0:
            report.confidence_score = 0.0
            report.overall_result = ValidationResult.FAILED
            return
        
        # Calculate base confidence score
        report.confidence_score = (verified_count / total_fields) * 100
        
        # Adjust for critical issues
        critical_issues = len([issue for issue in report.flagged_issues if 
                              'Invalid' in issue or 'missing' in issue.lower()])
        
        if critical_issues > 0:
            report.confidence_score -= (critical_issues * 10)
        
        # Determine overall result
        if report.confidence_score >= 95:
            report.overall_result = ValidationResult.VERIFIED
        elif report.confidence_score >= 80:
            report.overall_result = ValidationResult.FLAGGED
            report.recommended_actions.append("Review flagged issues and verify manually")
        elif report.confidence_score >= 50:
            report.overall_result = ValidationResult.MANUAL_REVIEW
            report.recommended_actions.append("Requires manual verification before use")
        else:
            report.overall_result = ValidationResult.FAILED
            report.recommended_actions.append("Do not use - too many validation failures")
        
        # Ensure score stays within bounds
        report.confidence_score = max(0.0, min(100.0, report.confidence_score))

    async def validate_batch(self, officials_data: List[Dict[str, Any]]) -> List[OfficialValidationReport]:
        """Validate a batch of officials data"""
        
        tasks = []
        for official_data in officials_data:
            task = self.validate_official(official_data)
            tasks.append(task)
        
        reports = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out any exceptions and log them
        valid_reports = []
        for i, report in enumerate(reports):
            if isinstance(report, Exception):
                logger.error(f"Validation failed for official {i}: {str(report)}")
                # Create a failed report
                failed_report = OfficialValidationReport(
                    official_id=officials_data[i].get('id', f'unknown_{i}'),
                    name=officials_data[i].get('name', 'Unknown'),
                    overall_result=ValidationResult.FAILED,
                    confidence_score=0.0,
                    verified_fields={},
                    flagged_issues=[f"Validation error: {str(report)}"],
                    data_sources=[],
                    last_verified=datetime.utcnow(),
                    recommended_actions=["Fix validation error and retry"]
                )
                valid_reports.append(failed_report)
            else:
                valid_reports.append(report)
        
        return valid_reports

    def generate_validation_summary(self, reports: List[OfficialValidationReport]) -> Dict[str, Any]:
        """Generate a summary of validation results"""
        
        total_officials = len(reports)
        verified_count = len([r for r in reports if r.overall_result == ValidationResult.VERIFIED])
        flagged_count = len([r for r in reports if r.overall_result == ValidationResult.FLAGGED])
        manual_review_count = len([r for r in reports if r.overall_result == ValidationResult.MANUAL_REVIEW])
        failed_count = len([r for r in reports if r.overall_result == ValidationResult.FAILED])
        
        avg_confidence = sum(r.confidence_score for r in reports) / total_officials if total_officials > 0 else 0
        
        # Collect most common issues
        all_issues = []
        for report in reports:
            all_issues.extend(report.flagged_issues)
        
        issue_counts = {}
        for issue in all_issues:
            issue_type = issue.split(':')[0]  # Get the type of issue
            issue_counts[issue_type] = issue_counts.get(issue_type, 0) + 1
        
        common_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            "total_officials": total_officials,
            "verification_summary": {
                "verified": verified_count,
                "flagged": flagged_count,
                "manual_review": manual_review_count,
                "failed": failed_count
            },
            "percentages": {
                "verified": (verified_count / total_officials) * 100 if total_officials > 0 else 0,
                "flagged": (flagged_count / total_officials) * 100 if total_officials > 0 else 0,
                "manual_review": (manual_review_count / total_officials) * 100 if total_officials > 0 else 0,
                "failed": (failed_count / total_officials) * 100 if total_officials > 0 else 0
            },
            "average_confidence_score": avg_confidence,
            "common_issues": common_issues,
            "recommendation": self._get_overall_recommendation(verified_count, total_officials, avg_confidence)
        }

    def _get_overall_recommendation(self, verified_count: int, total_officials: int, avg_confidence: float) -> str:
        """Get overall recommendation for the dataset"""
        
        if total_officials == 0:
            return "No data to validate"
        
        verification_rate = (verified_count / total_officials) * 100
        
        if verification_rate >= 95 and avg_confidence >= 95:
            return "Dataset ready for production deployment"
        elif verification_rate >= 90 and avg_confidence >= 90:
            return "Dataset ready for staging deployment with monitoring"
        elif verification_rate >= 80 and avg_confidence >= 80:
            return "Review flagged items before staging deployment"
        elif verification_rate >= 70 and avg_confidence >= 70:
            return "Significant manual review required before deployment"
        else:
            return "Dataset not ready for deployment - requires extensive review and correction"