# app/schemas/bill_details.py
# flake8: noqa

from pydantic import BaseModel, ConfigDict
from typing import List, Optional, Dict, Any
from datetime import datetime


class Citation(BaseModel):
    quote: str
    start_offset: int
    end_offset: int
    heading: Optional[str] = None
    anchor_id: Optional[str] = None


class SectionWithCitations(BaseModel):
    content: str
    citations: List[Citation] = []


class ProvisionItem(BaseModel):
    content: str
    citations: List[Citation] = []


class TimelineItem(BaseModel):
    content: str
    citations: List[Citation] = []


class Overview(BaseModel):
    what_does: Optional[SectionWithCitations] = None
    who_affects: Optional[SectionWithCitations] = None
    why_matters: Optional[SectionWithCitations] = None
    key_provisions: Optional[List[ProvisionItem]] = None
    cost_impact: Optional[SectionWithCitations] = None
    timeline: Optional[List[TimelineItem]] = None


class PositionReason(BaseModel):
    claim: str
    justification: str
    citations: List[Citation] = []


class Positions(BaseModel):
    support_reasons: Optional[List[PositionReason]] = None
    oppose_reasons: Optional[List[PositionReason]] = None
    amend_reasons: Optional[List[PositionReason]] = None


class SourceIndexItem(BaseModel):
    heading: Optional[str] = None
    start_offset: int
    end_offset: int
    anchor_id: Optional[str] = None
    summary: Optional[str] = None


class Metrics(BaseModel):
    coverage_ratio: Optional[float] = None
    unverified_count: Optional[int] = None


class BillDetailsBase(BaseModel):
    bill_id: str
    seo_slug: Optional[str] = None
    seo_title: Optional[str] = None
    seo_meta_description: Optional[str] = None
    canonical_url: Optional[str] = None
    hero_summary: Optional[str] = None
    hero_summary_citations: Optional[List[Citation]] = None

    overview: Optional[Overview] = None
    positions: Optional[Positions] = None
    message_templates: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    other_details: Optional[List[SectionWithCitations]] = None
    source_index: Optional[List[SourceIndexItem]] = None

    needs_human_review: bool = False
    reviewed_at: Optional[datetime] = None
    reviewed_by: Optional[str] = None
    moderation_notes: Optional[str] = None
    require_review_before_publish: bool = False

    metrics: Optional[Metrics] = None


class BillDetailsCreate(BillDetailsBase):
    pass


class BillDetailsUpdate(BaseModel):
    seo_slug: Optional[str] = None
    seo_title: Optional[str] = None
    seo_meta_description: Optional[str] = None
    canonical_url: Optional[str] = None
    hero_summary: Optional[str] = None
    hero_summary_citations: Optional[List[Citation]] = None

    overview: Optional[Overview] = None
    positions: Optional[Positions] = None
    message_templates: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    other_details: Optional[List[SectionWithCitations]] = None
    source_index: Optional[List[SourceIndexItem]] = None

    needs_human_review: Optional[bool] = None
    reviewed_at: Optional[datetime] = None
    reviewed_by: Optional[str] = None
    moderation_notes: Optional[str] = None
    require_review_before_publish: Optional[bool] = None

    metrics: Optional[Metrics] = None


class BillDetailsResponse(BillDetailsBase):
    id: str
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

