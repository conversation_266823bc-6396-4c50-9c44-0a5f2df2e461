'use client';

import React, { useState, useEffect } from 'react';
import { useUser } from '@auth0/nextjs-auth0/client';
import { Official } from '@/types';
import { officialApi } from '@/services/apiClient';
import Link from 'next/link';
import { RepresentativeQuickContact } from './RepresentativeQuickContact';

interface YourRepresentativesProps {
  className?: string;
}

export const YourRepresentatives: React.FC<YourRepresentativesProps> = ({
  className = ''
}) => {
  const { user } = useUser();
  const [officials, setOfficials] = useState<Official[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userZipCode, setUserZipCode] = useState<string>('');

  useEffect(() => {
    // Get ZIP code from user profile or prompt user
    const zipCode = user?.user_metadata?.zip_code;
    if (zipCode) {
      setUserZipCode(zipCode);
      fetchRepresentatives(zipCode);
    }
  }, [user]);

  const fetchRepresentatives = async (zipCode: string) => {
    try {
      setLoading(true);
      setError(null);
      const fetchedOfficials = await officialApi.getOfficialsByZip(zipCode);
      setOfficials(fetchedOfficials);
    } catch (err) {
      console.error('Error fetching representatives:', err);
      setError('Failed to load your representatives. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleZipCodeUpdate = async () => {
    const newZipCode = prompt('Enter your ZIP code:', userZipCode);
    if (newZipCode && /^\d{5}$/.test(newZipCode.trim())) {
      setUserZipCode(newZipCode.trim());
      await fetchRepresentatives(newZipCode.trim());
    }
  };


  if (!user) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Your Representatives</h2>
        <div className="text-center py-8">
          <div className="text-gray-500 mb-4">
            <svg className="w-12 h-12 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <p className="text-gray-600 mb-4">Sign in to see your representatives</p>
          <Link
            href="/api/auth/login"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  if (!userZipCode) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Your Representatives</h2>
        <div className="text-center py-8">
          <div className="text-gray-500 mb-4">
            <svg className="w-12 h-12 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </div>
          <p className="text-gray-600 mb-4">Add your ZIP code to see your representatives</p>
          <button
            onClick={handleZipCodeUpdate}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Add ZIP Code
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Your Representatives</h2>
          <p className="text-sm text-gray-600 mt-1">
            ZIP Code: {userZipCode} • {officials.length} representative{officials.length !== 1 ? 's' : ''}
          </p>
        </div>
        <button
          onClick={handleZipCodeUpdate}
          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
        >
          Change ZIP
        </button>
      </div>

      {/* Content */}
      <div className="p-6">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3"></div>
            <p className="text-gray-600">Loading your representatives...</p>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <div className="text-red-500 mb-3">
              <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <p className="text-gray-600 mb-3">{error}</p>
            <button
              onClick={() => fetchRepresentatives(userZipCode)}
              className="text-blue-600 hover:text-blue-800 font-medium text-sm"
            >
              Try Again
            </button>
          </div>
        ) : officials.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-3">
              <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <p className="text-gray-600 mb-3">No representatives found for ZIP code {userZipCode}</p>
            <button
              onClick={handleZipCodeUpdate}
              className="text-blue-600 hover:text-blue-800 font-medium text-sm"
            >
              Update ZIP Code
            </button>
          </div>
        ) : (
          <div>
            <RepresentativeQuickContact
              representatives={officials}
              userZip={userZipCode}
              showAllContacts={true}
            />

            {/* View All Link */}
            <div className="pt-4 border-t border-gray-200 text-center">
              <Link
                href="/officials"
                className="text-blue-600 hover:text-blue-800 font-medium text-sm transition-colors duration-200"
              >
                Find All Representatives →
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default YourRepresentatives;