#!/usr/bin/env python3
"""
Real Phase 2 Quality Test - Run from within API environment
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Any

from app.services.balanced_analysis_service import BalancedAnalysisService
from app.services.ai_service import AIService
from app.core.config import get_settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_phase2_real():
    """Test Phase 2 improvements with real OpenAI API calls"""
    
    print("🚀 REAL Phase 2 Quality Test")
    print("=" * 50)
    
    # Check configuration
    settings = get_settings()
    print(f"✅ OpenAI API Key: {'Configured' if settings.OPENAI_API_KEY else 'Missing'}")
    print(f"✅ Environment: {settings.ENVIRONMENT}")
    print()
    
    # Initialize services
    ai_service = AIService()
    balanced_service = BalancedAnalysisService(ai_service)
    
    if not ai_service.enabled:
        print("❌ AI Service not enabled - check configuration")
        return False
    
    print("✅ AI Service initialized successfully")
    print()
    
    # Test bill data
    test_bill = {
        'bill_id': 'test-phase2-real-001', 
        'title': 'Environmental Monitoring Enhancement Act of 2024',
        'bill_text': """
        SEC. 1. SHORT TITLE.
        This Act may be cited as the "Environmental Monitoring Enhancement Act of 2024".
        
        SEC. 2. APPROPRIATIONS.
        There is authorized to be appropriated $750,000,000 for fiscal year 2025 to the Environmental Protection Agency to carry out environmental monitoring activities under this Act.
        
        SEC. 3. IMPLEMENTATION REQUIREMENTS.
        (a) MONITORING STATIONS.—Not later than 120 days after the date of enactment of this Act, the Administrator of the Environmental Protection Agency shall establish not fewer than 100 environmental monitoring stations in areas designated as high-priority under subsection (b).
        
        (b) HIGH-PRIORITY AREAS.—The Administrator shall designate high-priority areas based on population density, industrial activity, and environmental risk factors.
        
        SEC. 4. ENFORCEMENT AND PENALTIES.
        (a) CIVIL PENALTIES.—Any person who interferes with monitoring equipment or fails to provide required data shall be subject to a civil penalty of not more than $50,000 per day for each violation.
        
        (b) CRIMINAL PENALTIES.—Any person who willfully destroys monitoring equipment shall be fined not more than $250,000 or imprisoned for not more than 2 years, or both.
        
        SEC. 5. REPORTING REQUIREMENTS.
        The Administrator shall submit quarterly reports to Congress on monitoring results and enforcement actions taken under this Act.
        """
    }
    
    # High-quality evidence spans
    test_evidence = [
        {
            'id': 'ev_funding_001',
            'heading': 'Appropriations Authorization',
            'quote': 'There is authorized to be appropriated $750,000,000 for fiscal year 2025 to the Environmental Protection Agency to carry out environmental monitoring activities under this Act.',
            'start_offset': 150,
            'end_offset': 300
        },
        {
            'id': 'ev_timeline_001', 
            'heading': 'Implementation Timeline',
            'quote': 'Not later than 120 days after the date of enactment of this Act, the Administrator of the Environmental Protection Agency shall establish not fewer than 100 environmental monitoring stations in areas designated as high-priority.',
            'start_offset': 400,
            'end_offset': 580
        },
        {
            'id': 'ev_penalty_civil_001',
            'heading': 'Civil Penalties',
            'quote': 'Any person who interferes with monitoring equipment or fails to provide required data shall be subject to a civil penalty of not more than $50,000 per day for each violation.',
            'start_offset': 800,
            'end_offset': 950
        },
        {
            'id': 'ev_penalty_criminal_001',
            'heading': 'Criminal Penalties', 
            'quote': 'Any person who willfully destroys monitoring equipment shall be fined not more than $250,000 or imprisoned for not more than 2 years, or both.',
            'start_offset': 1000,
            'end_offset': 1150
        },
        {
            'id': 'ev_reporting_001',
            'heading': 'Congressional Reporting',
            'quote': 'The Administrator shall submit quarterly reports to Congress on monitoring results and enforcement actions taken under this Act.',
            'start_offset': 1200,
            'end_offset': 1350
        },
        {
            'id': 'ev_designation_001',
            'heading': 'Area Designation Criteria',
            'quote': 'The Administrator shall designate high-priority areas based on population density, industrial activity, and environmental risk factors.',
            'start_offset': 650,
            'end_offset': 800
        }
    ]
    
    print(f"📊 Test Data:")
    print(f"   Bill: {test_bill['title']}")
    print(f"   Evidence Spans: {len(test_evidence)}")
    print()
    
    # Run the analysis
    print("🔄 Running Phase 2 Analysis...")
    start_time = time.time()
    
    try:
        result = await balanced_service.analyze_bill_balanced(
            bill_text=test_bill['bill_text'],
            bill_metadata=test_bill,
            evidence_spans=test_evidence
        )
        
        analysis_time = time.time() - start_time
        
        if result['success']:
            print(f"✅ Analysis completed in {analysis_time:.2f}s")
            print()
            
            # Analyze results
            await analyze_phase2_results(result, test_bill, analysis_time)
            return True
        else:
            print(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Analysis exception: {e}")
        import traceback
        traceback.print_exc()
        return False

async def analyze_phase2_results(result: Dict[str, Any], test_bill: Dict[str, Any], analysis_time: float):
    """Analyze Phase 2 results for quality improvements"""
    
    print("🎯 PHASE 2 RESULTS ANALYSIS")
    print("=" * 50)
    
    # Extract data
    quality_metrics = result.get('quality_metrics', {})
    evidence_quality = result.get('evidence_quality', {})
    cost_breakdown = result.get('cost_breakdown', {})
    analysis = result.get('analysis', {})
    
    # 1. Quality Scores
    print("📊 QUALITY SCORES:")
    print(f"   Overall Score: {quality_metrics.get('overall_score', 0):.2f}")
    print(f"   Quality Level: {quality_metrics.get('quality_level', 'unknown')}")
    print(f"   Specificity: {quality_metrics.get('specificity_score', 0):.2f}")
    print(f"   Evidence Grounding: {quality_metrics.get('evidence_grounding_score', 0):.2f}")
    print(f"   Comprehensiveness: {quality_metrics.get('comprehensiveness_score', 0):.2f}")
    print(f"   Clarity: {quality_metrics.get('clarity_score', 0):.2f}")
    print(f"   Actionability: {quality_metrics.get('actionability_score', 0):.2f}")
    print()
    
    # 2. Evidence Quality
    print("📚 EVIDENCE QUALITY:")
    print(f"   Input Spans: {evidence_quality.get('total_spans_input', 0)}")
    print(f"   Validated Spans: {evidence_quality.get('total_spans_validated', 0)}")
    print(f"   Average Quality: {evidence_quality.get('average_quality_score', 0):.2f}")
    print(f"   Quality Filter Applied: {evidence_quality.get('quality_improvement_applied', False)}")
    print()
    
    # 3. Cost Control
    print("💰 COST ANALYSIS:")
    total_cost = cost_breakdown.get('total_cost', 0)
    budget_remaining = cost_breakdown.get('budget_remaining', 0)
    print(f"   Total Cost: ${total_cost:.4f}")
    print(f"   Budget Remaining: ${budget_remaining:.4f}")
    print(f"   Within Budget: {'✅' if total_cost < 0.25 else '❌'}")
    print(f"   Analysis Time: {analysis_time:.2f}s")
    print()
    
    # 4. Content Analysis
    print("📝 CONTENT ANALYSIS:")
    complete_analysis = analysis.get('complete_analysis', [])
    print(f"   Sections Generated: {len(complete_analysis)}")
    
    # Check for Phase 2 quality indicators
    has_specific_amounts = check_specific_amounts(analysis)
    has_specific_deadlines = check_specific_deadlines(analysis) 
    has_specific_enforcement = check_specific_enforcement(analysis)
    has_evidence_grounding = check_evidence_grounding(complete_analysis)
    
    print(f"   Specific Dollar Amounts: {'✅' if has_specific_amounts else '❌'}")
    print(f"   Specific Deadlines: {'✅' if has_specific_deadlines else '❌'}")
    print(f"   Specific Enforcement: {'✅' if has_specific_enforcement else '❌'}")
    print(f"   Evidence Grounded: {'✅' if has_evidence_grounding else '❌'}")
    print()
    
    # 5. HR5-118 Compliance
    print("🏆 HR5-118 COMPLIANCE:")
    overall_score = quality_metrics.get('overall_score', 0)
    hr5118_compliant = overall_score >= 80  # 80/100 threshold
    print(f"   Overall Score: {overall_score:.1f}/100")
    print(f"   HR5-118 Compliant: {'✅' if hr5118_compliant else '❌'}")
    print()
    
    # 6. Sample Content Review
    print("🔍 SAMPLE CONTENT:")
    if complete_analysis:
        first_section = complete_analysis[0]
        print(f"   First Section: '{first_section.get('title', 'N/A')}'")
        print(f"   Summary: {first_section.get('detailed_summary', 'N/A')[:100]}...")
        print(f"   Evidence IDs: {first_section.get('ev_ids', [])}")
        print()
    
    # 7. Phase 2 Success Assessment
    print("🚀 PHASE 2 SUCCESS METRICS:")
    
    success_criteria = [
        ("Quality Score ≥ 70", overall_score >= 70),
        ("Cost ≤ $0.25", total_cost <= 0.25),
        ("Specific Amounts", has_specific_amounts),
        ("Specific Deadlines", has_specific_deadlines), 
        ("Evidence Grounded", has_evidence_grounding),
        ("Multiple Sections", len(complete_analysis) >= 4),
        ("Quality Tracking", 'quality_metrics' in result),
        ("Evidence Filtering", evidence_quality.get('quality_improvement_applied', False))
    ]
    
    passed = sum(1 for _, success in success_criteria if success)
    total = len(success_criteria)
    
    for criterion, success in success_criteria:
        print(f"   {criterion}: {'✅' if success else '❌'}")
    
    print()
    print(f"📈 FINAL ASSESSMENT: {passed}/{total} criteria met ({passed/total:.1%})")
    
    if passed >= 7:
        print("🎉 PHASE 2 SUCCESS: Quality improvements working excellently!")
    elif passed >= 5:
        print("⚠️ PHASE 2 PARTIAL: Good progress, minor refinements needed")
    else:
        print("❌ PHASE 2 NEEDS WORK: Significant improvements required")

def check_specific_amounts(analysis: Dict[str, Any]) -> bool:
    """Check for specific monetary amounts"""
    import re
    content = json.dumps(analysis)
    amounts = re.findall(r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion))?', content)
    return len(amounts) > 0

def check_specific_deadlines(analysis: Dict[str, Any]) -> bool:
    """Check for specific deadlines"""
    import re
    content = json.dumps(analysis)
    deadlines = re.findall(r'(?:120\s*days?|quarterly|within\s+\d+|not later than)', content, re.IGNORECASE)
    return len(deadlines) > 0

def check_specific_enforcement(analysis: Dict[str, Any]) -> bool:
    """Check for specific enforcement mechanisms"""
    import re
    content = json.dumps(analysis)
    enforcement = re.findall(r'(?:penalty|fine)[^.]*\$[\d,]+', content, re.IGNORECASE)
    return len(enforcement) > 0

def check_evidence_grounding(complete_analysis: List[Dict[str, Any]]) -> bool:
    """Check if sections are grounded in evidence"""
    if not complete_analysis:
        return False
    grounded = sum(1 for section in complete_analysis if section.get('ev_ids'))
    return grounded >= len(complete_analysis) * 0.8

if __name__ == "__main__":
    asyncio.run(test_phase2_real())