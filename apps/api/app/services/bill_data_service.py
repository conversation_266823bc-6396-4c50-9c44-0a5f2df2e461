# app/services/bill_data_service.py
"""
Enhanced BillDataService: The authoritative bill ingestion service for ModernAction.io

This service implements the enhanced data pipeline for Implementation-2.MD, providing:
1. Congress.gov API integration for bill metadata and full text
2. OpenAI GPT-4 powered analysis for comprehensive bill processing
3. Async processing for improved performance
4. Enhanced data storage with JSONB fields
5. Complete AI analysis pipeline

This is the "engine" that powers the ModernAction.io data pipeline.
"""

import asyncio
import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import Session

from app.services.congress_api import get_congress_api_service
from app.services.ai_service import get_ai_service
from app.models.bill import Bill, BillType, BillStatus
from app.schemas.bill import BillCreate

logger = logging.getLogger(__name__)


class BillDataService:
    """
    Authoritative bill ingestion service that creates fully processed bills
    with AI-generated analysis, ready for campaign creation.

    This service is the core of the ModernAction.io data pipeline.
    """

    def __init__(self, db: Session = None):
        """Initialize the BillDataService with required dependencies"""
        self.congress_api = get_congress_api_service()
        self.ai_service = get_ai_service()
        self.db = db
        logger.info("Enhanced BillDataService initialized")

    async def ingest_bill(self, bill_number: str, congress_session: int = 118) -> Optional[Bill]:
        """
        The core ingestion function that creates a fully processed bill.

        This is the main function specified in Sprint A that:
        1. Gets bill metadata from Congress.gov API
        2. Scrapes full text from official source
        3. Generates AI summary and analysis (including TL;DR)
        4. Saves structured data to database

        Args:
            bill_number: Bill number like "HR5", "S1234", etc.
            congress_session: Congress session number (default: 118)

        Returns:
            Bill object with complete data or None if ingestion fails
        """
        logger.info(f"Starting bill ingestion for {bill_number} in Congress {congress_session}")

        try:
            # Step 1: Parse bill number and get metadata from Congress.gov API
            bill_info = self._get_bill_metadata(bill_number, congress_session)
            if not bill_info:
                logger.error(f"Failed to get metadata for bill {bill_number}")
                return None

            # Step 2: Check if bill already exists
            existing_bill = self._check_existing_bill(bill_info.get('congress_gov_id'))
            if existing_bill:
                logger.info(f"Bill {bill_number} already exists, updating instead")
                return await self._update_existing_bill(existing_bill, bill_info)

            # Step 3: Get full text from official source
            full_text = self._get_bill_full_text(bill_info)

            # Step 4: Generate AI-powered analysis (including TL;DR)
            ai_analysis = await self._generate_ai_analysis(full_text, bill_info.get('title', ''))

            # Step 5: Create and save the bill
            bill_data = self._prepare_bill_data(bill_info, full_text, ai_analysis)
            bill = self._create_bill_record(bill_data)

            logger.info(f"Successfully ingested bill {bill_number} with ID {bill.id} including TL;DR")
            return bill

        except Exception as e:
            logger.error(f"Bill ingestion failed for {bill_number}: {e}")
            if hasattr(self, 'db'):
                self.db.rollback()
            return None

    def _get_bill_metadata(self, bill_number: str, congress_session: int) -> Optional[Dict[str, Any]]:
        """Get bill metadata from Congress.gov API"""
        try:
            # Parse the bill number
            parsed = self.congress_api.parse_bill_number(bill_number)
            parsed['congress'] = congress_session

            # Get bill data from API
            bill_data = self.congress_api.get_bill_by_number(
                congress=parsed['congress'],
                bill_type=parsed['bill_type'],
                bill_number=parsed['number']
            )

            if not bill_data:
                logger.error(f"No data found for bill {bill_number}")
                return None

            # Get additional data
            actions = self.congress_api.get_bill_actions(
                congress=parsed['congress'],
                bill_type=parsed['bill_type'],
                bill_number=parsed['number']
            )

            text_versions = self.congress_api.get_bill_text(
                congress=parsed['congress'],
                bill_type=parsed['bill_type'],
                bill_number=parsed['number']
            )

            # Structure the metadata
            metadata = {
                'congress_gov_id': f"{parsed['congress']}-{parsed['bill_type']}-{parsed['number']}",
                'title': bill_data.get('title', ''),
                'number': bill_data.get('number', bill_number),
                'bill_type': self._map_congress_type_to_model(parsed['bill_type']),
                'congress_session': parsed['congress'],
                'introduced_date': self._parse_date(bill_data.get('introducedDate')),
                'sponsor': self._extract_sponsor_info(bill_data.get('sponsors', [])),
                'summary': bill_data.get('summary', {}).get('text', ''),
                'actions': actions or [],
                'text_versions': text_versions or [],
                'raw_data': bill_data
            }

            logger.info(f"Successfully retrieved metadata for {bill_number}")
            return metadata

        except Exception as e:
            logger.error(f"Failed to get bill metadata: {e}")
            return None

    def _get_bill_full_text(self, bill_info: Dict[str, Any]) -> str:
        """Get the full text of the bill from official sources"""
        try:
            # Try to get text URL from text versions
            text_versions = bill_info.get('text_versions', [])
            if text_versions:
                # Get the most recent text version
                latest_version = text_versions[0]
                formats = latest_version.get('formats', [])
                if formats:
                    text_url = formats[0].get('url')
                    if text_url:
                        # For now, we'll use the summary as a placeholder for full text
                        # TODO: Implement proper text scraping service
                        logger.info(f"Text URL found: {text_url}")
                        return bill_info.get('summary', '')

            # Fallback: use summary as bill text
            summary = bill_info.get('summary', '')
            if summary:
                logger.info(f"Using bill summary as text: {len(summary)} characters")
                return summary

            logger.warning("Could not retrieve full text for bill")
            return ""

        except Exception as e:
            logger.error(f"Failed to get bill full text: {e}")
            return ""

    async def _generate_ai_analysis(self, full_text: str, title: str) -> Dict[str, Any]:
        """
        Generate AI-powered analysis of the bill using modern AI service.

        This implements the AI Intelligence Layer from Sprint A requirements:
        - Plain English summary
        - TL;DR for 8th grade reading level
        - Arguments for support/oppose/amend
        - Relevant tags
        - Structured summary sections
        """
        try:
            if not full_text:
                logger.warning("No bill text available for AI analysis")
                return self._get_fallback_analysis(title)

            # Use modern AI service for complete analysis
            bill_metadata = {'title': title}
            ai_analysis = await self.ai_service.process_bill_complete(full_text, bill_metadata)
            
            logger.info("Generated complete AI analysis including TL;DR")
            return ai_analysis

        except Exception as e:
            logger.error(f"AI analysis generation failed: {e}")
            return self._get_fallback_analysis(title)

    def _get_fallback_analysis(self, title: str) -> Dict[str, Any]:
        """Return fallback analysis when AI processing fails"""
        return {
            'ai_summary': f"AI analysis for '{title}' is being processed.",
            'tldr': f"This bill is about {title} and is currently being analyzed.",
            'structured_summary': {},
            'support_reasons': ["This bill addresses an important issue"],
            'oppose_reasons': ["This bill may have unintended consequences"],
            'amend_reasons': ["The bill needs stronger enforcement"],
            'message_templates': {
                'support': "Dear [REPRESENTATIVE_NAME], I support this legislation.",
                'oppose': "Dear [REPRESENTATIVE_NAME], I have concerns about this legislation.",
                'amend': "Dear [REPRESENTATIVE_NAME], I believe this legislation needs amendments."
            },
            'tags': ["legislation", "policy"]
        }


    def _prepare_bill_data(self, bill_info: Dict, full_text: str, ai_analysis: Dict) -> Dict[str, Any]:
        """Prepare the complete bill data for database insertion"""
        sponsor = bill_info.get('sponsor', {})

        # Extract structured summary data
        structured_summary = ai_analysis.get('structured_summary', {})

        return {
            'title': bill_info.get('title', ''),
            'bill_number': bill_info.get('number', ''),
            'bill_type': bill_info.get('bill_type', BillType.HOUSE_BILL),
            'status': BillStatus.INTRODUCED,  # Default status
            'session_year': bill_info.get('congress_session', 118),
            'chamber': 'house' if 'hr' in bill_info.get('number', '').lower() else 'senate',
            'state': 'federal',
            'full_text': full_text,
            'summary': bill_info.get('summary', ''),
            
            # AI-generated content (legacy fields)
            'ai_summary': ai_analysis.get('ai_summary', ''),
            'tldr': ai_analysis.get('tldr', ''),  # CRITICAL: Add TL;DR field
            
            # Structured AI summary fields (JSONB)
            'summary_what_does': structured_summary.get('what_does'),
            'summary_who_affects': structured_summary.get('who_affects'),
            'summary_why_matters': structured_summary.get('why_matters'),
            'summary_key_provisions': structured_summary.get('key_provisions'),
            'summary_timeline': structured_summary.get('timeline'),
            'summary_cost_impact': structured_summary.get('cost_impact'),
            
            # Enhanced AI processing fields (JSONB arrays)
            'support_reasons': ai_analysis.get('support_reasons', []),
            'oppose_reasons': ai_analysis.get('oppose_reasons', []),
            'amend_reasons': ai_analysis.get('amend_reasons', []),
            'message_templates': ai_analysis.get('message_templates', {}),
            'ai_tags': ai_analysis.get('tags', []),
            'ai_processed_at': datetime.utcnow(),
            
            # Legacy JSON string fields (for backward compatibility)
            'reasons_for_support': json.dumps(ai_analysis.get('support_reasons', [])),
            'reasons_for_opposition': json.dumps(ai_analysis.get('oppose_reasons', [])),
            'tags': json.dumps(ai_analysis.get('tags', [])),
            
            # External references
            'congress_gov_id': bill_info.get('congress_gov_id'),
            'introduced_date': bill_info.get('introduced_date'),
            
            # Sponsor information
            'sponsor_name': sponsor.get('name', ''),
            'sponsor_party': sponsor.get('party', ''),
            'sponsor_state': sponsor.get('state', ''),
            
            # Metadata
            'bill_metadata': json.dumps(bill_info.get('raw_data', {}))
        }

    def _create_bill_record(self, bill_data: Dict[str, Any]) -> Bill:
        """Create and save the bill record to the database"""
        from app.services.bills import BillService

        bill_service = BillService(self.db)

        # Convert to BillCreate schema
        bill_create = BillCreate(**bill_data)
        bill = bill_service.create_bill(bill_create)

        logger.info(f"Created bill record with ID {bill.id}")
        return bill

    def _check_existing_bill(self, congress_gov_id: str) -> Optional[Bill]:
        """Check if a bill with this Congress.gov ID already exists"""
        if not congress_gov_id:
            return None

        return self.db.query(Bill).filter(Bill.congress_gov_id == congress_gov_id).first()

    async def _update_existing_bill(self, bill: Bill, bill_info: Dict[str, Any]) -> Bill:
        """Update an existing bill with new information and regenerate AI analysis if needed"""
        logger.info(f"Updating existing bill {bill.id}")

        # Update key fields that might have changed
        bill.title = bill_info.get('title', bill.title)
        bill.summary = bill_info.get('summary', bill.summary)

        # Update metadata
        if bill_info.get('raw_data'):
            bill.bill_metadata = json.dumps(bill_info['raw_data'])

        # If bill doesn't have TL;DR, regenerate AI analysis
        if not bill.tldr and bill.full_text:
            logger.info(f"Bill {bill.id} missing TL;DR, regenerating AI analysis")
            ai_analysis = await self._generate_ai_analysis(bill.full_text, bill.title)
            
            # Update AI fields
            bill.tldr = ai_analysis.get('tldr', '')
            bill.ai_summary = ai_analysis.get('ai_summary', bill.ai_summary)
            
            # Update structured summary fields
            structured_summary = ai_analysis.get('structured_summary', {})
            bill.summary_what_does = structured_summary.get('what_does')
            bill.summary_who_affects = structured_summary.get('who_affects')
            bill.summary_why_matters = structured_summary.get('why_matters')
            bill.summary_key_provisions = structured_summary.get('key_provisions')
            bill.summary_timeline = structured_summary.get('timeline')
            bill.summary_cost_impact = structured_summary.get('cost_impact')
            
            # Update enhanced AI processing fields
            bill.support_reasons = ai_analysis.get('support_reasons', [])
            bill.oppose_reasons = ai_analysis.get('oppose_reasons', [])
            bill.amend_reasons = ai_analysis.get('amend_reasons', [])
            bill.message_templates = ai_analysis.get('message_templates', {})
            bill.ai_tags = ai_analysis.get('tags', [])
            bill.ai_processed_at = datetime.utcnow()
            
            logger.info(f"Updated bill {bill.id} with TL;DR and complete AI analysis")

        if hasattr(self, 'db'):
            self.db.commit()
            self.db.refresh(bill)

        return bill

    def _map_congress_type_to_model(self, congress_type: str) -> BillType:
        """Map Congress.gov bill type to our model enum"""
        mapping = {
            'hr': BillType.HOUSE_BILL,
            's': BillType.SENATE_BILL,
            'hres': BillType.HOUSE_RESOLUTION,
            'sres': BillType.SENATE_RESOLUTION,
            'hjres': BillType.JOINT_RESOLUTION,
            'sjres': BillType.JOINT_RESOLUTION,
            'hconres': BillType.CONCURRENT_RESOLUTION,
            'sconres': BillType.CONCURRENT_RESOLUTION
        }
        return mapping.get(congress_type.lower(), BillType.HOUSE_BILL)

    def _parse_date(self, date_str: Optional[str]) -> Optional[datetime]:
        """Parse date string to datetime object"""
        if not date_str:
            return None

        try:
            # Handle different date formats from Congress.gov API
            if 'T' in date_str:
                return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            else:
                return datetime.strptime(date_str, '%Y-%m-%d')
        except Exception as e:
            logger.warning(f"Failed to parse date {date_str}: {e}")
            return None

    def _extract_sponsor_info(self, sponsors: List[Dict]) -> Dict[str, str]:
        """Extract sponsor information from Congress.gov API response"""
        if not sponsors:
            return {}

        sponsor = sponsors[0]  # Primary sponsor
        return {
            'name': sponsor.get('fullName', ''),
            'party': sponsor.get('party', ''),
            'state': sponsor.get('state', '')
        }

    def health_check(self) -> Dict[str, Any]:
        """Check the health of the BillDataService"""
        status = {
            'service': 'BillDataService',
            'status': 'healthy',
            'components': {}
        }

        # Check Congress.gov API
        congress_health = self.congress_api.health_check()
        status['components']['congress_api'] = congress_health

        # Check database connection
        try:
            self.db.execute('SELECT 1')
            status['components']['database'] = {'status': 'healthy'}
        except Exception as e:
            status['components']['database'] = {'status': 'error', 'error': str(e)}
            status['status'] = 'degraded'

        # Check AI service
        try:
            from app.services.ai import health_check as ai_health_check
            ai_status = ai_health_check()
            status['components']['ai_service'] = ai_status
        except Exception as e:
            status['components']['ai_service'] = {'status': 'error', 'error': str(e)}
            status['status'] = 'degraded'

        return status


# Convenience function for easy import
def get_bill_data_service(db: Session) -> BillDataService:
    """Get a BillDataService instance"""
    return BillDataService(db)
