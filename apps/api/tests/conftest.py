import os
from dotenv import load_dotenv

# Load test environment variables from .env.test file BEFORE importing app
# Ensure test values override any defaults from .env
load_dotenv(".env.test", override=True)

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from app.main import app
from app.db.database import get_db
from app.db.base_class import Base


@pytest.fixture(scope="session")
def test_engine():
    """Create a PostgreSQL test engine for the entire test session"""
    database_url = os.getenv("DATABASE_URL")
    assert database_url and database_url.startswith("postgresql"), (
        "DATABASE_URL must point to a PostgreSQL instance for tests. "
        "Example: postgresql://user:pass@localhost:5432/modernaction_test"
    )

    # Safety guard: never allow tests to run against non-test DBs
    # Require database name to end with _test to avoid truncating dev/staging/prod data
    lower_url = database_url.lower()
    forbidden = ["modernaction", "postgres", "production", "staging"]
    db_name = lower_url.rsplit("/", 1)[-1]
    assert db_name.endswith("_test") or db_name.endswith("-test"), (
        f"Refusing to run tests against non-test database: {db_name}. "
        "Set DATABASE_URL to a dedicated test DB ending with _test."
    )

    test_engine = create_engine(database_url, echo=False)

    # Create all tables (migrations preferred in CI, but create_all is fine for unit tests)
    Base.metadata.create_all(bind=test_engine)

    # Ensure a clean database at the start of the test session
    try:
        from sqlalchemy import text
        with test_engine.connect() as conn:
            table_names = ", ".join([t.name for t in Base.metadata.sorted_tables])
            if table_names:
                conn.execute(text(f"TRUNCATE TABLE {table_names} RESTART IDENTITY CASCADE;"))
                conn.commit()
    except Exception:
        # Best-effort cleanup; tests also use transaction rollback for isolation
        pass

    yield test_engine

    # Cleanup for PostgreSQL - just dispose the engine
    test_engine.dispose()


@pytest.fixture
def test_db_session(test_engine):
    """
    Yields a new, isolated transaction for each test function.
    Rolls back the transaction after the test is complete.
    This ensures perfect test isolation.
    """
    connection = test_engine.connect()

    # begin a non-ORM transaction
    transaction = connection.begin()

    # bind an individual session to the connection
    Session = sessionmaker(bind=connection)
    session = Session()

    yield session

    # rollback the transaction and close the connection
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def test_db(test_engine):
    """Create a test database session factory for each test function"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    yield TestingSessionLocal


@pytest.fixture(scope="function")
def test_client(test_db_session):
    """Create a test client with test database"""
    def override_get_db():
        try:
            yield test_db_session
        finally:
            pass  # Session cleanup handled by test_db_session fixture

    app.dependency_overrides[get_db] = override_get_db

    with TestClient(app) as client:
        yield client

    app.dependency_overrides.clear()


@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        "email": "<EMAIL>",
        "password": "testpassword123",
        "first_name": "Test",
        "last_name": "User",
        "zip_code": "12345",
        "auth0_user_id": "auth0|test123456789"  # Required for Auth0 integration
    }


@pytest.fixture
def sample_bill_data():
    """Sample bill data for testing"""
    return {
        "title": "Test Bill",
        "description": "A test bill for testing purposes",
        "bill_number": "HR-123",
        "bill_type": "house_bill",
        "status": "introduced",
        "session_year": 2024,
        "chamber": "house",
        "state": "federal",
        "full_text": "This is the full text of the test bill.",
        "summary": "This is a summary of the test bill.",
        "is_featured": False,
        "priority_score": 0
    }


@pytest.fixture
def sample_official_data():
    """Sample official data for testing"""
    return {
        "name": "Test Official",
        "title": "Representative",
        "party": "Independent",
        "email": "<EMAIL>",
        "phone": "555-0123",
        "level": "federal",
        "chamber": "house",
        "state": "CA",
        "district": "1",
        "is_active": True
    }


@pytest.fixture
def sample_campaign_data():
    """Sample campaign data for testing"""
    return {
        "title": "Test Campaign",
        "description": "A test campaign for testing purposes",
        "campaign_type": "support",
        "status": "active",
        "call_to_action": "Support this important legislation!",
        "is_featured": False,
        "is_public": True,
        "requires_verification": False,
        "actual_actions": 0
    }
