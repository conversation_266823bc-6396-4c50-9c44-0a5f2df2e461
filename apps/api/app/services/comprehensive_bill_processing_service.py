# app/services/comprehensive_bill_processing_service.py
import os
import logging
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from app.utils.ai_tracking_decorator import AIUsageContext

from .bill_chunking_service import BillChunkingService, Bill<PERSON>hunk
from .deep_analysis_service import DeepAnalysisService, ChunkAnalysis
from .synthesis_service import SynthesisService, ComprehensiveBillAnalysis
from .bill_details_service import BillDetailsService
from app.models.bill import Bill

logger = logging.getLogger(__name__)


class ComprehensiveBillProcessingService:
    """
    World-class bill processing service using chunk-analyze-synthesize approach
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        
        if not self.openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        
        # Initialize services
        self.chunking_service = BillChunkingService()
        self.deep_analysis_service = DeepAnalysisService(self.openai_api_key)
        self.synthesis_service = SynthesisService(self.openai_api_key)
        # Citation enrichment is handled by BillDetailsService
        self.details_service = BillDetailsService(db)

    async def process_bill_comprehensive(self, bill: Bill) -> Dict[str, Any]:
        """
        Process a bill using the MAXIMUM DETAIL comprehensive chunk-analyze-synthesize approach
        Ensures NO detail is left out and COMPLETE transparency
        """
        try:
            logger.info(f"Starting COMPREHENSIVE processing for bill {bill.bill_number} - MAXIMUM DETAIL MODE")

            # Track comprehensive analysis with AI usage context
            with AIUsageContext(
                operation_type="comprehensive_analysis",
                operation_subtype="full_bill_processing",
                model_name="gpt-4-turbo",
                bill_id=bill.id
            ) as usage_tracker:

                # Step 1: Enhanced Intelligent Chunking for Maximum Detail
                logger.info("Step 1: Chunking bill into granular sections for maximum detail extraction")
                chunks = self.chunking_service.chunk_bill(bill.full_text, max_chunk_size=2500)  # Smaller chunks = more detail
                logger.info(f"Created {len(chunks)} granular chunks for comprehensive analysis")

                # Log chunk details for transparency
                for i, chunk in enumerate(chunks):
                    logger.info(f"Chunk {i+1}: {chunk.title} ({chunk.importance}, {chunk.chunk_type}) - {len(chunk.content)} chars")

            # Step 2: DEEP Analysis of Each Chunk with Maximum Detail Extraction
            logger.info("Step 2: Performing DEEP analysis of each chunk - extracting EVERY detail")
            chunk_analyses = await self.deep_analysis_service.analyze_multiple_chunks(chunks)
            logger.info(f"Analyzed {len(chunk_analyses)} chunks with comprehensive detail extraction")

            # Log analysis coverage for transparency
            total_provisions = sum(len(analysis.specific_actions) + len(analysis.affected_parties) +
                                 len(analysis.enforcement_mechanisms) for analysis in chunk_analyses)
            logger.info(f"Extracted {total_provisions} total provisions/details across all chunks")

            # Step 3: Synthesis into COMPREHENSIVE Analysis with ALL Details
            logger.info("Step 3: Synthesizing comprehensive analysis - preserving ALL extracted details")
            bill_metadata = {
                "title": bill.title,
                "bill_number": bill.bill_number,
                "session_year": bill.session_year
            }
            
            comprehensive_analysis = await self.synthesis_service.synthesize_bill_analysis(
                chunks, chunk_analyses, bill_metadata
            )
            
            # Step 4: Convert to Bill Details Format
            logger.info("Step 4: Converting to bill details format")
            details_payload = self._convert_to_details_format(comprehensive_analysis)
            
            # Step 5: Return data for caller to save (don't save here to avoid bill_id issues)
            logger.info("Step 5: Returning comprehensive analysis data for caller to save")
            
            logger.info(f"Comprehensive processing completed for bill {bill.bill_number}")
            
            return {
                "success": True,
                "bill_id": bill.id,
                "chunks_created": len(chunks),
                "analyses_completed": len(chunk_analyses),
                "primary_mechanisms": len(comprehensive_analysis.primary_mechanisms),
                "secondary_provisions": len(comprehensive_analysis.secondary_provisions),
                "complete_analysis_sections": len(comprehensive_analysis.complete_analysis),
                "comprehensive_analysis": comprehensive_analysis,
                "details_payload": details_payload
            }
            
        except Exception as e:
            logger.error(f"Comprehensive processing failed for bill {bill.bill_number}: {e}")
            return {
                "success": False,
                "error": str(e),
                "bill_id": bill.id
            }

    def _convert_to_details_format(self, analysis: ComprehensiveBillAnalysis) -> Dict[str, Any]:
        """Convert comprehensive analysis to bill details format"""
        
        # Create enhanced overview with new comprehensive data
        overview = analysis.overview.copy()
        
        # Add comprehensive analysis as additional sections
        overview["primary_mechanisms"] = analysis.primary_mechanisms
        overview["secondary_provisions"] = analysis.secondary_provisions
        overview["enforcement_framework"] = analysis.enforcement_framework
        overview["funding_impacts"] = analysis.funding_impacts
        overview["implementation_timeline"] = analysis.implementation_timeline
        
        # Add complete transparency section
        overview["complete_analysis"] = analysis.complete_analysis

        # Add additional details section (fluff)
        overview["additional_details"] = analysis.additional_details

        return {
            "hero_summary": analysis.hero_summary,
            "hero_summary_citations": analysis.hero_summary_citations,
            "overview": overview,
            "positions": analysis.positions,
            "message_templates": self._generate_message_templates(analysis),
            "tags": self._generate_tags(analysis),
            "other_details": {
                "processing_method": "comprehensive_chunk_analysis",
                "chunks_analyzed": len(analysis.complete_analysis),
                "primary_mechanisms_count": len(analysis.primary_mechanisms),
                "secondary_provisions_count": len(analysis.secondary_provisions)
            }
        }

    def _generate_message_templates(self, analysis: ComprehensiveBillAnalysis) -> Dict[str, str]:
        """Generate message templates based on comprehensive analysis"""
        
        # Extract key points for templates
        key_mechanisms = [m["mechanism"] for m in analysis.primary_mechanisms[:3]]
        key_concerns = []
        key_benefits = []
        
        if analysis.positions.get("support_reasons"):
            key_benefits = [r["claim"] for r in analysis.positions["support_reasons"][:2]]
        
        if analysis.positions.get("oppose_reasons"):
            key_concerns = [r["claim"] for r in analysis.positions["oppose_reasons"][:2]]
        
        return {
            "support": f"Dear [REPRESENTATIVE_NAME], I am writing to express my strong support for this legislation. "
                      f"This bill will {', '.join(key_mechanisms[:2])}. "
                      f"I believe this is important because {', '.join(key_benefits[:2]) if key_benefits else 'it addresses critical issues'}. "
                      f"Please vote in favor of this bill.",
            
            "oppose": f"Dear [REPRESENTATIVE_NAME], I am writing to express my concerns about this legislation. "
                     f"While I understand the intent, I am worried that {', '.join(key_concerns[:2]) if key_concerns else 'this bill may have unintended consequences'}. "
                     f"I urge you to vote against this bill or seek amendments to address these concerns.",
            
            "amend": f"Dear [REPRESENTATIVE_NAME], I am writing about this legislation. "
                    f"While I support the general goals, I believe amendments are needed to ensure {', '.join(key_mechanisms[:1])} "
                    f"is implemented effectively. Please work to improve this bill before passage."
        }

    def _generate_tags(self, analysis: ComprehensiveBillAnalysis) -> List[str]:
        """Generate tags based on comprehensive analysis"""
        tags = set()
        
        # Extract tags from affected parties and mechanisms
        for mechanism in analysis.primary_mechanisms:
            affected = mechanism.get("affected_parties", "").lower()
            if "school" in affected:
                tags.add("Education Policy")
            if "parent" in affected:
                tags.add("Parental Rights")
            if "student" in affected:
                tags.add("Student Rights")
            if "teacher" in affected:
                tags.add("Teacher Policy")
        
        # Extract tags from enforcement
        if analysis.enforcement_framework.get("mechanisms"):
            tags.add("Regulatory Compliance")
        
        # Extract tags from funding
        if analysis.funding_impacts.get("changes"):
            tags.add("Education Funding")
        
        # Add general tags based on content
        hero_lower = analysis.hero_summary.lower()
        if "privacy" in hero_lower:
            tags.add("Privacy Rights")
        if "transparency" in hero_lower:
            tags.add("Government Transparency")
        if "curriculum" in hero_lower:
            tags.add("Curriculum Policy")
        
        return list(tags)[:8]  # Limit to 8 tags

    async def get_processing_status(self, bill_id: str) -> Dict[str, Any]:
        """Get the status of comprehensive processing for a bill"""
        # This could be enhanced with a processing status table
        return {
            "status": "completed",  # For now, assume completed if called
            "method": "comprehensive_chunk_analysis"
        }

    def get_chunk_summary(self, bill: Bill) -> Dict[str, Any]:
        """Get a summary of how the bill was chunked (for debugging/transparency)"""
        chunks = self.chunking_service.chunk_bill(bill.full_text)
        
        return {
            "total_chunks": len(chunks),
            "chunk_breakdown": [
                {
                    "id": chunk.id,
                    "title": chunk.title,
                    "importance": chunk.importance,
                    "affected_parties": chunk.affected_parties,
                    "chunk_type": chunk.chunk_type,
                    "size": len(chunk.content)
                }
                for chunk in chunks
            ]
        }
