# Bill Details System
> Last updated: 2025-08-11


A comprehensive, citation-based legislative analysis system that transforms bills into transparent, user-friendly content with verifiable sources.

## 🎯 Overview

The Bill Details System is the definitive implementation of ModernAction's "source of truth" initiative. Every claim is backed by exact quotes from the original bill text, ensuring maximum transparency and trustworthiness in legislative analysis.

## ✨ Key Features

### 🔍 Citation-First Analysis
- **Exact Quotes Required**: Every claim includes verbatim text from the source bill
- **Precise Source Tracking**: Character-level offsets and section metadata for each citation
- **Automatic Validation**: Unverified claims are dropped or flagged for human review
- **Transparent Anchoring**: Citations link directly to source sections with stable anchor IDs

### 🛡️ Trust & Transparency
- **Moderation Banner**: Clear disclosure when content awaits human review
  - *"This content was generated by our AI and is awaiting review by a human expert. Use it as a starting point for your own research, not as a definitive source."*
- **Coverage Metrics**: Real-time display of citation coverage percentage
- **Additional Bill Content**: Complete transparency section for non-key bill content
- **Source Index**: Navigable map of all bill sections with anchor links

### 🚀 SEO & User Experience
- **Clean URLs**: `/bills/hr5-118` format with canonical slug support
- **Server-Side Rendering**: Full SSR with Next.js App Router for optimal performance
- **JSON-LD Structured Data**: Rich snippets for search engines
- **Mobile-Responsive**: Optimized for all device sizes
- **Anchor Navigation**: Deep links from action modals to specific bill sections

## 🏗️ Architecture

```mermaid
graph TD
    A[Bill Text] --> B[Text Citation Service]
    B --> C[AI Analysis Service]
    C --> D[Citation Validation]
    D --> E[Bill Details Service]
    E --> F[Database: bill_details]
    F --> G[API Endpoints]
    G --> H[SSR Bill Details Page]
    H --> I[Action Modal Integration]
```

## 📊 Data Model

### Core Structure

```typescript
interface BillDetailsResponse {
  // SEO & Metadata
  seo_slug: string;           // "hr5-118"
  seo_title: string;          // "Healthcare Access Act - Bill Details"
  canonical_url: string;      // "https://modernaction.io/bills/hr5-118"

  // Content Sections
  overview: {
    what_does: SectionWithCitations;      // What the bill does
    who_affects: SectionWithCitations;    // Who is affected
    why_matters: SectionWithCitations;    // Why it matters
    key_provisions: ProvisionItem[];      // Key provisions
    cost_impact: SectionWithCitations;    // Cost analysis
    timeline: TimelineItem[];             // Implementation timeline
  };

  // Positions with Justifications
  positions: {
    support_reasons: PositionReason[];    // Why support
    oppose_reasons: PositionReason[];     // Why oppose
    amend_reasons: PositionReason[];      // Why amend
  };

  // Transparency
  other_details: SectionWithCitations[]; // "Additional Bill Content"
  source_index: SourceIndexItem[];       // Section map with anchors

  // Quality Assurance
  needs_human_review: boolean;           // Moderation flag
  metrics: {
    coverage_ratio: number;              // Percentage with citations
    unverified_count: number;            // Sections needing citations
  };
}
```

### Citation Schema

```typescript
interface Citation {
  quote: string;           // Exact text from bill
  start_offset: number;    // Character position start
  end_offset: number;      // Character position end
  heading: string;         // Source section heading
  anchor_id: string;       // HTML anchor for navigation
}
```

## 🛠️ Implementation

### Backend Services

#### 1. Text Citation Service (`TextCitationService`)
- **Purpose**: Bill text segmentation and quote validation
- **Key Methods**: `build_source_index()`, `bind_quote()`, `bind_citations_bulk()`
- **Features**: Regex-based section detection, offset calculation, anchor ID generation

#### 2. AI Analysis Service (`AIService`)
- **Purpose**: Structured content generation with OpenAI GPT-4
- **Key Method**: `generate_detailed_bill_analysis()`
- **Features**: Rate-limited processing, fallback handling, citation placeholder creation

#### 3. Bill Details Service (`BillDetailsService`)
- **Purpose**: Pipeline orchestration and data management
- **Key Method**: `create_or_update_details()`
- **Features**: Citation enrichment, metrics computation, review flag management

### API Endpoints

- `GET /api/v1/bills/{id}/details` - Fetch by bill ID
- `GET /api/v1/bills/details/by-slug/{slug}` - Fetch by SEO slug (supports multiple formats)

### Frontend Components

- **BillDetailsPage**: Main SSR page at `/bills/[slug]`
- **Citations**: Citation display with source links
- **PositionColumn**: Support/oppose/amend reasons
- **BillActionModal**: Integration with "Learn more" anchor links

## 🚀 Quick Start

### Prerequisites
- Python 3.9+ with Poetry
- Node.js 18+ with npm
- PostgreSQL database
- OpenAI API key

### Setup

1. **Database Migration**
   ```bash
   cd apps/api
   poetry run alembic upgrade head
   ```

2. **Start API Server**
   ```bash
   poetry run uvicorn app.main:app --reload
   ```

3. **Start Web Server**
   ```bash
   cd apps/web
   npm run dev
   ```

4. **Access Bill Details**
   ```
   http://localhost:3000/bills/hr5-118
   ```

### Environment Configuration

```bash
# API (.env)
OPENAI_API_KEY=your_api_key
DATABASE_URL=postgresql://user:pass@localhost/db
BILL_DETAILS_COVERAGE_THRESHOLD=0.6

# Web (.env.local)
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
```

## 🧪 Testing

### Unit Tests
```bash
cd apps/api
poetry run pytest test_citation_service.py test_bill_details_service.py -v
```

### E2E Tests
```bash
cd apps/web
npm run test:e2e
```

### Test Coverage
- **Citation Validation**: Exact quote matching, offset accuracy
- **API Contracts**: Response schema validation
- **User Flows**: Homepage → action modal → bill details → anchor navigation

## 📈 Monitoring & Quality

### Key Metrics
- **Citation Coverage**: Percentage of sections with valid citations
- **Review Queue Length**: Number of bills awaiting human review
- **Processing Success Rate**: AI pipeline completion rate
- **User Engagement**: Bill details page views and anchor clicks

### Quality Assurance
- **Automatic Review Flagging**: Coverage < 60% triggers human review
- **Quote Validation**: All citations verified against source text
- **Fail-Safe Design**: Graceful degradation when AI services unavailable

## 🔧 Configuration

### Bill Text Source Restriction
**Phase 1 Policy**: Citations limited to bill text only
- No external sources allowed
- Future "External Sources" section planned
- Maximum transparency and integrity

### Moderation Settings
```python
# Coverage threshold for human review
COVERAGE_THRESHOLD = 0.6

# Maximum citations per section
MAX_CITATIONS_PER_SECTION = 3

# Show unreviewed content with banner
SHOW_UNREVIEWED_WITH_BANNER = True
```

## 🔄 Integration Points

### Action Modal Integration
- **"Learn more" Links**: Deep-link to bill details with anchors
- **Section Mapping**: What it does → `#overview-what`, etc.
- **Seamless UX**: Opens in new tab, maintains user context

### SEO Integration
- **Canonical URLs**: Consistent `/bills/{slug}` format
- **Meta Tags**: Dynamic title and description generation
- **Structured Data**: JSON-LD for rich search results

### Content Management
- **Backward Compatibility**: Existing Bill fields populated for legacy UI
- **Incremental Migration**: Bill details enhance, don't replace
- **Admin Tools**: Review interface for quality assurance (Phase 2)

## 📚 Documentation

Comprehensive guides available:

- **[API Documentation](./docs/api-documentation.md)** - Complete API reference
- **[Frontend Guide](./docs/frontend-guide.md)** - Component integration and SSR
- **[AI Pipeline](./docs/ai-pipeline.md)** - Citation validation and content generation
- **[Testing Guide](./docs/testing-guide.md)** - Unit, integration, and E2E testing
- **[Deployment Guide](./docs/deployment.md)** - Production deployment and monitoring

## 🎯 Success Metrics

### Acceptance Criteria ✅

- [x] **Every claim includes exact quotes** from bill text
- [x] **Citations include justifications** explaining the inference
- [x] **Anchor navigation** via citation links to source sections
- [x] **Fail-safe AI pipeline** with unverified content flagging
- [x] **SEO-ready pages** with meta tags, canonical URLs, JSON-LD
- [x] **Action modal integration** with "Learn more" deep links
- [x] **Comprehensive test coverage** including E2E user flows

### Quality Standards

- **Citation Accuracy**: 100% of displayed quotes verified against source
- **Coverage Target**: >60% of sections include citations (triggers review if below)
- **Performance**: Bill details pages load in <2 seconds
- **Accessibility**: WCAG 2.1 AA compliance
- **Mobile Experience**: Fully responsive design

## 🚀 Deployment

### Production Checklist

- [ ] Database migrations applied
- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] CDN configured for static assets
- [ ] Health checks passing
- [ ] Monitoring dashboards created
- [ ] Backup procedures tested

### Infrastructure

- **Backend**: AWS ECS Fargate with Application Load Balancer
- **Frontend**: Next.js with SSR on AWS ECS
- **Database**: AWS RDS PostgreSQL with automated backups
- **CDN**: AWS CloudFront for global distribution
- **Monitoring**: CloudWatch with custom metrics

## 🤝 Contributing

### Development Workflow

1. Create feature branch
2. Implement changes with tests
3. Run full test suite
4. Submit pull request
5. Pass code review
6. Deploy to staging
7. Verify E2E functionality
8. Deploy to production

### Code Standards

- **TypeScript**: Strict mode with comprehensive types
- **Python**: Type hints, docstrings, Black formatting
- **Testing**: 90%+ coverage requirement
- **Documentation**: Update docs with any API changes

## 📞 Support

### Troubleshooting

- **Citations not appearing**: Check AI service configuration and quote validation
- **Slow performance**: Verify database indexes and CDN configuration
- **Build errors**: Ensure dependencies match lockfiles

### Getting Help

- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Comprehensive guides in `/docs`
- **Code Examples**: Reference implementation in test files

---

## 🏆 Achievement Summary

The Bill Details System successfully delivers on the vision of being a **trustworthy source of truth** for legislative analysis:

✅ **Citation-based transparency** with exact quote verification
✅ **User-friendly presentation** with clear navigation and mobile support
✅ **SEO optimization** for maximum discoverability
✅ **Quality assurance** through automated validation and human review workflows
✅ **Seamless integration** with existing ModernAction user flows
✅ **Comprehensive testing** ensuring reliability and accuracy

*Making democracy more accessible, one cited claim at a time.*