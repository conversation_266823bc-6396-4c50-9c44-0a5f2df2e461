"""
Intelligent Evidence Extractor - Phase 3 Advanced Evidence Processing
Extracts, filters, and enhances evidence using machine learning-inspired techniques
"""

import logging
import re
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
import asyncio
from collections import defaultdict, Counter

logger = logging.getLogger(__name__)

@dataclass
class ExtractionRule:
    """Rule for extracting evidence"""
    name: str
    pattern: str
    evidence_type: str
    priority: float
    context_required: List[str]
    extraction_method: str  # 'regex', 'semantic', 'hybrid'

@dataclass
class ExtractedEvidence:
    """Raw extracted evidence before processing"""
    content: str
    heading: str
    context: str
    extraction_rule: str
    confidence_score: float
    start_offset: int
    end_offset: int
    surrounding_text: str

class IntelligentEvidenceExtractor:
    """
    Advanced evidence extraction system that uses intelligent patterns,
    contextual analysis, and adaptive filtering for Phase 3
    """
    
    def __init__(self):
        # Advanced extraction rules
        self.extraction_rules = self._initialize_extraction_rules()
        
        # Semantic patterns for context-aware extraction
        self.semantic_patterns = self._initialize_semantic_patterns()
        
        # Quality filters
        self.quality_filters = self._initialize_quality_filters()
        
        # Evidence enhancement patterns
        self.enhancement_patterns = self._initialize_enhancement_patterns()
    
    async def extract_intelligent_evidence(self, bill_text: str, 
                                         bill_metadata: Dict[str, Any]) -> List[ExtractedEvidence]:
        """
        Intelligently extract evidence using multiple advanced techniques
        """
        
        logger.info(f"🧠 Starting intelligent evidence extraction for {bill_metadata.get('title', 'Unknown')}")
        
        # Step 1: Preprocess bill text for optimal extraction
        processed_text = self._preprocess_bill_text(bill_text)
        
        # Step 2: Apply multiple extraction methods
        rule_based_evidence = await self._extract_rule_based_evidence(processed_text, bill_metadata)
        semantic_evidence = await self._extract_semantic_evidence(processed_text, bill_metadata)
        contextual_evidence = await self._extract_contextual_evidence(processed_text, bill_metadata)
        
        # Step 3: Merge and deduplicate evidence
        all_evidence = self._merge_evidence_sources(
            rule_based_evidence, semantic_evidence, contextual_evidence
        )
        
        # Step 4: Apply intelligent filtering
        filtered_evidence = await self._apply_intelligent_filtering(all_evidence, bill_metadata)
        
        # Step 5: Enhance evidence with additional context
        enhanced_evidence = await self._enhance_evidence(filtered_evidence, processed_text)
        
        logger.info(f"✅ Intelligent extraction complete: {len(enhanced_evidence)} evidence spans extracted")
        
        return enhanced_evidence
    
    def _initialize_extraction_rules(self) -> List[ExtractionRule]:
        """Initialize advanced extraction rules"""
        
        return [
            # High-priority funding patterns
            ExtractionRule(
                name="specific_appropriations",
                pattern=r'(?:appropriated|authorized|made available)[^.]*\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion))?[^.]*',
                evidence_type="funding",
                priority=0.95,
                context_required=["fiscal", "budget", "fund"],
                extraction_method="regex"
            ),
            
            ExtractionRule(
                name="grant_allocations", 
                pattern=r'(?:grant|subsidy|award)[^.]*\$[\d,]+[^.]*(?:to|for)[^.]*',
                evidence_type="funding",
                priority=0.90,
                context_required=["program", "recipient"],
                extraction_method="regex"
            ),
            
            # Advanced enforcement patterns
            ExtractionRule(
                name="specific_penalties",
                pattern=r'(?:penalty|fine|sanction)[^.]*\$[\d,]+[^.]*(?:per|for each)[^.]*',
                evidence_type="enforcement",
                priority=0.95,
                context_required=["violation", "comply"],
                extraction_method="regex"
            ),
            
            ExtractionRule(
                name="criminal_penalties",
                pattern=r'(?:fined|imprisoned|sentenced)[^.]*(?:\$[\d,]+|[\d,]+\s*years?)[^.]*',
                evidence_type="enforcement", 
                priority=0.90,
                context_required=["criminal", "willful"],
                extraction_method="regex"
            ),
            
            # Precise timeline patterns
            ExtractionRule(
                name="specific_deadlines",
                pattern=r'(?:not later than|within|by)[^.]*(?:\d+\s*(?:days?|months?|years?)|[A-Z][a-z]+ \d+, \d{4})[^.]*',
                evidence_type="timeline",
                priority=0.90,
                context_required=["shall", "must", "required"],
                extraction_method="regex"
            ),
            
            ExtractionRule(
                name="implementation_timelines",
                pattern=r'(?:effective|commence|begin|start|implement)[^.]*(?:on|by|within)[^.]*(?:\d+|[A-Z][a-z]+)[^.]*',
                evidence_type="timeline",
                priority=0.85,
                context_required=["date", "period"],
                extraction_method="regex"
            ),
            
            # Authority and mandate patterns
            ExtractionRule(
                name="agency_authority",
                pattern=r'(?:Secretary|Administrator|Director|Commissioner)\s+(?:of|for)\s+[\w\s]+\s+(?:shall|may|must)[^.]*',
                evidence_type="authority",
                priority=0.85,
                context_required=["department", "agency"],
                extraction_method="regex"
            ),
            
            ExtractionRule(
                name="mandatory_actions",
                pattern=r'(?:shall|must|required to|directed to)\s+(?:establish|create|implement|develop|maintain|provide)[^.]*',
                evidence_type="mandate",
                priority=0.88,
                context_required=["compliance", "requirement"],
                extraction_method="regex"
            ),
            
            # Impact and scope patterns
            ExtractionRule(
                name="impact_statements",
                pattern=r'(?:affect|impact|apply to|cover|include)[^.]*(?:person|entity|organization|business)[^.]*',
                evidence_type="scope",
                priority=0.75,
                context_required=["subject", "applicable"],
                extraction_method="semantic"
            ),
            
            ExtractionRule(
                name="reporting_requirements",
                pattern=r'(?:report|submit|provide|notify)[^.]*(?:annually|quarterly|monthly|periodically)[^.]*(?:to|with)[^.]*',
                evidence_type="reporting",
                priority=0.70,
                context_required=["congress", "public", "secretary"],
                extraction_method="regex"
            )
        ]
    
    def _initialize_semantic_patterns(self) -> Dict[str, List[str]]:
        """Initialize semantic patterns for context-aware extraction"""
        
        return {
            'funding_context': [
                'appropriation', 'budget', 'fiscal', 'allocation', 'funding',
                'grant', 'subsidy', 'payment', 'disbursement', 'expenditure'
            ],
            'enforcement_context': [
                'violation', 'penalty', 'fine', 'compliance', 'enforcement',
                'sanction', 'criminal', 'civil', 'liability', 'breach'
            ],
            'timeline_context': [
                'deadline', 'timeline', 'schedule', 'effective', 'implementation',
                'commence', 'begin', 'expire', 'period', 'duration'
            ],
            'authority_context': [
                'secretary', 'administrator', 'director', 'agency', 'department',
                'jurisdiction', 'oversight', 'supervision', 'responsibility'
            ],
            'mandate_context': [
                'requirement', 'obligation', 'mandate', 'compliance', 'necessary',
                'shall', 'must', 'required', 'directed', 'ordered'
            ],
            'impact_context': [
                'affect', 'impact', 'consequence', 'result', 'outcome',
                'benefit', 'harm', 'change', 'influence', 'effect'
            ]
        }
    
    def _initialize_quality_filters(self) -> Dict[str, Any]:
        """Initialize quality filters for evidence validation"""
        
        return {
            'min_length': 20,           # Minimum character length
            'max_length': 800,          # Maximum character length
            'min_specificity': 0.3,     # Minimum specificity score
            'blacklist_patterns': [
                r'^(?:the|this|such|any|all)\s+\w+\s+(?:may|shall|will)\s*$',  # Too generic
                r'comprehensive\s+(?:review|analysis|approach)$',              # Generic language
                r'appropriate\s+(?:measures|actions|steps)$',                  # Vague language
                r'various\s+(?:stakeholders|parties|entities)$',               # Non-specific
            ],
            'required_elements': {
                'funding': ['$', 'million', 'billion', 'thousand'],
                'enforcement': ['penalty', 'fine', 'violation', '$'],
                'timeline': ['days', 'months', 'years', 'date', 'deadline'],
                'authority': ['secretary', 'administrator', 'director', 'agency'],
                'mandate': ['shall', 'must', 'required', 'directed']
            }
        }
    
    def _initialize_enhancement_patterns(self) -> Dict[str, List[str]]:
        """Initialize patterns for evidence enhancement"""
        
        return {
            'cross_references': [
                r'(?:section|subsection|paragraph)\s+\d+(?:\([a-z]\))?',
                r'(?:title|chapter|part)\s+[IVX]+',
                r'(?:see|refer to|pursuant to)\s+section\s+\d+'
            ],
            'definitions': [
                r'(?:means|defined as|refers to|includes)',
                r'for purposes of this (?:act|section)',
                r'shall be construed to mean'
            ],
            'exceptions': [
                r'(?:except|unless|provided that|notwithstanding)',
                r'(?:does not apply|shall not|may not)',
                r'(?:waived|exempted|excluded)'
            ]
        }
    
    def _preprocess_bill_text(self, bill_text: str) -> str:
        """Preprocess bill text for optimal extraction"""
        
        # Clean up common formatting issues
        processed = re.sub(r'\s+', ' ', bill_text)  # Normalize whitespace
        processed = re.sub(r'([.!?])\s*([A-Z])', r'\1\n\2', processed)  # Add line breaks after sentences
        
        # Remove page numbers and headers
        processed = re.sub(r'\n\s*\d+\s*\n', '\n', processed)
        
        # Normalize section headers
        processed = re.sub(r'SEC\.\s*(\d+)', r'SECTION \1', processed, flags=re.IGNORECASE)
        
        return processed
    
    async def _extract_rule_based_evidence(self, text: str, 
                                         bill_metadata: Dict[str, Any]) -> List[ExtractedEvidence]:
        """Extract evidence using predefined rules"""
        
        evidence_list = []
        
        for rule in self.extraction_rules:
            if rule.extraction_method in ['regex', 'hybrid']:
                matches = re.finditer(rule.pattern, text, re.IGNORECASE | re.MULTILINE)
                
                for match in matches:
                    # Check context requirements
                    if self._check_context_requirements(match, text, rule.context_required):
                        # Extract surrounding context
                        start = max(0, match.start() - 100)
                        end = min(len(text), match.end() + 100)
                        context = text[start:end]
                        
                        # Find section heading
                        heading = self._find_section_heading(text, match.start())
                        
                        evidence = ExtractedEvidence(
                            content=match.group(0),
                            heading=heading,
                            context=context,
                            extraction_rule=rule.name,
                            confidence_score=rule.priority,
                            start_offset=match.start(),
                            end_offset=match.end(),
                            surrounding_text=text[start:end]
                        )
                        
                        evidence_list.append(evidence)
        
        return evidence_list
    
    async def _extract_semantic_evidence(self, text: str, 
                                       bill_metadata: Dict[str, Any]) -> List[ExtractedEvidence]:
        """Extract evidence using semantic analysis"""
        
        evidence_list = []
        
        # Split text into sentences for semantic analysis
        sentences = re.split(r'[.!?]+', text)
        
        for i, sentence in enumerate(sentences):
            if len(sentence.strip()) < 30:  # Skip very short sentences
                continue
            
            # Analyze semantic content
            semantic_score = self._calculate_semantic_score(sentence)
            
            if semantic_score > 0.6:  # Threshold for semantic relevance
                # Determine evidence type based on semantic content
                evidence_type = self._determine_semantic_type(sentence)
                
                # Find position in original text
                start_pos = text.find(sentence.strip())
                if start_pos != -1:
                    end_pos = start_pos + len(sentence.strip())
                    
                    # Find section heading
                    heading = self._find_section_heading(text, start_pos)
                    
                    # Extract context
                    context_start = max(0, start_pos - 200)
                    context_end = min(len(text), end_pos + 200)
                    context = text[context_start:context_end]
                    
                    evidence = ExtractedEvidence(
                        content=sentence.strip(),
                        heading=heading,
                        context=context,
                        extraction_rule="semantic_analysis",
                        confidence_score=semantic_score,
                        start_offset=start_pos,
                        end_offset=end_pos,
                        surrounding_text=context
                    )
                    
                    evidence_list.append(evidence)
        
        return evidence_list
    
    async def _extract_contextual_evidence(self, text: str, 
                                          bill_metadata: Dict[str, Any]) -> List[ExtractedEvidence]:
        """Extract evidence using contextual analysis"""
        
        evidence_list = []
        
        # Extract evidence based on bill context
        bill_title = bill_metadata.get('title', '').lower()
        
        # Context-specific extraction patterns
        if 'appropriation' in bill_title or 'budget' in bill_title:
            # Focus on funding evidence for appropriation bills
            funding_patterns = [
                r'(?:appropriated|authorized)[^.]*\$[\d,]+[^.]*',
                r'fiscal year \d{4}[^.]*\$[\d,]+[^.]*',
                r'budget authority[^.]*\$[\d,]+[^.]*'
            ]
            
            for pattern in funding_patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    evidence = self._create_evidence_from_match(match, text, "contextual_funding", 0.85)
                    evidence_list.append(evidence)
        
        elif 'enforcement' in bill_title or 'penalty' in bill_title:
            # Focus on enforcement evidence
            enforcement_patterns = [
                r'(?:penalty|fine|sanction)[^.]*\$[\d,]+[^.]*',
                r'criminal penalty[^.]*',
                r'civil action[^.]*'
            ]
            
            for pattern in enforcement_patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    evidence = self._create_evidence_from_match(match, text, "contextual_enforcement", 0.85)
                    evidence_list.append(evidence)
        
        # Extract cross-referenced sections
        cross_ref_evidence = self._extract_cross_referenced_evidence(text)
        evidence_list.extend(cross_ref_evidence)
        
        return evidence_list
    
    def _merge_evidence_sources(self, *evidence_sources: List[ExtractedEvidence]) -> List[ExtractedEvidence]:
        """Merge evidence from multiple sources and remove duplicates"""
        
        all_evidence = []
        for source in evidence_sources:
            all_evidence.extend(source)
        
        # Remove near-duplicates based on content similarity
        deduplicated = []
        
        for evidence in all_evidence:
            is_duplicate = False
            
            for existing in deduplicated:
                # Check for content overlap
                overlap = self._calculate_content_overlap(evidence.content, existing.content)
                position_close = abs(evidence.start_offset - existing.start_offset) < 50
                
                if overlap > 0.8 and position_close:
                    # Keep the evidence with higher confidence
                    if evidence.confidence_score > existing.confidence_score:
                        deduplicated.remove(existing)
                        deduplicated.append(evidence)
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                deduplicated.append(evidence)
        
        return deduplicated
    
    async def _apply_intelligent_filtering(self, evidence_list: List[ExtractedEvidence],
                                         bill_metadata: Dict[str, Any]) -> List[ExtractedEvidence]:
        """Apply intelligent filtering to remove low-quality evidence"""
        
        filtered_evidence = []
        
        for evidence in evidence_list:
            # Apply quality filters
            if self._passes_quality_filters(evidence):
                # Calculate enhanced confidence score
                enhanced_score = self._calculate_enhanced_confidence(evidence, bill_metadata)
                evidence.confidence_score = enhanced_score
                
                filtered_evidence.append(evidence)
        
        # Sort by confidence score
        filtered_evidence.sort(key=lambda e: e.confidence_score, reverse=True)
        
        # Apply intelligent selection to prevent information overload
        selected_evidence = self._apply_intelligent_selection(filtered_evidence)
        
        return selected_evidence
    
    async def _enhance_evidence(self, evidence_list: List[ExtractedEvidence], 
                              full_text: str) -> List[ExtractedEvidence]:
        """Enhance evidence with additional context and connections"""
        
        enhanced_evidence = []
        
        for evidence in evidence_list:
            # Add cross-references
            cross_refs = self._find_cross_references(evidence.content, full_text)
            
            # Add related definitions
            definitions = self._find_related_definitions(evidence.content, full_text)
            
            # Add exceptions and limitations
            exceptions = self._find_exceptions(evidence.content, full_text)
            
            # Enhance the evidence object
            enhanced_evidence.append(evidence)
        
        return enhanced_evidence
    
    def _check_context_requirements(self, match, text: str, context_required: List[str]) -> bool:
        """Check if context requirements are met"""
        
        if not context_required:
            return True
        
        # Check surrounding text for required context
        start = max(0, match.start() - 200)
        end = min(len(text), match.end() + 200)
        surrounding = text[start:end].lower()
        
        # At least one context requirement must be met
        return any(req.lower() in surrounding for req in context_required)
    
    def _find_section_heading(self, text: str, position: int) -> str:
        """Find the section heading for a given position"""
        
        # Look backwards for section headers
        text_before = text[:position]
        
        # Section header patterns
        section_patterns = [
            r'SECTION\s+\d+[^.]*\.',
            r'SEC\.\s+\d+[^.]*\.',
            r'\n([A-Z][A-Z\s]+)\n',
            r'\(([a-z])\)\s*([A-Z][^.]*)\.'
        ]
        
        for pattern in section_patterns:
            matches = list(re.finditer(pattern, text_before, re.IGNORECASE))
            if matches:
                last_match = matches[-1]
                return last_match.group(0).strip()
        
        return "Unknown Section"
    
    def _calculate_semantic_score(self, sentence: str) -> float:
        """Calculate semantic relevance score for a sentence"""
        
        score = 0.0
        sentence_lower = sentence.lower()
        
        # Check for semantic indicators
        for context_type, keywords in self.semantic_patterns.items():
            keyword_count = sum(1 for keyword in keywords if keyword in sentence_lower)
            if keyword_count > 0:
                score += min(0.3, keyword_count * 0.1)
        
        # Check for specific indicators
        if re.search(r'\$[\d,]+', sentence):
            score += 0.2  # Money amounts
        
        if re.search(r'\d+\s*(?:days?|months?|years?)', sentence):
            score += 0.2  # Time periods
        
        if re.search(r'(?:shall|must|required)', sentence, re.IGNORECASE):
            score += 0.2  # Legal obligations
        
        return min(1.0, score)
    
    def _determine_semantic_type(self, sentence: str) -> str:
        """Determine evidence type based on semantic content"""
        
        sentence_lower = sentence.lower()
        type_scores = {}
        
        for context_type, keywords in self.semantic_patterns.items():
            score = sum(1 for keyword in keywords if keyword in sentence_lower)
            type_scores[context_type] = score
        
        if type_scores:
            best_type = max(type_scores.items(), key=lambda x: x[1])[0]
            return best_type.replace('_context', '')
        
        return 'general'
    
    def _create_evidence_from_match(self, match, text: str, rule_name: str, 
                                  confidence: float) -> ExtractedEvidence:
        """Create ExtractedEvidence from a regex match"""
        
        heading = self._find_section_heading(text, match.start())
        
        start = max(0, match.start() - 100)
        end = min(len(text), match.end() + 100)
        context = text[start:end]
        
        return ExtractedEvidence(
            content=match.group(0),
            heading=heading,
            context=context,
            extraction_rule=rule_name,
            confidence_score=confidence,
            start_offset=match.start(),
            end_offset=match.end(),
            surrounding_text=context
        )
    
    def _extract_cross_referenced_evidence(self, text: str) -> List[ExtractedEvidence]:
        """Extract evidence from cross-referenced sections"""
        
        evidence_list = []
        
        # Find cross-references
        cross_ref_pattern = r'(?:see|refer to|pursuant to|under|in accordance with)\s+section\s+\d+'
        matches = re.finditer(cross_ref_pattern, text, re.IGNORECASE)
        
        for match in matches:
            # Try to find the referenced section
            section_num = re.search(r'\d+', match.group(0))
            if section_num:
                section_pattern = rf'SECTION\s+{section_num.group(0)}[^S]*?(?=SECTION|\Z)'
                section_match = re.search(section_pattern, text, re.IGNORECASE | re.DOTALL)
                
                if section_match:
                    evidence = self._create_evidence_from_match(
                        section_match, text, "cross_reference", 0.75
                    )
                    evidence_list.append(evidence)
        
        return evidence_list
    
    def _calculate_content_overlap(self, content1: str, content2: str) -> float:
        """Calculate content overlap between two evidence pieces"""
        
        words1 = set(content1.lower().split())
        words2 = set(content2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1 & words2
        union = words1 | words2
        
        return len(intersection) / len(union)
    
    def _passes_quality_filters(self, evidence: ExtractedEvidence) -> bool:
        """Check if evidence passes quality filters"""
        
        content = evidence.content
        
        # Length check
        if len(content) < self.quality_filters['min_length']:
            return False
        
        if len(content) > self.quality_filters['max_length']:
            return False
        
        # Blacklist check
        for pattern in self.quality_filters['blacklist_patterns']:
            if re.search(pattern, content, re.IGNORECASE):
                return False
        
        return True
    
    def _calculate_enhanced_confidence(self, evidence: ExtractedEvidence, 
                                     bill_metadata: Dict[str, Any]) -> float:
        """Calculate enhanced confidence score"""
        
        base_score = evidence.confidence_score
        
        # Boost for bill title relevance
        title = bill_metadata.get('title', '').lower()
        title_words = set(title.split())
        content_words = set(evidence.content.lower().split())
        
        title_overlap = len(title_words & content_words) / max(len(title_words), 1)
        title_boost = title_overlap * 0.1
        
        # Boost for heading relevance
        heading_boost = 0
        if evidence.heading and any(word in evidence.heading.lower() 
                                  for word in ['section', 'funding', 'penalty', 'requirement']):
            heading_boost = 0.05
        
        enhanced_score = min(1.0, base_score + title_boost + heading_boost)
        
        return enhanced_score
    
    def _apply_intelligent_selection(self, evidence_list: List[ExtractedEvidence]) -> List[ExtractedEvidence]:
        """Apply intelligent selection to prevent information overload"""
        
        # Group by extraction rule to ensure diversity
        rule_groups = defaultdict(list)
        for evidence in evidence_list:
            rule_groups[evidence.extraction_rule].append(evidence)
        
        # Select top evidence from each rule group
        selected = []
        for rule_name, group in rule_groups.items():
            # Sort group by confidence
            group.sort(key=lambda e: e.confidence_score, reverse=True)
            
            # Take top 2 from each high-priority rule, top 1 from others
            if any(rule.name == rule_name and rule.priority > 0.85 for rule in self.extraction_rules):
                selected.extend(group[:2])
            else:
                selected.extend(group[:1])
        
        # Final sort by confidence
        selected.sort(key=lambda e: e.confidence_score, reverse=True)
        
        # Limit total evidence to prevent overwhelm
        return selected[:15]
    
    def _find_cross_references(self, content: str, full_text: str) -> List[str]:
        """Find cross-references related to this evidence"""
        
        cross_refs = []
        for pattern in self.enhancement_patterns['cross_references']:
            matches = re.findall(pattern, content, re.IGNORECASE)
            cross_refs.extend(matches)
        
        return cross_refs
    
    def _find_related_definitions(self, content: str, full_text: str) -> List[str]:
        """Find related definitions"""
        
        definitions = []
        for pattern in self.enhancement_patterns['definitions']:
            matches = re.findall(pattern, content, re.IGNORECASE)
            definitions.extend(matches)
        
        return definitions
    
    def _find_exceptions(self, content: str, full_text: str) -> List[str]:
        """Find exceptions and limitations"""
        
        exceptions = []
        for pattern in self.enhancement_patterns['exceptions']:
            matches = re.findall(pattern, content, re.IGNORECASE)
            exceptions.extend(matches)
        
        return exceptions

# Global instance
intelligent_extractor = IntelligentEvidenceExtractor()

def get_intelligent_extractor() -> IntelligentEvidenceExtractor:
    """Get the global intelligent evidence extractor instance"""
    return intelligent_extractor