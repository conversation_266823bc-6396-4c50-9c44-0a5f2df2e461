#!/usr/bin/env python3
"""
Staging Database Seeder with 100% Data Validation

This script seeds the staging database with validated officials data,
ensuring 100% accuracy through multi-source verification.

Usage:
    python seed_staging_with_validation.py --dry-run  # Preview validation results
    python seed_staging_with_validation.py --validate-only  # Just run validation
    python seed_staging_with_validation.py --seed  # Actually seed the database
"""

import asyncio
import argparse
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent))

from app.services.officials_data_validator import OfficialsDataValidator, ValidationResult
from app.services.openstates_officials_api import get_openstates_officials_client
from app.db.database import SessionLocal
from app.models.official import Official

class ValidatedStagingSeeder:
    """
    Seeds staging database with 100% validated officials data
    """
    
    def __init__(self):
        self.validator = OfficialsDataValidator()
        self.db = None
        
        # Data sources configuration
        self.data_sources = {
            "high_priority_zips": [
                "90210",  # Beverly Hills - high-profile representatives
                "10001",  # NYC Manhattan - diverse representation
                "20001",  # Washington DC - federal officials
                "94102",  # San Francisco - tech-forward representatives
                "77001",  # Houston - energy/business representatives
            ],
            "test_officials": [
                # Known accurate test cases for validation
                {
                    "bioguide_id": "S001150",  # Adam Schiff
                    "name": "Adam Schiff",
                    "expected_title": "U.S. Senator",
                    "expected_party": "Democratic",
                    "expected_state": "CA"
                }
            ]
        }

    async def get_officials_data_from_sources(self) -> List[Dict[str, Any]]:
        """
        Gather officials data from multiple authoritative sources
        """
        all_officials = []
        
        print("📊 Gathering officials data from authoritative sources...")
        
        # 1. Get federal officials from OpenStates API for high-priority ZIP codes
        openstates_client = get_openstates_officials_client()
        
        if openstates_client.enabled:
            print("🔗 Fetching from OpenStates API...")
            for zip_code in self.data_sources["high_priority_zips"]:
                try:
                    officials = openstates_client.get_officials_by_zip(zip_code)
                    for official in officials:
                        official_dict = {
                            'name': official.name,
                            'title': official.title,
                            'party': official.party,
                            'email': official.email,
                            'phone': official.phone,
                            'website': official.website,
                            'homepage_url': official.homepage_url,
                            'twitter_handle': official.twitter_handle,
                            'facebook_url': official.facebook_url,
                            'instagram_handle': official.instagram_handle,
                            'social_media': official.social_media,
                            'level': official.level,
                            'chamber': official.chamber,
                            'state': official.state,
                            'district': official.district,
                            'bioguide_id': getattr(official, 'bioguide_id', None),
                            'openstates_id': official.id,
                            'bio': official.bio,
                            'office_address': official.office_address,
                            'official_photo_url': getattr(official, 'official_photo_url', None),
                            'zip_code_source': zip_code
                        }
                        all_officials.append(official_dict)
                        
                    print(f"   ✓ {zip_code}: Found {len(officials)} officials")
                    
                except Exception as e:
                    print(f"   ❌ {zip_code}: Error - {str(e)}")
        else:
            print("❌ OpenStates API not configured")
        
        # 2. Add manually curated high-accuracy officials
        print("📋 Adding manually curated officials...")
        
        # Adam Schiff - known accurate as of our recent update
        adam_schiff_accurate = {
            'name': 'Adam Schiff',
            'title': 'U.S. Senator',
            'party': 'Democratic',
            'level': 'federal',
            'chamber': 'senate',
            'state': 'CA',
            'bioguide_id': 'S001150',
            'homepage_url': 'https://www.schiff.senate.gov/',
            'twitter_handle': 'SenAdamSchiff',
            'facebook_url': 'https://www.facebook.com/SenAdamSchiff/',
            'instagram_handle': 'adamschiffca',
            'social_media': {
                "Twitter": "https://x.com/SenAdamSchiff",
                "Facebook": "https://www.facebook.com/SenAdamSchiff/",
                "Instagram": "https://www.instagram.com/adamschiffca/",
                "Official Website": "https://www.schiff.senate.gov/"
            },
            'manual_verification': True,
            'verified_date': datetime.utcnow().isoformat()
        }
        all_officials.append(adam_schiff_accurate)
        
        print(f"📊 Total officials gathered: {len(all_officials)}")
        return all_officials

    async def validate_all_data(self, officials_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate all officials data and return validation report
        """
        print("\n🔍 Starting comprehensive data validation...")
        
        async with OfficialsDataValidator() as validator:
            # Run validation on all officials
            validation_reports = await validator.validate_batch(officials_data)
            
            # Generate summary
            summary = validator.generate_validation_summary(validation_reports)
            
            print(f"\n📋 VALIDATION SUMMARY:")
            print(f"   Total Officials: {summary['total_officials']}")
            print(f"   ✅ Verified: {summary['verification_summary']['verified']} ({summary['percentages']['verified']:.1f}%)")
            print(f"   ⚠️  Flagged: {summary['verification_summary']['flagged']} ({summary['percentages']['flagged']:.1f}%)")
            print(f"   👁️  Manual Review: {summary['verification_summary']['manual_review']} ({summary['percentages']['manual_review']:.1f}%)")
            print(f"   ❌ Failed: {summary['verification_summary']['failed']} ({summary['percentages']['failed']:.1f}%)")
            print(f"   🎯 Average Confidence: {summary['average_confidence_score']:.1f}%")
            print(f"   📊 Recommendation: {summary['recommendation']}")
            
            if summary['common_issues']:
                print(f"\n🔍 Most Common Issues:")
                for issue_type, count in summary['common_issues'][:5]:
                    print(f"   • {issue_type}: {count} occurrences")
            
            return {
                'reports': validation_reports,
                'summary': summary,
                'verified_officials': [
                    (officials_data[i], report) 
                    for i, report in enumerate(validation_reports)
                    if report.overall_result == ValidationResult.VERIFIED
                ],
                'flagged_officials': [
                    (officials_data[i], report) 
                    for i, report in enumerate(validation_reports)
                    if report.overall_result == ValidationResult.FLAGGED
                ],
                'failed_officials': [
                    (officials_data[i], report) 
                    for i, report in enumerate(validation_reports)
                    if report.overall_result in [ValidationResult.FAILED, ValidationResult.MANUAL_REVIEW]
                ]
            }

    def save_validation_report(self, validation_results: Dict[str, Any], filename: str = None):
        """Save detailed validation report to file"""
        
        if not filename:
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            filename = f"validation_report_{timestamp}.json"
        
        report_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'summary': validation_results['summary'],
            'detailed_reports': []
        }
        
        # Add detailed reports
        for i, report in enumerate(validation_results['reports']):
            report_dict = {
                'official_id': report.official_id,
                'name': report.name,
                'result': report.overall_result.value,
                'confidence_score': report.confidence_score,
                'verified_fields': report.verified_fields,
                'flagged_issues': report.flagged_issues,
                'data_sources': report.data_sources,
                'recommended_actions': report.recommended_actions
            }
            report_data['detailed_reports'].append(report_dict)
        
        with open(filename, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        print(f"📄 Validation report saved: {filename}")
        return filename

    async def seed_database(self, verified_officials: List[tuple]) -> Dict[str, Any]:
        """
        Seed the database with only verified officials
        """
        if not verified_officials:
            print("❌ No verified officials to seed!")
            return {'success': False, 'message': 'No verified data'}
        
        print(f"\n🌱 Seeding database with {len(verified_officials)} verified officials...")
        
        self.db = SessionLocal()
        try:
            seeded_count = 0
            updated_count = 0
            errors = []
            
            for official_data, validation_report in verified_officials:
                try:
                    # Check if official already exists
                    existing_official = None
                    
                    if official_data.get('bioguide_id'):
                        existing_official = self.db.query(Official).filter(
                            Official.bioguide_id == official_data['bioguide_id']
                        ).first()
                    
                    if not existing_official and official_data.get('openstates_id'):
                        existing_official = self.db.query(Official).filter(
                            Official.openstates_id == official_data['openstates_id']
                        ).first()
                    
                    if not existing_official:
                        existing_official = self.db.query(Official).filter(
                            Official.name == official_data['name'],
                            Official.title == official_data['title']
                        ).first()
                    
                    # Prepare data for database
                    db_data = {
                        'name': official_data['name'],
                        'title': official_data['title'],
                        'party': official_data.get('party'),
                        'email': official_data.get('email'),
                        'phone': official_data.get('phone'),
                        'website': official_data.get('website'),
                        'homepage_url': official_data.get('homepage_url'),
                        'level': official_data.get('level', 'federal'),
                        'chamber': official_data.get('chamber'),
                        'state': official_data.get('state'),
                        'district': official_data.get('district'),
                        'bioguide_id': official_data.get('bioguide_id'),
                        'openstates_id': official_data.get('openstates_id'),
                        'twitter_handle': official_data.get('twitter_handle'),
                        'facebook_url': official_data.get('facebook_url'),
                        'instagram_handle': official_data.get('instagram_handle'),
                        'bio': official_data.get('bio'),
                        'office_address': official_data.get('office_address'),
                        'official_photo_url': official_data.get('official_photo_url'),
                        'is_active': True
                    }
                    
                    # Handle social media JSON
                    if official_data.get('social_media'):
                        if isinstance(official_data['social_media'], dict):
                            db_data['social_media'] = json.dumps(official_data['social_media'])
                        else:
                            db_data['social_media'] = official_data['social_media']
                    
                    if existing_official:
                        # Update existing official
                        for key, value in db_data.items():
                            if value is not None:  # Only update non-null values
                                setattr(existing_official, key, value)
                        updated_count += 1
                        print(f"   ✓ Updated: {official_data['name']}")
                    else:
                        # Create new official
                        new_official = Official(**db_data)
                        self.db.add(new_official)
                        seeded_count += 1
                        print(f"   ✓ Added: {official_data['name']}")
                
                except Exception as e:
                    error_msg = f"Error processing {official_data.get('name', 'Unknown')}: {str(e)}"
                    errors.append(error_msg)
                    print(f"   ❌ {error_msg}")
            
            # Commit all changes
            self.db.commit()
            
            result = {
                'success': True,
                'seeded_count': seeded_count,
                'updated_count': updated_count,
                'total_processed': seeded_count + updated_count,
                'errors': errors
            }
            
            print(f"\n🎉 Database seeding completed!")
            print(f"   ➕ New officials added: {seeded_count}")
            print(f"   🔄 Officials updated: {updated_count}")
            print(f"   ❌ Errors: {len(errors)}")
            
            return result
            
        except Exception as e:
            self.db.rollback()
            error_msg = f"Database seeding failed: {str(e)}"
            print(f"❌ {error_msg}")
            return {'success': False, 'message': error_msg}
        finally:
            self.db.close()

async def main():
    parser = argparse.ArgumentParser(description='Seed staging database with validated officials data')
    parser.add_argument('--dry-run', action='store_true', help='Preview validation results without seeding')
    parser.add_argument('--validate-only', action='store_true', help='Run validation only, save report')
    parser.add_argument('--seed', action='store_true', help='Actually seed the database with verified data')
    parser.add_argument('--report-file', type=str, help='Custom filename for validation report')
    
    args = parser.parse_args()
    
    if not any([args.dry_run, args.validate_only, args.seed]):
        print("❌ Please specify an action: --dry-run, --validate-only, or --seed")
        return
    
    seeder = ValidatedStagingSeeder()
    
    try:
        # Step 1: Gather data
        officials_data = await seeder.get_officials_data_from_sources()
        
        if not officials_data:
            print("❌ No officials data found!")
            return
        
        # Step 2: Validate data
        validation_results = await seeder.validate_all_data(officials_data)
        
        # Step 3: Save validation report
        report_file = seeder.save_validation_report(validation_results, args.report_file)
        
        # Step 4: Handle actions
        if args.dry_run:
            print(f"\n🔍 DRY RUN MODE - No database changes made")
            print(f"📄 Review validation report: {report_file}")
            
            verified_count = len(validation_results['verified_officials'])
            if verified_count > 0:
                print(f"✅ Ready to seed {verified_count} verified officials")
                print("   Run with --seed to actually seed the database")
            else:
                print("❌ No officials passed validation - fix issues before seeding")
        
        elif args.validate_only:
            print(f"\n✅ Validation complete - check report: {report_file}")
        
        elif args.seed:
            if validation_results['summary']['percentages']['verified'] < 80:
                print("❌ Validation score too low for automatic seeding!")
                print("   Less than 80% of officials verified.")
                print("   Review validation report and fix issues first.")
                return
            
            # Seed the database
            seed_result = await seeder.seed_database(validation_results['verified_officials'])
            
            if seed_result['success']:
                print("🎉 Staging database seeded successfully!")
            else:
                print(f"❌ Seeding failed: {seed_result['message']}")
    
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main())