/**
 * Playwright Tests for Bill Details API Integration
 * 
 * Tests API behavior and data handling for bill details endpoints
 */

import { test, expect, Page } from '@playwright/test';

test.describe('Bill Details API Integration', () => {
  test('handles different slug formats correctly', async ({ page }) => {
    const testSlugFormats = [
      'hr5-118',      // Canonical format
      '5-118',        // Alternative format
      'HR5-118',      // Uppercase variant
    ];

    const mockBillDetails = {
      id: 'bill_123',
      seo_slug: 'hr5-118',
      seo_title: 'Healthcare Access Act - Bill Details',
      overview: {
        what_does: {
          content: 'Test content',
          citations: []
        }
      },
      needs_human_review: false,
      metrics: { coverage_ratio: 1.0, unverified_count: 0 }
    };

    // Test each slug format
    for (const slug of testSlugFormats) {
      await page.route(`**/api/v1/bills/details/by-slug/${slug}`, async route => {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(mockBillDetails),
        });
      });

      await page.goto(`/bills/${slug}`);
      await expect(page.locator('h1')).toContainText('Healthcare Access Act');
      
      // Verify canonical URL is set correctly regardless of input format
      const canonicalLink = page.locator('link[rel="canonical"]');
      const href = await canonicalLink.getAttribute('href');
      expect(href).toContain('/bills/hr5-118'); // Should always normalize to canonical
    }
  });

  test('handles missing data gracefully with partial responses', async ({ page }) => {
    const partialBillDetails = {
      id: 'bill_124',
      seo_slug: 'partial-bill-118',
      seo_title: 'Partial Bill Data Test',
      overview: {
        what_does: {
          content: 'This bill has minimal data for testing',
          citations: []
        }
        // Missing who_affects, why_matters, etc.
      },
      positions: {
        support_reasons: [],
        oppose_reasons: [],
        amend_reasons: []
      },
      needs_human_review: true,
      metrics: { coverage_ratio: 0.2, unverified_count: 8 }
    };

    await page.route('**/api/v1/bills/details/by-slug/partial-bill-118', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(partialBillDetails),
      });
    });

    await page.goto('/bills/partial-bill-118');
    
    // Should still render the page structure
    await expect(page.locator('h1')).toBeVisible();
    
    // Should show moderation banner due to low coverage
    await expect(page.locator('[data-testid="moderation-banner"]')).toBeVisible();
    await expect(page.locator('[data-testid="coverage-metric"]')).toContainText('Coverage: 20%');
    await expect(page.locator('[data-testid="unverified-metric"]')).toContainText('8 sections awaiting');
    
    // Should handle missing sections gracefully
    await expect(page.locator('#overview-who p')).toContainText('No content');
    
    // Should show empty states for positions
    const positionColumns = page.locator('#positions .grid > div');
    await expect(positionColumns.locator('text=No reasons')).toHaveCount(3);
  });

  test('validates API response schema compliance', async ({ page }) => {
    const validBillDetails = {
      id: 'bill_125',
      bill_id: 'bill_125',
      seo_slug: 'schema-test-118',
      seo_title: 'Schema Validation Test Bill',
      seo_meta_description: 'Test description for schema validation',
      canonical_url: 'https://modernaction.io/bills/schema-test-118',
      hero_summary: 'Test hero summary',
      overview: {
        what_does: {
          content: 'Test what does content',
          citations: [
            {
              quote: 'test quote from bill text',
              start_offset: 100,
              end_offset: 125,
              heading: 'SEC. 1. TEST SECTION',
              anchor_id: 'sec-1'
            }
          ]
        },
        who_affects: {
          content: 'Test who affects content',
          citations: []
        },
        key_provisions: [
          {
            content: 'Test provision content',
            citations: [
              {
                quote: 'provision quote',
                start_offset: 200,
                end_offset: 216,
                heading: 'SEC. 2. PROVISIONS',
                anchor_id: 'sec-2'
              }
            ]
          }
        ]
      },
      positions: {
        support_reasons: [
          {
            claim: 'Test support claim',
            justification: 'Test support justification',
            citations: [
              {
                quote: 'supporting evidence quote',
                start_offset: 300,
                end_offset: 325,
                heading: 'SEC. 1. TEST SECTION',
                anchor_id: 'sec-1'
              }
            ]
          }
        ],
        oppose_reasons: [],
        amend_reasons: []
      },
      other_details: [
        {
          content: 'Additional bill content for transparency',
          citations: []
        }
      ],
      source_index: [
        {
          heading: 'SEC. 1. TEST SECTION',
          start_offset: 0,
          end_offset: 150,
          anchor_id: 'sec-1'
        },
        {
          heading: 'SEC. 2. PROVISIONS',
          start_offset: 151,
          end_offset: 300,
          anchor_id: 'sec-2'
        }
      ],
      needs_human_review: false,
      metrics: {
        coverage_ratio: 0.9,
        unverified_count: 1
      },
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z'
    };

    await page.route('**/api/v1/bills/details/by-slug/schema-test-118', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(validBillDetails),
      });
    });

    await page.goto('/bills/schema-test-118');
    
    // Verify all required fields are rendered correctly
    await expect(page.locator('h1')).toContainText('Schema Validation Test Bill');
    
    // Verify hero summary
    await expect(page.locator('p')).toContainText('Test hero summary');
    
    // Verify overview sections
    await expect(page.locator('#overview-what')).toContainText('Test what does content');
    await expect(page.locator('#overview-who')).toContainText('Test who affects content');
    
    // Verify provisions
    await expect(page.locator('#provisions')).toContainText('Test provision content');
    
    // Verify citations with all required fields
    const citations = page.locator('[data-testid="citations"]').first();
    await expect(citations.locator('[data-testid="citation-quote"]')).toContainText('test quote from bill text');
    await expect(citations.locator('[data-testid="citation-link"]')).toHaveAttribute('href', '#sec-1');
    await expect(citations.locator('[data-testid="citation-link"]')).toContainText('SEC. 1. TEST SECTION');
    
    // Verify positions
    const supportColumn = page.locator('#positions').locator('text=Support').locator('..').first();
    await expect(supportColumn).toContainText('Test support claim');
    await expect(supportColumn).toContainText('Test support justification');
    
    // Verify additional content section
    await expect(page.locator('#additional-content')).toBeVisible();
    await expect(page.locator('#additional-content')).toContainText('Additional bill content');
    
    // Verify source index
    await expect(page.locator('#source')).toBeVisible();
    await expect(page.locator('#source li')).toContainText('SEC. 1. TEST SECTION');
    
    // Verify metrics (should not show banner when coverage is high and review not needed)
    await expect(page.locator('[data-testid="moderation-banner"]')).not.toBeVisible();
  });

  test('handles API timeout scenarios', async ({ page, context }) => {
    // Set shorter timeout for this test
    context.setDefaultTimeout(5000);
    
    // Mock slow API response that times out
    await page.route('**/api/v1/bills/details/by-slug/timeout-test-118', async route => {
      // Never fulfill - simulates timeout
      // In a real scenario, this would eventually timeout
    });

    await page.goto('/bills/timeout-test-118');
    
    // Should show error state after timeout
    await expect(page.locator('h1')).toContainText('Bill details not available yet', { timeout: 10000 });
  });

  test('validates citation data integrity', async ({ page }) => {
    const billWithInvalidCitations = {
      id: 'bill_126',
      seo_slug: 'citation-test-118',
      overview: {
        what_does: {
          content: 'Content with citations',
          citations: [
            {
              quote: 'valid quote',
              start_offset: 0,
              end_offset: 11,
              heading: 'SEC. 1',
              anchor_id: 'sec-1'
            },
            {
              quote: '', // Invalid empty quote
              start_offset: 20,
              end_offset: 20,
              heading: null,
              anchor_id: null
            },
            {
              quote: 'quote without heading',
              start_offset: 30,
              end_offset: 50,
              heading: null, // Missing heading should be handled gracefully
              anchor_id: null
            }
          ]
        }
      },
      needs_human_review: true,
      metrics: { coverage_ratio: 0.5, unverified_count: 3 }
    };

    await page.route('**/api/v1/bills/details/by-slug/citation-test-118', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(billWithInvalidCitations),
      });
    });

    await page.goto('/bills/citation-test-118');
    
    // Should render citations that have valid data
    const citations = page.locator('[data-testid="citations"]');
    await expect(citations).toBeVisible();
    
    // Should show valid quote
    await expect(citations.locator('[data-testid="citation-quote"]')).toContainText('valid quote');
    
    // Should handle missing heading gracefully (no link rendered)
    const citationLinks = citations.locator('[data-testid="citation-link"]');
    const linkCount = await citationLinks.count();
    
    // Should only show links for citations with valid headings
    expect(linkCount).toBeLessThanOrEqual(1); // Only the first citation has a heading
  });

  test('handles Unicode and special characters in bill content', async ({ page }) => {
    const unicodeBillDetails = {
      id: 'bill_127',
      seo_slug: 'unicode-test-118',
      seo_title: 'Unicode Test Bill - Spëçîål Çhåråçtërs',
      overview: {
        what_does: {
          content: 'This bill contains émojis 🏥, special characters ñáméš, and Unicode symbols ≈ ∞ ™',
          citations: [
            {
              quote: 'special characters ñáméš',
              start_offset: 50,
              end_offset: 75,
              heading: 'SEC. 1. SPËÇÎÅL PROVISIONS',
              anchor_id: 'sec-1'
            }
          ]
        }
      },
      positions: {
        support_reasons: [
          {
            claim: 'Supports international accessibility ∆',
            justification: 'The bill includes provisions for multilingual support',
            citations: []
          }
        ]
      },
      needs_human_review: false,
      metrics: { coverage_ratio: 0.8, unverified_count: 0 }
    };

    await page.route('**/api/v1/bills/details/by-slug/unicode-test-118', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(unicodeBillDetails),
      });
    });

    await page.goto('/bills/unicode-test-118');
    
    // Should handle Unicode in title
    await expect(page.locator('h1')).toContainText('Spëçîål Çhåråçtërs');
    
    // Should handle Unicode in content
    await expect(page.locator('#overview-what')).toContainText('émojis 🏥');
    await expect(page.locator('#overview-what')).toContainText('ñáméš');
    await expect(page.locator('#overview-what')).toContainText('≈ ∞ ™');
    
    // Should handle Unicode in citations
    await expect(page.locator('[data-testid="citation-quote"]')).toContainText('ñáméš');
    await expect(page.locator('[data-testid="citation-link"]')).toContainText('SPËÇÎÅL PROVISIONS');
    
    // Should handle Unicode in positions
    await expect(page.locator('#positions')).toContainText('international accessibility ∆');
  });

  test('validates error response handling with detailed error messages', async ({ page }) => {
    const errorScenarios = [
      {
        status: 400,
        response: { detail: 'Invalid slug format provided' },
        slug: 'bad-format',
        expectedMessage: 'Bill details not available yet'
      },
      {
        status: 403,
        response: { detail: 'Access forbidden to this bill' },
        slug: 'forbidden-bill',
        expectedMessage: 'Bill details not available yet'
      },
      {
        status: 429,
        response: { detail: 'Rate limit exceeded' },
        slug: 'rate-limited',
        expectedMessage: 'Bill details not available yet'
      },
      {
        status: 503,
        response: { detail: 'Service temporarily unavailable' },
        slug: 'service-down',
        expectedMessage: 'Bill details not available yet'
      }
    ];

    for (const scenario of errorScenarios) {
      await page.route(`**/api/v1/bills/details/by-slug/${scenario.slug}`, async route => {
        await route.fulfill({
          status: scenario.status,
          contentType: 'application/json',
          body: JSON.stringify(scenario.response),
        });
      });

      await page.goto(`/bills/${scenario.slug}`);
      
      // Should show appropriate error state for all error types
      await expect(page.locator('h1')).toContainText(scenario.expectedMessage);
      await expect(page.locator('p')).toContainText("We couldn't load the detailed analysis");
      
      // Should not show any bill details content
      await expect(page.locator('[data-testid="moderation-banner"]')).not.toBeVisible();
      await expect(page.locator('#overview-what')).not.toBeVisible();
    }
  });
});