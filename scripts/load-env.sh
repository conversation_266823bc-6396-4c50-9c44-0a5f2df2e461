#!/bin/bash
# Environment loading script for ModernAction

set -e

# Get environment from first argument or default to development
ENVIRONMENT=${1:-development}

# Validate environment
case $ENVIRONMENT in
    development|staging|production)
        echo "Loading $ENVIRONMENT environment..."
        ;;
    *)
        echo "Error: Invalid environment '$ENVIRONMENT'. Use: development, staging, or production"
        exit 1
        ;;
esac

# Root directory
ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# API environment setup
API_DIR="$ROOT_DIR/apps/api"
if [ -f "$ROOT_DIR/.env.$ENVIRONMENT" ]; then
    echo "Copying API environment file: .env.$ENVIRONMENT -> apps/api/.env"
    cp "$ROOT_DIR/.env.$ENVIRONMENT" "$API_DIR/.env"
else
    echo "Warning: API environment file .env.$ENVIRONMENT not found"
fi

# Web environment setup  
WEB_DIR="$ROOT_DIR/apps/web"
if [ -f "$WEB_DIR/.env.$ENVIRONMENT" ]; then
    echo "Copying Web environment file: apps/web/.env.$ENVIRONMENT -> apps/web/.env.local"
    cp "$WEB_DIR/.env.$ENVIRONMENT" "$WEB_DIR/.env.local"
else
    echo "Warning: Web environment file apps/web/.env.$ENVIRONMENT not found"
fi

# Infrastructure environment setup
INFRA_DIR="$ROOT_DIR/infrastructure"
if [ -f "$ROOT_DIR/.env.$ENVIRONMENT" ]; then
    echo "Copying Infrastructure environment file for CDK"
    cp "$ROOT_DIR/.env.$ENVIRONMENT" "$INFRA_DIR/.env"
else
    echo "Warning: Infrastructure environment file not found"
fi

echo "Environment setup complete for: $ENVIRONMENT"
echo ""
echo "Next steps:"
echo "1. Update Auth0 applications with correct domains and credentials"
echo "2. Verify database connections"
echo "3. Test API startup: cd apps/api && poetry run uvicorn app.main:app --reload"
echo "4. Test Web startup: cd apps/web && npm run dev"