import React from 'react';
import { 
  CheckCircleIcon, 
  DocumentTextIcon, 
  CogIcon, 
  InformationCircleIcon,
  ClockIcon,
  SparklesIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  UsersIcon
} from '@heroicons/react/24/outline';
import type { BillDetailsResponse } from '../../types/billDetails';

interface EnhancedCompleteAnalysisProps {
  details: BillDetailsResponse;
}

export function EnhancedCompleteAnalysis({ details }: EnhancedCompleteAnalysisProps) {
  const overview = details.overview;
  const sections = overview?.complete_analysis || [];
  const isInProgress = overview?.processing_status === 'in_progress';
  
  // Don't show if no sections available and not in progress
  if (sections.length === 0 && !isInProgress) {
    return null;
  }

  return (
    <section id="detailed-bill-breakdown" className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
            <InformationCircleIcon className="h-5 w-5 text-indigo-600" />
          </div>
          <div>
            <h2 className="heading-2 text-gray-900">Detailed Bill Breakdown</h2>
            <p className="text-sm text-gray-600 mt-1">Complete analysis of every major provision</p>
          </div>
        </div>
        
        {/* Section Count */}
        {sections.length > 0 && (
          <div className="text-right">
            <span className="text-lg font-bold text-indigo-600">
              {sections.length} sections
            </span>
            <div className="text-xs text-gray-500">
              Comprehensive coverage
            </div>
          </div>
        )}
      </div>

      {/* Analysis Description */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-4 mb-6 border border-indigo-200">
        <p className="text-sm text-indigo-800">
          This comprehensive breakdown examines every major provision in the bill, 
          explaining what each section does, who it affects, and what actions are required. 
          Each analysis is grounded in direct quotes from the legislation.
        </p>
      </div>

      {/* Sections Grid */}
      {sections.length > 0 && (
        <div className="space-y-6">
          {sections.map((section, i) => (
            <AnalysisSection 
              key={i} 
              section={section} 
              index={i + 1}
            />
          ))}
        </div>
      )}

      {/* In Progress Message */}
      {isInProgress && sections.length === 0 && (
        <div className="text-center py-12">
          <ClockIcon className="h-12 w-12 text-blue-300 mx-auto mb-4 animate-pulse" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Analysis In Progress</h3>
          <p className="text-gray-600">
            We're currently analyzing this bill to provide you with a detailed breakdown. 
            Please check back shortly for the complete analysis.
          </p>
        </div>
      )}
    </section>
  );
}

function AnalysisSection({ 
  section, 
  index 
}: { 
  section: any; 
  index: number;
}) {
  const content = section.detailed_summary || section.summary || '';
  
  return (
    <div className="border border-gray-200 bg-white rounded-lg p-6 hover:shadow-sm transition-shadow duration-200">
      {/* Section Header */}
      <div className="flex items-start gap-3 mb-4">
        <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-sm font-bold ${
          section.importance === 'primary' ? 'bg-blue-100 text-blue-800' :
          section.importance === 'secondary' ? 'bg-yellow-100 text-yellow-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {index}
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900 text-base leading-tight mb-2">
            {section.title}
          </h3>
          <span className={`inline-block text-xs px-2 py-1 rounded ${
            section.importance === 'primary' ? 'bg-blue-100 text-blue-800' :
            section.importance === 'secondary' ? 'bg-yellow-100 text-yellow-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {section.importance === 'primary' ? 'Key provision' : 
             section.importance === 'secondary' ? 'Important detail' : 
             'Supporting information'}
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="space-y-4">
        {content && (
          <div className="prose prose-sm max-w-none">
            <p className="text-gray-800 leading-relaxed">
              {content}
            </p>
          </div>
        )}

        {/* Affected Parties */}
        {(section.affected_parties || section.who_it_affects) && (
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <UsersIcon className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">Who This Affects</span>
            </div>
            <div className="flex flex-wrap gap-1">
              {(section.affected_parties || section.who_it_affects || []).map((party: string, j: number) => (
                <span key={j} className="text-xs bg-white text-gray-700 px-2 py-1 rounded border">
                  {party}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Key Actions */}
        {section.key_actions && section.key_actions.length > 0 && (
          <div className="bg-blue-50 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <CogIcon className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700">Key Actions Required</span>
            </div>
            <ul className="space-y-1">
              {section.key_actions.slice(0, 4).map((action: string, j: number) => (
                <li key={j} className="text-sm text-blue-800 flex items-start gap-2">
                  <span className="text-blue-400 mt-1">•</span>
                  <span>{action}</span>
                </li>
              ))}
              {section.key_actions.length > 4 && (
                <li className="text-xs text-blue-600 italic">
                  + {section.key_actions.length - 4} more actions...
                </li>
              )}
            </ul>
          </div>
        )}

        {/* Source Information */}
        {section.ev_ids && section.ev_ids.length > 0 && (
          <div className="flex items-center gap-2 text-xs text-green-600 bg-green-50 rounded px-2 py-1">
            <CheckCircleIcon className="h-3 w-3" />
            <span>Sourced from {section.ev_ids.length} bill provision{section.ev_ids.length !== 1 ? 's' : ''}</span>
          </div>
        )}

        {/* Direct Quotes from Bill */}
        {section.citations && section.citations.length > 0 && (
          <div className="border-t border-gray-200 pt-3">
            <span className="text-xs font-medium text-gray-600 mb-2 block">Direct quotes from the bill:</span>
            <div className="space-y-2">
              {section.citations.slice(0, 2).map((citation: any, j: number) => (
                <blockquote key={j} className="text-xs text-gray-700 italic bg-gray-50 p-2 rounded border-l-2 border-gray-300">
                  "{citation.quote}"
                </blockquote>
              ))}
              {section.citations.length > 2 && (
                <span className="text-xs text-gray-500">
                  + {section.citations.length - 2} more quotes from the bill text
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}