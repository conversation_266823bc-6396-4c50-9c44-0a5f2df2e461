"""
Enhanced Citation Mining Service - PHASE 3.3
Comprehensive evidence grounding and citation mining for HR5-118 standards
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass 
class CitationContext:
    """Context information for a citation"""
    quote: str
    heading: str
    section_reference: str
    legal_citation: str
    importance_score: float
    context_before: str
    context_after: str
    statutory_reference: Optional[str] = None
    amendment_type: Optional[str] = None

@dataclass
class EvidenceCluster:
    """Cluster of related evidence spans"""
    theme: str
    evidence_ids: List[str]
    primary_citation: CitationContext
    supporting_citations: List[CitationContext]
    cluster_importance: float
    coverage_scope: str

class EnhancedCitationMiningService:
    """
    PHASE 3.3: Advanced citation mining and evidence grounding
    Provides comprehensive citation analysis for HR5-118 legal precision
    """
    
    def __init__(self):
        self.legal_citation_patterns = self._initialize_citation_patterns()
        self.importance_keywords = self._initialize_importance_keywords()
    
    def _initialize_citation_patterns(self) -> Dict[str, str]:
        """Initialize patterns for legal citations"""
        return {
            'usc_section': r'Section\s+(\d+)(?:\([^)]+\))?\s+of\s+(?:title\s+)?(\d+),?\s+United\s+States\s+Code',
            'dc_code': r'Section\s+([\d-]+)(?:\([^)]+\))?,?\s+District\s+of\s+Columbia\s+Official\s+Code',
            'sec_reference': r'SEC\.?\s*(\d+)\.?\s*([A-Z\s]+)',
            'subsection': r'\(([a-zA-Z]+)\)',
            'paragraph': r'\((\d+)\)',
            'amendment_ref': r'(?:amend|amendment|amending)\s+(?:Section|section)\s+([\d-]+)',
            'public_law': r'Public\s+Law\s+(\d+)-(\d+)',
            'cfr_reference': r'(\d+)\s+C\.F\.R\.?\s+(?:§\s*)?(\d+)',
            'dollar_amounts': r'\$[\d,]+(?:\.\d{2})?(?:\s*(?:million|billion|thousand))?',
            'deadlines': r'(?:not\s+later\s+than|within\s+\d+|deadline|by\s+\w+\s+\d+)',
            'penalties': r'(?:penalty|fine|sanction).{0,50}\$[\d,]+',
        }
    
    def _initialize_importance_keywords(self) -> Dict[str, float]:
        """Initialize keyword importance weights"""
        return {
            # High importance - money and enforcement
            'appropriation': 0.9, 'authorization': 0.9, 'funding': 0.8,
            'penalty': 0.9, 'fine': 0.8, 'violation': 0.7, 'enforcement': 0.8,
            'shall': 0.8, 'must': 0.8, 'required': 0.7, 'mandatory': 0.8,
            
            # Medium importance - structure and process  
            'establishment': 0.7, 'creation': 0.6, 'implementation': 0.6,
            'deadline': 0.7, 'timeline': 0.6, 'report': 0.5,
            'definition': 0.5, 'means': 0.4,
            
            # Technical importance
            'amendment': 0.6, 'modification': 0.5, 'revision': 0.4,
            'effective': 0.5, 'applicability': 0.4
        }
    
    def mine_comprehensive_citations(self, bill_text: str, evidence_spans: List[Dict],
                                   bill_metadata: Dict) -> Dict[str, Any]:
        """
        PHASE 3.3: Comprehensive citation mining for HR5-118 standards
        Returns enhanced citations with legal precision and context
        """
        
        logger.info(f"🔍 Mining comprehensive citations from {len(evidence_spans)} evidence spans")
        
        # Step 1: Extract legal citations from bill text
        legal_citations = self._extract_legal_citations(bill_text)
        
        # Step 2: Enhance evidence spans with citation context
        enhanced_evidence = self._enhance_evidence_with_citations(evidence_spans, legal_citations, bill_text)
        
        # Step 3: Create evidence clusters by theme
        evidence_clusters = self._create_evidence_clusters(enhanced_evidence)
        
        # Step 4: Extract statutory references and amendments
        statutory_analysis = self._analyze_statutory_references(bill_text, enhanced_evidence)
        
        # Step 5: Mine specific factual citations
        factual_citations = self._mine_factual_citations(enhanced_evidence)
        
        # Step 6: Create hierarchical citation map
        citation_hierarchy = self._create_citation_hierarchy(evidence_clusters, statutory_analysis)
        
        return {
            'enhanced_evidence_spans': enhanced_evidence,
            'evidence_clusters': evidence_clusters,
            'legal_citations': legal_citations,
            'statutory_analysis': statutory_analysis,
            'factual_citations': factual_citations,
            'citation_hierarchy': citation_hierarchy,
            'citation_stats': {
                'total_citations': len(enhanced_evidence),
                'high_importance': len([e for e in enhanced_evidence if e['importance_score'] > 0.7]),
                'statutory_refs': len(statutory_analysis['references']),
                'amendment_count': len(statutory_analysis['amendments']),
                'factual_count': len(factual_citations['money']) + len(factual_citations['deadlines']) + len(factual_citations['penalties'])
            }
        }
    
    def _extract_legal_citations(self, bill_text: str) -> Dict[str, List[Dict]]:
        """Extract structured legal citations from bill text"""
        citations = {
            'usc_sections': [],
            'dc_code_sections': [],
            'sec_references': [],
            'amendments': [],
            'public_laws': [],
            'cfr_references': []
        }
        
        # Extract USC sections
        for match in re.finditer(self.legal_citation_patterns['usc_section'], bill_text, re.IGNORECASE):
            citations['usc_sections'].append({
                'section': match.group(1),
                'title': match.group(2),
                'full_text': match.group(0),
                'start_pos': match.start(),
                'end_pos': match.end()
            })
        
        # Extract DC Code sections  
        for match in re.finditer(self.legal_citation_patterns['dc_code'], bill_text, re.IGNORECASE):
            citations['dc_code_sections'].append({
                'section': match.group(1),
                'full_text': match.group(0),
                'start_pos': match.start(),
                'end_pos': match.end()
            })
        
        # Extract SEC references
        for match in re.finditer(self.legal_citation_patterns['sec_reference'], bill_text, re.IGNORECASE):
            citations['sec_references'].append({
                'number': match.group(1),
                'title': match.group(2).strip(),
                'full_text': match.group(0),
                'start_pos': match.start(),
                'end_pos': match.end()
            })
        
        # Extract amendment references
        for match in re.finditer(self.legal_citation_patterns['amendment_ref'], bill_text, re.IGNORECASE):
            citations['amendments'].append({
                'section': match.group(1),
                'full_text': match.group(0),
                'start_pos': match.start(),
                'end_pos': match.end()
            })
        
        logger.info(f"📋 Extracted legal citations: {sum(len(v) for v in citations.values())} total")
        return citations
    
    def _enhance_evidence_with_citations(self, evidence_spans: List[Dict], 
                                       legal_citations: Dict, bill_text: str) -> List[Dict]:
        """Enhance evidence spans with citation context and importance scoring"""
        enhanced_spans = []
        
        for span in evidence_spans:
            enhanced_span = span.copy()
            
            # Calculate importance score
            importance_score = self._calculate_importance_score(span)
            enhanced_span['importance_score'] = importance_score
            
            # Extract citation context
            citation_context = self._extract_citation_context(span, bill_text)
            enhanced_span['citation_context'] = citation_context
            
            # Find related legal citations
            related_citations = self._find_related_legal_citations(span, legal_citations)
            enhanced_span['related_legal_citations'] = related_citations
            
            # Extract factual details
            factual_details = self._extract_factual_details(span)
            enhanced_span['factual_details'] = factual_details
            
            # Determine section theme
            section_theme = self._determine_section_theme(span)
            enhanced_span['section_theme'] = section_theme
            
            enhanced_spans.append(enhanced_span)
        
        return enhanced_spans
    
    def _calculate_importance_score(self, span: Dict) -> float:
        """Calculate importance score for evidence span"""
        text_content = (span.get('quote', '') + ' ' + span.get('heading', '')).lower()
        
        score = 0.0
        word_count = len(text_content.split())
        
        # Base score from keyword importance
        for keyword, weight in self.importance_keywords.items():
            if keyword in text_content:
                score += weight
        
        # Boost for specific patterns
        if re.search(self.legal_citation_patterns['dollar_amounts'], text_content):
            score += 0.8
        if re.search(self.legal_citation_patterns['deadlines'], text_content):
            score += 0.7
        if re.search(self.legal_citation_patterns['penalties'], text_content):
            score += 0.9
        if re.search(self.legal_citation_patterns['usc_section'], text_content):
            score += 0.6
        
        # Normalize by content length (longer spans get slight boost)
        length_factor = min(word_count / 50.0, 1.2)
        score *= length_factor
        
        return min(score, 1.0)  # Cap at 1.0
    
    def _extract_citation_context(self, span: Dict, bill_text: str) -> Dict:
        """Extract surrounding context for citation"""
        quote = span.get('quote', '')
        
        # Find quote position in bill text
        quote_pos = bill_text.find(quote)
        if quote_pos == -1:
            return {'context_before': '', 'context_after': ''}
        
        # Extract context (200 chars before/after)
        context_start = max(0, quote_pos - 200)
        context_end = min(len(bill_text), quote_pos + len(quote) + 200)
        
        context_before = bill_text[context_start:quote_pos].strip()
        context_after = bill_text[quote_pos + len(quote):context_end].strip()
        
        return {
            'context_before': context_before[-100:] if len(context_before) > 100 else context_before,
            'context_after': context_after[:100] if len(context_after) > 100 else context_after,
            'full_context': bill_text[context_start:context_end]
        }
    
    def _find_related_legal_citations(self, span: Dict, legal_citations: Dict) -> List[Dict]:
        """Find legal citations related to this evidence span"""
        related = []
        span_text = span.get('quote', '').lower()
        
        # Check each type of legal citation
        for citation_type, citations in legal_citations.items():
            for citation in citations:
                citation_text = citation['full_text'].lower()
                
                # Simple relevance check - shared keywords
                span_words = set(span_text.split())
                citation_words = set(citation_text.split())
                
                overlap = len(span_words.intersection(citation_words))
                if overlap > 2:  # At least 2 shared words
                    related.append({
                        'type': citation_type,
                        'citation': citation,
                        'relevance_score': overlap / min(len(span_words), len(citation_words))
                    })
        
        # Sort by relevance
        related.sort(key=lambda x: x['relevance_score'], reverse=True)
        return related[:3]  # Top 3 most relevant
    
    def _extract_factual_details(self, span: Dict) -> Dict:
        """Extract specific factual details from evidence span"""
        quote = span.get('quote', '')
        
        details = {
            'money_amounts': re.findall(self.legal_citation_patterns['dollar_amounts'], quote),
            'deadlines': re.findall(self.legal_citation_patterns['deadlines'], quote, re.IGNORECASE),
            'penalties': re.findall(self.legal_citation_patterns['penalties'], quote, re.IGNORECASE),
            'subsections': re.findall(self.legal_citation_patterns['subsection'], quote),
            'paragraphs': re.findall(self.legal_citation_patterns['paragraph'], quote)
        }
        
        return details
    
    def _determine_section_theme(self, span: Dict) -> str:
        """Determine the thematic category of this evidence span"""
        text = (span.get('quote', '') + ' ' + span.get('heading', '')).lower()
        
        # Theme classification based on content
        if any(word in text for word in ['appropriation', 'funding', 'authorization', '$']):
            return 'funding'
        elif any(word in text for word in ['penalty', 'fine', 'enforcement', 'violation']):
            return 'enforcement'
        elif any(word in text for word in ['deadline', 'timeline', 'implementation', 'effective']):
            return 'implementation'
        elif any(word in text for word in ['definition', 'means', 'term']):
            return 'definitions'
        elif any(word in text for word in ['amendment', 'modify', 'amend']):
            return 'amendments'
        elif any(word in text for word in ['report', 'disclosure', 'notification']):
            return 'reporting'
        elif any(word in text for word in ['requirement', 'standard', 'compliance']):
            return 'requirements'
        elif any(word in text for word in ['establishment', 'creation', 'program']):
            return 'establishment'
        else:
            return 'general'
    
    def _create_evidence_clusters(self, enhanced_evidence: List[Dict]) -> List[EvidenceCluster]:
        """Create thematic clusters of evidence spans"""
        # Group by theme
        theme_groups = {}
        for evidence in enhanced_evidence:
            theme = evidence['section_theme']
            if theme not in theme_groups:
                theme_groups[theme] = []
            theme_groups[theme].append(evidence)
        
        clusters = []
        for theme, evidence_list in theme_groups.items():
            if not evidence_list:
                continue
            
            # Sort by importance score
            evidence_list.sort(key=lambda x: x['importance_score'], reverse=True)
            
            # Create primary citation from highest scored evidence
            primary_evidence = evidence_list[0]
            primary_citation = CitationContext(
                quote=primary_evidence.get('quote', ''),
                heading=primary_evidence.get('heading', ''),
                section_reference=primary_evidence.get('id', ''),
                legal_citation=self._format_legal_citation(primary_evidence),
                importance_score=primary_evidence['importance_score'],
                context_before=primary_evidence['citation_context'].get('context_before', ''),
                context_after=primary_evidence['citation_context'].get('context_after', '')
            )
            
            # Create supporting citations
            supporting_citations = []
            for evidence in evidence_list[1:4]:  # Up to 3 supporting citations
                supporting_citations.append(CitationContext(
                    quote=evidence.get('quote', ''),
                    heading=evidence.get('heading', ''),
                    section_reference=evidence.get('id', ''),
                    legal_citation=self._format_legal_citation(evidence),
                    importance_score=evidence['importance_score'],
                    context_before=evidence['citation_context'].get('context_before', ''),
                    context_after=evidence['citation_context'].get('context_after', '')
                ))
            
            cluster = EvidenceCluster(
                theme=theme,
                evidence_ids=[e.get('id', '') for e in evidence_list],
                primary_citation=primary_citation,
                supporting_citations=supporting_citations,
                cluster_importance=sum(e['importance_score'] for e in evidence_list) / len(evidence_list),
                coverage_scope=f"{len(evidence_list)} provisions"
            )
            
            clusters.append(cluster)
        
        # Sort clusters by importance
        clusters.sort(key=lambda x: x.cluster_importance, reverse=True)
        
        logger.info(f"📊 Created {len(clusters)} evidence clusters: {[c.theme for c in clusters]}")
        return clusters
    
    def _format_legal_citation(self, evidence: Dict) -> str:
        """Format a proper legal citation for evidence"""
        related_citations = evidence.get('related_legal_citations', [])
        
        if related_citations:
            # Use the most relevant legal citation
            best_citation = related_citations[0]['citation']
            return best_citation['full_text']
        else:
            # Create a citation from the heading
            heading = evidence.get('heading', 'Unknown Section')
            return f"See {heading}"
    
    def _analyze_statutory_references(self, bill_text: str, enhanced_evidence: List[Dict]) -> Dict:
        """Analyze statutory references and amendments"""
        
        # Find all statutory references
        references = []
        amendments = []
        
        # Extract from legal citations in evidence
        for evidence in enhanced_evidence:
            related_citations = evidence.get('related_legal_citations', [])
            for rel_cite in related_citations:
                if rel_cite['type'] in ['usc_sections', 'dc_code_sections']:
                    references.append({
                        'type': rel_cite['type'],
                        'citation': rel_cite['citation'],
                        'evidence_id': evidence.get('id', ''),
                        'context': evidence.get('quote', '')[:100]
                    })
                elif rel_cite['type'] == 'amendments':
                    amendments.append({
                        'section': rel_cite['citation']['section'],
                        'evidence_id': evidence.get('id', ''),
                        'amendment_text': evidence.get('quote', '')[:150]
                    })
        
        return {
            'references': references,
            'amendments': amendments,
            'total_statutory_refs': len(references),
            'total_amendments': len(amendments)
        }
    
    def _mine_factual_citations(self, enhanced_evidence: List[Dict]) -> Dict:
        """Mine specific factual citations (money, deadlines, penalties)"""
        factual_citations = {
            'money': [],
            'deadlines': [],
            'penalties': [],
            'requirements': []
        }
        
        for evidence in enhanced_evidence:
            factual_details = evidence.get('factual_details', {})
            evidence_id = evidence.get('id', '')
            context = evidence.get('quote', '')
            
            # Money citations
            for amount in factual_details.get('money_amounts', []):
                factual_citations['money'].append({
                    'amount': amount,
                    'evidence_id': evidence_id,
                    'context': context[:100],
                    'importance_score': evidence.get('importance_score', 0)
                })
            
            # Deadline citations
            for deadline in factual_details.get('deadlines', []):
                factual_citations['deadlines'].append({
                    'deadline': deadline,
                    'evidence_id': evidence_id,
                    'context': context[:100],
                    'importance_score': evidence.get('importance_score', 0)
                })
            
            # Penalty citations
            for penalty in factual_details.get('penalties', []):
                factual_citations['penalties'].append({
                    'penalty': penalty,
                    'evidence_id': evidence_id,
                    'context': context[:100],
                    'importance_score': evidence.get('importance_score', 0)
                })
        
        # Sort each type by importance
        for category in factual_citations:
            factual_citations[category].sort(key=lambda x: x['importance_score'], reverse=True)
        
        return factual_citations
    
    def _create_citation_hierarchy(self, evidence_clusters: List[EvidenceCluster], 
                                 statutory_analysis: Dict) -> Dict:
        """Create hierarchical citation map for comprehensive coverage"""
        
        hierarchy = {
            'primary_themes': [],
            'secondary_themes': [],
            'statutory_foundation': [],
            'factual_support': []
        }
        
        # Classify themes by importance
        for cluster in evidence_clusters:
            if cluster.cluster_importance > 0.7:
                hierarchy['primary_themes'].append({
                    'theme': cluster.theme,
                    'evidence_count': len(cluster.evidence_ids),
                    'primary_citation': {
                        'quote': cluster.primary_citation.quote[:150],
                        'legal_citation': cluster.primary_citation.legal_citation,
                        'importance': cluster.primary_citation.importance_score
                    },
                    'supporting_count': len(cluster.supporting_citations)
                })
            else:
                hierarchy['secondary_themes'].append({
                    'theme': cluster.theme,
                    'evidence_count': len(cluster.evidence_ids),
                    'importance': cluster.cluster_importance
                })
        
        # Add statutory foundation
        for ref in statutory_analysis['references'][:10]:  # Top 10
            hierarchy['statutory_foundation'].append({
                'type': ref['type'],
                'citation': ref['citation']['full_text'],
                'evidence_id': ref['evidence_id']
            })
        
        return hierarchy


def get_enhanced_citation_mining_service():
    """Factory function for enhanced citation mining service"""
    return EnhancedCitationMiningService()