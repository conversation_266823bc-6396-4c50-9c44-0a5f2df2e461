#!/usr/bin/env python3
"""
Test script for AI usage tracking system
"""

import asyncio
import sys
import os

# Add the API directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'apps', 'api'))

from app.db.database import get_db
from app.models.bill import Bill
from app.services.ai_usage_tracking_service import AIUsageTrackingService

async def test_ai_usage_tracking():
    """Test the AI usage tracking system"""
    print("🧪 Testing AI Usage Tracking System")
    print("=" * 50)
    
    # Get database session
    db = next(get_db())
    
    try:
        tracking_service = AIUsageTrackingService(db)
        
        # Test 1: Track a sample AI usage
        print("📊 Test 1: Tracking sample AI usage...")
        usage_log = tracking_service.track_ai_usage(
            operation_type="test_operation",
            model_name="gpt-4-turbo",
            prompt_tokens=1000,
            completion_tokens=500,
            response_time_ms=2500.0,
            success=True,
            operation_subtype="test_subtype",
            bill_id=None,
            user_id="test_user",
            prompt_length=5000,
            response_length=2000
        )
        
        print(f"✅ Created usage log: {usage_log.id}")
        print(f"   - Total tokens: {usage_log.total_tokens}")
        print(f"   - Total cost: ${usage_log.total_cost:.4f}")
        print(f"   - Response time: {usage_log.response_time_ms}ms")
        print()
        
        # Test 2: Get usage stats
        print("📈 Test 2: Getting usage statistics...")
        
        # Get a bill to test with
        bill = db.query(Bill).first()
        if bill:
            # Track usage for a specific bill
            bill_usage = tracking_service.track_ai_usage(
                operation_type="bill_analysis",
                model_name="gpt-4-turbo",
                prompt_tokens=2000,
                completion_tokens=800,
                response_time_ms=3000.0,
                success=True,
                operation_subtype="comprehensive_analysis",
                bill_id=bill.id,
                prompt_length=10000,
                response_length=4000
            )
            
            print(f"✅ Tracked bill analysis for: {bill.bill_number}")
            print(f"   - Cost: ${bill_usage.total_cost:.4f}")
            print()
            
            # Get bill costs
            print("💰 Test 3: Getting bill AI costs...")
            bill_costs = tracking_service.get_bill_ai_costs(bill.id)
            
            if 'error' not in bill_costs:
                print(f"✅ Bill {bill.bill_number} AI costs:")
                print(f"   - Total cost: ${bill_costs['total_cost']}")
                print(f"   - Total tokens: {bill_costs['total_tokens']:,}")
                print(f"   - Total requests: {bill_costs['total_requests']}")
                print(f"   - Success rate: {bill_costs['success_rate']}%")
                print(f"   - Operations: {len(bill_costs['operations'])}")
                
                for op in bill_costs['operations']:
                    print(f"     - {op['operation_type']}: ${op['total_cost']:.4f} ({op['requests']} requests)")
            else:
                print(f"❌ Error getting bill costs: {bill_costs['error']}")
        else:
            print("⚠️  No bills found in database")
        
        print()
        
        # Test 4: Get daily summary
        print("📅 Test 4: Getting daily usage summary...")
        daily_summary = tracking_service.get_daily_usage_summary(days=7)
        
        if 'error' not in daily_summary:
            print(f"✅ Daily summary (last 7 days):")
            print(f"   - Total cost: ${daily_summary['total_cost']}")
            print(f"   - Total tokens: {daily_summary['total_tokens']:,}")
            print(f"   - Total requests: {daily_summary['total_requests']}")
            print(f"   - Bills processed: {daily_summary['bills_processed']}")
            print(f"   - Avg cost per day: ${daily_summary['avg_cost_per_day']:.4f}")
            print(f"   - Avg cost per bill: ${daily_summary['avg_cost_per_bill']:.4f}")
        else:
            print(f"❌ Error getting daily summary: {daily_summary['error']}")
        
        print()
        
        # Test 5: Test pricing calculations
        print("💲 Test 5: Testing pricing calculations...")
        
        # Test different models
        models_to_test = ['gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo']
        
        for model in models_to_test:
            test_usage = tracking_service.track_ai_usage(
                operation_type="pricing_test",
                model_name=model,
                prompt_tokens=1000,
                completion_tokens=500,
                response_time_ms=1000.0,
                success=True
            )
            
            print(f"   - {model}: ${test_usage.total_cost:.4f} (1000 prompt + 500 completion tokens)")
        
        print()
        print("🎉 All AI usage tracking tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_ai_usage_tracking())
