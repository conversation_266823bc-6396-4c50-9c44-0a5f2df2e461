"""Add AI usage tracking tables

Revision ID: add_ai_usage_tracking
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_ai_usage_tracking'
down_revision = None  # Update this to the latest revision
branch_labels = None
depends_on = None


def upgrade():
    # Create ai_usage_logs table
    op.create_table('ai_usage_logs',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('operation_type', sa.String(), nullable=False),
        sa.Column('operation_subtype', sa.String(), nullable=True),
        sa.Column('bill_id', sa.String(), nullable=True),
        sa.Column('model_name', sa.String(), nullable=False),
        sa.Column('provider', sa.String(), nullable=False),
        sa.Column('prompt_tokens', sa.Integer(), nullable=False),
        sa.Column('completion_tokens', sa.Integer(), nullable=False),
        sa.Column('total_tokens', sa.Integer(), nullable=False),
        sa.Column('prompt_cost', sa.Float(), nullable=False),
        sa.Column('completion_cost', sa.Float(), nullable=False),
        sa.Column('total_cost', sa.Float(), nullable=False),
        sa.Column('response_time_ms', sa.Float(), nullable=True),
        sa.Column('success', sa.Boolean(), nullable=False),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('prompt_length', sa.Integer(), nullable=True),
        sa.Column('response_length', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=True),
        sa.Column('session_id', sa.String(), nullable=True),
        sa.ForeignKeyConstraint(['bill_id'], ['bills.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for performance
    op.create_index('idx_ai_usage_logs_bill_id', 'ai_usage_logs', ['bill_id'])
    op.create_index('idx_ai_usage_logs_operation_type', 'ai_usage_logs', ['operation_type'])
    op.create_index('idx_ai_usage_logs_created_at', 'ai_usage_logs', ['created_at'])
    op.create_index('idx_ai_usage_logs_model_name', 'ai_usage_logs', ['model_name'])
    op.create_index('idx_ai_usage_logs_success', 'ai_usage_logs', ['success'])

    # Create ai_usage_summaries table
    op.create_table('ai_usage_summaries',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('date_bucket', sa.DateTime(), nullable=False),
        sa.Column('period_type', sa.String(), nullable=False),
        sa.Column('operation_type', sa.String(), nullable=True),
        sa.Column('model_name', sa.String(), nullable=True),
        sa.Column('total_requests', sa.Integer(), nullable=False),
        sa.Column('successful_requests', sa.Integer(), nullable=False),
        sa.Column('failed_requests', sa.Integer(), nullable=False),
        sa.Column('total_prompt_tokens', sa.Integer(), nullable=False),
        sa.Column('total_completion_tokens', sa.Integer(), nullable=False),
        sa.Column('total_tokens', sa.Integer(), nullable=False),
        sa.Column('total_prompt_cost', sa.Float(), nullable=False),
        sa.Column('total_completion_cost', sa.Float(), nullable=False),
        sa.Column('total_cost', sa.Float(), nullable=False),
        sa.Column('avg_response_time_ms', sa.Float(), nullable=True),
        sa.Column('avg_tokens_per_request', sa.Float(), nullable=True),
        sa.Column('avg_cost_per_request', sa.Float(), nullable=True),
        sa.Column('bills_processed', sa.Integer(), nullable=False),
        sa.Column('avg_cost_per_bill', sa.Float(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for summaries
    op.create_index('idx_ai_usage_summaries_date_bucket', 'ai_usage_summaries', ['date_bucket'])
    op.create_index('idx_ai_usage_summaries_period_type', 'ai_usage_summaries', ['period_type'])
    op.create_index('idx_ai_usage_summaries_operation_type', 'ai_usage_summaries', ['operation_type'])

    # Create ai_budget_alerts table
    op.create_table('ai_budget_alerts',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('alert_name', sa.String(), nullable=False),
        sa.Column('alert_type', sa.String(), nullable=False),
        sa.Column('threshold_amount', sa.Float(), nullable=False),
        sa.Column('threshold_tokens', sa.Integer(), nullable=True),
        sa.Column('operation_type', sa.String(), nullable=True),
        sa.Column('model_name', sa.String(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('last_triggered', sa.DateTime(), nullable=True),
        sa.Column('times_triggered', sa.Integer(), nullable=False),
        sa.Column('notification_emails', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('webhook_url', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for alerts
    op.create_index('idx_ai_budget_alerts_is_active', 'ai_budget_alerts', ['is_active'])
    op.create_index('idx_ai_budget_alerts_alert_type', 'ai_budget_alerts', ['alert_type'])


def downgrade():
    # Drop tables in reverse order
    op.drop_table('ai_budget_alerts')
    op.drop_table('ai_usage_summaries')
    op.drop_table('ai_usage_logs')
