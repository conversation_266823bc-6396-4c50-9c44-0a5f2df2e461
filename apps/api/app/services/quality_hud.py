"""
Quality HUD - Real-time monitoring for AI bill analysis quality
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.models.ai_usage import AIUsageLog, AIUsageSummary

logger = logging.getLogger(__name__)


@dataclass
class QualityMetrics:
    """Quality metrics for a bill analysis"""
    bill_id: str
    evidence_coverage: float  # 0.0 to 1.0
    avg_evidence_words: float
    tokens_in: int
    tokens_out: int
    truncated: bool
    triage_mix: Dict[str, int]  # {'high': 2, 'medium': 1, 'rules_only': 0}
    regen_rate: float  # % sentences retried
    processing_level: str
    cost: float
    response_time_ms: float
    timestamp: datetime


class QualityHUD:
    """Real-time quality monitoring dashboard for AI analysis"""
    
    def __init__(self, db: Session):
        self.db = db
        
    def track_analysis_quality(self, 
                             bill_id: str,
                             analysis_result: Dict[str, Any],
                             spans: Dict[str, List[Dict]] = None) -> QualityMetrics:
        """Track quality metrics for a bill analysis"""
        
        # Calculate evidence coverage
        evidence_coverage = self._calculate_evidence_coverage(analysis_result, spans)
        
        # Calculate average evidence words
        avg_evidence_words = self._calculate_avg_evidence_words(spans)
        
        # Extract metadata
        metadata = analysis_result.get('_metadata', {})
        
        # Calculate triage mix (placeholder for now)
        triage_mix = self._calculate_triage_mix(analysis_result)
        
        metrics = QualityMetrics(
            bill_id=bill_id,
            evidence_coverage=evidence_coverage,
            avg_evidence_words=avg_evidence_words,
            tokens_in=metadata.get('tokens', 0) // 2,  # Rough estimate
            tokens_out=metadata.get('tokens', 0) // 2,  # Rough estimate
            truncated=metadata.get('truncated', False),
            triage_mix=triage_mix,
            regen_rate=metadata.get('regen_rate', 0.0),
            processing_level=analysis_result.get('processing_level', 'unknown'),
            cost=metadata.get('cost', 0.0),
            response_time_ms=metadata.get('response_time_ms', 0.0),
            timestamp=datetime.utcnow()
        )
        
        # Log quality alerts
        self._check_quality_alerts(metrics)
        
        return metrics
    
    def _calculate_evidence_coverage(self, 
                                   analysis_result: Dict[str, Any], 
                                   spans: Dict[str, List[Dict]] = None) -> float:
        """Calculate % of sentences with evidence coverage"""
        
        if not spans:
            # If no spans provided, check if analysis claims to be span-grounded
            if analysis_result.get('_metadata', {}).get('span_grounded'):
                return 1.0  # Assume 100% if span-grounded
            else:
                return 0.0  # No evidence if not span-grounded
        
        # Count sentences with evidence
        total_sentences = 0
        sentences_with_evidence = 0
        
        for category, span_list in spans.items():
            for span in span_list:
                total_sentences += 1
                if span.get('ev') or span.get('q'):  # Has evidence
                    sentences_with_evidence += 1
        
        if total_sentences == 0:
            return 0.0
            
        return sentences_with_evidence / total_sentences
    
    def _calculate_avg_evidence_words(self, spans: Dict[str, List[Dict]] = None) -> float:
        """Calculate average words per evidence span"""
        
        if not spans:
            return 0.0
        
        total_words = 0
        total_spans = 0
        
        for category, span_list in spans.items():
            for span in span_list:
                quote = span.get('q', '')
                if quote:
                    total_words += len(quote.split())
                    total_spans += 1
        
        if total_spans == 0:
            return 0.0
            
        return total_words / total_spans
    
    def _calculate_triage_mix(self, analysis_result: Dict[str, Any]) -> Dict[str, int]:
        """Calculate triage routing mix"""
        
        processing_level = analysis_result.get('processing_level', 'unknown')
        
        # Map processing levels to triage categories
        triage_map = {
            'span_grounded': 'high',
            'quality_optimized': 'high', 
            'quality_retry': 'high',
            'placeholder': 'rules_only',
            'insufficient_evidence': 'medium',
            'minimal': 'rules_only',
            'fallback': 'rules_only'
        }
        
        category = triage_map.get(processing_level, 'medium')
        
        return {
            'high': 1 if category == 'high' else 0,
            'medium': 1 if category == 'medium' else 0,
            'rules_only': 1 if category == 'rules_only' else 0
        }
    
    def _check_quality_alerts(self, metrics: QualityMetrics):
        """Check for quality issues and log alerts"""
        
        alerts = []
        
        # Evidence coverage alert
        if metrics.evidence_coverage < 1.0:
            alerts.append(f"Evidence coverage below 100%: {metrics.evidence_coverage:.1%}")
        
        # Token truncation alert
        if metrics.truncated:
            alerts.append("Response truncated due to token limits")
        
        # Evidence word count alert (sweet spot is 15-40 words)
        if metrics.avg_evidence_words < 15:
            alerts.append(f"Evidence spans too short: {metrics.avg_evidence_words:.1f} words avg")
        elif metrics.avg_evidence_words > 40:
            alerts.append(f"Evidence spans too long: {metrics.avg_evidence_words:.1f} words avg")
        
        # High regeneration rate alert
        if metrics.regen_rate > 0.3:
            alerts.append(f"High retry rate: {metrics.regen_rate:.1%}")
        
        # Cost alert
        if metrics.cost > 0.05:  # More than 5 cents per bill
            alerts.append(f"High cost per bill: ${metrics.cost:.4f}")
        
        # Log alerts
        if alerts:
            logger.warning(f"Quality alerts for {metrics.bill_id}: {'; '.join(alerts)}")
        else:
            logger.info(f"Quality check passed for {metrics.bill_id}")
    
    def get_daily_quality_summary(self, days: int = 1) -> Dict[str, Any]:
        """Get quality summary for the last N days"""
        
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # Get recent AI usage logs
            logs = self.db.query(AIUsageLog).filter(
                AIUsageLog.created_at >= start_date
            ).all()
            
            if not logs:
                return {
                    "total_analyses": 0,
                    "avg_cost": 0.0,
                    "avg_tokens": 0,
                    "success_rate": 0.0,
                    "avg_response_time": 0.0,
                    "quality_alerts": []
                }
            
            # Calculate summary metrics
            total_analyses = len(logs)
            total_cost = sum(log.total_cost for log in logs)
            total_tokens = sum(log.total_tokens for log in logs)
            successful = sum(1 for log in logs if log.success)
            total_response_time = sum(log.response_time_ms or 0 for log in logs)
            
            return {
                "total_analyses": total_analyses,
                "avg_cost": total_cost / total_analyses if total_analyses > 0 else 0.0,
                "avg_tokens": total_tokens / total_analyses if total_analyses > 0 else 0,
                "success_rate": successful / total_analyses if total_analyses > 0 else 0.0,
                "avg_response_time": total_response_time / total_analyses if total_analyses > 0 else 0.0,
                "span_grounded_rate": sum(1 for log in logs if 'span_grounded' in log.operation_type) / total_analyses if total_analyses > 0 else 0.0,
                "quality_alerts": self._get_recent_alerts(logs)
            }
            
        except Exception as e:
            logger.error(f"Failed to get quality summary: {e}")
            return {"error": str(e)}
    
    def _get_recent_alerts(self, logs: List[AIUsageLog]) -> List[str]:
        """Extract quality alerts from recent logs"""
        
        alerts = []
        
        # Check for high costs
        high_cost_logs = [log for log in logs if log.total_cost > 0.05]
        if high_cost_logs:
            alerts.append(f"{len(high_cost_logs)} analyses exceeded cost threshold")
        
        # Check for failures
        failed_logs = [log for log in logs if not log.success]
        if failed_logs:
            alerts.append(f"{len(failed_logs)} analyses failed")
        
        # Check for slow responses
        slow_logs = [log for log in logs if (log.response_time_ms or 0) > 10000]  # >10 seconds
        if slow_logs:
            alerts.append(f"{len(slow_logs)} analyses were slow (>10s)")
        
        return alerts
    
    def log_quality_metrics(self, metrics: QualityMetrics):
        """Log quality metrics for monitoring"""
        
        logger.info(
            f"Quality metrics for {metrics.bill_id}: "
            f"evidence_coverage={metrics.evidence_coverage:.1%}, "
            f"avg_evidence_words={metrics.avg_evidence_words:.1f}, "
            f"cost=${metrics.cost:.4f}, "
            f"processing_level={metrics.processing_level}"
        )
