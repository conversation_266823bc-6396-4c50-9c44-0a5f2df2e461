import { Metadata } from 'next';
import { ZipCodeLookup } from '@/components/officials';

export const metadata: Metadata = {
  title: 'Find Your Representatives | ModernAction.io',
  description: 'Easily find and contact your federal and state representatives by ZIP code. Get contact information, social media links, and see their legislative activity. Search senators, house representatives, and state officials.',
  keywords: [
    'representatives',
    'congress',
    'senators', 
    'house members',
    'zip code lookup',
    'contact officials',
    'government representatives',
    'elected officials',
    'federal representatives',
    'state representatives',
    'contact information',
    'phone numbers',
    'email addresses',
    'legislative activity'
  ].join(', '),
  openGraph: {
    title: 'Find Your Representatives | ModernAction.io',
    description: 'Search and contact your elected representatives by ZIP code. Find senators, house members, and state officials with complete contact information.',
    type: 'website',
    siteName: 'ModernAction.io',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Find Your Representatives | ModernAction.io',
    description: 'Search your elected representatives by ZIP code and get their contact information.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://modernaction.io/officials'
  }
};

export default function OfficialsPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
            Find Your
            <span className="text-blue-600"> Representatives</span>
          </h1>
          <p className="mt-6 max-w-2xl mx-auto text-xl text-gray-600">
            Discover who represents you in Congress and connect with them directly. 
            Get their contact information, social media links, and track their legislative activity.
          </p>
        </div>

        {/* Key Features */}
        <div className="mb-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
              <div className="w-12 h-12 mx-auto bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Instant Lookup</h3>
              <p className="text-gray-600">
                Enter your ZIP code and instantly find your federal senators and house representative.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
              <div className="w-12 h-12 mx-auto bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Direct Contact</h3>
              <p className="text-gray-600">
                Get email addresses, phone numbers, and direct links to contact your representatives.
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center">
              <div className="w-12 h-12 mx-auto bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Track Activity</h3>
              <p className="text-gray-600">
                See how many bills they've sponsored, their committee memberships, and voting patterns.
              </p>
            </div>
          </div>
        </div>

        {/* Main ZIP Code Lookup Component */}
        <ZipCodeLookup />

        {/* Additional Information */}
        <div className="mt-16 bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Why Contact Your Representatives?
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Make Your Voice Heard</h3>
              <p className="text-gray-600 mb-4">
                Your representatives work for you. Contacting them lets them know what issues matter to you and your community.
              </p>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-start">
                  <span className="text-green-600 mr-2">✓</span>
                  Influence voting decisions on important bills
                </li>
                <li className="flex items-start">
                  <span className="text-green-600 mr-2">✓</span>
                  Share your personal experiences with policies
                </li>
                <li className="flex items-start">
                  <span className="text-green-600 mr-2">✓</span>
                  Request help with federal agencies
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Best Practices</h3>
              <p className="text-gray-600 mb-4">
                Follow these tips to make your communication with representatives more effective.
              </p>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  Be specific about the bill or issue you're discussing
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  Share your personal story and how the issue affects you
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  Be respectful and professional in your communication
                </li>
                <li className="flex items-start">
                  <span className="text-blue-600 mr-2">•</span>
                  Include your full name and ZIP code to verify residency
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}