import { Metadata } from 'next'
import { billApi } from '../../services/apiClient'
import { Bill, BillStatus } from '../../types'
import { ModernizedHomepageSection } from '../../components/ui/modernized-homepage-section'

export const metadata: Metadata = {
  title: 'UI Demo - Before vs After | ModernAction',
  description: 'Compare the old and new UI designs side by side',
}

// Server-side data fetching for demo bills
async function getDemoBills(): Promise<Bill[]> {
  try {
    const bills = await billApi.searchBills({ 
      is_featured: true,
      limit: 3
    });
    
    if (bills.length < 3) {
      const additionalBills = await billApi.getBills({ limit: 6 });
      const existingIds = new Set(bills.map(b => b.id));
      const uniqueAdditionalBills = additionalBills.filter(bill => !existingIds.has(bill.id));
      return [...bills, ...uniqueAdditionalBills.slice(0, 3 - bills.length)];
    }
    
    return bills.slice(0, 3);
  } catch (error) {
    console.error('Error fetching demo bills:', error);
    // Return mock data for demo
    return [
      {
        id: 1,
        bill_number: 'HR.1234',
        title: 'Healthcare Innovation and Modernization Act',
        status: 'committee',
        chamber: 'house',
        session_year: 2024,
        sponsor_party: 'D',
        tldr: 'This bill modernizes healthcare infrastructure by investing in digital health records, telemedicine capabilities, and AI-powered diagnostic tools to improve patient outcomes and reduce costs.',
        popular_title: 'Healthcare Modernization Act'
      },
      {
        id: 2,
        bill_number: 'S.567',
        title: 'Clean Energy Infrastructure Investment Act',
        status: 'floor',
        chamber: 'senate',
        session_year: 2024,
        sponsor_party: 'R',
        tldr: 'Provides federal funding for renewable energy projects, electric vehicle charging stations, and grid modernization to accelerate the transition to clean energy.',
        popular_title: 'Clean Energy Investment Act'
      },
      {
        id: 3,
        bill_number: 'HR.890',
        title: 'Education Technology Equity Act',
        status: 'passed',
        chamber: 'house',
        session_year: 2024,
        sponsor_party: 'D',
        tldr: 'Ensures all students have access to high-speed internet and modern computing devices for remote learning and digital literacy education.',
        popular_title: 'Digital Education Equity Act'
      }
    ];
  }
}

export default async function UIDemo() {
  const demoBills = await getDemoBills();

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <div className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <h1 className="text-3xl font-bold text-slate-900 mb-2">UI Modernization Demo</h1>
          <p className="text-lg text-slate-600">Compare the before and after of our shadcn/ui integration</p>
        </div>
      </div>

      {/* Before Section */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-slate-900 mb-2">❌ BEFORE (Current Design)</h2>
            <p className="text-slate-600">Custom CSS with basic styling</p>
          </div>
          
          {/* Original Bill Cards */}
          <div className="grid md:grid-cols-3 gap-8">
            {demoBills.map((bill) => {
              const getStatusColor = (status: string) => {
                switch (status) {
                  case 'committee': return 'from-amber-500 to-orange-500';
                  case 'floor': return 'from-red-500 to-red-600';
                  case 'passed': return 'from-green-500 to-green-600';
                  default: return 'from-blue-500 to-blue-600';
                }
              };

              const getStatusText = (status: string) => {
                switch (status) {
                  case 'committee': return '⏳ In Committee';
                  case 'floor': return '🗳️ Floor Vote Soon';
                  case 'passed': return '✅ Passed Chamber';
                  default: return '🔄 Active';
                }
              };

              return (
                <div key={`old-${bill.id}`} className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 bill-card">
                  {/* Old Status Badge */}
                  <div className={`bg-gradient-to-r ${getStatusColor(bill.status)} text-white text-sm font-bold px-4 py-3 text-center`}>
                    {getStatusText(bill.status)}
                  </div>
                  
                  <div className="p-6">
                    {/* Bill Number & Title */}
                    <div className="mb-4">
                      <span className="text-sm font-mono font-semibold text-blue-700 mb-2 block bg-blue-50 px-2 py-1 rounded inline-block">
                        {bill.bill_number}
                      </span>
                      <h3 className="text-xl font-bold text-gray-900 leading-tight mb-2">
                        {bill.title}
                      </h3>
                      <p className="text-sm text-gray-600 font-medium">
                        Popular name: {bill.popular_title || "Processing..."}
                      </p>
                    </div>

                    {/* AI Summary */}
                    <div className="mb-6">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-xs font-semibold text-purple-700 bg-purple-100 px-2 py-1 rounded">
                          🤖 AI Summary
                        </span>
                      </div>
                      <p className="text-gray-700 text-sm leading-relaxed line-clamp-3">
                        {bill.tldr || 'Legislative summary being processed...'}
                      </p>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-3">
                      <button className="flex-1 bg-blue-700 text-white px-4 py-3 rounded-xl font-semibold hover:bg-blue-800 transition-all duration-200 text-center">
                        Send Message to Congress
                      </button>
                      <button className="px-4 py-3 border-2 border-gray-300 text-gray-700 font-medium rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200">
                        Read Analysis
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* After Section */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-slate-900 mb-2">✨ AFTER (shadcn/ui Design)</h2>
            <p className="text-slate-600">Modern, polished components with enhanced UX</p>
          </div>
        </div>
      </section>

      {/* New Design */}
      <ModernizedHomepageSection bills={demoBills} />

      {/* Comparison Summary */}
      <section className="py-12 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-slate-900 mb-8 text-center">Key Improvements</h2>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-red-600">❌ Before</h3>
              <ul className="space-y-2 text-sm text-slate-600">
                <li>• Custom CSS classes with inconsistent styling</li>
                <li>• Basic hover effects and transitions</li>
                <li>• Limited component variants</li>
                <li>• Manual color coding for status</li>
                <li>• Basic accessibility support</li>
                <li>• Dated visual hierarchy</li>
              </ul>
            </div>
            
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-green-600">✅ After</h3>
              <ul className="space-y-2 text-sm text-slate-600">
                <li>• Consistent design system with shadcn/ui</li>
                <li>• Smooth animations and micro-interactions</li>
                <li>• Extensive component variants (civic, action, etc.)</li>
                <li>• Semantic badge system with meaning</li>
                <li>• Built-in accessibility with Radix primitives</li>
                <li>• Modern 2025 visual language</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}