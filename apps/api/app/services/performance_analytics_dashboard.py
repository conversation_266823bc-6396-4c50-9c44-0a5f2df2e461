"""
Performance Analytics Dashboard - Phase 4 Comprehensive Monitoring
Real-time performance monitoring, analytics, and insights for the bill processing system
"""

import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
from collections import defaultdict, deque
import asyncio

from sqlalchemy.orm import Session
from sqlalchemy import and_, func

logger = logging.getLogger(__name__)

class MetricCategory(Enum):
    """Categories of performance metrics"""
    QUALITY = "quality"
    COST = "cost"
    PERFORMANCE = "performance"
    RELIABILITY = "reliability"
    EFFICIENCY = "efficiency"
    USER_EXPERIENCE = "user_experience"

class TimeRange(Enum):
    """Time ranges for analytics"""
    LAST_HOUR = "last_hour"
    LAST_24H = "last_24h"
    LAST_7D = "last_7d"
    LAST_30D = "last_30d"
    LAST_90D = "last_90d"

class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class PerformanceMetric:
    """Individual performance metric data point"""
    metric_id: str
    timestamp: datetime
    category: MetricCategory
    name: str
    value: float
    unit: str
    context: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)

@dataclass
class TrendAnalysis:
    """Trend analysis for metrics"""
    metric_name: str
    time_range: TimeRange
    current_value: float
    previous_value: float
    change_percentage: float
    trend_direction: str  # "up", "down", "stable"
    confidence_level: float
    data_points: int

@dataclass
class PerformanceAlert:
    """Performance alert"""
    alert_id: str
    timestamp: datetime
    severity: AlertSeverity
    category: MetricCategory
    title: str
    description: str
    current_value: float
    threshold_value: float
    affected_components: List[str]
    recommended_actions: List[str]
    auto_resolvable: bool

@dataclass
class SystemHealth:
    """Overall system health status"""
    timestamp: datetime
    overall_score: float  # 0-100
    component_scores: Dict[str, float]
    active_alerts: int
    critical_issues: int
    performance_summary: str
    uptime_percentage: float
    last_24h_bills_processed: int

@dataclass
class AnalyticsSummary:
    """Summary analytics for different time periods"""
    time_range: TimeRange
    bills_processed: int
    average_quality: float
    average_cost: float
    average_processing_time: float
    success_rate: float
    cost_efficiency: float  # Quality per dollar
    throughput: float  # Bills per hour
    error_rate: float

@dataclass
class DashboardData:
    """Complete dashboard data structure"""
    timestamp: datetime
    system_health: SystemHealth
    active_alerts: List[PerformanceAlert]
    analytics_summary: Dict[TimeRange, AnalyticsSummary]
    trend_analysis: Dict[str, TrendAnalysis]
    top_metrics: Dict[MetricCategory, List[PerformanceMetric]]
    cost_breakdown: Dict[str, float]
    quality_distribution: Dict[str, int]
    processing_pipeline_status: Dict[str, str]

class PerformanceAnalyticsDashboard:
    """
    Phase 4 Performance Analytics Dashboard that provides real-time monitoring,
    analytics, and insights for the bill processing system
    """
    
    def __init__(self, db: Session):
        self.db = db
        
        # Metric storage
        self.metrics = deque(maxlen=50000)  # Store last 50k metrics
        self.metric_history = defaultdict(lambda: deque(maxlen=1000))
        
        # Alert management
        self.active_alerts = {}
        self.alert_history = deque(maxlen=1000)
        
        # Performance thresholds
        self.thresholds = self._initialize_performance_thresholds()
        
        # System components for health monitoring
        self.system_components = {
            'evidence_processor': {'status': 'healthy', 'last_check': datetime.utcnow()},
            'quality_feedback': {'status': 'healthy', 'last_check': datetime.utcnow()},
            'cost_optimizer': {'status': 'healthy', 'last_check': datetime.utcnow()},
            'pattern_recognition': {'status': 'healthy', 'last_check': datetime.utcnow()},
            'adaptive_learning': {'status': 'healthy', 'last_check': datetime.utcnow()}
        }
        
        # Cache for dashboard data
        self.dashboard_cache = {
            'data': None,
            'last_updated': None,
            'cache_duration': 60  # seconds
        }
        
        # Initialize baseline metrics
        self.baseline_metrics = {}
    
    async def record_metric(self, category: MetricCategory, name: str, 
                          value: float, unit: str = "", 
                          context: Dict[str, Any] = None,
                          tags: List[str] = None) -> None:
        """Record a performance metric"""
        
        timestamp = datetime.utcnow()
        metric_id = f"{category.value}_{name}_{timestamp.strftime('%Y%m%d_%H%M%S_%f')}"
        
        metric = PerformanceMetric(
            metric_id=metric_id,
            timestamp=timestamp,
            category=category,
            name=name,
            value=value,
            unit=unit,
            context=context or {},
            tags=tags or []
        )
        
        # Store metric
        self.metrics.append(metric)
        self.metric_history[f"{category.value}_{name}"].append(metric)
        
        # Check for alerts
        await self._check_metric_alerts(metric)
        
        # Update baseline if needed
        await self._update_baseline_metrics(metric)
    
    def _initialize_performance_thresholds(self) -> Dict[str, Dict[str, float]]:
        """Initialize performance thresholds for alerting"""
        
        return {
            'quality_overall_score': {
                'critical': 0.60,
                'warning': 0.70,
                'target': 0.80
            },
            'cost_total_cost': {
                'critical': 0.30,
                'warning': 0.25,
                'target': 0.15
            },
            'performance_processing_time': {
                'critical': 300,  # 5 minutes
                'warning': 180,   # 3 minutes
                'target': 120     # 2 minutes
            },
            'reliability_success_rate': {
                'critical': 0.85,
                'warning': 0.90,
                'target': 0.95
            },
            'efficiency_cost_per_quality': {
                'critical': 0.40,
                'warning': 0.30,
                'target': 0.20
            }
        }
    
    async def _check_metric_alerts(self, metric: PerformanceMetric) -> None:
        """Check if metric triggers any alerts"""
        
        threshold_key = f"{metric.category.value}_{metric.name}"
        thresholds = self.thresholds.get(threshold_key, {})
        
        if not thresholds:
            return
        
        # Check critical threshold
        if 'critical' in thresholds:
            critical_threshold = thresholds['critical']
            is_critical = False
            
            # Different logic for different metrics
            if metric.name in ['overall_score', 'success_rate']:
                # Higher is better
                is_critical = metric.value < critical_threshold
            else:
                # Lower is better (cost, time, etc.)
                is_critical = metric.value > critical_threshold
            
            if is_critical:
                await self._create_alert(
                    severity=AlertSeverity.CRITICAL,
                    category=metric.category,
                    title=f"Critical {metric.name} threshold exceeded",
                    description=f"{metric.name} is {metric.value:.3f} {metric.unit}, exceeding critical threshold of {critical_threshold}",
                    current_value=metric.value,
                    threshold_value=critical_threshold,
                    metric_context=metric.context
                )
        
        # Check warning threshold
        if 'warning' in thresholds:
            warning_threshold = thresholds['warning']
            is_warning = False
            
            if metric.name in ['overall_score', 'success_rate']:
                is_warning = metric.value < warning_threshold
            else:
                is_warning = metric.value > warning_threshold
            
            if is_warning and threshold_key not in self.active_alerts:
                await self._create_alert(
                    severity=AlertSeverity.WARNING,
                    category=metric.category,
                    title=f"{metric.name} approaching threshold",
                    description=f"{metric.name} is {metric.value:.3f} {metric.unit}, approaching threshold of {warning_threshold}",
                    current_value=metric.value,
                    threshold_value=warning_threshold,
                    metric_context=metric.context
                )
    
    async def _create_alert(self, severity: AlertSeverity, category: MetricCategory,
                          title: str, description: str, current_value: float,
                          threshold_value: float, metric_context: Dict[str, Any]) -> None:
        """Create a performance alert"""
        
        alert_id = f"alert_{severity.value}_{category.value}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        # Generate recommended actions
        recommended_actions = self._generate_alert_actions(category, severity, current_value, threshold_value)
        
        # Identify affected components
        affected_components = self._identify_affected_components(category, metric_context)
        
        alert = PerformanceAlert(
            alert_id=alert_id,
            timestamp=datetime.utcnow(),
            severity=severity,
            category=category,
            title=title,
            description=description,
            current_value=current_value,
            threshold_value=threshold_value,
            affected_components=affected_components,
            recommended_actions=recommended_actions,
            auto_resolvable=self._is_auto_resolvable(category, severity)
        )
        
        self.active_alerts[alert_id] = alert
        self.alert_history.append(alert)
        
        logger.warning(f"🚨 {severity.value.upper()} Alert: {title}")
    
    def _generate_alert_actions(self, category: MetricCategory, severity: AlertSeverity,
                              current_value: float, threshold_value: float) -> List[str]:
        """Generate recommended actions for alerts"""
        
        actions = []
        
        if category == MetricCategory.QUALITY:
            if severity == AlertSeverity.CRITICAL:
                actions = [
                    "Immediately review quality control parameters",
                    "Enable premium processing for next 10 bills",
                    "Check evidence validation thresholds",
                    "Review AI model performance"
                ]
            else:
                actions = [
                    "Monitor quality trends closely",
                    "Consider adjusting evidence requirements",
                    "Review recent processing configurations"
                ]
        
        elif category == MetricCategory.COST:
            actions = [
                "Review cost optimization settings",
                "Check for runaway processing costs",
                "Consider switching to cost-efficient strategy",
                "Analyze cost breakdown by component"
            ]
        
        elif category == MetricCategory.PERFORMANCE:
            actions = [
                "Check system resource utilization",
                "Review processing pipeline bottlenecks",
                "Consider scaling resources",
                "Analyze processing time distribution"
            ]
        
        elif category == MetricCategory.RELIABILITY:
            actions = [
                "Check system error logs",
                "Review API connection status",
                "Verify database connectivity",
                "Check external service availability"
            ]
        
        return actions
    
    def _identify_affected_components(self, category: MetricCategory, 
                                    context: Dict[str, Any]) -> List[str]:
        """Identify system components affected by the alert"""
        
        components = []
        
        if category == MetricCategory.QUALITY:
            components = ['evidence_processor', 'quality_feedback', 'analysis_generator']
        elif category == MetricCategory.COST:
            components = ['cost_optimizer', 'budget_allocator']
        elif category == MetricCategory.PERFORMANCE:
            components = ['processing_pipeline', 'ai_service']
        elif category == MetricCategory.RELIABILITY:
            components = ['database', 'external_apis', 'file_system']
        
        # Add context-specific components
        if 'component' in context:
            components.append(context['component'])
        
        return components
    
    def _is_auto_resolvable(self, category: MetricCategory, severity: AlertSeverity) -> bool:
        """Determine if alert can be automatically resolved"""
        
        # Only low-severity performance alerts are auto-resolvable
        return (category in [MetricCategory.PERFORMANCE, MetricCategory.EFFICIENCY] and 
                severity in [AlertSeverity.INFO, AlertSeverity.WARNING])
    
    async def _update_baseline_metrics(self, metric: PerformanceMetric) -> None:
        """Update baseline metrics for comparison"""
        
        metric_key = f"{metric.category.value}_{metric.name}"
        
        if metric_key not in self.baseline_metrics:
            self.baseline_metrics[metric_key] = {
                'values': deque(maxlen=100),
                'baseline': metric.value
            }
        
        baseline_data = self.baseline_metrics[metric_key]
        baseline_data['values'].append(metric.value)
        
        # Update baseline as rolling average
        if len(baseline_data['values']) >= 10:
            baseline_data['baseline'] = sum(baseline_data['values']) / len(baseline_data['values'])
    
    async def get_system_health(self) -> SystemHealth:
        """Get current system health status"""
        
        # Calculate component scores
        component_scores = {}
        for component, status_info in self.system_components.items():
            if status_info['status'] == 'healthy':
                component_scores[component] = 100.0
            elif status_info['status'] == 'warning':
                component_scores[component] = 75.0
            elif status_info['status'] == 'critical':
                component_scores[component] = 25.0
            else:  # unknown/error
                component_scores[component] = 0.0
        
        # Calculate overall score
        overall_score = sum(component_scores.values()) / len(component_scores)
        
        # Count alerts
        active_alerts_count = len(self.active_alerts)
        critical_issues = sum(1 for alert in self.active_alerts.values() 
                            if alert.severity in [AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY])
        
        # Calculate uptime (simplified)
        uptime_percentage = max(95.0, 100.0 - (critical_issues * 2))
        
        # Count recent bills processed
        last_24h = datetime.utcnow() - timedelta(hours=24)
        recent_metrics = [m for m in self.metrics if m.timestamp > last_24h]
        bills_processed_24h = len([m for m in recent_metrics if m.name == 'bills_processed'])
        
        # Generate performance summary
        if overall_score >= 95:
            performance_summary = "All systems operating normally"
        elif overall_score >= 85:
            performance_summary = "Minor performance issues detected"
        elif overall_score >= 70:
            performance_summary = "Performance degradation in some components"
        else:
            performance_summary = "Major system issues requiring attention"
        
        return SystemHealth(
            timestamp=datetime.utcnow(),
            overall_score=overall_score,
            component_scores=component_scores,
            active_alerts=active_alerts_count,
            critical_issues=critical_issues,
            performance_summary=performance_summary,
            uptime_percentage=uptime_percentage,
            last_24h_bills_processed=bills_processed_24h
        )
    
    async def get_analytics_summary(self, time_range: TimeRange) -> AnalyticsSummary:
        """Get analytics summary for specified time range"""
        
        # Calculate time cutoff
        now = datetime.utcnow()
        if time_range == TimeRange.LAST_HOUR:
            cutoff = now - timedelta(hours=1)
        elif time_range == TimeRange.LAST_24H:
            cutoff = now - timedelta(hours=24)
        elif time_range == TimeRange.LAST_7D:
            cutoff = now - timedelta(days=7)
        elif time_range == TimeRange.LAST_30D:
            cutoff = now - timedelta(days=30)
        else:  # LAST_90D
            cutoff = now - timedelta(days=90)
        
        # Filter metrics by time range
        relevant_metrics = [m for m in self.metrics if m.timestamp > cutoff]
        
        # Calculate summary statistics
        bills_processed = len([m for m in relevant_metrics if m.name == 'bills_processed'])
        
        quality_metrics = [m for m in relevant_metrics if m.category == MetricCategory.QUALITY and m.name == 'overall_score']
        average_quality = sum(m.value for m in quality_metrics) / len(quality_metrics) if quality_metrics else 0.0
        
        cost_metrics = [m for m in relevant_metrics if m.category == MetricCategory.COST and m.name == 'total_cost']
        average_cost = sum(m.value for m in cost_metrics) / len(cost_metrics) if cost_metrics else 0.0
        
        time_metrics = [m for m in relevant_metrics if m.name == 'processing_time']
        average_processing_time = sum(m.value for m in time_metrics) / len(time_metrics) if time_metrics else 0.0
        
        success_metrics = [m for m in relevant_metrics if m.name == 'success_rate']
        success_rate = sum(m.value for m in success_metrics) / len(success_metrics) if success_metrics else 1.0
        
        # Calculate derived metrics
        cost_efficiency = (average_quality / average_cost) if average_cost > 0 else 0.0
        
        time_delta_hours = (now - cutoff).total_seconds() / 3600
        throughput = bills_processed / time_delta_hours if time_delta_hours > 0 else 0.0
        
        error_metrics = [m for m in relevant_metrics if m.name == 'error_rate']
        error_rate = sum(m.value for m in error_metrics) / len(error_metrics) if error_metrics else 0.0
        
        return AnalyticsSummary(
            time_range=time_range,
            bills_processed=bills_processed,
            average_quality=average_quality,
            average_cost=average_cost,
            average_processing_time=average_processing_time,
            success_rate=success_rate,
            cost_efficiency=cost_efficiency,
            throughput=throughput,
            error_rate=error_rate
        )
    
    async def get_trend_analysis(self, metric_name: str, time_range: TimeRange) -> TrendAnalysis:
        """Get trend analysis for a specific metric"""
        
        # Get time cutoff
        now = datetime.utcnow()
        if time_range == TimeRange.LAST_24H:
            cutoff = now - timedelta(hours=24)
            comparison_cutoff = cutoff - timedelta(hours=24)  # Previous 24h
        elif time_range == TimeRange.LAST_7D:
            cutoff = now - timedelta(days=7)
            comparison_cutoff = cutoff - timedelta(days=7)  # Previous 7d
        else:  # Default to 30d
            cutoff = now - timedelta(days=30)
            comparison_cutoff = cutoff - timedelta(days=30)  # Previous 30d
        
        # Get current period metrics
        current_metrics = [m for m in self.metrics 
                         if m.timestamp > cutoff and metric_name in f"{m.category.value}_{m.name}"]
        
        # Get previous period metrics
        previous_metrics = [m for m in self.metrics 
                          if comparison_cutoff < m.timestamp <= cutoff and metric_name in f"{m.category.value}_{m.name}"]
        
        # Calculate averages
        current_value = sum(m.value for m in current_metrics) / len(current_metrics) if current_metrics else 0.0
        previous_value = sum(m.value for m in previous_metrics) / len(previous_metrics) if previous_metrics else 0.0
        
        # Calculate change
        if previous_value > 0:
            change_percentage = ((current_value - previous_value) / previous_value) * 100
        else:
            change_percentage = 0.0
        
        # Determine trend direction
        if abs(change_percentage) < 2:  # Less than 2% change
            trend_direction = "stable"
        elif change_percentage > 0:
            trend_direction = "up"
        else:
            trend_direction = "down"
        
        # Calculate confidence based on data points
        total_data_points = len(current_metrics) + len(previous_metrics)
        confidence_level = min(1.0, total_data_points / 50)  # Max confidence at 50 data points
        
        return TrendAnalysis(
            metric_name=metric_name,
            time_range=time_range,
            current_value=current_value,
            previous_value=previous_value,
            change_percentage=change_percentage,
            trend_direction=trend_direction,
            confidence_level=confidence_level,
            data_points=total_data_points
        )
    
    async def get_dashboard_data(self, force_refresh: bool = False) -> DashboardData:
        """Get complete dashboard data with caching"""
        
        # Check cache
        if not force_refresh and self.dashboard_cache['data'] is not None:
            cache_age = (datetime.utcnow() - self.dashboard_cache['last_updated']).seconds
            if cache_age < self.dashboard_cache['cache_duration']:
                return self.dashboard_cache['data']
        
        # Generate fresh dashboard data
        system_health = await self.get_system_health()
        
        # Get analytics for different time ranges
        analytics_summary = {}
        for time_range in TimeRange:
            analytics_summary[time_range] = await self.get_analytics_summary(time_range)
        
        # Get trend analysis for key metrics
        key_metrics = ['quality_overall_score', 'cost_total_cost', 'performance_processing_time']
        trend_analysis = {}
        for metric_name in key_metrics:
            trend_analysis[metric_name] = await self.get_trend_analysis(metric_name, TimeRange.LAST_7D)
        
        # Get top metrics by category
        top_metrics = {}
        for category in MetricCategory:
            category_metrics = [m for m in list(self.metrics)[-1000:] 
                              if m.category == category]
            # Get most recent unique metrics
            unique_metrics = {}
            for metric in reversed(category_metrics):
                if metric.name not in unique_metrics:
                    unique_metrics[metric.name] = metric
                if len(unique_metrics) >= 5:
                    break
            top_metrics[category] = list(unique_metrics.values())
        
        # Calculate cost breakdown
        recent_cost_metrics = [m for m in self.metrics 
                             if m.category == MetricCategory.COST and 
                             m.timestamp > datetime.utcnow() - timedelta(hours=24)]
        
        cost_breakdown = defaultdict(float)
        for metric in recent_cost_metrics:
            cost_breakdown[metric.name] += metric.value
        
        # Calculate quality distribution
        recent_quality_metrics = [m for m in self.metrics 
                                if m.category == MetricCategory.QUALITY and m.name == 'overall_score' and
                                m.timestamp > datetime.utcnow() - timedelta(hours=24)]
        
        quality_distribution = {
            'excellent (>0.9)': len([m for m in recent_quality_metrics if m.value > 0.9]),
            'good (0.8-0.9)': len([m for m in recent_quality_metrics if 0.8 <= m.value <= 0.9]),
            'acceptable (0.7-0.8)': len([m for m in recent_quality_metrics if 0.7 <= m.value < 0.8]),
            'poor (<0.7)': len([m for m in recent_quality_metrics if m.value < 0.7])
        }
        
        # Get processing pipeline status
        processing_pipeline_status = {
            'evidence_extraction': 'operational',
            'quality_validation': 'operational',
            'cost_optimization': 'operational',
            'analysis_generation': 'operational',
            'feedback_loop': 'operational'
        }
        
        dashboard_data = DashboardData(
            timestamp=datetime.utcnow(),
            system_health=system_health,
            active_alerts=list(self.active_alerts.values())[-10:],  # Last 10 alerts
            analytics_summary=analytics_summary,
            trend_analysis=trend_analysis,
            top_metrics=top_metrics,
            cost_breakdown=dict(cost_breakdown),
            quality_distribution=quality_distribution,
            processing_pipeline_status=processing_pipeline_status
        )
        
        # Cache the data
        self.dashboard_cache['data'] = dashboard_data
        self.dashboard_cache['last_updated'] = datetime.utcnow()
        
        return dashboard_data
    
    async def update_component_status(self, component: str, status: str) -> None:
        """Update the status of a system component"""
        
        if component in self.system_components:
            self.system_components[component]['status'] = status
            self.system_components[component]['last_check'] = datetime.utcnow()
            logger.info(f"Component {component} status updated to: {status}")
    
    async def record_bill_processing_metrics(self, bill_id: str, 
                                           processing_result: Dict[str, Any]) -> None:
        """Record comprehensive metrics from bill processing result"""
        
        timestamp = datetime.utcnow()
        
        # Quality metrics
        quality_metrics = processing_result.get('quality_metrics', {})
        if 'overall_score' in quality_metrics:
            await self.record_metric(
                MetricCategory.QUALITY, 'overall_score',
                quality_metrics['overall_score'], 'score',
                context={'bill_id': bill_id, 'component': 'quality_system'}
            )
        
        # Cost metrics
        cost_breakdown = processing_result.get('cost_breakdown', {})
        if 'total_cost' in cost_breakdown:
            await self.record_metric(
                MetricCategory.COST, 'total_cost',
                cost_breakdown['total_cost'], 'dollars',
                context={'bill_id': bill_id, 'component': 'cost_system'}
            )
        
        # Performance metrics
        processing_time = processing_result.get('processing_time', 0)
        if processing_time > 0:
            await self.record_metric(
                MetricCategory.PERFORMANCE, 'processing_time',
                processing_time, 'seconds',
                context={'bill_id': bill_id, 'component': 'processing_pipeline'}
            )
        
        # Success/reliability metrics
        success = processing_result.get('success', False)
        await self.record_metric(
            MetricCategory.RELIABILITY, 'success_rate',
            1.0 if success else 0.0, 'rate',
            context={'bill_id': bill_id, 'component': 'system_reliability'}
        )
        
        # Efficiency metrics
        if 'overall_score' in quality_metrics and 'total_cost' in cost_breakdown:
            quality = quality_metrics['overall_score']
            cost = cost_breakdown['total_cost']
            if cost > 0:
                efficiency = quality / cost
                await self.record_metric(
                    MetricCategory.EFFICIENCY, 'cost_per_quality',
                    cost / quality, 'cost_per_point',
                    context={'bill_id': bill_id, 'component': 'efficiency_system'}
                )
        
        # Bills processed counter
        await self.record_metric(
            MetricCategory.PERFORMANCE, 'bills_processed',
            1.0, 'count',
            context={'bill_id': bill_id}
        )
    
    def get_metrics_export(self, time_range: TimeRange, 
                          format: str = 'json') -> Dict[str, Any]:
        """Export metrics data for external analysis"""
        
        # Get time cutoff
        now = datetime.utcnow()
        if time_range == TimeRange.LAST_HOUR:
            cutoff = now - timedelta(hours=1)
        elif time_range == TimeRange.LAST_24H:
            cutoff = now - timedelta(hours=24)
        elif time_range == TimeRange.LAST_7D:
            cutoff = now - timedelta(days=7)
        elif time_range == TimeRange.LAST_30D:
            cutoff = now - timedelta(days=30)
        else:  # LAST_90D
            cutoff = now - timedelta(days=90)
        
        # Filter metrics
        relevant_metrics = [m for m in self.metrics if m.timestamp > cutoff]
        
        # Convert to exportable format
        export_data = {
            'export_timestamp': now.isoformat(),
            'time_range': time_range.value,
            'total_metrics': len(relevant_metrics),
            'metrics': []
        }
        
        for metric in relevant_metrics:
            export_data['metrics'].append({
                'timestamp': metric.timestamp.isoformat(),
                'category': metric.category.value,
                'name': metric.name,
                'value': metric.value,
                'unit': metric.unit,
                'context': metric.context,
                'tags': metric.tags
            })
        
        return export_data

# Global instance
performance_dashboard = None

def get_performance_dashboard(db: Session) -> PerformanceAnalyticsDashboard:
    """Get or create the global performance analytics dashboard instance"""
    global performance_dashboard
    if performance_dashboard is None:
        performance_dashboard = PerformanceAnalyticsDashboard(db)
    return performance_dashboard