#!/usr/bin/env python3
"""
Unit tests for TextCitationService - citation validation and text indexing.

Tests the core functionality for:
- Building source index from bill text
- Validating exact quotes against bill text
- Binding citations with offsets and section metadata
"""

import pytest
from app.services.text_citation_service import TextCitationService, SourceIndexItem, CitationBinding


class TestTextCitationService:
    
    def setup_method(self):
        """Set up test instance."""
        self.service = TextCitationService()
        
        # Sample bill text with sections (matching the actual regex pattern)
        self.sample_bill_text = """TITLE I - HEALTHCARE ACCESS

SEC. 101. MEDICAID EXPANSION

This section expands Medicaid eligibility to all individuals with incomes up to 138% of the federal poverty level. The expansion will provide healthcare coverage to approximately 12 million previously uninsured Americans.

SEC. 102. SUBSIDIES FOR HEALTH INSURANCE

The Secretary shall provide premium subsidies for individuals purchasing health insurance through state exchanges. Subsidies will be available for households with incomes between 138% and 400% of the federal poverty level.

TITLE II - IMPLEMENTATION

Section 201. EFFECTIVE DATES

The provisions of this Act shall take effect on January 1, 2025, except as otherwise provided."""
    
    def test_build_source_index_with_sections(self):
        """Test building source index with SEC. and Section headings."""
        index = self.service.build_source_index(self.sample_bill_text)
        
        assert len(index) == 5  # TITLE I, SEC. 101, SEC. 102, TITLE II, Section 201
        
        # Check first section (regex only captures "TITLE I" not the full line)
        assert index[0].heading == "TITLE I"
        assert index[0].start_offset == 0
        assert index[0].anchor_id == "sec-1"
        
        # Check SEC. 101
        sec_101 = next((item for item in index if "SEC. 101" in item.heading), None)
        assert sec_101 is not None
        assert sec_101.heading == "SEC. 101. MEDICAID EXPANSION"
        assert sec_101.anchor_id.startswith("sec-")
        
        # Check that offsets are sequential and non-overlapping
        for i in range(len(index) - 1):
            assert index[i].end_offset <= index[i + 1].start_offset
    
    def test_build_source_index_no_sections(self):
        """Test building source index with text that has no section headings."""
        simple_text = "This is a simple bill with no sections. It just has plain text content."
        index = self.service.build_source_index(simple_text)
        
        assert len(index) == 1
        assert index[0].heading is None
        assert index[0].start_offset == 0
        assert index[0].end_offset == len(simple_text)
        assert index[0].anchor_id == "sec-0"
    
    def test_bind_quote_exact_match(self):
        """Test binding a quote that exists exactly in the text."""
        index = self.service.build_source_index(self.sample_bill_text)
        
        quote = "expands Medicaid eligibility to all individuals"
        binding = self.service.bind_quote(self.sample_bill_text, index, quote)
        
        assert binding is not None
        assert binding.quote == quote
        assert binding.start_offset > 0
        assert binding.end_offset > binding.start_offset
        
        # Check that it found the correct section
        assert binding.heading == "SEC. 101. MEDICAID EXPANSION"
        assert binding.anchor_id is not None
        
        # Verify the quote actually exists at the specified offset
        extracted = self.sample_bill_text[binding.start_offset:binding.end_offset]
        assert quote in extracted
    
    def test_bind_quote_with_whitespace_normalization(self):
        """Test binding quotes with different whitespace that should still match."""
        index = self.service.build_source_index(self.sample_bill_text)
        
        # Quote with extra/different whitespace
        quote = "The  Secretary   shall provide premium subsidies"
        binding = self.service.bind_quote(self.sample_bill_text, index, quote)
        
        assert binding is not None
        assert "Secretary shall provide premium subsidies" in self.sample_bill_text
        assert binding.heading == "SEC. 102. SUBSIDIES FOR HEALTH INSURANCE"
    
    def test_bind_quote_not_found(self):
        """Test binding a quote that doesn't exist in the text."""
        index = self.service.build_source_index(self.sample_bill_text)
        
        fake_quote = "This quote does not exist in the bill text at all"
        binding = self.service.bind_quote(self.sample_bill_text, index, fake_quote)
        
        assert binding is None
    
    def test_bind_quote_empty_inputs(self):
        """Test binding with empty or None inputs."""
        index = self.service.build_source_index(self.sample_bill_text)
        
        # Empty quote
        binding = self.service.bind_quote(self.sample_bill_text, index, "")
        assert binding is None
        
        # None quote
        binding = self.service.bind_quote(self.sample_bill_text, index, None)
        assert binding is None
        
        # Empty text
        binding = self.service.bind_quote("", index, "some quote")
        assert binding is None
    
    def test_bind_citations_bulk(self):
        """Test binding multiple quotes in bulk."""
        index = self.service.build_source_index(self.sample_bill_text)
        
        quotes = [
            "expands Medicaid eligibility",
            "premium subsidies for individuals",
            "January 1, 2025",
            "This quote does not exist"  # Should not bind
        ]
        
        bindings = self.service.bind_citations_bulk(self.sample_bill_text, index, quotes)
        
        # Should get 3 bindings (4 quotes - 1 invalid)
        assert len(bindings) == 3
        
        # All bindings should have valid offsets and sections
        for binding in bindings:
            assert binding.start_offset >= 0
            assert binding.end_offset > binding.start_offset
            assert binding.heading is not None
            assert binding.anchor_id is not None
    
    def test_quote_validation_with_exact_text_extraction(self):
        """Test that quotes can be exactly extracted from the specified offsets."""
        index = self.service.build_source_index(self.sample_bill_text)
        
        test_quotes = [
            "138% of the federal poverty level",
            "healthcare coverage to approximately 12 million",
            "take effect on January 1, 2025"
        ]
        
        for quote in test_quotes:
            binding = self.service.bind_quote(self.sample_bill_text, index, quote)
            assert binding is not None, f"Failed to bind quote: {quote}"
            
            # Extract text at the binding offsets
            extracted = self.sample_bill_text[binding.start_offset:binding.end_offset]
            
            # The extracted text should contain the quote (may not be exact due to whitespace normalization)
            assert quote.lower() in extracted.lower(), f"Quote '{quote}' not found in extracted text: '{extracted}'"
    
    def test_section_boundary_detection(self):
        """Test that quotes are correctly attributed to their sections."""
        index = self.service.build_source_index(self.sample_bill_text)
        
        # Quote from SEC. 101
        medicaid_quote = "expands Medicaid eligibility"
        binding1 = self.service.bind_quote(self.sample_bill_text, index, medicaid_quote)
        assert "SEC. 101" in binding1.heading
        
        # Quote from SEC. 102  
        subsidy_quote = "premium subsidies for individuals"
        binding2 = self.service.bind_quote(self.sample_bill_text, index, subsidy_quote)
        assert "SEC. 102" in binding2.heading
        
        # Quote from Section 201
        effective_quote = "January 1, 2025"
        binding3 = self.service.bind_quote(self.sample_bill_text, index, effective_quote)
        assert "Section 201" in binding3.heading
        
        # All should have different section IDs
        section_ids = {binding1.anchor_id, binding2.anchor_id, binding3.anchor_id}
        assert len(section_ids) == 3
    
    def test_custom_heading_pattern(self):
        """Test using a custom regex pattern for section headings."""
        custom_text = """Article I: General Provisions

This is the content of Article I with some important text.

Article II: Specific Rules

This is the content of Article II with different important text."""
        
        # Custom service with different heading pattern
        custom_service = TextCitationService(heading_pattern=r"^Article\s+[IVX]+:")
        index = custom_service.build_source_index(custom_text)
        
        assert len(index) == 2
        assert "Article I:" in index[0].heading
        assert "Article II:" in index[1].heading
        
        # Test quote binding with custom pattern
        quote = "important text"
        binding = custom_service.bind_quote(custom_text, index, quote)
        assert binding is not None
        # Should match first occurrence in Article I
        assert "Article I:" in binding.heading


if __name__ == "__main__":
    pytest.main([__file__, "-v"])