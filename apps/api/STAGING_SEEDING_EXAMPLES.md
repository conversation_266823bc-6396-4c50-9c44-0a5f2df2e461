# Staging Database Seeding Examples

## Quick Start Guide

### 1. Preview What Will Be Seeded (Dry Run)
```bash
# See validation results without making any database changes
python seed_staging_with_validation.py --dry-run
```

### 2. Run Validation Only
```bash
# Generate a detailed validation report
python seed_staging_with_validation.py --validate-only --report-file validation_report_$(date +%Y%m%d).json
```

### 3. Seed Database with Verified Data
```bash
# Only seed officials that pass validation (requires 80%+ verification rate)
python seed_staging_with_validation.py --seed
```

## Example Validation Report

```json
{
  "timestamp": "2025-08-07T10:30:00Z",
  "summary": {
    "total_officials": 25,
    "verification_summary": {
      "verified": 20,
      "flagged": 3,
      "manual_review": 1,
      "failed": 1
    },
    "percentages": {
      "verified": 80.0,
      "flagged": 12.0,
      "manual_review": 4.0,
      "failed": 4.0
    },
    "average_confidence_score": 87.5,
    "common_issues": [
      ["Non-government email domain", 3],
      ["Website not accessible", 2],
      ["Invalid social media URL format", 2]
    ],
    "recommendation": "Dataset ready for staging deployment with monitoring"
  }
}
```

## Manual Verification Workflow

When officials require manual review:

### 1. Identify Issues
```bash
# Review the validation report for flagged issues
cat validation_report_20250807.json | jq '.detailed_reports[] | select(.result == "manual_review")'
```

### 2. Research Correct Information
- Check official government websites (.gov domains)
- Verify recent role transitions (House → Senate)
- Confirm current social media accounts

### 3. Update Data Sources
- Fix incorrect information in the seeding script
- Update social media links with current accounts
- Verify government email domains

### 4. Re-run Validation
```bash
# Validate again after fixes
python seed_staging_with_validation.py --validate-only
```

## Quality Gates

### For Staging Deployment
- ✅ Minimum 80% verification rate
- ✅ Zero critical validation failures
- ✅ Government domains verified for official contacts

### For Production Deployment  
- ✅ Minimum 95% verification rate
- ✅ All flagged items manually reviewed
- ✅ Recent political transitions verified

## Common Validation Issues and Solutions

### Issue: "Non-government email domain"
```
Problem: Official has gmail.com or other non-gov email
Solution: Research official .gov email or remove email field
```

### Issue: "Website not accessible (HTTP 403)"
```
Problem: Website returns 403 Forbidden
Solution: Check if URL is correct, may need updated link
```

### Issue: "Invalid social media URL format"
```
Problem: Social media URL doesn't match platform patterns
Solution: Verify correct account URL format for platform
```

### Issue: "Questionable title"
```
Problem: Title doesn't match standard government positions
Solution: Verify current official title from government sources
```

## Data Source Priority Order

1. **congress.gov API** - Most authoritative for federal officials
2. **senate.gov/house.gov** - Official chamber websites  
3. **Individual official .gov sites** - Personal government sites
4. **OpenStates API** - State and federal data
5. **Social media platforms** - Direct verification

## Best Practices

### Before Seeding
- Always run `--dry-run` first to preview changes
- Review validation reports for any concerning patterns
- Manually verify any officials flagged for review
- Ensure data sources are current (post-election updates)

### During Seeding
- Monitor for any database errors during the process
- Keep validation reports for audit trail
- Note any manual corrections made

### After Seeding
- Spot-check random officials in the staging database
- Test frontend display of social media links
- Verify that government domains are working
- Document any issues found for next seeding cycle

## Troubleshooting

### Low Verification Rates
```
If verification rate < 80%:
1. Check data source APIs are accessible
2. Review common validation issues
3. Update social media URL patterns if needed
4. Consider expanding government domain whitelist
```

### Manual Review Items
```
When officials need manual review:
1. Research official government sources
2. Check for recent role transitions
3. Verify social media accounts are current
4. Update data and re-validate
```

### Database Errors
```
If seeding fails:
1. Check database connection
2. Verify data model compatibility
3. Review any constraint violations
4. Check for duplicate officials
```