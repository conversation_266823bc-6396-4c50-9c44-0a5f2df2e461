#!/usr/bin/env python3
"""
Administrative Backfill Script for Officials Data

This script populates the officials database with all current federal legislators
by querying the OpenStates API for each US state and creating/updating official records.

Usage:
    python backfill_officials.py [--dry-run] [--state STATE] [--limit N]

Options:
    --dry-run    : Show what would be done without making changes
    --state STATE: Only process a specific state (e.g., --state CA)
    --limit N    : Limit the number of officials to process (for testing)

Example:
    python backfill_officials.py                    # Backfill all states
    python backfill_officials.py --state CA         # Only California
    python backfill_officials.py --dry-run          # See what would happen
"""

import asyncio
import sys
import argparse
import logging
from typing import List
from datetime import datetime

# Add the app directory to Python path so we can import our modules
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.core.config import get_settings
from app.db.database import SessionLocal
from app.services.officials import OfficialService
from app.services.openstates_officials_api import get_openstates_officials_client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# US States and territories for federal legislature
US_STATES = [
    "AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA",
    "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD",
    "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ",
    "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC",
    "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY",
    "DC"  # Washington D.C.
]

class OfficialsBackfillService:
    """Service for backfilling officials data from OpenStates API"""
    
    def __init__(self, dry_run: bool = False):
        self.dry_run = dry_run
        self.db = SessionLocal()
        self.official_service = OfficialService(self.db)
        self.openstates_client = get_openstates_officials_client()
        
        if not self.openstates_client.enabled:
            raise RuntimeError("OpenStates API client is not enabled. Please configure OPENSTATES_API_KEY.")
        
        self.stats = {
            'states_processed': 0,
            'officials_created': 0,
            'officials_updated': 0,
            'officials_skipped': 0,
            'errors': 0,
            'start_time': datetime.now()
        }
    
    def __del__(self):
        """Clean up database connection"""
        if hasattr(self, 'db'):
            self.db.close()
    
    async def backfill_all_states(self, target_states: List[str] = None, limit: int = None):
        """
        Backfill officials for all US states.
        
        Args:
            target_states: Optional list of specific states to process
            limit: Optional limit on total officials to process
        """
        states_to_process = target_states if target_states else US_STATES
        
        logger.info(f"Starting backfill for {len(states_to_process)} states...")
        logger.info(f"Mode: {'DRY RUN' if self.dry_run else 'LIVE'}")
        
        total_processed = 0
        
        for state in states_to_process:
            if limit and total_processed >= limit:
                logger.info(f"Reached limit of {limit} officials, stopping.")
                break
                
            logger.info(f"\n{'='*60}")
            logger.info(f"Processing state: {state}")
            logger.info(f"{'='*60}")
            
            try:
                state_count = await self.backfill_state(state, limit - total_processed if limit else None)
                total_processed += state_count
                self.stats['states_processed'] += 1
                
                logger.info(f"Completed {state}: {state_count} officials processed")
                
            except Exception as e:
                logger.error(f"Error processing state {state}: {e}")
                self.stats['errors'] += 1
                continue
        
        self._print_summary()
    
    async def backfill_state(self, state: str, limit: int = None) -> int:
        """
        Backfill officials for a single state.
        
        Args:
            state: State abbreviation (e.g., 'CA', 'NY')
            limit: Optional limit on officials to process for this state
            
        Returns:
            Number of officials processed for this state
        """
        # Use a major city's ZIP code for each state to get federal representatives
        state_zip_codes = {
            'AL': '35203', 'AK': '99501', 'AZ': '85001', 'AR': '72201', 'CA': '90210',
            'CO': '80202', 'CT': '06103', 'DE': '19901', 'FL': '33101', 'GA': '30303',
            'HI': '96813', 'ID': '83702', 'IL': '60601', 'IN': '46204', 'IA': '50309',
            'KS': '66603', 'KY': '40202', 'LA': '70112', 'ME': '04101', 'MD': '21201',
            'MA': '02108', 'MI': '48226', 'MN': '55401', 'MS': '39201', 'MO': '63101',
            'MT': '59601', 'NE': '68508', 'NV': '89101', 'NH': '03301', 'NJ': '07102',
            'NM': '87101', 'NY': '10001', 'NC': '27601', 'ND': '58501', 'OH': '43215',
            'OK': '73102', 'OR': '97201', 'PA': '19103', 'RI': '02903', 'SC': '29201',
            'SD': '57501', 'TN': '37201', 'TX': '75201', 'UT': '84101', 'VT': '05601',
            'VA': '23219', 'WA': '98101', 'WV': '25301', 'WI': '53202', 'WY': '82001',
            'DC': '20001'
        }
        
        zip_code = state_zip_codes.get(state)
        if not zip_code:
            logger.warning(f"No ZIP code configured for state {state}, skipping")
            return 0
        
        try:
            # Get officials using the existing service
            logger.info(f"Fetching officials for {state} using ZIP code {zip_code}...")
            
            if self.dry_run:
                # In dry run mode, just fetch from API without saving
                officials = self.openstates_client.get_officials_by_zip(zip_code)
                logger.info(f"DRY RUN: Would process {len(officials)} officials for {state}")
                
                for official in officials:
                    logger.info(f"  - Would process: {official.name} ({official.title})")
                    
                return len(officials)
            else:
                # Use the service's get_officials_by_zip_code method which handles caching/creation
                officials = self.official_service.get_officials_by_zip_code(zip_code)
                
                processed_count = 0
                for official in officials:
                    if limit and processed_count >= limit:
                        break
                    
                    logger.info(f"  ✓ Processed: {official.name} ({official.title})")
                    
                    # Check if this was newly created or updated
                    if hasattr(official, 'created_at') and hasattr(official, 'updated_at'):
                        if (official.updated_at - official.created_at).total_seconds() < 60:
                            self.stats['officials_created'] += 1
                        else:
                            self.stats['officials_updated'] += 1
                    else:
                        self.stats['officials_updated'] += 1
                    
                    processed_count += 1
                
                logger.info(f"Successfully processed {processed_count} officials for {state}")
                return processed_count
                
        except Exception as e:
            logger.error(f"Error fetching officials for state {state}: {e}")
            self.stats['errors'] += 1
            return 0
    
    def _print_summary(self):
        """Print a summary of the backfill operation"""
        end_time = datetime.now()
        duration = end_time - self.stats['start_time']
        
        print(f"\n{'='*60}")
        print("BACKFILL SUMMARY")
        print(f"{'='*60}")
        print(f"Mode: {'DRY RUN' if self.dry_run else 'LIVE'}")
        print(f"Duration: {duration}")
        print(f"States processed: {self.stats['states_processed']}")
        print(f"Officials created: {self.stats['officials_created']}")
        print(f"Officials updated: {self.stats['officials_updated']}")
        print(f"Officials skipped: {self.stats['officials_skipped']}")
        print(f"Errors: {self.stats['errors']}")
        print(f"Total officials: {self.stats['officials_created'] + self.stats['officials_updated']}")
        print(f"{'='*60}")

async def main():
    """Main entry point for the backfill script"""
    parser = argparse.ArgumentParser(
        description="Backfill officials database with federal legislators from OpenStates API"
    )
    parser.add_argument(
        '--dry-run', 
        action='store_true',
        help="Show what would be done without making changes"
    )
    parser.add_argument(
        '--state',
        type=str,
        help="Only process a specific state (e.g., CA, NY)"
    )
    parser.add_argument(
        '--limit',
        type=int,
        help="Limit the number of officials to process (for testing)"
    )
    parser.add_argument(
        '--verbose',
        action='store_true',
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate state argument
    target_states = None
    if args.state:
        state = args.state.upper()
        if state not in US_STATES:
            logger.error(f"Invalid state: {state}")
            logger.error(f"Valid states: {', '.join(US_STATES)}")
            sys.exit(1)
        target_states = [state]
    
    try:
        # Create and run the backfill service
        backfill_service = OfficialsBackfillService(dry_run=args.dry_run)
        await backfill_service.backfill_all_states(target_states, args.limit)
        
        logger.info("Backfill completed successfully!")
        
    except Exception as e:
        logger.error(f"Backfill failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())