-- Create Values Analysis Tables
-- Run this SQL to create the values analysis system tables

-- 1. Main bill values analysis table
CREATE TABLE IF NOT EXISTS bill_values_analysis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bill_id UUID NOT NULL,
    democracy_threat_score INTEGER DEFAULT 0 CHECK (democracy_threat_score >= 0 AND democracy_threat_score <= 10),
    democracy_support_score INTEGER DEFAULT 0 CHECK (democracy_support_score >= 0 AND democracy_support_score <= 10),
    human_rights_threat_score INTEGER DEFAULT 0 CHECK (human_rights_threat_score >= 0 AND human_rights_threat_score <= 10),
    human_rights_support_score INTEGER DEFAULT 0 CHECK (human_rights_support_score >= 0 AND human_rights_support_score <= 10),
    environmental_threat_score INTEGER DEFAULT 0 CHECK (environmental_threat_score >= 0 AND environmental_threat_score <= 10),
    environmental_support_score INTEGER DEFAULT 0 CHECK (environmental_support_score >= 0 AND environmental_support_score <= 10),
    overall_threat_level VARCHAR(20) DEFAULT 'NONE' CHECK (overall_threat_level IN ('NONE', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    overall_support_level VARCHAR(20) DEFAULT 'NONE' CHECK (overall_support_level IN ('NONE', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    analysis_reasoning JSONB,
    confidence_score DECIMAL(3,2) DEFAULT 0.00 CHECK (confidence_score >= 0.00 AND confidence_score <= 1.00),
    requires_human_review BOOLEAN DEFAULT FALSE,
    is_flagged BOOLEAN DEFAULT FALSE,
    analyzed_at TIMESTAMP DEFAULT NOW(),
    reviewed_by VARCHAR(255),
    reviewed_at TIMESTAMP,
    review_notes TEXT,
    ai_model_version VARCHAR(50) DEFAULT 'v1.0',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 2. Bill values tags table for user-friendly labels
CREATE TABLE IF NOT EXISTS bill_values_tags (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    bill_id UUID NOT NULL,
    analysis_id UUID NOT NULL REFERENCES bill_values_analysis(id) ON DELETE CASCADE,
    tag_category VARCHAR(50) NOT NULL, -- 'democracy', 'human_rights', 'environmental'
    tag_type VARCHAR(50) NOT NULL, -- 'support', 'threat', 'impact', 'neutral'
    tag_name VARCHAR(100) NOT NULL, -- 'voting_rights', 'climate_action', etc.
    display_text VARCHAR(200) NOT NULL, -- User-friendly text like "Supports Voting Rights"
    description TEXT, -- Detailed explanation
    severity_level INTEGER DEFAULT 1 CHECK (severity_level >= 1 AND severity_level <= 10),
    display_priority INTEGER DEFAULT 1 CHECK (display_priority >= 1 AND display_priority <= 10),
    color_theme VARCHAR(20) DEFAULT 'blue', -- UI color theme
    icon_name VARCHAR(50), -- Icon for UI display
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 3. Add missing simple_summary column to bills table
ALTER TABLE bills ADD COLUMN IF NOT EXISTS simple_summary TEXT;

-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_bill_values_analysis_bill_id ON bill_values_analysis(bill_id);
CREATE INDEX IF NOT EXISTS idx_bill_values_analysis_threat_level ON bill_values_analysis(overall_threat_level);
CREATE INDEX IF NOT EXISTS idx_bill_values_analysis_support_level ON bill_values_analysis(overall_support_level);
CREATE INDEX IF NOT EXISTS idx_bill_values_analysis_requires_review ON bill_values_analysis(requires_human_review) WHERE requires_human_review = TRUE;
CREATE INDEX IF NOT EXISTS idx_bill_values_analysis_analyzed_at ON bill_values_analysis(analyzed_at);

CREATE INDEX IF NOT EXISTS idx_bill_values_tags_bill_id ON bill_values_tags(bill_id);
CREATE INDEX IF NOT EXISTS idx_bill_values_tags_analysis_id ON bill_values_tags(analysis_id);
CREATE INDEX IF NOT EXISTS idx_bill_values_tags_category ON bill_values_tags(tag_category);
CREATE INDEX IF NOT EXISTS idx_bill_values_tags_active ON bill_values_tags(is_active) WHERE is_active = TRUE;

-- 5. Add comments for documentation
COMMENT ON TABLE bill_values_analysis IS 'AI-powered analysis of bills against core democratic values';
COMMENT ON TABLE bill_values_tags IS 'User-friendly tags and labels for bill values analysis';

COMMENT ON COLUMN bill_values_analysis.democracy_threat_score IS 'Threat level to democratic processes (0-10)';
COMMENT ON COLUMN bill_values_analysis.democracy_support_score IS 'Support level for democratic processes (0-10)';
COMMENT ON COLUMN bill_values_analysis.human_rights_threat_score IS 'Threat level to human rights (0-10)';
COMMENT ON COLUMN bill_values_analysis.human_rights_support_score IS 'Support level for human rights (0-10)';
COMMENT ON COLUMN bill_values_analysis.environmental_threat_score IS 'Threat level to environment (0-10)';
COMMENT ON COLUMN bill_values_analysis.environmental_support_score IS 'Support level for environment (0-10)';
COMMENT ON COLUMN bill_values_analysis.confidence_score IS 'AI confidence in analysis (0.00-1.00)';
COMMENT ON COLUMN bill_values_analysis.requires_human_review IS 'Flag indicating human expert review is needed';
COMMENT ON COLUMN bill_values_analysis.analysis_reasoning IS 'JSON containing AI analysis reasoning and details';

-- 6. Create trigger to auto-update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_bill_values_analysis_updated_at 
    BEFORE UPDATE ON bill_values_analysis 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bill_values_tags_updated_at 
    BEFORE UPDATE ON bill_values_tags 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();