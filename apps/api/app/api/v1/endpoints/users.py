# app/api/v1/endpoints/users.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional

from app.core.auth import get_current_user
from app.db.database import get_db
from app.models.user import User
from app.models.action_tracking import UserLocation, UserPrivacySettings
from app.schemas.user import UserResponse, UserUpdate
from app.services.location_encryption import get_location_encryption_service

router = APIRouter()


@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current user's profile with decrypted address information"""
    # Load user with relationships
    user = db.query(User).filter(User.id == current_user.id).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Build response with address data if available
    user_data = {
        "id": str(user.id),
        "email": user.email,
        "first_name": user.first_name or "",
        "last_name": user.last_name or "",
        "zip_code": user.zip_code,
        "phone_number": user.phone_number,
        "email_notifications": user.email_notifications,
        "sms_notifications": user.sms_notifications,
        "bio": user.bio,
        "profile_picture_url": user.profile_picture_url,
        "is_active": user.is_active,
        "is_verified": user.is_verified,
        "is_superuser": user.is_superuser,
        "created_at": user.created_at,
        "updated_at": user.updated_at,
        "last_login_at": user.last_login_at,
        # Address fields (decrypted from UserLocation if available)
        "address": None,
        "city": None,
        "state": None,
        "save_address_for_future_actions": False
    }
    
    # Add decrypted address info if user has saved location
    if user.location:
        try:
            encryption_service = get_location_encryption_service()
            if user.location.encrypted_address:
                user_data["address"] = encryption_service.decrypt_address(user.location.encrypted_address)
            if user.location.encrypted_zip_code:
                # Use decrypted zip if available, fallback to user.zip_code
                decrypted_zip = encryption_service.decrypt_zip_code(user.location.encrypted_zip_code)
                if decrypted_zip:
                    user_data["zip_code"] = decrypted_zip
            user_data["state"] = user.location.state_code
        except Exception as e:
            # Log decryption error but don't fail the request
            print(f"Warning: Failed to decrypt address for user {user.id}: {e}")
    
    # Add privacy settings
    if user.privacy_settings:
        user_data["save_address_for_future_actions"] = user.privacy_settings.share_location_analytics
    
    return UserResponse(**user_data)


@router.patch("/profile", response_model=UserResponse)
async def update_user_profile(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update user profile with secure address storage"""
    # Get user with relationships
    user = db.query(User).filter(User.id == current_user.id).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Update basic user fields
    update_data = user_update.model_dump(exclude_unset=True)
    
    # Handle address fields separately for encryption
    address_fields = {}
    for field in ['address', 'city', 'state', 'save_address_for_future_actions']:
        if field in update_data:
            address_fields[field] = update_data.pop(field)
    
    # Update basic user fields
    for field, value in update_data.items():
        if hasattr(user, field) and value is not None:
            setattr(user, field, value)
    
    # Handle address storage with encryption
    if address_fields:
        # Ensure user has privacy settings
        if not user.privacy_settings:
            user.privacy_settings = UserPrivacySettings(user_id=user.id)
            db.add(user.privacy_settings)
        
        # Update privacy consent
        if 'save_address_for_future_actions' in address_fields:
            user.privacy_settings.share_location_analytics = address_fields['save_address_for_future_actions']
        
        # Only save address if user consents
        if user.privacy_settings.share_location_analytics:
            # Ensure user has location record
            if not user.location:
                user.location = UserLocation(user_id=user.id)
                db.add(user.location)
            
            # Encrypt and store address fields
            encryption_service = get_location_encryption_service()
            if 'address' in address_fields and address_fields['address']:
                user.location.encrypted_address = encryption_service.encrypt_address(address_fields['address'])
            
            if user_update.zip_code:  # Use zip from main update, not address_fields
                user.location.encrypted_zip_code = encryption_service.encrypt_zip_code(user_update.zip_code)
                user.zip_code = user_update.zip_code  # Also store unencrypted for quick access
            
            if 'state' in address_fields and address_fields['state']:
                user.location.state_code = address_fields['state'].upper()
            
            # Update metadata
            user.location.location_source = 'user_input'
            user.location.accuracy_level = 'address'
        else:
            # User doesn't want address saved - clear existing data
            if user.location:
                db.delete(user.location)
                user.location = None
    
    try:
        db.commit()
        db.refresh(user)
        
        # Return updated profile using the get endpoint logic
        return await get_user_profile(current_user=user, db=db)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update profile: {str(e)}"
        )


@router.delete("/profile/address")
async def delete_saved_address(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete user's saved address data"""
    user = db.query(User).filter(User.id == current_user.id).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Remove location data
    if user.location:
        db.delete(user.location)
    
    # Update privacy settings
    if user.privacy_settings:
        user.privacy_settings.share_location_analytics = False
    
    # Clear zip code from user table too
    user.zip_code = None
    
    try:
        db.commit()
        return {"message": "Saved address data deleted successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete address data: {str(e)}"
        )