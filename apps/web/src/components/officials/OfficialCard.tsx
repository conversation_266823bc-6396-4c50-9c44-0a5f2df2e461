'use client';

import React from 'react';
import { Official } from '@/types';
import Link from 'next/link';
import Image from 'next/image';

interface OfficialCardProps {
  official: Official;
  showFullDetails?: boolean;
  className?: string;
}

export const OfficialCard: React.FC<OfficialCardProps> = ({
  official,
  showFullDetails = false,
  className = ''
}) => {
  const getPhotoUrl = () => {
    return official.official_photo_url || official.photo_url || '/images/default-official.png';
  };

  const getDisplayName = () => {
    return official.full_name || official.name;
  };

  const getTitle = () => {
    return official.title || (official.chamber === 'house' ? 'Representative' : 'Senator');
  };

  const getPartyColor = (party?: string) => {
    if (!party) return 'bg-gray-100 text-gray-800';
    
    const partyLower = party.toLowerCase();
    if (partyLower.includes('democrat') || partyLower.includes('dem')) {
      return 'bg-blue-100 text-blue-800';
    }
    if (partyLower.includes('republican') || partyLower.includes('rep')) {
      return 'bg-red-100 text-red-800';
    }
    return 'bg-gray-100 text-gray-800';
  };

  const getSocialMediaLinks = () => {
    if (!official.social_media) return [];
    
    return Object.entries(official.social_media).map(([platform, url]) => ({
      platform,
      url,
      icon: getSocialIcon(platform)
    }));
  };

  const getSocialIcon = (platform: string) => {
    const platformLower = platform.toLowerCase();
    
    if (platformLower.includes('twitter')) return '🐦';
    if (platformLower.includes('facebook')) return '📘';
    if (platformLower.includes('instagram')) return '📷';
    if (platformLower.includes('youtube')) return '📺';
    if (platformLower.includes('linkedin')) return '💼';
    if (platformLower.includes('website')) return '🌐';
    
    return '🔗';
  };

  return (
    <div className={`bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow duration-200 ${className}`}>
      <div className="p-6">
        {/* Header Section */}
        <div className="flex items-start space-x-4 mb-4">
          <div className="flex-shrink-0">
            <Image
              src={getPhotoUrl()}
              alt={`Photo of ${getDisplayName()}`}
              width={64}
              height={64}
              className="w-16 h-16 rounded-full object-cover border-2 border-gray-200"
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/images/default-official.png';
              }}
            />
          </div>
          
          <div className="flex-1 min-w-0">
            <Link 
              href={`/officials/${official.id}`}
              className="group"
            >
              <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                {getDisplayName()}
              </h3>
            </Link>
            
            <p className="text-sm text-gray-600 mb-2">
              {getTitle()}
              {official.state && (
                <span className="ml-2 text-gray-500">({official.state})</span>
              )}
              {official.district && (
                <span className="ml-1 text-gray-500">- District {official.district}</span>
              )}
            </p>

            {official.party && (
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPartyColor(official.party)}`}>
                {official.party}
              </span>
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-2 text-sm text-gray-600">
            {official.email && (
              <a 
                href={`mailto:${official.email}`}
                className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
              >
                <span>📧</span>
                <span>Email</span>
              </a>
            )}
            
            {official.phone && (
              <a 
                href={`tel:${official.phone}`}
                className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
              >
                <span>📞</span>
                <span>Phone</span>
              </a>
            )}
            
            {official.website && (
              <a 
                href={official.website}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-1 hover:text-blue-600 transition-colors duration-200"
              >
                <span>🌐</span>
                <span>Website</span>
              </a>
            )}
          </div>
        </div>

        {/* Social Media Links */}
        {getSocialMediaLinks().length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-2">
              {getSocialMediaLinks().map(({ platform, url, icon }) => (
                <a
                  key={platform}
                  href={url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center space-x-1 px-2 py-1 bg-gray-100 rounded-md text-xs text-gray-700 hover:bg-gray-200 transition-colors duration-200"
                  title={`${getDisplayName()} on ${platform}`}
                >
                  <span>{icon}</span>
                  <span>{platform}</span>
                </a>
              ))}
            </div>
          </div>
        )}

        {/* Extended Details for Full View */}
        {showFullDetails && (
          <>
            {/* Committees */}
            {official.committees && official.committees.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Committees</h4>
                <div className="flex flex-wrap gap-1">
                  {official.committees.map((committee, index) => (
                    <span 
                      key={index}
                      className="inline-block px-2 py-1 bg-blue-50 text-blue-700 rounded-md text-xs"
                    >
                      {committee.name}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Leadership Positions */}
            {official.leadership_positions && official.leadership_positions.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Leadership</h4>
                <div className="flex flex-wrap gap-1">
                  {official.leadership_positions.map((position, index) => (
                    <span 
                      key={index}
                      className="inline-block px-2 py-1 bg-green-50 text-green-700 rounded-md text-xs"
                    >
                      {position.title}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Legislative Statistics */}
            {(official.bills_sponsored_count || official.bills_cosponsored_count || official.votes_cast_count) && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Legislative Activity</h4>
                <div className="grid grid-cols-3 gap-4 text-center">
                  {official.bills_sponsored_count !== undefined && (
                    <div className="bg-gray-50 rounded-lg p-2">
                      <div className="text-lg font-semibold text-gray-900">{official.bills_sponsored_count}</div>
                      <div className="text-xs text-gray-600">Sponsored</div>
                    </div>
                  )}
                  {official.bills_cosponsored_count !== undefined && (
                    <div className="bg-gray-50 rounded-lg p-2">
                      <div className="text-lg font-semibold text-gray-900">{official.bills_cosponsored_count}</div>
                      <div className="text-xs text-gray-600">Co-sponsored</div>
                    </div>
                  )}
                  {official.votes_cast_count !== undefined && (
                    <div className="bg-gray-50 rounded-lg p-2">
                      <div className="text-lg font-semibold text-gray-900">{official.votes_cast_count}</div>
                      <div className="text-xs text-gray-600">Votes Cast</div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Bio */}
            {official.bio && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Biography</h4>
                <p className="text-sm text-gray-700 line-clamp-3">{official.bio}</p>
              </div>
            )}
          </>
        )}

        {/* Action Button */}
        <div className="flex justify-between items-center">
          <Link
            href={`/officials/${official.id}`}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
          >
            View Profile
          </Link>

          {official.response_rate !== undefined && (
            <div className="text-xs text-gray-500">
              {official.response_rate}% response rate
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OfficialCard;