[tool.poetry]
name = "modernaction-api"
version = "0.1.0"
description = "ModernAction.io API Backend"
authors = ["ModernAction Team"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.9"
fastapi = ">=0.116.1,<0.117.0"
uvicorn = ">=0.35.0,<0.36.0"
pydantic = {extras = ["email"], version = ">=2.11.7,<3.0.0"}
python-dotenv = ">=1.1.1,<2.0.0"
sqlalchemy = ">=2.0.0,<3.0.0"
alembic = ">=1.13.0,<2.0.0"
psycopg2-binary = "==2.9.9"  # Pin to prebuilt wheel to avoid pg_config build issues
asyncpg = ">=0.28.0,<1.0.0"
redis = ">=5.0.0,<6.0.0"
celery = ">=5.3.0,<6.0.0"
passlib = {extras = ["bcrypt"], version = ">=1.7.4,<2.0.0"}
python-jose = {extras = ["cryptography"], version = ">=3.3.0,<4.0.0"}
python-multipart = ">=0.0.6,<1.0.0"
aiofiles = ">=23.2.0,<24.0.0"
transformers = ">=4.30.0,<5.0.0"
torch = ">=2.0.0,<3.0.0"
requests = ">=2.31.0,<3.0.0"
pydantic-settings = ">=2.0.0,<3.0.0"
boto3 = ">=1.26.0,<2.0.0"
slowapi = ">=0.1.9,<1.0.0"
sentencepiece = ">=0.2.0,<0.3.0"
tweepy = ">=4.14.0,<5.0.0"
beautifulsoup4 = ">=4.12.0,<5.0.0"
lxml = ">=4.9.0,<5.0.0"
aiohttp = "^3.12.15"
openai = "^1.98.0"
tenacity = "^9.1.2"


[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
httpx = "^0.28.1"
pytest-asyncio = "^0.23.8"
pytest-cov = "^4.1.0"
pytest-env = "^1.1.5"
black = "^23.0.0"
ruff = "^0.1.0"
mypy = "^1.5.0"
pre-commit = "^3.4.0"
factory-boy = "^3.3.0"
faker = "^19.0.0"
moto = "^5.1.8"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.flake8]
max-line-length = 88
exclude = [
    "alembic/versions/*.py",
    "migrations/",
    "__pycache__",
    ".git",
    ".venv",
    "venv",
    "build",
    "dist"
]
ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by black)
    "W503",  # line break before binary operator
    "W293",  # blank line contains whitespace (auto-fixable)
]

[tool.ruff]
line-length = 88
exclude = [
    "alembic/versions/*.py",
    "migrations/",
    "__pycache__",
    ".git",
    ".venv",
    "venv",
    "build",
    "dist",
    "seed.py",
    "test_openstates_federal.py"
]

[tool.ruff.lint]
ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by black)
    "W503",  # line break before binary operator
    "W293",  # blank line contains whitespace (auto-fixable)
    "E302",  # expected 2 blank lines
    "E122",  # continuation line missing indentation
    "E128",  # continuation line under-indented
    "E127",  # continuation line over-indented
    "E129",  # visually indented line with same indent as next logical line
    "E305",  # expected 2 blank lines after class or function definition
    "E303",  # too many blank lines
    "E226",  # missing whitespace around arithmetic operator
    "E402",  # module level import not at top of file
    "F401",  # imported but unused
    "F811",  # redefined while unused
    "F841",  # local variable assigned but never used
    "F541",  # f-string is missing placeholders
    "W504",  # line break after binary operator
    "W391",  # blank line at end of file
    "W291",  # trailing whitespace
]

