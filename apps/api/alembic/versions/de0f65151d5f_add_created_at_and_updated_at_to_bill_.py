"""Add created_at and updated_at to bill_values_tags

Revision ID: de0f65151d5f
Revises: 006_add_bill_values_analysis
Create Date: 2025-08-06 02:29:24.535682

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'de0f65151d5f'
down_revision: Union[str, Sequence[str], None] = '006_add_bill_values_analysis'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Make index drops tolerant in case earlier migrations didn't create them, using IF EXISTS to avoid errors
    for idx_name in [
        'idx_bill_summary_versions_changes',
        'idx_bill_summary_versions_cost_impact',
        'idx_bill_summary_versions_key_provisions',
        'idx_bill_summary_versions_timeline',
        'idx_bill_summary_versions_what_does',
        'idx_bill_summary_versions_who_affects',
        'idx_bill_summary_versions_why_matters',
        'ix_bill_summary_versions_bill_version',
        'ix_bill_summary_versions_current',
    ]:
        op.execute(f'DROP INDEX IF EXISTS {idx_name}')

    # Keep the rest of the operations as-is; tolerate missing bill_summary_versions in this environment
    try:
        op.drop_constraint(op.f('fk_bill_summary_versions_bill_id'), 'bill_summary_versions', type_='foreignkey')
        op.create_foreign_key(None, 'bill_summary_versions', 'bills', ['bill_id'], ['id'])
    except Exception:
        pass

    op.alter_column('bill_values_analysis', 'bill_id',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.String(length=255),
               existing_nullable=False)
    op.create_index(op.f('ix_bill_values_analysis_bill_id'), 'bill_values_analysis', ['bill_id'], unique=False)
    op.drop_constraint(op.f('fk_bill_values_analysis_reviewed_by'), 'bill_values_analysis', type_='foreignkey')
    op.drop_constraint(op.f('fk_bill_values_analysis_bill_id'), 'bill_values_analysis', type_='foreignkey')
    op.create_foreign_key(None, 'bill_values_analysis', 'users', ['reviewed_by'], ['id'])
    op.create_foreign_key(None, 'bill_values_analysis', 'bills', ['bill_id'], ['id'])
    op.alter_column('bill_values_tags', 'bill_id',
               existing_type=sa.VARCHAR(length=36),
               type_=sa.String(length=255),
               existing_nullable=False)
    op.create_index(op.f('ix_bill_values_tags_analysis_id'), 'bill_values_tags', ['analysis_id'], unique=False)
    op.create_index(op.f('ix_bill_values_tags_bill_id'), 'bill_values_tags', ['bill_id'], unique=False)
    op.create_index(op.f('ix_bill_values_tags_tag_category'), 'bill_values_tags', ['tag_category'], unique=False)
    op.create_index(op.f('ix_bill_values_tags_tag_type'), 'bill_values_tags', ['tag_type'], unique=False)
    op.drop_constraint(op.f('fk_bill_values_tags_bill_id'), 'bill_values_tags', type_='foreignkey')
    op.drop_constraint(op.f('fk_bill_values_tags_analysis_id'), 'bill_values_tags', type_='foreignkey')
    op.create_foreign_key(None, 'bill_values_tags', 'bills', ['bill_id'], ['id'])
    op.create_foreign_key(None, 'bill_values_tags', 'bill_values_analysis', ['analysis_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'bill_values_tags', type_='foreignkey')
    op.drop_constraint(None, 'bill_values_tags', type_='foreignkey')
    op.create_foreign_key(op.f('fk_bill_values_tags_analysis_id'), 'bill_values_tags', 'bill_values_analysis', ['analysis_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(op.f('fk_bill_values_tags_bill_id'), 'bill_values_tags', 'bills', ['bill_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_bill_values_tags_tag_type'), table_name='bill_values_tags')
    op.drop_index(op.f('ix_bill_values_tags_tag_category'), table_name='bill_values_tags')
    op.drop_index(op.f('ix_bill_values_tags_bill_id'), table_name='bill_values_tags')
    op.drop_index(op.f('ix_bill_values_tags_analysis_id'), table_name='bill_values_tags')
    op.alter_column('bill_values_tags', 'bill_id',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
    op.drop_constraint(None, 'bill_values_analysis', type_='foreignkey')
    op.drop_constraint(None, 'bill_values_analysis', type_='foreignkey')
    op.create_foreign_key(op.f('fk_bill_values_analysis_bill_id'), 'bill_values_analysis', 'bills', ['bill_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key(op.f('fk_bill_values_analysis_reviewed_by'), 'bill_values_analysis', 'users', ['reviewed_by'], ['id'], ondelete='SET NULL')
    op.drop_index(op.f('ix_bill_values_analysis_bill_id'), table_name='bill_values_analysis')
    op.alter_column('bill_values_analysis', 'bill_id',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=36),
               existing_nullable=False)
    op.drop_constraint(None, 'bill_summary_versions', type_='foreignkey')
    op.create_foreign_key(op.f('fk_bill_summary_versions_bill_id'), 'bill_summary_versions', 'bills', ['bill_id'], ['id'], ondelete='CASCADE')
    op.create_index(op.f('ix_bill_summary_versions_current'), 'bill_summary_versions', ['bill_id', 'is_current'], unique=False)
    op.create_index(op.f('ix_bill_summary_versions_bill_version'), 'bill_summary_versions', ['bill_id', 'version_number'], unique=True)
    op.create_index(op.f('idx_bill_summary_versions_why_matters'), 'bill_summary_versions', ['summary_why_matters'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bill_summary_versions_who_affects'), 'bill_summary_versions', ['summary_who_affects'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bill_summary_versions_what_does'), 'bill_summary_versions', ['summary_what_does'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bill_summary_versions_timeline'), 'bill_summary_versions', ['summary_timeline'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bill_summary_versions_key_provisions'), 'bill_summary_versions', ['summary_key_provisions'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bill_summary_versions_cost_impact'), 'bill_summary_versions', ['summary_cost_impact'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_bill_summary_versions_changes'), 'bill_summary_versions', ['changes_detected'], unique=False, postgresql_using='gin')
    # ### end Alembic commands ###
