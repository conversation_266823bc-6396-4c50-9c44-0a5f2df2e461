"""
World-Class Analysis Service - Production-ready with proper JSON handling and evidence-by-ID
Implements comprehensive analysis while staying under $0.30 per bill
"""

import json
import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class WorldClassAnalysisService:
    """Production-ready analysis service with bulletproof JSON and evidence handling"""
    
    def __init__(self, ai_service):
        self.ai_service = ai_service
        
        # Budget controls (hard limits)
        self.max_bill_budget = 0.28  # Leave $0.02 slop under $0.30
        self.pass_a_input_limit = 1500  # tokens
        self.pass_a_output_limit = 800   # tokens
        self.pass_b_input_limit = 800    # tokens per section
        self.pass_b_output_limit = 350   # tokens per section
        self.max_enriched_sections = 5
        
        # Cost estimates (conservative)
        self.gpt4o_mini_input_cost = 0.00015  # per 1k tokens
        self.gpt4o_mini_output_cost = 0.0006  # per 1k tokens
        self.gpt4o_input_cost = 0.005         # per 1k tokens
        self.gpt4o_output_cost = 0.015        # per 1k tokens
    
    async def analyze_bill_world_class(self, bill_text: str, bill_metadata: Dict, 
                                     evidence_spans: List[Dict]) -> Dict[str, Any]:
        """
        World-class analysis with bulletproof JSON and evidence-by-ID
        """
        
        try:
            # Step 1: Create evidence store with stable IDs
            evidence_store = self._create_evidence_store(evidence_spans)
            
            # Step 2: Free deterministic enrichments
            free_enrichments = self._extract_free_enrichments(bill_text, evidence_store)
            
            # Step 3: Pass A - Skeleton extraction (gpt-4o-mini)
            skeleton_result = await self._pass_a_skeleton_bulletproof(evidence_store, bill_metadata)
            
            if not skeleton_result.get('success'):
                return skeleton_result
            
            skeleton_analysis = skeleton_result['analysis']
            pass_a_cost = skeleton_result['cost']
            
            # Step 4: Section routing and budget check
            sections_to_enrich = self._route_sections_for_enrichment(
                skeleton_analysis, evidence_store, self.max_bill_budget - pass_a_cost
            )
            
            # Step 5: Pass B - Selective enrichment
            enriched_sections = []
            pass_b_cost = 0.0
            
            for section_data in sections_to_enrich:
                if pass_a_cost + pass_b_cost >= self.max_bill_budget:
                    logger.warning(f"Budget exhausted at ${pass_a_cost + pass_b_cost:.4f}")
                    break
                
                enriched_result = await self._pass_b_selective_bulletproof(
                    section_data, skeleton_analysis, evidence_store
                )
                
                if enriched_result.get('success'):
                    enriched_sections.append(enriched_result['section'])
                    pass_b_cost += enriched_result['cost']
            
            # Step 6: Resolve evidence IDs to full citations
            final_analysis = self._resolve_evidence_ids_to_citations(
                skeleton_analysis, enriched_sections, evidence_store
            )
            
            # Step 7: Combine with free enrichments
            complete_analysis = self._combine_all_analysis(
                final_analysis, free_enrichments
            )
            
            total_cost = pass_a_cost + pass_b_cost
            
            return {
                'success': True,
                'analysis': complete_analysis,
                'cost_breakdown': {
                    'pass_a_cost': pass_a_cost,
                    'pass_b_cost': pass_b_cost,
                    'total_cost': total_cost,
                    'budget_remaining': self.max_bill_budget - total_cost,
                    'sections_enriched': len(enriched_sections),
                    'budget_exhausted': total_cost >= self.max_bill_budget
                },
                'processing_level': 'world_class_comprehensive'
            }
            
        except Exception as e:
            logger.error(f"World-class analysis failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_level': 'world_class_failed'
            }
    
    def _create_evidence_store(self, evidence_spans: List[Dict]) -> Dict[str, Dict]:
        """Create evidence store with stable IDs"""
        
        evidence_store = {}
        
        for i, span in enumerate(evidence_spans):
            # Create stable ID
            span_id = f"S{i+1:02d}"
            
            evidence_store[span_id] = {
                'id': span_id,
                'quote': span.get('quote', ''),
                'heading': span.get('heading', ''),
                'anchor_id': span.get('anchor_id', ''),
                'start_offset': span.get('start_offset', 0),
                'end_offset': span.get('end_offset', 0),
                'type': span.get('type', 'general'),
                'priority': span.get('priority', 'low')
            }
        
        logger.info(f"Created evidence store with {len(evidence_store)} spans")
        return evidence_store
    
    def _sanitize_json_response(self, raw_response: str) -> str:
        """Bulletproof JSON sanitization"""
        
        # Remove code fences
        response = re.sub(r'```json\s*', '', raw_response)
        response = re.sub(r'```\s*$', '', response)
        
        # Find first { and last }
        start = response.find('{')
        end = response.rfind('}')
        
        if start == -1 or end == -1:
            raise ValueError("No valid JSON object found")
        
        json_str = response[start:end+1]
        
        # Try to parse and re-serialize for validation
        try:
            parsed = json.loads(json_str)
            return json.dumps(parsed)
        except json.JSONDecodeError as e:
            logger.error(f"JSON sanitization failed: {e}")
            raise ValueError(f"Invalid JSON after sanitization: {e}")
    
    async def _pass_a_skeleton_bulletproof(self, evidence_store: Dict, 
                                         bill_metadata: Dict) -> Dict[str, Any]:
        """Pass A with bulletproof JSON handling"""
        
        # Create evidence context for AI
        evidence_context = []
        for span_id, span_data in list(evidence_store.items())[:12]:  # Limit for budget
            evidence_context.append({
                'id': span_id,
                'quote': span_data['quote'][:80],  # Truncate for budget
                'type': span_data['type']
            })
        
        system_message = """You write structured analysis of U.S. bills for citizens.
You are only allowed to use the EVIDENCE SPANS provided.
Every sentence you write must be supported by at least one evidence ID in ev_ids.
Do not invent IDs. Output MUST be valid JSON. No code fences, no comments.
If any required field cannot be grounded, return an empty array for that field."""
        
        user_content = {
            "bill_metadata": bill_metadata,
            "evidence_spans": evidence_context,
            "instruction": "Create skeleton analysis. Each field must include ev_ids array with evidence IDs that support the content."
        }
        
        try:
            response = await self.ai_service.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": json.dumps(user_content)}
                ],
                max_tokens=self.pass_a_output_limit,
                temperature=0.1,
                response_format={"type": "json_object"}
            )
            
            # Sanitize JSON response
            sanitized_json = self._sanitize_json_response(response.choices[0].message.content)
            analysis = json.loads(sanitized_json)
            
            # Calculate cost
            cost = (
                response.usage.prompt_tokens * self.gpt4o_mini_input_cost / 1000 +
                response.usage.completion_tokens * self.gpt4o_mini_output_cost / 1000
            )
            
            logger.info(f"Pass A completed: ${cost:.4f}, {len(analysis.get('complete_analysis', []))} sections")
            
            return {
                'success': True,
                'analysis': analysis,
                'cost': cost,
                'tokens_used': response.usage.prompt_tokens + response.usage.completion_tokens
            }
            
        except Exception as e:
            logger.error(f"Pass A skeleton extraction failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'cost': 0.0
            }
    
    async def _pass_b_selective_bulletproof(self, section_data: Dict, 
                                          skeleton_analysis: Dict,
                                          evidence_store: Dict) -> Dict[str, Any]:
        """Pass B with bulletproof JSON and evidence handling"""
        
        section = section_data['section']
        
        # Get relevant evidence for this section
        relevant_evidence = self._get_relevant_evidence_for_section(
            section, evidence_store, max_spans=6
        )
        
        system_message = """Enrich the provided section with detailed analysis.
Use only evidence IDs provided. Every sentence must include ev_ids.
Output valid JSON. No code fences."""
        
        user_content = {
            "skeleton_section": section,
            "evidence_spans": relevant_evidence,
            "instruction": "Expand with detailed_summary (120-180 words), potential_impact, compliance_requirements, enforcement. All fields must have ev_ids."
        }
        
        try:
            response = await self.ai_service.client.chat.completions.create(
                model="gpt-4o-mini",  # Use mini for cost control
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": json.dumps(user_content)}
                ],
                max_tokens=self.pass_b_output_limit,
                temperature=0.1,
                response_format={"type": "json_object"}
            )
            
            # Sanitize JSON response
            sanitized_json = self._sanitize_json_response(response.choices[0].message.content)
            enriched_section = json.loads(sanitized_json)
            
            # Calculate cost
            cost = (
                response.usage.prompt_tokens * self.gpt4o_mini_input_cost / 1000 +
                response.usage.completion_tokens * self.gpt4o_mini_output_cost / 1000
            )
            
            logger.info(f"Pass B section enriched: ${cost:.4f}")
            
            return {
                'success': True,
                'section': enriched_section,
                'cost': cost
            }
            
        except Exception as e:
            logger.error(f"Pass B enrichment failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'cost': 0.0
            }

    def _extract_free_enrichments(self, bill_text: str, evidence_store: Dict) -> Dict[str, Any]:
        """Extract enrichments without LLM calls (free)"""

        enrichments = {
            'budgets_table': [],
            'mandates_table': [],
            'prohibitions_table': [],
            'deadlines': [],
            'cross_references': []
        }

        # Process each evidence span for free enrichments
        for span_id, span_data in evidence_store.items():
            quote = span_data['quote']

            # Budget classification
            if re.search(r'authorized to be appropriated', quote, re.IGNORECASE):
                amount_match = re.search(r'\$[\d,]+(?:\.\d{2})?', quote)
                enrichments['budgets_table'].append({
                    'kind': 'authorization',
                    'amount': amount_match.group(0) if amount_match else None,
                    'source': span_data['heading'],
                    'ev_ids': [span_id]
                })

            elif re.search(r'civil penalty|fine', quote, re.IGNORECASE):
                amount_match = re.search(r'\$[\d,]+(?:\.\d{2})?', quote)
                enrichments['budgets_table'].append({
                    'kind': 'penalty',
                    'amount': amount_match.group(0) if amount_match else None,
                    'source': span_data['heading'],
                    'ev_ids': [span_id]
                })

            # Mandate detection
            if re.search(r'\bshall\b|\bmust\b|\brequired\b', quote, re.IGNORECASE):
                enrichments['mandates_table'].append({
                    'actor': 'Entity',
                    'must_may_not': 'must',
                    'verb': 'comply',
                    'object': quote[:60] + '...' if len(quote) > 60 else quote,
                    'ev_ids': [span_id]
                })

            # Prohibition detection
            if re.search(r'shall not|may not|prohibited', quote, re.IGNORECASE):
                enrichments['prohibitions_table'].append({
                    'actor': 'Entity',
                    'prohibited_action': quote[:60] + '...' if len(quote) > 60 else quote,
                    'scope': span_data['heading'],
                    'ev_ids': [span_id]
                })

        # Limit each category
        for key in enrichments:
            enrichments[key] = enrichments[key][:3]

        return enrichments

    def _route_sections_for_enrichment(self, skeleton_analysis: Dict,
                                     evidence_store: Dict,
                                     remaining_budget: float) -> List[Dict]:
        """Route sections for Pass B enrichment"""

        sections = skeleton_analysis.get('complete_analysis', [])

        # Score sections for enrichment priority
        scored_sections = []
        for section in sections:
            score = 0
            title = section.get('title', '').lower()

            # Priority scoring
            if any(term in title for term in ['appropriat', 'fund', 'budget']):
                score += 3
            if any(term in title for term in ['requirement', 'shall', 'must']):
                score += 2
            if any(term in title for term in ['enforce', 'penalty']):
                score += 2

            importance = section.get('importance', 'technical')
            if importance == 'primary':
                score += 2
            elif importance == 'secondary':
                score += 1

            scored_sections.append({
                'section': section,
                'score': score
            })

        # Sort by score and limit by budget
        scored_sections.sort(key=lambda x: x['score'], reverse=True)

        # Estimate cost and select sections
        estimated_cost_per_section = (
            self.pass_b_input_limit * self.gpt4o_mini_input_cost / 1000 +
            self.pass_b_output_limit * self.gpt4o_mini_output_cost / 1000
        )

        max_sections = min(
            self.max_enriched_sections,
            int(remaining_budget / estimated_cost_per_section)
        )

        return scored_sections[:max_sections]

    def _get_relevant_evidence_for_section(self, section: Dict,
                                         evidence_store: Dict,
                                         max_spans: int = 6) -> List[Dict]:
        """Get relevant evidence for a specific section"""

        section_title = section.get('title', '').lower()
        section_keywords = set(section_title.split())

        relevant_spans = []

        for span_id, span_data in evidence_store.items():
            heading = span_data['heading'].lower()
            quote = span_data['quote'].lower()

            # Score relevance
            relevance_score = 0

            if section_title in heading:
                relevance_score += 10

            heading_keywords = set(heading.split())
            quote_keywords = set(quote.split()[:10])

            keyword_overlap = len(section_keywords & (heading_keywords | quote_keywords))
            relevance_score += keyword_overlap

            if relevance_score > 0:
                relevant_spans.append({
                    'id': span_id,
                    'quote': span_data['quote'][:60],
                    'relevance_score': relevance_score
                })

        # Sort by relevance and return top spans
        relevant_spans.sort(key=lambda x: x['relevance_score'], reverse=True)
        return relevant_spans[:max_spans]

    def _resolve_evidence_ids_to_citations(self, skeleton_analysis: Dict,
                                         enriched_sections: List[Dict],
                                         evidence_store: Dict) -> Dict[str, Any]:
        """Resolve evidence IDs to full citations with headings/anchors"""

        def resolve_ev_ids(obj):
            """Recursively resolve ev_ids to full citations"""
            if isinstance(obj, dict):
                if 'ev_ids' in obj:
                    # Replace ev_ids with full citations
                    citations = []
                    for ev_id in obj.get('ev_ids', []):
                        if ev_id in evidence_store:
                            span_data = evidence_store[ev_id]
                            citations.append({
                                'quote': span_data['quote'],
                                'heading': span_data['heading'],
                                'anchor_id': span_data['anchor_id'],
                                'start_offset': span_data['start_offset'],
                                'end_offset': span_data['end_offset']
                            })
                    obj['citations'] = citations
                    if 'ev_ids' in obj:
                        del obj['ev_ids']  # Remove ev_ids after resolution

                # Recursively process nested objects
                for key, value in obj.items():
                    obj[key] = resolve_ev_ids(value)

            elif isinstance(obj, list):
                return [resolve_ev_ids(item) for item in obj]

            return obj

        # Resolve skeleton analysis
        resolved_skeleton = resolve_ev_ids(skeleton_analysis.copy())

        # Replace enriched sections in skeleton
        complete_analysis = resolved_skeleton.get('complete_analysis', [])
        enriched_titles = {section.get('title', '') for section in enriched_sections}

        final_sections = []
        for section in complete_analysis:
            if section.get('title', '') in enriched_titles:
                # Find and use enriched version
                enriched_section = next(
                    (s for s in enriched_sections if s.get('title', '') == section.get('title', '')),
                    section
                )
                final_sections.append(resolve_ev_ids(enriched_section))
            else:
                final_sections.append(section)

        resolved_skeleton['complete_analysis'] = final_sections
        return resolved_skeleton

    def _combine_all_analysis(self, final_analysis: Dict,
                            free_enrichments: Dict) -> Dict[str, Any]:
        """Combine all analysis components"""

        combined = final_analysis.copy()
        combined['additional_details'] = free_enrichments

        complete_analysis = combined.get('complete_analysis', [])
        combined['processing_metadata'] = {
            'total_sections': len(complete_analysis),
            'free_enrichments': sum(len(v) for v in free_enrichments.values()),
            'processing_method': 'world_class_two_pass'
        }

        return combined
