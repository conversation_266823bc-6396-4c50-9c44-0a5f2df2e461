# Testing Guide
> Last updated: 2025-08-11


## Overview

The Bill Details system includes comprehensive testing across unit, integration, and end-to-end levels. All tests ensure citation accuracy, data integrity, and user experience quality.

## Test Structure

```
apps/api/
├── test_citation_service.py           # Unit tests for citation validation
├── test_bill_details_service.py       # Unit tests for bill details processing
├── tests/
│   ├── conftest.py                    # Test configuration
│   ├── test_bill_details_api.py       # API integration tests
│   └── fixtures/                      # Test data

apps/web/
├── cypress/
│   ├── e2e/
│   │   └── bill-details-flow.cy.ts    # End-to-end tests
│   ├── fixtures/
│   │   ├── bill-details.json          # Mock API responses
│   │   ├── bills-list.json
│   │   └── bill-action-data.json
│   └── support/
│       └── commands.ts                # Custom Cypress commands
```

## Unit Tests

### Citation Service Tests (`test_citation_service.py`)

Tests the core citation validation functionality:

```python
class TestTextCitationService:
    def test_build_source_index_with_sections(self):
        """Test building source index with SEC. and Section headings."""
        service = TextCitationService()
        bill_text = """TITLE I - HEALTHCARE ACCESS

SEC. 101. MEDICAID EXPANSION

This section expands Medicaid eligibility..."""

        index = service.build_source_index(bill_text)

        assert len(index) == 5  # Expected number of sections
        assert index[0].heading == "TITLE I"
        assert index[0].anchor_id == "sec-1"

        # Verify non-overlapping offsets
        for i in range(len(index) - 1):
            assert index[i].end_offset <= index[i + 1].start_offset

    def test_bind_quote_exact_match(self):
        """Test binding quotes that exist exactly in the text."""
        service = TextCitationService()
        index = service.build_source_index(self.sample_bill_text)

        quote = "expands Medicaid eligibility to all individuals"
        binding = service.bind_quote(self.sample_bill_text, index, quote)

        assert binding is not None
        assert binding.quote == quote
        assert binding.heading == "SEC. 101. MEDICAID EXPANSION"
        assert binding.anchor_id is not None

        # Verify quote exists at specified offset
        extracted = self.sample_bill_text[binding.start_offset:binding.end_offset]
        assert quote in extracted
```

### Bill Details Service Tests (`test_bill_details_service.py`)

Tests the complete processing pipeline:

```python
class TestBillDetailsService:
    def test_slugify_basic(self):
        """Test basic slug generation."""
        service = BillDetailsService(mock_db)
        bill = MagicMock()
        bill.bill_number = "H.R.5"
        bill.session_year = 118

        slug = service._slugify(bill)
        assert slug == "hr5-118"

    def test_compute_metrics_full_coverage(self):
        """Test metrics when all sections have citations."""
        details_payload = {
            "overview": {
                "what_does": {
                    "content": "Some content",
                    "citations": [{"quote": "test quote"}]
                },
                "who_affects": {
                    "content": "More content",
                    "citations": [{"quote": "another quote"}]
                }
            }
        }

        metrics = service._compute_metrics(details_payload)

        assert metrics["coverage_ratio"] == 1.0
        assert metrics["unverified_count"] == 0

    def test_create_or_update_details_new_record(self):
        """Test creating new bill details record."""
        # Mock database to return no existing record
        mock_db.query.return_value.filter.return_value.first.return_value = None

        result = service.create_or_update_details(sample_bill, bill_text, details_payload)

        # Verify database operations
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()
```

### Running Unit Tests

```bash
# Run all unit tests
cd apps/api
poetry run pytest test_citation_service.py test_bill_details_service.py -v

# Run with coverage
poetry run pytest test_citation_service.py test_bill_details_service.py -v --cov=app

# Run specific test
poetry run pytest test_citation_service.py::TestTextCitationService::test_bind_quote_exact_match -v
```

## Integration Tests

### API Tests (`tests/test_bill_details_api.py`)

Tests the complete API endpoints:

```python
class TestBillDetailsAPI:
    def test_get_bill_details_by_slug(self, client, sample_bill_details):
        """Test fetching bill details by slug."""
        response = client.get(f"/api/v1/bills/details/by-slug/{sample_bill_details.seo_slug}")

        assert response.status_code == 200
        data = response.json()

        assert data["seo_slug"] == sample_bill_details.seo_slug
        assert data["seo_title"] == sample_bill_details.seo_title
        assert "overview" in data
        assert "positions" in data
        assert "citations" in data["overview"]["what_does"]

    def test_get_bill_details_not_found(self, client):
        """Test 404 handling for missing bill details."""
        response = client.get("/api/v1/bills/details/by-slug/nonexistent-bill")

        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()

    def test_slug_normalization(self, client, sample_bill_details):
        """Test that different slug formats work."""
        # Test canonical format
        response1 = client.get(f"/api/v1/bills/details/by-slug/{sample_bill_details.seo_slug}")

        # Test alternative format (5-118 instead of hr5-118)
        alt_slug = sample_bill_details.seo_slug.replace("hr", "")
        response2 = client.get(f"/api/v1/bills/details/by-slug/{alt_slug}")

        assert response1.status_code == 200
        assert response2.status_code == 200
        assert response1.json()["id"] == response2.json()["id"]
```

### Test Fixtures

```python
# tests/conftest.py
@pytest.fixture
def sample_bill_details(db_session):
    """Create sample bill details for testing."""
    bill = Bill(
        id="bill_123",
        title="Healthcare Access Act",
        bill_number="H.R.5",
        session_year=118,
        status="introduced",
        chamber="house"
    )
    db_session.add(bill)

    details = BillDetails(
        bill_id="bill_123",
        seo_slug="hr5-118",
        seo_title="Healthcare Access Act - Bill Details",
        overview={
            "what_does": {
                "content": "Expands healthcare access",
                "citations": [
                    {
                        "quote": "expands Medicaid eligibility",
                        "start_offset": 45,
                        "end_offset": 68,
                        "heading": "SEC. 101",
                        "anchor_id": "sec-1"
                    }
                ]
            }
        },
        needs_human_review=True,
        metrics={"coverage_ratio": 0.75, "unverified_count": 2}
    )
    db_session.add(details)
    db_session.commit()
## New Integration Test

A new API integration test validates end-to-end persistence of BillDetails:

- File: apps/api/tests/test_unified_processing_bill_details.py
- Behavior:
  - Mocks AIService outputs for determinism
  - Calls /api/v1/admin/process-bill-details
  - Asserts details persisted and source_index populated

Run:
```bash
cd apps/api
poetry run pytest -q apps/api/tests/test_unified_processing_bill_details.py
```

## Test Environment

- Always use PostgreSQL (never SQLite)
- apps/api/pytest.ini sets DATABASE_URL to a _test database
- Use moto to mock AWS services during tests


    return details
```

## End-to-End Tests

### Cypress Tests (`cypress/e2e/bill-details-flow.cy.ts`)

Tests the complete user journey:

```typescript
describe('Bill Details Flow', () => {
  beforeEach(() => {
    // Mock API responses
    cy.intercept('GET', '/api/v1/bills?*', { fixture: 'bills-list.json' }).as('getBills')
    cy.intercept('GET', '/api/v1/bills/*/action-data', { fixture: 'bill-action-data.json' }).as('getBillActionData')
    cy.intercept('GET', '/api/v1/bills/details/by-slug/*', { fixture: 'bill-details.json' }).as('getBillDetails')
  })

  it('completes the full bill details flow', () => {
    // Step 1: Start at homepage
    cy.visit('/')
    cy.wait('@getBills')

    // Step 2: Click bill card to open action modal
    cy.get('[data-testid="bill-card"]').first().click()
    cy.wait('@getBillActionData')

    // Step 3: Verify modal opened
    cy.get('[data-testid="bill-action-modal"]').should('be.visible')
    cy.get('[data-testid="modal-title"]').should('contain', 'Take Action on')

    // Step 4: Click "Learn more" link
    cy.get('[data-testid="learn-more-link"]').first().invoke('removeAttr', 'target').click()

    // Step 5: Verify bill details page
    cy.url().should('include', '/bills/')
    cy.get('h1').should('contain', 'Bill Details')

    // Step 6: Test anchor navigation
    cy.get('nav a[href="#overview-what"]').click()
    cy.get('#overview-what').should('be.visible')

    // Step 7: Verify citations
    cy.get('[data-testid="citations"]').should('exist')
    cy.get('[data-testid="citation-quote"]').should('be.visible')
    cy.get('[data-testid="citation-link"]').should('have.attr', 'href').and('include', '#sec-')
  })

  it('displays moderation banner correctly', () => {
    cy.visit('/bills/hr5-118')
    cy.wait('@getBillDetails')

    cy.get('[data-testid="moderation-banner"]').should('be.visible')
    cy.get('[data-testid="moderation-banner"]').should('contain', 'AI-Generated Analysis')
    cy.get('[data-testid="coverage-metric"]').should('match', /Coverage: \d+%/)
  })

  it('handles citation navigation', () => {
    cy.visit('/bills/hr5-118')

    cy.get('[data-testid="citation-link"]').first().then(($link) => {
      const href = $link.prop('href')
      const anchor = href.split('#')[1]

      cy.get('[data-testid="citation-link"]').first().click()
      cy.get(`#${anchor}`).should('be.visible')
    })
  })
})
```

### Test Fixtures

Mock data for consistent E2E testing:

```json
// cypress/fixtures/bill-details.json
{
  "id": "bill_123",
  "seo_slug": "hr5-118",
  "seo_title": "Healthcare Access Act - Bill Details",
  "needs_human_review": true,
  "metrics": {
    "coverage_ratio": 0.75,
    "unverified_count": 2
  },
  "overview": {
    "what_does": {
      "content": "This bill expands Medicaid eligibility...",
      "citations": [
        {
          "quote": "expands Medicaid eligibility to all individuals",
          "start_offset": 45,
          "end_offset": 120,
          "heading": "SEC. 101. MEDICAID EXPANSION",
          "anchor_id": "sec-1"
        }
      ]
    }
  }
}
```

### Running E2E Tests

```bash
# Run E2E tests in headless mode
cd apps/web
npm run test:e2e

# Run with UI for debugging
npm run test:e2e:ui

# Run specific test file
npx cypress run --spec "cypress/e2e/bill-details-flow.cy.ts"
```

## Test Data Management

### Database Fixtures

Create consistent test data:

```python
# tests/fixtures/sample_bills.py
def create_sample_bill_with_details(db_session):
    bill = Bill(
        id="test_bill_123",
        title="Sample Healthcare Bill",
        bill_number="H.R.999",
        session_year=118,
        status=BillStatus.INTRODUCED,
        chamber=Chamber.HOUSE
    )

    bill_details = BillDetails(
        bill_id="test_bill_123",
        seo_slug="hr999-118",
        overview={
            "what_does": {
                "content": "Sample content for testing",
                "citations": [
                    {
                        "quote": "sample quote from bill",
                        "start_offset": 100,
                        "end_offset": 125,
                        "heading": "SEC. 1. SAMPLE SECTION",
                        "anchor_id": "sec-1"
                    }
                ]
            }
        },
        needs_human_review=False,
        metrics={"coverage_ratio": 1.0, "unverified_count": 0}
    )

    db_session.add_all([bill, bill_details])
    db_session.commit()
    return bill, bill_details
```

### Mock Services

Mock external dependencies for reliable testing:

```python
# tests/mocks.py
class MockAIService:
    def __init__(self):
        self.enabled = True

    async def generate_detailed_bill_analysis(self, bill_text, metadata):
        return {
            "hero_summary": "Mock AI generated summary",
            "overview": {
                "what_does": {
                    "content": "Mock analysis of what the bill does",
                    "citations": []
                }
            },
            "positions": {
                "support_reasons": ["Mock support reason"],
                "oppose_reasons": ["Mock oppose reason"],
                "amend_reasons": []
            }
        }

@pytest.fixture
def mock_ai_service():
    return MockAIService()
```

## Performance Testing

### Load Testing

Test API performance under load:

```python
# tests/performance/test_load.py
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor

def test_bill_details_api_load():
    """Test API performance under concurrent requests."""

    def fetch_bill_details():
        response = requests.get("http://localhost:8000/api/v1/bills/details/by-slug/hr5-118")
        return response.status_code == 200

    # Test 100 concurrent requests
    with ThreadPoolExecutor(max_workers=10) as executor:
        start_time = time.time()
        futures = [executor.submit(fetch_bill_details) for _ in range(100)]
        results = [f.result() for f in futures]
        end_time = time.time()

    success_rate = sum(results) / len(results)
    avg_response_time = (end_time - start_time) / len(results)

    assert success_rate >= 0.95  # 95% success rate
    assert avg_response_time < 0.5  # Under 500ms average
```

## Testing Best Practices

### 1. Citation Accuracy

Always verify that citations match exactly:

```python
def test_citation_accuracy():
    # Test that quoted text exists in source
    citation = {"quote": "test quote", "start_offset": 10, "end_offset": 20}
    source_text = "Sample test quote from bill"

    extracted = source_text[citation["start_offset"]:citation["end_offset"]]
    assert citation["quote"] in extracted
```

### 2. Data Consistency

Verify relationships between entities:

```python
def test_bill_details_consistency():
    bill_details = get_bill_details("hr5-118")

    # Verify all citations reference valid anchors
    for section in bill_details.overview.values():
        for citation in section.get("citations", []):
            anchor_id = citation.get("anchor_id")
            assert any(idx["anchor_id"] == anchor_id for idx in bill_details.source_index)
```

### 3. Error Handling

Test all error scenarios:

```python
def test_graceful_error_handling():
    # Test missing bill
    response = client.get("/api/v1/bills/details/by-slug/nonexistent")
    assert response.status_code == 404

    # Test malformed slug
    response = client.get("/api/v1/bills/details/by-slug/invalid-format")
    assert response.status_code in [400, 404]
```

### 4. Mobile Testing

Include mobile viewport testing:

```typescript
it('works on mobile devices', () => {
  cy.viewport('iphone-6')
  cy.visit('/bills/hr5-118')

  cy.get('h1').should('be.visible')
  cy.get('nav').should('be.visible')
  cy.get('[data-testid="citations"]').should('be.visible')
})
```

## Continuous Integration

### GitHub Actions Workflow

```yaml
name: Test Bill Details System

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      - name: Install dependencies
        run: |
          cd apps/api
          poetry install

      - name: Run unit tests
        run: |
          cd apps/api
          poetry run pytest test_citation_service.py test_bill_details_service.py -v --cov=app

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install dependencies
        run: |
          cd apps/web
          npm ci

      - name: Run E2E tests
        run: |
          cd apps/web
          npm run test:e2e
```

## Debugging Tests

### Common Issues

1. **Citation Mismatches**: Check whitespace normalization
2. **Offset Errors**: Verify text encoding consistency
3. **Mock Data Stale**: Update fixtures when API changes
4. **Timing Issues**: Add proper waits in E2E tests

### Debug Utilities

```python
# Debug citation binding issues
def debug_citation_binding(bill_text, quote):
    service = TextCitationService()
    index = service.build_source_index(bill_text)

    print(f"Looking for quote: '{quote}'")
    print(f"Bill text length: {len(bill_text)}")
    print(f"Sections found: {len(index)}")

    binding = service.bind_quote(bill_text, index, quote)
    if binding:
        extracted = bill_text[binding.start_offset:binding.end_offset]
        print(f"Found at offset {binding.start_offset}-{binding.end_offset}")
        print(f"Extracted: '{extracted}'")
    else:
        print("Quote not found in text")
```

## See Also

- [API Documentation](./api-documentation.md)
- [AI Pipeline Documentation](./ai-pipeline.md)
- [Frontend Integration Guide](./frontend-guide.md)