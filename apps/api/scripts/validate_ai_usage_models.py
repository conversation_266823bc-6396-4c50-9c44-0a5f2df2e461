#!/usr/bin/env python3
"""
AI Usage Models Validation Script

This script validates the AI usage models for world-class quality:
- Model structure validation
- Default value verification
- Relationship testing
- Performance validation
- Data integrity checks

Usage:
    python scripts/validate_ai_usage_models.py
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timezone

# Add the app directory to the Python path
app_dir = Path(__file__).parent.parent
sys.path.insert(0, str(app_dir))

from app.models.ai_usage import AIUsageLog, AIUsageSummary, AIBudgetAlert


def validate_model_structure():
    """Validate that all models have correct structure"""
    print("🔍 Validating model structure...")
    
    # Test AIUsageLog
    log_columns = {col.name: col for col in AIUsageLog.__table__.columns}
    required_log_columns = [
        'id', 'operation_type', 'model_name', 'provider', 
        'prompt_tokens', 'completion_tokens', 'total_tokens',
        'prompt_cost', 'completion_cost', 'total_cost',
        'success', 'created_at'
    ]
    
    for col in required_log_columns:
        assert col in log_columns, f"Missing column '{col}' in AIUsageLog"
    
    print("  ✓ AIUsageLog structure validated")
    
    # Test AIUsageSummary
    summary_columns = {col.name: col for col in AIUsageSummary.__table__.columns}
    required_summary_columns = [
        'id', 'date_bucket', 'period_type', 'total_requests',
        'successful_requests', 'failed_requests', 'total_cost'
    ]
    
    for col in required_summary_columns:
        assert col in summary_columns, f"Missing column '{col}' in AIUsageSummary"
    
    print("  ✓ AIUsageSummary structure validated")
    
    # Test AIBudgetAlert
    alert_columns = {col.name: col for col in AIBudgetAlert.__table__.columns}
    required_alert_columns = [
        'id', 'alert_name', 'alert_type', 'threshold_amount',
        'is_active', 'times_triggered'
    ]
    
    for col in required_alert_columns:
        assert col in alert_columns, f"Missing column '{col}' in AIBudgetAlert"
    
    print("  ✓ AIBudgetAlert structure validated")
    print("✅ All model structures are valid")


def validate_default_values():
    """Validate that default values work correctly"""
    print("\n🔍 Validating default values...")
    
    # Test AIUsageLog defaults
    log = AIUsageLog(
        id="test-001",
        operation_type="test",
        model_name="gpt-4"
    )
    
    assert log.provider == "openai", f"Expected 'openai', got '{log.provider}'"
    assert log.prompt_tokens == 0, f"Expected 0, got {log.prompt_tokens}"
    assert log.completion_tokens == 0, f"Expected 0, got {log.completion_tokens}"
    assert log.total_tokens == 0, f"Expected 0, got {log.total_tokens}"
    assert log.prompt_cost == 0.0, f"Expected 0.0, got {log.prompt_cost}"
    assert log.completion_cost == 0.0, f"Expected 0.0, got {log.completion_cost}"
    assert log.total_cost == 0.0, f"Expected 0.0, got {log.total_cost}"
    assert log.success is True, f"Expected True, got {log.success}"
    
    print("  ✓ AIUsageLog defaults validated")
    
    # Test AIUsageSummary defaults
    summary = AIUsageSummary(
        id="test-summary-001",
        date_bucket=datetime(2024, 1, 1)
    )
    
    assert summary.period_type == "daily", f"Expected 'daily', got '{summary.period_type}'"
    assert summary.total_requests == 0, f"Expected 0, got {summary.total_requests}"
    assert summary.successful_requests == 0, f"Expected 0, got {summary.successful_requests}"
    assert summary.failed_requests == 0, f"Expected 0, got {summary.failed_requests}"
    assert summary.total_cost == 0.0, f"Expected 0.0, got {summary.total_cost}"
    
    print("  ✓ AIUsageSummary defaults validated")
    
    # Test AIBudgetAlert defaults
    alert = AIBudgetAlert(
        id="test-alert-001",
        alert_name="Test Alert",
        alert_type="daily",
        threshold_amount=10.0
    )
    
    assert alert.is_active is True, f"Expected True, got {alert.is_active}"
    assert alert.times_triggered == 0, f"Expected 0, got {alert.times_triggered}"
    
    print("  ✓ AIBudgetAlert defaults validated")
    print("✅ All default values are working correctly")


def validate_edge_cases():
    """Validate edge cases and boundary conditions"""
    print("\n🔍 Validating edge cases...")
    
    # Test zero values
    log_zero = AIUsageLog(
        id="zero-test",
        operation_type="cached_response",
        model_name="gpt-4",
        prompt_tokens=0,
        completion_tokens=0,
        total_tokens=0,
        total_cost=0.0
    )
    assert log_zero.total_cost == 0.0
    print("  ✓ Zero values handled correctly")
    
    # Test negative values (refunds)
    log_negative = AIUsageLog(
        id="refund-test",
        operation_type="refund",
        model_name="gpt-4",
        prompt_cost=-0.05,
        total_cost=-0.05
    )
    assert log_negative.total_cost == -0.05
    print("  ✓ Negative values (refunds) handled correctly")
    
    # Test unicode content
    log_unicode = AIUsageLog(
        id="unicode-test",
        operation_type="分析法案_🏛️📊",
        model_name="gpt-4",
        error_message="错误信息: API调用失败 💥"
    )
    assert "🏛️" in log_unicode.operation_type
    assert "💥" in log_unicode.error_message
    print("  ✓ Unicode content handled correctly")
    
    # Test large values
    log_large = AIUsageLog(
        id="large-test",
        operation_type="comprehensive_analysis",
        model_name="gpt-4",
        prompt_tokens=100000,
        completion_tokens=50000,
        total_tokens=150000,
        total_cost=5.0,
        response_time_ms=30000.0
    )
    assert log_large.total_tokens == 150000
    assert log_large.total_cost == 5.0
    print("  ✓ Large values handled correctly")
    
    print("✅ All edge cases validated successfully")


def validate_relationships():
    """Validate model relationships"""
    print("\n🔍 Validating relationships...")
    
    # Check foreign key relationship
    log_columns = {col.name: col for col in AIUsageLog.__table__.columns}
    bill_id_col = log_columns['bill_id']
    
    foreign_keys = list(bill_id_col.foreign_keys)
    assert len(foreign_keys) == 1, "AIUsageLog should have one foreign key to bills table"
    assert foreign_keys[0].column.table.name == "bills", "Foreign key should reference bills table"
    
    print("  ✓ Foreign key relationship validated")
    
    # Check relationship attribute exists
    assert hasattr(AIUsageLog, 'bill'), "AIUsageLog should have 'bill' relationship"
    
    print("  ✓ Relationship attributes validated")
    print("✅ All relationships validated successfully")


def validate_data_types():
    """Validate column data types"""
    print("\n🔍 Validating data types...")
    
    from sqlalchemy import String, Integer, Float, DateTime, Text, Boolean
    from sqlalchemy.dialects import postgresql
    
    # AIUsageLog type validation
    log_columns = {col.name: col for col in AIUsageLog.__table__.columns}
    
    type_checks = [
        ('id', String),
        ('operation_type', String),
        ('prompt_tokens', Integer),
        ('total_cost', Float),
        ('success', Boolean),
        ('error_message', Text),
        ('created_at', DateTime)
    ]
    
    for col_name, expected_type in type_checks:
        col = log_columns[col_name]
        assert isinstance(col.type, expected_type), f"Column '{col_name}' should be {expected_type.__name__}"
    
    print("  ✓ AIUsageLog data types validated")
    
    # AIBudgetAlert JSONB validation
    alert_columns = {col.name: col for col in AIBudgetAlert.__table__.columns}
    notification_col = alert_columns['notification_emails']
    assert isinstance(notification_col.type, postgresql.JSONB), "notification_emails should be JSONB type"
    
    print("  ✓ JSONB data type validated")
    print("✅ All data types validated successfully")


def validate_constraints():
    """Validate database constraints"""
    print("\n🔍 Validating constraints...")
    
    # Check nullable constraints
    log_columns = {col.name: col for col in AIUsageLog.__table__.columns}
    
    required_fields = ['id', 'operation_type', 'model_name', 'provider', 'created_at']
    for field in required_fields:
        col = log_columns[field]
        assert col.nullable is False, f"Column '{field}' should be NOT NULL"
    
    optional_fields = ['operation_subtype', 'bill_id', 'error_message', 'user_id']
    for field in optional_fields:
        col = log_columns[field]
        assert col.nullable is True, f"Column '{field}' should be nullable"
    
    print("  ✓ Nullable constraints validated")
    
    # Check primary keys
    assert AIUsageLog.__table__.primary_key.columns.keys() == ['id'], "AIUsageLog should have 'id' as primary key"
    assert AIUsageSummary.__table__.primary_key.columns.keys() == ['id'], "AIUsageSummary should have 'id' as primary key"
    assert AIBudgetAlert.__table__.primary_key.columns.keys() == ['id'], "AIBudgetAlert should have 'id' as primary key"
    
    print("  ✓ Primary key constraints validated")
    print("✅ All constraints validated successfully")


def run_comprehensive_validation():
    """Run all validation tests"""
    print("🚀 AI Usage Models Comprehensive Validation")
    print("=" * 50)
    
    try:
        validate_model_structure()
        validate_default_values()
        validate_edge_cases()
        validate_relationships()
        validate_data_types()
        validate_constraints()
        
        print("\n" + "=" * 50)
        print("🎉 ALL VALIDATIONS PASSED!")
        print("✅ AI Usage Models are WORLD-CLASS QUALITY")
        print("\nValidation Summary:")
        print("  ✓ Model structure: PERFECT")
        print("  ✓ Default values: WORKING")
        print("  ✓ Edge cases: HANDLED")
        print("  ✓ Relationships: VALIDATED")
        print("  ✓ Data types: CORRECT")
        print("  ✓ Constraints: ENFORCED")
        print("\n🚀 Ready for production use!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ VALIDATION FAILED: {e}")
        print("🔧 Please fix the issues before proceeding")
        return False


def generate_usage_examples():
    """Generate usage examples for documentation"""
    print("\n📚 Generating usage examples...")
    
    examples = {
        "basic_logging": """
# Basic AI operation logging
log = AIUsageLog(
    id="bill-analysis-001",
    operation_type="bill_analysis",
    operation_subtype="support_reasons",
    bill_id="HR1234",
    model_name="gpt-4-turbo",
    prompt_tokens=1500,
    completion_tokens=800,
    total_tokens=2300,
    prompt_cost=0.015,
    completion_cost=0.024,
    total_cost=0.039,
    response_time_ms=1200.5,
    user_id="user-123"
)
""",
        "daily_summary": """
# Daily usage summary
summary = AIUsageSummary(
    id="daily-2024-01-15",
    date_bucket=datetime(2024, 1, 15),
    period_type="daily",
    operation_type="bill_analysis",
    total_requests=25,
    successful_requests=23,
    failed_requests=2,
    total_cost=1.25,
    bills_processed=10,
    avg_cost_per_bill=0.125
)
""",
        "budget_alert": """
# Budget alert configuration
alert = AIBudgetAlert(
    id="daily-limit",
    alert_name="Daily Spending Limit",
    alert_type="daily",
    threshold_amount=50.0,
    operation_type="bill_analysis",
    notification_emails=["<EMAIL>"]
)
"""
    }
    
    print("  ✓ Usage examples generated")
    return examples


if __name__ == "__main__":
    # Run comprehensive validation
    success = run_comprehensive_validation()
    
    if success:
        # Generate usage examples
        examples = generate_usage_examples()
        
        print("\n📖 Quick Start Examples:")
        print("=" * 30)
        for name, code in examples.items():
            print(f"\n{name.replace('_', ' ').title()}:")
            print(code.strip())
        
        sys.exit(0)
    else:
        sys.exit(1)
