/**
 * Slug utilities for bill URLs and navigation
 */

/**
 * Normalizes a bill number by removing non-alphanumeric characters and converting to lowercase
 * Example: "H.R. 5" -> "hr5"
 */
export function normalizeBillNumber(input: string): string {
  return (input || '').toLowerCase().replace(/[^a-z0-9]/g, '');
}

/**
 * Builds a canonical bill slug in the format: {normalized-bill-number}-{session}
 * Example: buildBillSlug("H.R. 5", "118") -> "hr5-118"
 */
export function buildBillSlug(billNumber: string, sessionYear: string | number): string {
  const normalizedBillNumber = normalizeBillNumber(billNumber);
  return `${normalizedBillNumber}-${sessionYear}`;
}